<?php
// +----------------------------------------------------------------------
// | 高德地图坐标转换为CGCS2000坐标系测试
// | Author: AI Assistant
// +----------------------------------------------------------------------

require_once __DIR__ . '/../app/Extends/Helpers/CoordinateTransformer.php';

use App\Extends\Helpers\CoordinateTransformer;

class CoordinateTransformTest
{
    private $transformer;
    
    public function __construct()
    {
        $this->transformer = new CoordinateTransformer();
    }
    
    /**
     * 测试基本坐标转换
     */
    public function testBasicTransform()
    {
        echo "测试基本坐标转换...\n";
        
        // 测试成都天府广场坐标
        $lng = 104.072007;
        $lat = 30.663480;
        
        $result = $this->transformer->gaodeToCGCS2000($lng, $lat);
        
        // 验证结果格式
        $this->assertTrue(isset($result['lng']), '结果应包含经度');
        $this->assertTrue(isset($result['lat']), '结果应包含纬度');
        $this->assertTrue(isset($result['h']), '结果应包含高程');
        
        // 验证坐标范围合理性
        $this->assertTrue($result['lng'] > 100 && $result['lng'] < 110, '经度范围应合理');
        $this->assertTrue($result['lat'] > 25 && $result['lat'] < 35, '纬度范围应合理');
        
        echo "✓ 基本坐标转换测试通过\n";
        echo "输入: {$lng}, {$lat}\n";
        echo "输出: {$result['lng']}, {$result['lat']}\n\n";
    }
    
    /**
     * 测试批量转换
     */
    public function testBatchTransform()
    {
        echo "测试批量坐标转换...\n";
        
        $coordinates = [
            ['lng' => 104.072007, 'lat' => 30.663480], // 成都
            ['lng' => 116.397477, 'lat' => 39.909652], // 北京
            ['lng' => 121.487899, 'lat' => 31.240391], // 上海
        ];
        
        $results = $this->transformer->batchTransform($coordinates);
        
        $this->assertTrue(count($results) === 3, '应返回3个转换结果');
        
        foreach ($results as $i => $result) {
            $this->assertTrue(isset($result['lng']), "结果{$i}应包含经度");
            $this->assertTrue(isset($result['lat']), "结果{$i}应包含纬度");
            $this->assertTrue(isset($result['h']), "结果{$i}应包含高程");
        }
        
        echo "✓ 批量坐标转换测试通过\n";
        echo "转换了 " . count($results) . " 个坐标点\n\n";
    }
    
    /**
     * 测试七参数设置
     */
    public function testSevenParamsSettings()
    {
        echo "测试七参数设置...\n";
        
        // 获取默认参数
        $defaultParams = $this->transformer->getSevenParams();
        $this->assertTrue(count($defaultParams) === 7, '应有7个参数');
        
        // 设置自定义参数
        $customParams = [
            'dx' => -20.0,
            'dy' => 160.0,
            's' => 1.000050
        ];
        
        $this->transformer->setSevenParams($customParams);
        $newParams = $this->transformer->getSevenParams();
        
        $this->assertTrue($newParams['dx'] === -20.0, 'dx参数应已更新');
        $this->assertTrue($newParams['dy'] === 160.0, 'dy参数应已更新');
        $this->assertTrue($newParams['s'] === 1.000050, 's参数应已更新');
        
        echo "✓ 七参数设置测试通过\n\n";
    }
    
    /**
     * 测试边界条件
     */
    public function testBoundaryConditions()
    {
        echo "测试边界条件...\n";
        
        // 测试中国境外坐标（应该直接返回WGS84坐标）
        $outsideChina = $this->transformer->gaodeToCGCS2000(0, 0); // 非洲几内亚湾
        $this->assertTrue(isset($outsideChina['lng']), '境外坐标应正常转换');
        
        // 测试极值坐标
        try {
            $extreme = $this->transformer->gaodeToCGCS2000(180, 90);
            $this->assertTrue(isset($extreme['lng']), '极值坐标应正常处理');
        } catch (Exception $e) {
            // 极值坐标可能会抛出异常，这是正常的
            echo "极值坐标处理异常（正常）: " . $e->getMessage() . "\n";
        }
        
        echo "✓ 边界条件测试通过\n\n";
    }
    
    /**
     * 测试精度验证
     */
    public function testAccuracy()
    {
        echo "测试转换精度...\n";
        
        $lng = 104.072007;
        $lat = 30.663480;
        
        // 多次转换同一坐标，验证结果一致性
        $result1 = $this->transformer->gaodeToCGCS2000($lng, $lat);
        $result2 = $this->transformer->gaodeToCGCS2000($lng, $lat);
        
        $lngDiff = abs($result1['lng'] - $result2['lng']);
        $latDiff = abs($result1['lat'] - $result2['lat']);
        
        $this->assertTrue($lngDiff < 1e-10, '多次转换结果应一致（经度）');
        $this->assertTrue($latDiff < 1e-10, '多次转换结果应一致（纬度）');
        
        echo "✓ 转换精度测试通过\n";
        echo "经度差异: {$lngDiff}\n";
        echo "纬度差异: {$latDiff}\n\n";
    }
    
    /**
     * 性能测试
     */
    public function testPerformance()
    {
        echo "测试转换性能...\n";
        
        $testCount = 1000;
        $coordinates = [];
        
        // 生成测试数据
        for ($i = 0; $i < $testCount; $i++) {
            $coordinates[] = [
                'lng' => 104 + rand(-100, 100) / 1000,
                'lat' => 30 + rand(-100, 100) / 1000
            ];
        }
        
        $startTime = microtime(true);
        $results = $this->transformer->batchTransform($coordinates);
        $endTime = microtime(true);
        
        $duration = $endTime - $startTime;
        $avgTime = $duration / $testCount * 1000; // 毫秒
        
        $this->assertTrue(count($results) === $testCount, "应转换{$testCount}个坐标");
        
        echo "✓ 性能测试通过\n";
        echo "转换{$testCount}个坐标耗时: " . number_format($duration, 4) . "秒\n";
        echo "平均每个坐标耗时: " . number_format($avgTime, 4) . "毫秒\n\n";
    }
    
    /**
     * 断言方法
     */
    private function assertTrue($condition, $message = '')
    {
        if (!$condition) {
            throw new Exception("断言失败: {$message}");
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始运行坐标转换测试...\n\n";
        
        try {
            $this->testBasicTransform();
            $this->testBatchTransform();
            $this->testSevenParamsSettings();
            $this->testBoundaryConditions();
            $this->testAccuracy();
            $this->testPerformance();
            
            echo "🎉 所有测试通过！\n";
            
        } catch (Exception $e) {
            echo "❌ 测试失败: " . $e->getMessage() . "\n";
            echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
        }
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new CoordinateTransformTest();
    $test->runAllTests();
}
