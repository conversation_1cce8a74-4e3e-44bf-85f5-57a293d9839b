<?php
// +----------------------------------------------------------------------
// | 坐标转换控制器
// | Author: AI Assistant
// +----------------------------------------------------------------------

namespace App\Http\Controllers;

use App\Extends\Helpers\CoordinateTransformer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class CoordinateController extends Controller
{
    private $transformer;
    
    public function __construct()
    {
        $this->transformer = new CoordinateTransformer();
    }
    
    /**
     * 单个坐标转换
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function transform(Request $request): JsonResponse
    {
        // 验证输入参数
        $validator = Validator::make($request->all(), [
            'lng' => 'required|numeric|between:-180,180',
            'lat' => 'required|numeric|between:-90,90',
            'h' => 'nullable|numeric'
        ], [
            'lng.required' => '经度参数必填',
            'lng.numeric' => '经度必须为数字',
            'lng.between' => '经度范围必须在-180到180之间',
            'lat.required' => '纬度参数必填',
            'lat.numeric' => '纬度必须为数字',
            'lat.between' => '纬度范围必须在-90到90之间',
            'h.numeric' => '高程必须为数字'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $lng = $request->input('lng');
        $lat = $request->input('lat');
        $h = $request->input('h', 0);
        
        try {
            $result = $this->transformer->gaodeToCGCS2000($lng, $lat, $h);
            
            return response()->json([
                'success' => true,
                'message' => '坐标转换成功',
                'data' => [
                    'input' => [
                        'lng' => $lng,
                        'lat' => $lat,
                        'h' => $h,
                        'coordinate_system' => 'GCJ02 (高德地图)'
                    ],
                    'output' => [
                        'lng' => $result['lng'],
                        'lat' => $result['lat'],
                        'h' => $result['h'],
                        'coordinate_system' => 'CGCS2000'
                    ],
                    'difference' => [
                        'lng_diff' => $result['lng'] - $lng,
                        'lat_diff' => $result['lat'] - $lat,
                        'h_diff' => $result['h'] - $h
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('坐标转换失败', [
                'input' => ['lng' => $lng, 'lat' => $lat, 'h' => $h],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '坐标转换失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 批量坐标转换
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchTransform(Request $request): JsonResponse
    {
        // 验证输入参数
        $validator = Validator::make($request->all(), [
            'coordinates' => 'required|array|min:1|max:1000',
            'coordinates.*.lng' => 'required|numeric|between:-180,180',
            'coordinates.*.lat' => 'required|numeric|between:-90,90',
            'h' => 'nullable|numeric'
        ], [
            'coordinates.required' => '坐标数组必填',
            'coordinates.array' => '坐标必须为数组格式',
            'coordinates.min' => '至少需要1个坐标点',
            'coordinates.max' => '最多支持1000个坐标点',
            'coordinates.*.lng.required' => '每个坐标点的经度必填',
            'coordinates.*.lng.numeric' => '经度必须为数字',
            'coordinates.*.lng.between' => '经度范围必须在-180到180之间',
            'coordinates.*.lat.required' => '每个坐标点的纬度必填',
            'coordinates.*.lat.numeric' => '纬度必须为数字',
            'coordinates.*.lat.between' => '纬度范围必须在-90到90之间',
            'h.numeric' => '高程必须为数字'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        $coordinates = $request->input('coordinates');
        $h = $request->input('h', 0);
        
        try {
            $results = $this->transformer->batchTransform($coordinates, $h);
            
            // 组装返回数据
            $transformedData = [];
            for ($i = 0; $i < count($coordinates); $i++) {
                $transformedData[] = [
                    'index' => $i,
                    'input' => [
                        'lng' => $coordinates[$i]['lng'],
                        'lat' => $coordinates[$i]['lat'],
                        'h' => $h
                    ],
                    'output' => [
                        'lng' => $results[$i]['lng'],
                        'lat' => $results[$i]['lat'],
                        'h' => $results[$i]['h']
                    ],
                    'difference' => [
                        'lng_diff' => $results[$i]['lng'] - $coordinates[$i]['lng'],
                        'lat_diff' => $results[$i]['lat'] - $coordinates[$i]['lat']
                    ]
                ];
            }
            
            return response()->json([
                'success' => true,
                'message' => '批量坐标转换成功',
                'data' => [
                    'total_count' => count($coordinates),
                    'success_count' => count($results),
                    'coordinate_system' => [
                        'input' => 'GCJ02 (高德地图)',
                        'output' => 'CGCS2000'
                    ],
                    'results' => $transformedData
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('批量坐标转换失败', [
                'input_count' => count($coordinates),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '批量坐标转换失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 设置七参数
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function setSevenParams(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'dx' => 'nullable|numeric',
            'dy' => 'nullable|numeric',
            'dz' => 'nullable|numeric',
            'rx' => 'nullable|numeric',
            'ry' => 'nullable|numeric',
            'rz' => 'nullable|numeric',
            's' => 'nullable|numeric'
        ], [
            'dx.numeric' => 'X平移参数必须为数字',
            'dy.numeric' => 'Y平移参数必须为数字',
            'dz.numeric' => 'Z平移参数必须为数字',
            'rx.numeric' => 'X旋转参数必须为数字',
            'ry.numeric' => 'Y旋转参数必须为数字',
            'rz.numeric' => 'Z旋转参数必须为数字',
            's.numeric' => '尺度因子必须为数字'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        try {
            // 获取当前参数
            $currentParams = $this->transformer->getSevenParams();
            
            // 更新参数
            $newParams = array_filter($request->only(['dx', 'dy', 'dz', 'rx', 'ry', 'rz', 's']), function($value) {
                return $value !== null;
            });
            
            if (empty($newParams)) {
                return response()->json([
                    'success' => false,
                    'message' => '至少需要提供一个参数'
                ], 400);
            }
            
            $this->transformer->setSevenParams($newParams);
            $updatedParams = $this->transformer->getSevenParams();
            
            return response()->json([
                'success' => true,
                'message' => '七参数设置成功',
                'data' => [
                    'previous_params' => $currentParams,
                    'updated_params' => $updatedParams,
                    'changed_params' => $newParams
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('七参数设置失败', [
                'input' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '七参数设置失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取当前七参数
     * 
     * @return JsonResponse
     */
    public function getSevenParams(): JsonResponse
    {
        try {
            $params = $this->transformer->getSevenParams();
            
            return response()->json([
                'success' => true,
                'message' => '获取七参数成功',
                'data' => [
                    'seven_params' => $params,
                    'description' => [
                        'dx' => 'X轴平移 (米)',
                        'dy' => 'Y轴平移 (米)',
                        'dz' => 'Z轴平移 (米)',
                        'rx' => 'X轴旋转 (弧度)',
                        'ry' => 'Y轴旋转 (弧度)',
                        'rz' => 'Z轴旋转 (弧度)',
                        's' => '尺度因子'
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取七参数失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取API使用说明
     * 
     * @return JsonResponse
     */
    public function getApiDoc(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '坐标转换API使用说明',
            'data' => [
                'description' => '高德地图坐标(GCJ02)转换为CGCS2000坐标系',
                'endpoints' => [
                    [
                        'method' => 'POST',
                        'url' => '/api/coordinate/transform',
                        'description' => '单个坐标转换',
                        'parameters' => [
                            'lng' => '经度 (必填, -180~180)',
                            'lat' => '纬度 (必填, -90~90)',
                            'h' => '高程 (可选, 默认0)'
                        ]
                    ],
                    [
                        'method' => 'POST',
                        'url' => '/api/coordinate/batch-transform',
                        'description' => '批量坐标转换',
                        'parameters' => [
                            'coordinates' => '坐标数组 (必填, 最多1000个)',
                            'h' => '高程 (可选, 默认0)'
                        ]
                    ],
                    [
                        'method' => 'POST',
                        'url' => '/api/coordinate/set-seven-params',
                        'description' => '设置七参数',
                        'parameters' => [
                            'dx, dy, dz' => '平移参数 (米)',
                            'rx, ry, rz' => '旋转参数 (弧度)',
                            's' => '尺度因子'
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'url' => '/api/coordinate/seven-params',
                        'description' => '获取当前七参数'
                    ]
                ],
                'coordinate_systems' => [
                    'input' => 'GCJ02 (高德地图坐标系)',
                    'output' => 'CGCS2000 (中国大地坐标系2000)'
                ]
            ]
        ]);
    }
}
