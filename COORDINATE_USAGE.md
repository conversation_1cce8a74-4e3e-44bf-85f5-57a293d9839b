# 高德地图坐标转CGCS2000使用指南

## 🎯 功能概述

本项目实现了高德地图坐标系（GCJ02）到中国大地坐标系2000（CGCS2000）的完整转换解决方案。

## 📁 已创建的文件

```
app/Extends/Helpers/
├── CoordinateTransformer.php          # 主要转换类
├── Examples/
│   └── CoordinateTransformExample.php # 使用示例
app/Http/Controllers/
└── CoordinateController.php           # Laravel控制器
routes/
└── coordinate.php                      # API路由配置
tests/
└── CoordinateTransformTest.php        # 测试文件
docs/
└── CoordinateTransform_README.md      # 详细文档
```

## 🚀 快速开始

### 1. 基本使用

```php
use App\Extends\Helpers\CoordinateTransformer;

// 创建转换器
$transformer = new CoordinateTransformer();

// 高德地图坐标（成都天府广场）
$result = $transformer->gaodeToCGCS2000(104.072007, 30.663480, 500);

echo "CGCS2000坐标: {$result['lng']}, {$result['lat']}, {$result['h']}";
```

### 2. 批量转换

```php
$coordinates = [
    ['lng' => 104.072007, 'lat' => 30.663480], // 成都
    ['lng' => 116.397477, 'lat' => 39.909652], // 北京
];

$results = $transformer->batchTransform($coordinates);
```

### 3. API接口使用

#### 注册路由
在 `routes/api.php` 中添加：
```php
require __DIR__.'/coordinate.php';
```

#### 调用API
```bash
# 单个坐标转换
curl -X POST http://your-domain/api/coordinate/transform \
  -H "Content-Type: application/json" \
  -d '{"lng": 104.072007, "lat": 30.663480, "h": 500}'

# 批量转换
curl -X POST http://your-domain/api/coordinate/batch-transform \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [
      {"lng": 104.072007, "lat": 30.663480},
      {"lng": 116.397477, "lat": 39.909652}
    ],
    "h": 0
  }'
```

## 🔧 配置七参数

```php
// 设置自定义七参数（根据具体地区获取）
$transformer->setSevenParams([
    'dx' => -16.9,      // X平移(m)
    'dy' => 156.4,      // Y平移(m)
    'dz' => 173.8,      // Z平移(m)
    'rx' => 0.00001,    // X旋转(rad)
    'ry' => 0.00002,    // Y旋转(rad)
    'rz' => 0.00003,    // Z旋转(rad)
    's' => 1.00004      // 尺度因子
]);
```

## 🧪 运行测试

```bash
# 运行PHP测试
php tests/CoordinateTransformTest.php

# 运行示例
php app/Extends/Helpers/Examples/CoordinateTransformExample.php
```

## 📊 转换精度

- **GCJ02 → WGS84**: 精度约1-2米
- **WGS84 → CGCS2000**: 精度取决于七参数，通常可达厘米级

## ⚠️ 重要提醒

1. **七参数获取**: 示例中的七参数仅供测试，实际使用需要向当地测绘部门获取精确参数
2. **适用范围**: 主要适用于中国境内坐标
3. **性能限制**: 批量转换建议每次不超过1000个坐标点

## 📖 详细文档

更多详细信息请查看：
- [完整文档](docs/CoordinateTransform_README.md)
- [使用示例](app/Extends/Helpers/Examples/CoordinateTransformExample.php)
- [测试文件](tests/CoordinateTransformTest.php)

## 🎉 测试结果

已验证转换功能正常：
- 成都天府广场坐标转换成功
- GCJ02 (104.072007, 30.663480) → WGS84 (104.069489, 30.665891)
- 坐标差值：经度差-0.0025，纬度差0.0024

转换功能已就绪，可以开始使用！
