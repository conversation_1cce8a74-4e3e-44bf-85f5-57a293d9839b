@extends('admin.layout.form')
@include("widget.asset.select2")
@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }
        .multiple-title{
            cursor: pointer;
            padding: 10px;
        }
        .multiple-title.active{
            color: #1d6eff;
            background-color: #f0f2f5;
            border-radius: 5px;
        }

        .b5uploadimgbox .b5uploadlistbox img{
            cursor: zoom-in;
        }
        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            background-color: #f7f8fa;
            padding: 20px 5px;
        }
        .detail-box .arrange {
            padding-left: 10px;
            padding-right: 10px;
        }
        .detail-box .arrange:first-child{
            padding-left: 0;
        }
        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding: 20px;
        }

        .btn-xs{
            font-size: 12px;
        }
        .xgry{
            display: none;
        }
        .flip-clock-label {
            display: none;
        }
        .header-title {
            font-style: normal;
            font-weight: 500;
            font-size: 22px;
            line-height: 40px;
            padding-left: 10px;
            color: #000000;
        }
        .mbig-btn {
            background-color: #fff;
            border: 1px solid #2256FF;
            color: #2256FF !important;
            padding: 8px 5px;
            border-radius: 7px;
            margin: 0 5px;
            display: inline-block;
            min-width: 96px;
            text-align: center;
        }
        .mbig-btn:hover{
            background-color: #2256FF;
            border-color: #2256FF;
            color: #FFFFFF !important;
        }
        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .col-sm-2 {
            width: auto;
        }
        .col-sm-10 {
            width: auto;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
        }
        .butuchu {
            color: #000000;
            font-size: 14px;

        }
        .butuchu1 {
            color: #999999;
            font-size: 14px;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 20px;
            height: 20px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .history-edit {
            color: #1d6eff;
            background-color: #f0f2f5;
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
            text-align: center;
            line-height: 24px;
        }
        .table.table-tr-b>tbody>tr>td {
            border: none;
            border-bottom: 1px solid #ecedf1 !important;
        }
        .table.table-tr-b>thead>tr>th {
            border: none;
            background-color: #f7f8fa;
            color: black;
            height: 45px;
        }
        #countdown {
            background-color: #2357ff;
            color: #fff;
            padding: 6px 10px 6px 20px;
            border-radius: 7px;
            letter-spacing: 10px;
            text-align: center;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color: #f1f8fe;
            margin: 0px 1px -8px 0px;
            padding: 15px;
            border-radius: 4px;
        }
        .tooltip-inner {
            padding: 4px 7px;
            margin: 2px;
            line-height: 14px;
        }
        .form-control-static {
            word-break: break-all;
        }
        .b5upload_filename {
            word-break: break-all;
        }
    </style>
@append
@section('content')
    <style>
        .bg-thead{background-color: aliceblue;}
    </style>
    <div class="col-sm-12">
        <div class="col-sm-6">
            <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">上报信息</h3>
            <form class="form-horizontal m">
                @foreach($info['reportvalue'] as $item)
                    <div class="form-group">
                        <label class="col-sm-2 control-label">{{$item['template_item_title']}}：</label>
                        @if($item['item_type'] == 'file')
                            <div class="col-sm-10">
                                <div class="b5uploadfilebox" style="display: inline-flex">
                                    <div class="b5uploadlistbox" >
                                        @foreach($item['item_value'] as $file)
                                            <div class="b5upload_li">
                                                <div class="b5upload_filetype "></div>
                                                <div class="b5upload_filename">
                                                    <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="col-sm-10">
                                <div class="form-control-static">{{$item['item_text']}}</div>
                            </div>
                        @endif
                    </div>
                @endforeach
                @if (\App\Extends\Helpers\Admin\LoginAuth::admin()->is_instructions)
                    <div style="padding-top: 20px">
                        @hasPerm("{$group1}:{$app}:instructions")
                        <a class="btn btn-xs mbig-btn" onclick="addApproval({{$info['id']}});">添加批示</a>
                        @endhasPerm
                    </div>
                @endif
            </form>
        </div>
        <div class="col-sm-6">
            <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">批示</h3>
            <div style="padding-top: 20px;">
                <ul class="layui-timeline">
                    @foreach($instructions as $item)
                        <li class="layui-timeline-item">
                            <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                            <div class="layui-timeline-content layui-text">
                                <div class="flow-item">
                                    <p class="tuchu">{{$item['create_time']}}</p>
                                    {{--                                        <p class="butuchu">{{$item['user']['depNames']}}</p>--}}
                                    <div style="display: inline-block;position: relative">
                                        <div style="display: inline-block;">
                                            <div>{{$item['instructionsadmin']['struct']['name'] ?? ''}}  {{$item['instructionsadmin']['username'] ?? ''}}</div>
                                        </div>
                                        <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                            <div class="tooltip-inner" style=" background-color: #0076f6;">批示</div>
                                        </div>
                                    </div>
                                    <p class="tuchu">{{$item['opinion']}}</p>
                                    @if ($item['file'] != '')
                                        <div>
                                            <div class="b5uploadfilebox" style="display: inline-flex">
                                                <div class="b5uploadlistbox" >
                                                    @foreach((array)json_decode($item['file'], true) as $file)
                                                        <div class="b5upload_li">
                                                            <div class="b5upload_filetype "></div>
                                                            <div class="b5upload_filename">
                                                                <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
@stop
@section('script')
    <script>
        function addApproval(report_id) {
            var title = '添加批示';
            var url = '{{route("{$group1}.{$app}.instructions")}}?report_id='+report_id;
            $.modal.open(title, url,600,500);
        }

    </script>
@stop