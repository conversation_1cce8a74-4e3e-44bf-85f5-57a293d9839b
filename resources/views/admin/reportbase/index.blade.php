@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')

    <div class="col-sm-12 search-collapse">
        <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 10px;">
            @foreach($tmpList as $key=>$item)
            <li role="presentation" @if(!$key) class="active" @endif>
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_template({{$item['template_id']}})">{{$item['title']}} <span class="template_count" data-id="{{$item['template_id']}}">0</span></a>
            </li>
            @endforeach
        </ul>
        <form id="role-form">
            <input type="hidden" id="template_id" name="template_id" value="{{$tmpList[0]['template_id'] ?? 0}}">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <input type="text" name="between[create_time][start]" id="startTime" placeholder="上报开始时间" readonly>
                        <span>-</span>
                        <input type="text" name="between[create_time][end]" id="endTime" placeholder="上报结束时间" readonly>
                    </li>
                    @if ($user_type == \App\Models\System\Users::TYPE_WGY)
                        <li>
                            <select name="where[group_id]" class="select2">
                                <option value="">所有社区</option>
                                @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                                    <option value="{{$id}}">{{$name}}</option>
                                @endforeach
                            </select>
                        <li>
                    @elseif ($user_type == \App\Models\System\Users::TYPE_CG)
                        <li>
                            <select name="where[group_id]" class="select2">
                                <option value="">所有分组</option>
                                @foreach($urbanGroup as $item)
                                    <option value="{{$item['id']}}">{{$item['name']}}</option>
                                @endforeach
                            </select>
                        <li>
                    @endif
                    <li>
                        <select name="where[is_synchronization]" class="select2">
                            <option value="">是否同步</option>
                            <option value="0">未同步</option>
                            <option value="1">已同步</option>
                        </select>
                    <li>
                    <li>
                        <select name="where[is_instructions]" class="select2">
                            <option value="">是否有批示</option>
                            <option value="0">无批示</option>
                            <option value="1">有批示</option>
                        </select>
                    <li>
                    <li><input type="text" name="keywords" placeholder="搜索" autocomplete="off"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("{$group1}:report:export")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.exportExcel()">
            <i class="fa fa-download"></i> 导出
        </a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#create_time'
                , type: 'month'
                , max: '{{date('Y-m')}}'
            });
        });
        $(function () {
            var options = {
                modalName: "上报记录",
                sortName: 'id',
                sortOrder: "desc",
                columns: [
                    {field: 'id', title: 'ID', align: 'center', sortable: true,visible: false},
                    @if ($user_type == \App\Models\System\Users::TYPE_WGY)
                    {field: 'community.name', title: '社区', align: 'left'},
                    @elseif ($user_type == \App\Models\System\Users::TYPE_CG)
                    {field: 'groupinfo.name', title: '分组', align: 'left'},
                    @endif
                    {field: 'userinfo.username', title: '姓名', align: 'left'},
                    {field: 'userinfo.mobile', title: '电话', align: 'left'},
                    {field: 'create_time', title: '上报时间', align: 'left', sortable: true},
                    {
                        field: 'title', title: '上报内容', align: 'left', formatter: function (value, row, index) {
                            let htmls = '';
                            row.values.forEach((element, index) => {
                                htmls += "<p>" + element.item_title + ': ' + element.item_text + "</p>";
                            });
                            return htmls;
                        }
                    },
                    {field: 'is_synchronization', title: '是否同步', align: 'left',},
                    {field: 'is_instructions', title: '是否有批示', align: 'left',},
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("{{$group1}}:report:detail")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="operateview(\'' + row.id + '\')"> 详情</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ],
                responseHandler:function (res) {
                    if (res.code === 0) {
                        $('.template_count').each(function (i, item){
                            var id = $(item).data('id');
                            if (res.extend[id] > 0) {
                                $(item).html(res.extend[id]);
                            } else {
                                $(item).html('0');
                            }
                        })
                    }
                }
            };
            $.table.init(options);
        });

        function operateview(id) {
            $.operate.detail(id, "1200");
        }

        function switch_template(id) {
            $('#template_id').val(id);
            $.table.search();
        }
    </script>
@stop

