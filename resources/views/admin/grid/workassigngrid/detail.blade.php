@extends('admin.layout.form')

@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }

        .b5uploadimgbox .b5uploadlistbox img{
            cursor: zoom-in;
        }
        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            /*background-color: #f7f8fa;*/
            /*padding: 20px 5px;*/
        }

        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding-right: 20px;
        }
        #flow {
            border-left: 1px solid #e5e5e5;
        }
        .btn-xs{
            font-size: 14px;
        }

        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
            font-weight: bold;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 24px;
            height: 24px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color:#f1f8fe ;
            margin: -6px;
            padding: 5px;
            border-radius: 4px;
        }
        .tooltip-inner {
            /*padding: 6px 11px;*/
            margin: 2px;
        }
        #base-info .col-sm-2 {
            width: auto;
        }
        .form-horizontal .row .form-group {
            margin-right: 0;
            margin-left: 0;
        }
        .block {
            width: 100%;
            background-color: #f2f2f2;
            border-radius: 6px;
            margin-bottom: 10px;
            padding: 10px;
            position: relative;
        }
        .block-item{
            padding-bottom: 10px;
        }
        .block.active {
            background-color: #deeafd;
        }
        .block.pointer {
            cursor: pointer;
        }
        .block-item:last-child{
            padding-bottom: 0;
        }
        .block:hover .chevron-right i {
            display: inline;
        }
        .annex {
            display: flex;
            flex-wrap: wrap;
            padding-top: 5px;
        }
        .annex-item {
            display: inline-block;
            padding: 5px;
            margin: 5px;
            background-color: #FFFFFF;
            border-radius: 10px;
        }
        .user-label span {
            background-color: #fff;
            padding: 5px 10px;
            border-radius: 5px;
            margin-left: 10px;
        }
        .chevron-right {
            width: 30px;
            text-align: right;
        }
        .chevron-right i {
            display: none;
        }
        .progress {
            background-color: #fff;
            margin:10px 0;
        }
        .progress .progress-bar {
            background-color: #aaaaaa;
        }
        .data-status {
            position: absolute;
            right: 5px;
            top: -28px;
        }
        .data-status img{
            width: 100px;
            height: 100px;
        }
    </style>
@append
@section('content')
    @include('admin.grid.workassigngrid.detailhtml')
@stop

@section('script')
    <script>
        $(document).on('click', '#switch-flow .tree-switch-btn',function (){
            let value = $(this).data('value');
            let hideValue = value === 'tree' ? 'table' : 'tree';
            $(this).addClass('active');
            $('#switch-flow .tree-switch-btn[data-value='+hideValue+']').removeClass('active');
            $('.flow-'+value).show();
            $('.flow-'+hideValue).hide();
        });
    </script>
@stop
