<form action="" class="form-horizontal" style="padding-bottom: 10px;">
    <div>
        <div class="row">
            <div>工作内容</div>
        </div>
        <div class="row">
            @if (!empty($info['record']))
                @foreach($info['record'] as $key=>$record)
                    <div class="block">
                        @if($info['work_assign']['work_type'] == 2)
                            <div class="block-item">工作量：{{$record['workload']}}</div>
                        @endif
                        <div class="block-item">{{$record['content']}}</div>
                        @if ($record['annex'])
                            <div class="block-item">
                                <div class="annex">
                                    @foreach ($record['annex'] as $value)
                                        <a class="annex-item" href="{{$value['url']}}" target="_blank">{{$value['originName']}}</a>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        <div class="block-item">
                            {{$record['create_time']}}
                        </div>
                        <div class="clearfix"></div>
                        @if ($key == 0)
                            @if ($info['audit_status'] == 1)
                                <div class="data-status">
                                    <img src="{{asset('static/images/shz.png')}}">
                                </div>
                            @elseif ($info['status'] == 3)
                                <div class="data-status">
                                    <img src="{{asset('static/images/ywc.png')}}">
                                </div>
                            @endif
                        @endif
                    </div>
                @endforeach
            @else
            <div class="block">
                暂未提交
            </div>
            @endif
        </div>
    </div>
</form>
<div style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-bottom: 10px;">
    <div style="display: flex;align-items: center">
        审核记录
    </div>
    <div class="tree-switch" id="switch-flow" style="float: right">
        <div class="tree-switch-btn active" data-value="tree" type="button">树状</div>
        <div class="tree-switch-btn" data-value="table" type="button">列表</div>
    </div>
</div>
<div class="flow-tree">
    <ul class="layui-timeline">
        @foreach($audit as $item)
            <li class="layui-timeline-item">
                <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                <div class="layui-timeline-content layui-text">
                    <div class="flow-item">
                        <p class="butuchu">{{$item['audit_time']}}</p>
                        <div style="display: inline-block;">
                            @foreach($item['user_list'] as $user)
                                <div class="butuchu">{{$user['struct_name']}}  {{$user['username']}}</div>
                            @endforeach
                        </div>
                        <div style="display: inline-block;position: relative">
                            <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                            </div>
                        </div>
                        @if($item['note'] != '') <p>{{$item['note']}}</p> @endif
                        @if($item['signature'] != '')
                            <p>
                                电子签名：<img style="height: 50px" src="{{$item['signature']}}" alt="">
                            </p>
                        @endif
                    </div>
                </div>
            </li>
        @endforeach
    </ul>
</div>
<div class="flow-table" style="display: none;">
    <table class="table table-hover table-tr-b">
        <thead style="background-color: #eff3f8;" >
        <tr>
            <th>操作时间</th>
            <th>操作人</th>
            <th style="min-width: 100px;">操作类型</th>
            <th>操作信息</th>
        </tr>
        </thead>
        <tbody>
        @foreach($audit as $item)
            <tr>
                <td>{{$item['audit_time']}}</td>
                <td>
                    @foreach($item['user_list'] as $user)
                        <div class="butuchu">{{$user['struct_name']}}  {{$user['username']}}</div>
                    @endforeach
                </td>
                <td>
                    <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                        <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                    </div>
                </td>
                <td>
                    @if($item['note'] != '') <p>{{$item['note']}}</p> @endif
                </td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>
