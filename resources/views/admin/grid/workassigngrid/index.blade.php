@extends('admin.layout.layout')
@include("widget.asset.select2")
@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <input type="hidden" name="id" value="{{$input['id']}}">
            <div class="select-list">
                <ul>
                    <li>完成状态：
                        <select name="where[status]" >
                            <option value="">请选择</option>
                            <option value="1">未开始</option>
                            <option value="2">进行中</option>
                            <option value="3">已完成</option>
                        </select>
                    </li>
                    <li>审核状态：
                        <select name="where[audit_status]" >
                            <option value="">请选择</option>
                            <option value="1">待审核</option>
                            <option value="2">通过</option>
                            <option value="3">回退</option>
                        </select>
                    </li>
                    <li>时间状态：
                        <select name="is_timeout" >
                            <option value="">请选择</option>
                            <option value="0">正常</option>
                            <option value="1">临期</option>
                            <option value="2">超时</option>
                        </select>
                    </li>
                    <li>网格员：<input type="text" name="grid_user" placeholder="姓名或手机号" autocomplete="off"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "网格员完成情况",
                sortName:'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'center', sortable: true},
                        @if (!C_ID)
                    {field: 'community.name', title: '社区', align: 'left'},
                        @endif
                    {field: 'grid_user.grid.name', title: '网格', align: 'left'},
                    {field: 'grid_user.username', title: '网格员', align: 'left'},
                    {field: 'grid_user.mobile', title: '手机号', align: 'left'},
                    @if($info->workAssign['work_type'] == 2)
                    {field: 'completed_rate', title: '已完成', align: 'center',formatter: function (value, row, index) {
                            return value + '%';
                        }},
                    @endif
                    {field: 'status_text', title: '完成状态', align: 'center'},
                    {field: 'audit_status_text', title: '审核状态', align: 'center'},
                    {field: 'is_timeout', title: '时间状态', align: 'left',formatter: function (value, row, index) {
                            if (value === 0) {
                                return '正常';
                            } else if (value === 1) {
                                return '临期'
                            } else if (value === 2) {
                                return '超时'
                            }
                        }},
                    {field: 'create_time', title: '创建时间', align: 'center', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'center', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("grid:workassigngrid:detail")
                        if (row.btns.detail) {
                                actions.push('<a class="btn btn-primary btn-xs" href="javascript:;" onclick="$.operate.detail(\'' + row.id + '\')"><i class="fa fa-info"></i>详情</a> ');
                            }
                        @endhasPerm
                        @hasPerm("grid:workassigngrid:audit")
                        if (row.btns.audit) {
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="audit(\'' + row.id + '\')">审核</a> ');
                            }
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function audit(id) {
            $.modal.open("审核", '{{route('grid.workassigngrid.audit')}}?id='+id, 1000);
        }
    </script>
@stop

