@extends('admin.layout.form')

@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">分配统筹员：</label>
            <div class="col-sm-8">
                <select name="task_help_id" id="task_help"xm-select-radio xm-select="task_help" required  xm-select-search>
                    @foreach($helps as $id=>$item)
                        <option value="{{$id}}" @if($id == $info->task_help_id)) selected @endif>{{$item}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="assign_help_note" placeholder="请填写" class="form-control" rows="3">{{$info->assign_help_note}}</textarea>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>

        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassign.assignhelp')}}', $('#form-edit').serialize());
            }
        }
    </script>
@stop

