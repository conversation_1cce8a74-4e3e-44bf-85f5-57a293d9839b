@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('content')
    <script>
        var annexIndex = 0;
    </script>
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">标题：</label>
            <div class="col-sm-9">
                <input type="text" name="title" value="{{$info['title']}}" class="form-control" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">内容：</label>
            <div class="col-sm-9">
                <textarea type="text" name="content" rows="3" class="form-control" required autocomplete="off">{{$info['content']}}</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">附件：</label>
            <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                <div class="col-sm-9">
                    {{--                    <input type="hidden" name="files" value="" id="files" required>--}}
                    <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                        <button type="button" class="btn-b5upload btn btn-primary btn-sm" id="annex" data-exts=""  data-multi="9"  data-cat="work" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 最多上传9个文件</span>
                        <div class="b5uploadlistbox annex_filelist" id="annex_filelist"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">完成日期：</label>
            <div class="col-sm-9">
                <input type="text" name="completion_time" id="completion_time" value="{{date('Y-m-d', $info['completion_time'])}}" style="width: 150px;" class="form-control" required readonly autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">任务等级：</label>
            <div class="col-sm-4">
                <select name="level_id" id="level_id" required class="select2">
                    <option value="">请选择</option>
                    @foreach($level as $id=>$name)
                        <option value="{{$id}}" @if($id == $info['level_id']) selected @endif >{{$name}}</option>
                    @endforeach
                </select>
            </div>
            <label class="col-sm-2 control-label is-required">任务类别：</label>
            <div class="col-sm-4">
                <select name="category_id" id="category_id" required class="select2">
                    <option value="">请选择</option>
                    @foreach($category as $id=>$name)
                        <option value="{{$id}}" @if($id == $info['level_id']) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">工作类型：</label>
            <div class="col-sm-4">
                @foreach($workType as $id=>$name)
                    <label class="radio-box"><input type="radio"  value="{{$id}}"  name="work_type" @if($id == $info['work_type']) checked @endif required class="work_type">{{$name}}</label>
                @endforeach
            </div>
            <div class="workload" @if(2 != $info['work_type']) style="display: none" @endif>
                <label class="col-sm-2 control-label is-required">每人工作量：</label>
                <div class="col-sm-4">
                    <input type="number" min="1" name="workload" id="workload" value="{{$info['workload'] ?: ''}}" style="width: 150px;" class="form-control" required autocomplete="off"/>
                </div>
            </div>
        </div>

        @if($info['publish_type'] == 2)
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">分管领导：</label>
                <div class="col-sm-9">
                    <select name="assign_assign_leader" id="assign_assign_leader" class="select2" required>
                        @foreach($assign_assign_leader as $id=>$name)
                            <option value="{{$id}}" @if($id == $info['assign_assign_leader']) selected @endif>{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">下派方式：</label>
                <div class="col-sm-9">
                    <label class="radio-box"><input type="radio" @if($info['main_publish_type'] == 1) checked @endif value="1"  name="leader_publish_type" class="leader_publish_type">直接下派</label>
                    <label class="radio-box"><input type="radio" @if($info['main_publish_type'] == 2) checked @endif value="2"  name="leader_publish_type" class="leader_publish_type">城运中心下派</label>
                    <label class="radio-box"><input type="radio" @if($info['main_publish_type'] == 3) checked @endif value="3"  name="leader_publish_type" class="leader_publish_type">分管领导下派</label>
                </div>
            </div>
            <div id="direct" @if($info['main_publish_type'] != 1) style="display: none" @endif>
                <div class="form-group">
                    <label class="col-sm-2 control-label">抄送科室：</label>
                    <div class="col-sm-9">
                        <div class="input-group">
                            <input type="hidden" id="treeId" name="carbon_copy_structs" value="{{$info['carbon_copy_structs']}}">
                            <input type="text" id="treeName" value="{{$info['carbon_copy_structs_name']}}" class="form-control" placeholder="请选择抄送科室" readonly autocomplete="off"/>
                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">下派社区：</label>
                    <div class="col-sm-9">
                        <select name="cids" id="cids" xm-select="select3" required xm-select-search>
                            @foreach($community as $id=>$name)
                                <option value="{{$id}}" @if(in_array($id, $info['cids'])) selected @endif >{{$name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        @elseif($info['publish_type'] == 3)
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">下派方式：</label>
                <div class="col-sm-9">
                    <label class="radio-box"><input type="radio" @if($info['leader_publish_type'] == 1) checked @endif value="1"  name="leader_publish_type" class="leader_publish_type">直接下派</label>
                    <label class="radio-box"><input type="radio" @if($info['leader_publish_type'] == 2) checked @endif value="2"  name="leader_publish_type" class="leader_publish_type">城运中心下派</label>
                </div>
            </div>
            <div id="direct" @if($info['leader_publish_type'] != 1) style="display: none" @endif>
                <div class="form-group">
                    <label class="col-sm-2 control-label">抄送科室：</label>
                    <div class="col-sm-9">
                        <div class="input-group">
                            <input type="hidden" id="treeId" name="carbon_copy_structs" value="{{$info['carbon_copy_structs']}}">
                            <input type="text" id="treeName" value="{{$info['carbon_copy_structs_name']}}" class="form-control" placeholder="请选择抄送科室" readonly autocomplete="off"/>
                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">下派社区：</label>
                    <div class="col-sm-9">
                        <select name="cids" id="cids" xm-select="select3" required xm-select-search>
                            @foreach($community as $id=>$name)
                                <option value="{{$id}}" @if(in_array($id, $info['cids'])) selected @endif>{{$name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        @elseif($info['publish_type'] == 1)
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">下派社区：</label>
                <div class="col-sm-9">
                    <select name="cids" id="cids" xm-select="select2" required xm-select-search>
                        @foreach($community as $id=>$name)
                            <option value="{{$id}}" @if(in_array($id, $info['cids'])) selected @endif>{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        @elseif($info['publish_type'] == 5)
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">下派社区：</label>
                <div class="col-sm-9">
                    <select name="cids" id="cids" xm-select="select2" required xm-select-search>
                        @foreach($community as $id=>$name)
                            <option value="{{$id}}" @if(in_array($id, $info['cids'])) selected @endif>{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        @elseif($info['publish_type'] == 4)
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">选择统筹员：</label>
                <div class="col-sm-9">
                    <select name="task_help_id" id="task_help" xm-select-max="1" xm-select="task_help" required  xm-select-search>
                        @foreach($helps as $id=>$item)
                            <option value="{{$id}}" @if($id == $help_id)) selected @endif>{{$item}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        @endif
    </form>
@stop

@section('script')
    <script>
        $(function () {
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#completion_time'
                });
            });
            b5uploadfileinit('annex',uploadAfter);
            dragula([annex_filelist]);
            @foreach($info['annex'] as $item)
                uploadAfter('annex', @json($item));
            @endforeach
            //选择组织架构
            $("#treeName").click(function () {
                var treeId=$("#treeId").val();
                var url = urlcreate("{{route('system.struct.tree')}}","id="+treeId+"&parent=0&ismult=1&type=1");
                var options = {
                    title: '科室选择',
                    width: "380",
                    url: url,
                    callBack: doSubmit
                };
                $.modal.openOptions(options);
            });
            $(document).on("ifChanged", ".leader_publish_type", function () {
                let value = $('input[name=leader_publish_type]:checked').val();
                if (value == 1) {
                    $('#direct').show();
                } else {
                    $('#direct').hide();
                }
            });
        })
        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen(oesUrl, $('#form-edit').serialize());
            }
        }
        function doSubmit(index, layero){
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
        }
        function uploadAfter(id, data) {
            annexIndex++;
            let ext = data.ext;
            let url = data.url;
            let filename = data.originName;
            if(!filename){
                filename = getFileName(url);
            }
            var classname = getExtClass(url);
            var html='<div class="b5upload_li">' +
                '           <input type="hidden" name="annex['+annexIndex+'][url]" value="'+url+'">' +
                '           <input type="hidden" name="annex['+annexIndex+'][originName]" value="'+filename+'">' +
                '           <input type="hidden" name="annex['+annexIndex+'][ext]" value="'+ext+'">' +
                '           <div class="b5upload_filetype '+classname+'"></div>' +
                '           <div class="b5upload_filename">'+filename+
                '        </div>' +
                '               <div class="b5upload_fileop">' +
                '                   <a href="javascript:;" onclick="b5uploadImgRemove(this)"><i class="fa fa-trash-o"></i>删除</a>' +
                '                  <a href="'+url+'" target="_blank"><i class="fa fa-hand-o-right"></i>查看</a>' +
                '               </div>' +
                '      </div>';
            b5uploadhtmlshow(id,html);
        }
    </script>
@stop

