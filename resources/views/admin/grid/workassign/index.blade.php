@extends('admin.layout.layout')
@include("widget.asset.select2")
@section('content')
<div class="col-sm-12 search-collapse">
    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 10px;">
        <li role="presentation" class="active">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(0)">全部 <span id="status0">0</span></a>
        </li>

        <li role="presentation" class="">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(1)">未开始 <span id="status1">0</span></a>
        </li>

        <li role="presentation" class="">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(2)">进行中 <span id="status2">0</span></a>
        </li>
        <li role="presentation" class="">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(3)">已完成 <span id="status3">0</span></a>
        </li>
        <li role="presentation" class="">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(4)">已取消 <span id="status4">0</span></a>
        </li>
    </ul>
    <form id="role-form">
        <input type="hidden" name="status" value="">
        <div class="select-list">
            <ul>
                <li class="select-time">
                    <input type="text" name="between[work_assign.create_time][start]" id="startTime" placeholder="下派开始时间" readonly  autocomplete="off">
                    <span>-</span>
                    <input type="text" name="between[work_assign.create_time][end]" id="endTime" placeholder="下派结束时间" readonly  autocomplete="off">
                </li>
                <li>
                    <select name="where[work_assign.publish_type]" id="publish_type" class="select2">
                        <option value="">下派类型</option>
                        <option value="1">街道科室</option>
                        <option value="2">主要领导</option>
                        <option value="3">分管领导</option>
                        <option value="5">城运中心</option>
                    </select>
                </li>
                <li id="assign-struct" style="display: none">
                    <select name="struct_id" class="select2">
                        <option value="">下派科室</option>
                        @foreach($struct as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                </li>
                @if(!C_ID)
                    <li>
                        <select name="cid" class="select2">
                            <option value="">下派社区</option>
                            @foreach($community as $id=>$name)
                                <option value="{{$id}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </li>
                @endif
                <li>
                    <select name="where[work_assign.level_id]" class="select2">
                        <option value="">任务等级</option>
                        @foreach($level as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                </li>
                <li>
                    <select name="where[work_assign.category_id]" class="select2">
                        <option value="">任务类别</option>
                        @foreach($category as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                </li>
                <li>
                    <select name="is_timeout" class="select2">
                        <option value="">时间状态</option>
                        <option value="0">正常</option>
                        <option value="1">临期</option>
                        <option value="2">超时</option>
                    </select>
                </li>
                <li><input type="text" name="like[work_assign.title]" value="" placeholder="请输入任务标题"  autocomplete="off"></li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
    @if (!C_ID)
        @if(!empty($admin->assign_leader) || !empty($admin->assign_struct_leader) || !empty($admin->assign_assign_leader) || !empty($admin->assign_city_centre))
            @hasPerm("grid:workassign:add")
                <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增下派</a>
            @endhasPerm
        @endif
    @endif
    @hasPerm("grid:workassign:export")
        <a class="btn btn-warning" onclick="exportExcel()"><i class="fa fa-download"></i> 导出Excel</a>
    @endhasPerm
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "任务下派",
                sortName:'create_time',
                sortOrder: "desc",
                columns: [
                    {field: 'state',checkbox: true},
                    {field: 'id', title: 'ID', align: 'center', sortable: true,visible: false},
                    {field: 'title', title: '任务标题', align: 'left',formatter: function (value, row, index) {
                            return $.table.tooltip(value,10, 'open');
                        }},
                    {field: 'publish_type_text', title: '下派类型', align: 'center'},
                    {field: 'admin.username', title: '下派人', align: 'center'},
                    {field: 'create_time', title: '下派时间', align: 'center', sortable: true},
                    {field: 'category_text', title: '任务类别', align: 'left'},
                    {field: 'level_text', title: '任务等级', align: 'left'},
                    {field: 'completion_time', title: '完成时间', align: 'center'},
                    {field: 'status_text', title: '完成状态', align: 'center',formatter: function (value, row, index) {
                        if (row.work_type === 2) {
                            return value + '（'+row.completed_rate+'%）';
                        }
                            return value;
                        }},
                    {field: 'is_timeout', title: '时间状态', align: 'left',formatter: function (value, row, index) {
                            if (value === 0) {
                                return '正常';
                            } else if (value === 1) {
                                return '临期'
                            } else if (value === 2) {
                                return '超时'
                            }
                        }},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("grid:workassign:detail")
                            if (row.btns.detail) {
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="workDetail(\'' + row.id + '\')">详情</a> ');
                            }
                        @endhasPerm
                        @hasPerm("grid:workassigncommunity:detail")
                            if (row.btns.grid_detail) {
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="record(\'' + row.work_assign_community_id + '\')">网格员完成情况</a> ');
                            }
                        @endhasPerm
                        @hasPerm("grid:workassign:communitydetail")
                        if (row.btns.community_detail) {
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="community(\'' + row.id + '\')"> 社区完成情况</a> ');
                            }
                        @endhasPerm

                            return actions.join('');
                        }
                    }

                ],
                responseHandler:function (res) {
                    if (res.code === 0) {
                        $.each(res.extend, function (i, item) {
                            $('#'+i).html(item);
                        });
                    }
                }
            };
            $.table.init(options);
            $('#publish_type').change(function () {
                   var type = $('#publish_type').val();
                   if (type === '1') {
                       $('#assign-struct').show();
                   } else {
                       $('#assign-struct').hide();
                       $('select[name=struct_id]').val('');
                   }
            });
        });
        @hasPerm("grid:workassign:detail")
        function workDetail(id) {
            $.modal.openTab('任务详情', '{{route('grid.workassign.detail')}}?id='+id)
        }
        @endhasPerm

        function record(id) {
            $.modal.openTab("网格员完成情况", '{{route('grid.workassigncommunity.detail')}}?id='+id, true);
        }
        function community(id) {
            $.modal.openTab("社区完成情况", '{{route('grid.workassign.communitydetail')}}?id='+id, true);
        }
        function switch_status(status) {
            $('input[name=status]').val(status);
            $.table.search();
        }
        function exportExcel(dataId, formId){
            table.set();
            var currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
            var params = $("#" + table.options.id).bootstrapTable('getOptions');
            var dataParam = new FormData(document.querySelector('#'+ currentId));
            dataParam.append('orderByColumn',params.sortName);
            dataParam.append('isAsc',params.sortOrder);
            dataId = $.common.isEmpty(dataId) ? 'id' : dataId;
            var ids = $.table.selectColumns(dataId);
            var tipMsg = "确定导出所有" + table.options.modalName + "吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条" + table.options.modalName + "数据吗？";
                dataParam.append(dataId+"s", ids);
            }
            $.modal.confirm(tipMsg, function() {
                $.modal.loading('正在处理中，请稍后...');
                $.modal.disable();
                var url = '{{route('grid.workassign.export')}}';
                var xhr = new XMLHttpRequest();
                xhr.open('POST', url, true);    // 也可以使用POST方式，根据接口
                xhr.responseType = "blob";    // 返回类型blob
                xhr.setRequestHeader("X-CSRF-TOKEN", $('meta[name="csrf-token"]').attr('content'));
                // xhr.setRequestHeader('content-type', 'application/json');
                xhr.onload = function () {
                    // 请求完成
                    if (this.status === 200) {
                        // 返回200
                        var response = this.response;
                        let contentType = xhr.getResponseHeader('Content-Type');
                        contentType = contentType != '' ? contentType : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                        let blob = new Blob([response], {type: contentType});
                        const reader = new FileReader();
                        if (contentType === 'application/json' || contentType === 'application/javascript') {
                            reader.readAsText(blob, 'utf-8');
                            reader.onload = (e) => {
                                const resp = JSON.parse(reader.result);
                                if (resp.hasOwnProperty('code') && resp.code != web_status.SUCCESS) {
                                    $.modal.alertError(resp.msg);
                                } else {
                                    $.modal.alertError('导出失败');
                                }
                                $.modal.enable();
                            };
                        } else {
                            const disposition = xhr.getResponseHeader('Content-Disposition');
                            let fileName;
                            let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
                            let matches = filenameRegex.exec(disposition)
                            if (matches != null && matches[1]) {
                                fileName = matches[1].replace(/['"]/g, '')
                            }
                            // 通过 URLEncoder.encode(pFileName, StandardCharsets.UTF_8.name()) 加密编码的, 使用decodeURI(fileName) 解密
                            fileName = decodeURI(fileName)
                            var urlCreator = window.URL || window.webkitURL;
                            var url = urlCreator.createObjectURL(blob); //这个函数的返回值是一个字符串，指向一块内存的地址。
                            //以下代码保存我的excel导出文件
                            var link = document.createElement('a'); //创建事件对象
                            link.setAttribute('href', url);
                            link.setAttribute("download", fileName);
                            var event = document.createEvent("MouseEvents"); //初始化事件对象
                            event.initMouseEvent("click", true, true, document.defaultView, 0, 0, 0, 0, 0, false, false, false, false, 0, null); //触发事件
                            link.dispatchEvent(event);
                            $.modal.close();
                            $.modal.enable();
                        }
                    } else {
                        $.modal.alertError('导出失败');
                        $.modal.close();
                        $.modal.enable();
                    }
                    $.modal.closeLoading();
                };
                // 发送ajax请求
                xhr.send(dataParam);
            });
        }
    </script>
@stop

