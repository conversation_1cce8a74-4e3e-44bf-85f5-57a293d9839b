@extends('admin.layout.form')
@section('content')
    <style>
        .schedule-box {
            padding-left: 20px;
            padding-top: 20px;
        }
        .schedule {
            cursor: pointer;
        }
        .schedule-flow {
            display: none;
        }
    </style>
    <!-- 选项卡菜单-->
    <ul id="myTab" class="nav nav-tabs" role="tablist">
        <li class="active"><a href="#assign" role="tab" data-toggle="tab">下派 (
                @if($info->status != 4)
                    @if($info->community->count())
                        下派完成
                    @else
                        <span style="color: red">下派中</span>
                    @endif
                @else
                    已取消
                @endif
                )</a></li>
        @foreach($info->community as $community)
        <li><a href="#community{{$community->id}}" role="tab" data-toggle="tab">{{$community->community->name}}(
                @if(in_array($community->status, [1, 2]))
                    <span style="color: red">{{$community->status_text}}</span>
                @else
                    {{$community->status_text}}
                @endif
                )</a></li>
        @endforeach
    </ul>
    <!-- 选项卡面板 -->
    <div id="myTabContent" class="tab-content">
        <div class="tab-pane fade in active" id="assign">
            <div id="ibox-content">
                <div id="vertical-timeline" class="vertical-container light-timeline">
                    @foreach(\App\Models\Grid\WorkAssignAssignLog::getLogs($info->id, C_ID) as $item)
                        <div class="vertical-timeline-block">
                            <div class="vertical-timeline-icon @if($item['status'] == 1 || $item['status'] == -1) red-bg @else gray-bg @endif ">
                                <i class="fa fa-user"></i>
                            </div>
                            <div class="vertical-timeline-content">
                                <h3 style=" @if($item['status'] == 1) color:#1f9eee;@endif ">{{$item['admin_name']}}（{{$item['status_text']}}）</h3>
                                @if($item['type_text'] != '') <p>{{$item['type_text']}}</p> @endif
                                @if($item['note'] != '') <p>{{$item['note']}}</p> @endif
                                @if($item['create_time'] != '') <p>{{$item['create_time']}}</p> @endif
                                @if($item['signature'] != '') <p><img style="height: 50px" src="{{$item['signature']}}" alt=""></p> @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        @foreach($info->community as $community)
            <div class="tab-pane fade" id="community{{$community->id}}">
                <div class="schedule-box">
                    <div class="schedule"> <h3><i class="fa fa-angle-up"></i>
                            社区总体情况 (
                            @if($community->audit_status == 0)
                                <span style="color: red">未上报</span>
                            @elseif(in_array($community->audit_status, [1, 3]))
                                <span style="color: red">{{$community->audit_status_text}}</span>
                            @else
                                {{$community->audit_status_text}}
                            @endif
                            )
                        </h3></div>
                    <div class="schedule-flow">
                        <div id="ibox-content">
                            <div id="vertical-timeline" class="vertical-container light-timeline">
                                @foreach(\App\Models\Grid\WorkAssignCommunityAudit::auditList($community->id) as $item)
                                    <div class="vertical-timeline-block">
                                        <div class="vertical-timeline-icon @if($item['status'] == 1 || $item['status'] == -1) red-bg @else gray-bg @endif ">
                                            <i class="fa fa-user"></i>
                                        </div>
                                        <div class="vertical-timeline-content">
                                            <h3 style=" @if($item['status'] == 1) color:#1f9eee;@endif ">{{$item['audit_username']}}（{{$item['status_text']}}）</h3>
                                            @if($item['audit_type_text'] != '') <p>{{$item['audit_type_text']}}</p> @endif
                                            @if($item['note'] != '') <p>{{$item['note']}}</p> @endif
                                            @if($item['star'])
                                                @foreach($item['star'] as $star)
                                                    <p>{{$star['name']}}{!! str_repeat('<i class="fa fa-star" style="color: #ffb800;"></i> ' , $star['star']) !!}</p>
                                                @endforeach
                                            @endif
                                            @if($item['signature'] != '') <p><img style="height: 50px" src="{{$item['signature']}}" alt=""></p> @endif
                                            @if($item['audit_time'] != '') <p>{{$item['audit_time']}}</p> @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                <div class="schedule-box">
                    <div class="schedule"> <h3><i class="fa fa-angle-down"></i>网格员完成情况</h3></div>
                    <div class="schedule-flow" style="display: inline">
                        @foreach($community->grid as $grid)
                            <div class="schedule-box">
                                <div class="schedule"> <h3><i class="fa fa-angle-up"></i>
                                    {{$grid->gridUser->username ?? ''}} (
                                        @if($grid->audit_status == 0)
                                            <span style="color: red">未上报</span>
                                        @elseif(in_array($grid->audit_status, [1, 3]))
                                            <span style="color: red">{{$grid->audit_status_text}}</span>
                                        @else
                                            {{$grid->audit_status_text}}
                                        @endif
                                        )
                                    </h3></div>
                                <div class="schedule-flow">
                                    <div id="ibox-content">
                                        <div id="vertical-timeline" class="vertical-container light-timeline">
                                            @foreach(\App\Models\Grid\WorkAssignGridAudit::auditList($grid->id, false) as $item)
                                                <div class="vertical-timeline-block">
                                                    <div class="vertical-timeline-icon @if($item['status'] == 1 || $item['status'] == -1) red-bg @else gray-bg @endif ">
                                                        <i class="fa fa-user"></i>
                                                    </div>
                                                    <div class="vertical-timeline-content">
                                                        <h3 style=" @if($item['status'] == 1) color:#1f9eee;@endif ">{{$item['audit_username']}}（{{$item['status_text']}}）</h3>
                                                        @if($item['audit_type_text'] != '') <p>{{$item['audit_type_text']}}</p> @endif
                                                        @if($item['note'] != '') <p>{{$item['note']}}</p> @endif
                                                        @if($item['audit_time'] != '') <p>{{$item['audit_time']}}</p> @endif
                                                        @if($item['star']) <p>{!! str_repeat('<i class="fa fa-star" style="color: #ffb800;"></i> ' , $item['star']) !!}</p> @endif
                                                        @if($item['signature'] != '') <p><img style="height: 50px" src="{{$item['signature']}}" alt=""></p> @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@stop

@section('script')
    <script>
        $(function (){
            $('.schedule').click(function () {
                let i = $(this).find('i');
                if (i.hasClass('fa-angle-up')) {
                    i.removeClass('fa-angle-up')
                    i.addClass('fa-angle-down')
                    $(this).next().show();
                } else {
                    i.removeClass('fa-angle-down')
                    i.addClass('fa-angle-up')
                    $(this).next().hide();
                }
            });
        });
    </script>
@stop
