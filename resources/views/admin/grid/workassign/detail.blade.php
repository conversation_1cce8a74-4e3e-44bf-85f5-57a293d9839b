@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }

        .b5uploadimgbox .b5uploadlistbox img{
            cursor: zoom-in;
        }
        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            /*background-color: #f7f8fa;*/
            /*padding: 20px 5px;*/
        }

        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding: 20px;
        }
        #flow {
            border-left: 1px solid #e5e5e5;
        }
        .btn-xs{
            font-size: 14px;
        }

        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
            font-weight: bold;
        }
        .butuchu {
            color: #999999;
            font-size: 12px;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 24px;
            height: 24px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color:#f1f8fe ;
            margin: -6px;
            padding: 5px;
            border-radius: 4px;
        }
        .tooltip-inner {
            /*padding: 6px 11px;*/
            margin: 2px;
        }
        #base-info .col-sm-2 {
            width: auto;
        }
        .form-horizontal .row .form-group {
             margin-right: 0;
             margin-left: 0;
        }
        .mbig-btn {
            background-color: #fff;
            border: 1px solid #2256FF;
            color: #2256FF !important;
            padding: 8px 5px;
            border-radius: 7px;
            margin: 0 5px;
            display: inline-block;
            min-width: 96px;
            text-align: center;
        }
        .mbig-btn:hover{
            background-color: #2256FF;
            border-color: #2256FF;
            color: #FFFFFF !important;
        }
        #btns{
            margin-top: 20px;
        }
    </style>
@append
@section('content')
    <div class="row detail-box">
        <div class="col-sm-6 arrange" id="base-info">
            <div class="item">
                <form action="" class="form-horizontal" style="padding-bottom: 10px;">
                    <div>
                        <div class="row">
                            <div class="item-head-title">基本信息</div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">任务标题：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{$info['title']}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">任务描述：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{$info['content']}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">任务等级：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static">{{$info['level_text']}}</div>
                                </div>
                                <label class="col-sm-2 control-label">任务类别：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static">{{$info['category_text']}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">完成时间：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static">{{date('Y-m-d', $info['completion_time'])}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">任务类型：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static">{{$info['work_type_text']}}</div>
                                </div>
                                <div @if(2 != $info['work_type']) style="display: none" @endif>
                                    <label class="col-sm-2 control-label">每人工作量：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static">{{$info['workload']}}</div>
                                    </div>
                                </div>
                            </div>
                            @if($info['publish_type'] == 2)
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">分管领导：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static">{{$info['assign_assign_username']}}</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">下派方式：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static">{{$info['main_publish_type_text']}}</div>
                                    </div>
                                </div>
                            @elseif ($info['publish_type'] == 3)
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">下派方式：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static">{{$info['leader_publish_type_text']}}</div>
                                    </div>
                                </div>
                                @if ($info['leader_publish_type'] == 1)
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">抄送科室：</label>
                                        <div class="col-sm-9">
                                            <div class="form-control-static">{{implode('、', $info['carbon_copy_structs_list'])}}</div>
                                        </div>
                                    </div>
                                @endif
                            @endif
                            @if(in_array($info['publish_type'], [1, 5]) || ($info['publish_type'] == 3 && $info['leader_publish_type'] == 1))
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">下派社区：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static">{{implode('、', $info['assign_community'])}}</div>
                                    </div>
                                </div>
                            @endif
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件：</label>
                                <div class="col-sm-10">
                                    @if ($info['annex'])
                                        <div class="b5uploadfilebox" style="display: inline-flex">
                                            <div class="b5uploadlistbox" >
                                                @foreach($info['annex'] as $file)
                                                    <div class="b5upload_li">
                                                        <div class="b5upload_filetype "></div>
                                                        <div class="b5upload_filename">
                                                            <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div id="btns">
                            @if(!empty($info['btns']['drop']) && hasPerm('grid:workassign:drop'))
                                <a href="javascript:void(0)" onclick="drop()" class="mbig-btn">删除</a>
                            @endif
                            @if(!empty($info['btns']['cancel']) && hasPerm('grid:workassign:cancel'))
                                <a href="javascript:void(0)" onclick="cancel()" class="mbig-btn">取消</a>
                            @endif
                            @if(!empty($info['btns']['edit']) && hasPerm('grid:workassign:edit'))
                                <a href="javascript:void(0)" onclick="edit()" class="mbig-btn">编辑</a>
                            @endif
                            @if(!empty($info['btns']['grid_detail']) && hasPerm('grid:workassigncommunity:detail'))
                                <a href="javascript:void(0)" onclick="record()" class="mbig-btn">网格员完成情况</a>
                            @endif
                            @if(!empty($info['btns']['community_detail']) && hasPerm('grid:workassign:communitydetail'))
                                <a href="javascript:void(0)" onclick="community()" class="mbig-btn">社区完成情况</a>
                            @endif
                            @if(!empty($info['btns']['assign_grid_user']) && hasPerm('grid:workassign:assigngriduser'))
                                <a href="javascript:void(0)" onclick="assign_grid_user()" class="mbig-btn">{{$info['btns']['assign_grid_user'] == 1 ? '下派网格员' : '重新下派网格员'}}</a>
                            @endif
                            @if(!empty($info['btns']['assign_help']) && hasPerm('grid:workassign:assignhelp'))
                                <a href="javascript:void(0)" onclick="assign_help()" class="mbig-btn">{{$info['btns']['assign_help'] == 1 ? '下派统筹员' : '重新下派统筹员'}}</a>
                            @endif
                            @if(!empty($info['btns']['assign_community']) && hasPerm('grid:workassign:assigncommunity'))
                                <a href="javascript:void(0)" onclick="assign_community()" class="mbig-btn">下派社区</a>
                            @endif
                            @if(!empty($info['btns']['assign']) && hasPerm('grid:workassign:assign'))
                                <a href="javascript:void(0)" onclick="assign()" class="mbig-btn">下派</a>
                            @endif
                            @if(!empty($info['btns']['struct_cc']) && hasPerm('grid:workassign:structcc'))
                                <a href="javascript:void(0)" onclick="struct_cc()" class="mbig-btn">抄送科室人员</a>
                            @endif
                            @if(!empty($info['btns']['report']) && hasPerm('grid:workassigncommunity:report'))
                                <a href="javascript:void(0)" onclick="report()" class="mbig-btn">{{in_array($info['btns']['report'], [3, 4]) ? '重新上报' : '上报'}}</a>
                            @endif
                            @if(!empty($info['btns']['report_audit']) && hasPerm('grid:workassigncommunity:audit'))
                                <a href="javascript:void(0)" onclick="community_audit()" class="mbig-btn">审核上报</a>
                            @endif

                            @if(!empty($info['btns']['city_centre_audit']) && hasPerm('grid:workassign:citycentreaudit'))
                                <a href="javascript:void(0)" onclick="citycentreaudit()" class="mbig-btn">审核</a>
                            @endif
                            @if(!empty($info['btns']['back_grid']) && hasPerm('grid:workassigncommunity:backgrid"'))
                                <a href="javascript:void(0)" onclick="backgrid()" class="mbig-btn">回退网格员</a>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="col-sm-6 arrange" id="flow">
            <div class="item">
                <div style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-bottom: 10px;">
                    <div style="display: flex;align-items: center">
                        <strong class="item-head-title">下派流程</strong>
                    </div>
                    <div class="tree-switch" id="switch-flow" style="float: right">
                        <div class="tree-switch-btn active" data-value="tree" type="button">树状</div>
                        <div class="tree-switch-btn" data-value="table" type="button">列表</div>
                    </div>
                </div>
                <div class="flow-tree">
                    <ul class="layui-timeline">
                        @foreach($assign_list as $item)
                            <li class="layui-timeline-item">
                                <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                                <div class="layui-timeline-content layui-text">
                                    <div class="flow-item">
                                        <p class="tuchu">{{$item['create_time']}}</p>
{{--                                        <p class="butuchu">{{$item['user']['depNames']}}</p>--}}
                                        <div style="display: inline-block;position: relative">
                                           <div style="display: inline-block;">
                                               @foreach($item['user_list'] as $user)
                                                   <div>{{$user['struct_name']}}  {{$user['username']}}</div>
                                               @endforeach
                                           </div>
                                            <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                                <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                                            </div>
                                        </div>
                                        @if ($item['note'] != '')
                                            <p class="tuchu">{{$item['note']}}</p>
                                        @endif
                                        @if($item['signature'] != '') <p><img style="height: 50px" src="{{$item['signature']}}" alt=""></p> @endif
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="flow-table" style="display: none;">
                    <table class="table table-hover table-tr-b">
                        <thead style="background-color: #eff3f8;" >
                        <tr>
                            <th>操作时间</th>
                            <th>操作人</th>
                            <th style="min-width: 100px;">操作类型</th>
                            <th>操作信息</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($assign_list as $item)
                            <tr>
                                <td>{{$item['create_time']}}</td>
                                <td>
                                    @foreach($item['user_list'] as $user)
                                        <div>{{$user['struct_name']}}  {{$user['username']}}</div>
                                    @endforeach
                                </td>
                                <td>
                                    <div style="display: inline-block;position: relative">
                                        <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                            <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if ($item['note'] != '')
                                        <p>{{$item['note']}}</p>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
@stop

@section('script')
    <script>

    $(function () {
        $('#switch-flow').on('click', '.tree-switch-btn',function (){
            let value = $(this).data('value');
            let hideValue = value === 'tree' ? 'table' : 'tree';
            $(this).addClass('active');
            $('#switch-flow .tree-switch-btn[data-value='+hideValue+']').removeClass('active');
            $('.flow-'+value).show();
            $('.flow-'+hideValue).hide();
        });
    });
    function drop() {
        $.modal.confirm("确定删除该条任务下派吗？", function() {
            $.operate.saveTab('{{route('grid.workassign.drop')}}', { "id": {{$info['id']}} });
        });
    }
    function cancel() {
        $.modal.confirm("确定取消该条任务下派吗？", function() {
            $.operate.saveTabRefresh('{{route('grid.workassign.cancel')}}', { "id": {{$info['id']}} });
        });
    }
    function edit() {
        $.modal.open("编辑任务下派", '{{route('grid.workassign.edit')}}?id={{$info['id']}}');
    }
    function record() {
        $.modal.openCloseFull("网格员完成情况", '{{route('grid.workassigncommunity.detail')}}?id={{$info['work_assign_community_id']}}');
    }
    function community() {
        $.modal.openCloseFull("社区完成情况", '{{route('grid.workassign.communitydetail')}}?id={{$info['id']}}');
    }
    function assign_grid_user() {
        $.modal.open('下派网格员', '{{route('grid.workassign.assigngriduser')}}?id={{$info['work_assign_community_id']}}');
    }
    function backgrid() {
        $.modal.open('回退网格员', '{{route('grid.workassigncommunity.backgrid')}}?id={{$info['work_assign_community_id']}}');
    }
    function assign_help() {
        $.modal.open('下派统筹员', '{{route('grid.workassign.assignhelp')}}?id={{$info['work_assign_community_id']}}');
    }
    function struct_cc() {
        $.modal.open('抄送科室人员', '{{route('grid.workassign.structcc')}}?id='+id);
    }
    function assign_community() {
        $.modal.open("下派社区", '{{route('grid.workassign.assigncommunity')}}?id={{$info['id']}}');
    }
    function assign() {
        $.modal.open("下派", '{{route('grid.workassign.assign')}}?id={{$info['id']}}');
    }
    function citycentreaudit() {
        $.modal.open("审核", '{{route('grid.workassign.citycentreaudit')}}?id={{$info['id']}}', 1000);
    }
    function report() {
        $.modal.open("上报社区总体情况", '{{route('grid.workassigncommunity.report')}}?id={{$info['work_assign_community_id']}}');
    }
    function community_audit() {
        $.modal.open("审核", '{{route('grid.workassigncommunity.audit')}}?id={{$info['work_assign_community_id']}}', 1000);
    }
    </script>
@stop

