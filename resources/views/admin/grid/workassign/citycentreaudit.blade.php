@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }

        .b5uploadimgbox .b5uploadlistbox img{
            cursor: zoom-in;
        }
        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            /*background-color: #f7f8fa;*/
            /*padding: 20px 5px;*/
        }

        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding: 20px;
        }
        #flow {
            border-left: 1px solid #e5e5e5;
        }
        .btn-xs{
            font-size: 14px;
        }

        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
            font-weight: bold;
        }
        .butuchu {
            color: #999999;
            font-size: 12px;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 24px;
            height: 24px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color:#f1f8fe ;
            margin: -6px;
            padding: 5px;
            border-radius: 4px;
        }
        .tooltip-inner {
            /*padding: 6px 11px;*/
            margin: 2px;
        }
        #base-info .col-sm-2 {
            width: auto;
        }
        .form-horizontal .row .form-group {
            margin-right: 0;
            margin-left: 0;
        }
        .mbig-btn {
            background-color: #fff;
            border: 1px solid #2256FF;
            color: #2256FF !important;
            padding: 8px 5px;
            border-radius: 7px;
            margin: 0 5px;
            display: inline-block;
            min-width: 96px;
            text-align: center;
        }
        .mbig-btn:hover{
            background-color: #2256FF;
            border-color: #2256FF;
            color: #FFFFFF !important;
        }
        #btns{
            margin-top: 20px;
        }
    </style>
@append
@section('content')
    <script>
        var annexIndex = 0;
    </script>
    <div class="row">
        <div class="col-sm-6">
            <form class="form-horizontal m" id="form-edit">
                <input type="hidden" name="id" value="{{$info['id']}}">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">标题：</label>
                    <div class="col-sm-8">
                        <div class="form-control-static">{{$info['title']}}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">任务等级：</label>
                    <div class="col-sm-8">
                        <div class="form-control-static">{{$info['level_text']}}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">任务类别：</label>
                    <div class="col-sm-8">
                        <div class="form-control-static">{{$info['category_text']}}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">内容：</label>
                    <div class="form-control-static">{{$info['content']}}</div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">附件：</label>
                    <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                        <div class="col-sm-8">
                            {{--                    <input type="hidden" name="files" value="" id="files" required>--}}
                            <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                                <button type="button" style="display: none" class="btn-b5upload btn btn-primary btn-sm" id="annex" data-exts=""  data-multi="9"  data-cat="work" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                                <div class="b5uploadlistbox annex_filelist" id="annex_filelist"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">完成日期：</label>
                    <div class="col-sm-8">
                        <div class="form-control-static">{{date('Y-m-d', $info['completion_time'])}}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">工作类型：</label>
                    <div class="col-sm-8">
                        <div class="form-control-static">{{$info['work_type_text']}}</div>
                    </div>
                </div>
                <div class="form-group workload" @if(2 != $info['work_type']) style="display: none" @endif>
                    <label class="col-sm-3 control-label is-required">每人工作量：</label>
                    <div class="col-sm-8">
                        <div class="form-control-static">{{$info['workload']}}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">下派社区：</label>
                    <div class="col-sm-8">
                        <select name="" disabled id="cids" xm-select="select1" required xm-select-search>
                            @foreach($community as $id=>$name)
                                <option value="{{$id}}" @if(in_array($id, $info['cids'])) selected @endif >{{$name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="h4">审核</div>
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">审核结果：</label>
                    <div class="col-sm-8">
                        <label class="radio-box"><input type="radio" value="2" checked  name="status" class="status">通过</label>
                        <label class="radio-box"><input type="radio" value="3"  name="status" class="status">回退</label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核意见：</label>
                    <div class="col-sm-8">
                        <textarea type="text" name="note" rows="3" class="form-control" autocomplete="off"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-sm-6 arrange" id="flow">
            <div class="item">
                <div style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-bottom: 10px;">
                    <div style="display: flex;align-items: center">
                        <strong class="item-head-title">下派流程</strong>
                    </div>
                    <div class="tree-switch" id="switch-flow" style="float: right">
                        <div class="tree-switch-btn active" data-value="tree" type="button">树状</div>
                        <div class="tree-switch-btn" data-value="table" type="button">列表</div>
                    </div>
                </div>
                <div class="flow-tree">
                    <ul class="layui-timeline">
                        @foreach($assign_list as $item)
                            <li class="layui-timeline-item">
                                <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                                <div class="layui-timeline-content layui-text">
                                    <div class="flow-item">
                                        <p class="tuchu">{{$item['create_time']}}</p>
                                        {{--                                        <p class="butuchu">{{$item['user']['depNames']}}</p>--}}
                                        <div style="display: inline-block;position: relative">
                                            <div style="display: inline-block;">
                                                @foreach($item['user_list'] as $user)
                                                    <div>{{$user['struct_name']}}  {{$user['username']}}</div>
                                                @endforeach
                                            </div>
                                            <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                                <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                                            </div>
                                        </div>
                                        @if ($item['note'] != '')
                                            <p class="tuchu">{{$item['note']}}</p>
                                        @endif
                                        @if($item['signature'] != '') <p><img style="height: 50px" src="{{$item['signature']}}" alt=""></p> @endif
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="flow-table" style="display: none;">
                    <table class="table table-hover table-tr-b">
                        <thead style="background-color: #eff3f8;" >
                        <tr>
                            <th>操作时间</th>
                            <th>操作人</th>
                            <th style="min-width: 100px;">操作类型</th>
                            <th>操作信息</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($assign_list as $item)
                            <tr>
                                <td>{{$item['create_time']}}</td>
                                <td>
                                    @foreach($item['user_list'] as $user)
                                        <div>{{$user['struct_name']}}  {{$user['username']}}</div>
                                    @endforeach
                                </td>
                                <td>
                                    <div style="display: inline-block;position: relative">
                                        <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                            <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if ($item['note'] != '')
                                        <p>{{$item['note']}}</p>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>

@stop

@section('script')
    <script>
        $(function () {
            $('#switch-flow').on('click', '.tree-switch-btn',function (){
                let value = $(this).data('value');
                let hideValue = value === 'tree' ? 'table' : 'tree';
                $(this).addClass('active');
                $('#switch-flow .tree-switch-btn[data-value='+hideValue+']').removeClass('active');
                $('.flow-'+value).show();
                $('.flow-'+hideValue).hide();
            });
        });
        $(function () {
            b5uploadfileinit('annex',uploadAfter);
            dragula([annex_filelist]);
            @foreach($info['annex'] as $item)
                uploadAfter('annex', @json($item));
            @endforeach
        })
        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassign.citycentreaudit')}}', $('#form-edit').serialize());
            }
        }
        function uploadAfter(id, data) {
            annexIndex++;
            let ext = data.ext;
            let url = data.url;
            let filename = data.originName;
            if(!filename){
                filename = getFileName(url);
            }
            var classname = getExtClass(url);
            var html='<div class="b5upload_li" style="width: 100%" title="'+filename+'">' +
                '           <div class="b5upload_filetype '+classname+'"></div>' +
                '           <div class="b5upload_filename" style="line-height: 46px;display:inline;word-break: keep-all;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">'+filename+
                '        </div>' +
                '               <div class="b5upload_fileop">' +
                '                  <a href="'+url+'" target="_blank"><i class="fa fa-hand-o-right"></i>查看</a>' +
                '               </div>' +
                '      </div>';
            b5uploadhtmlshow(id,html);
        }
    </script>
@stop

