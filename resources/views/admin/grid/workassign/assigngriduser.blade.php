@extends('admin.layout.form')

@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$community['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">下派网格员：</label>
            <div class="col-sm-8">
                <select name="grid_user" id="grid_user" xm-select="select1" required xm-select-search>
                    @foreach($grid as $item)
                        <optgroup label="{{$item['title']}}">
                            @foreach($item['user'] as $user)
                                <option value="{{$user->id}}"
                                        @if(in_array($user->id, $grid_user_ids)) selected @endif >{{$user->username}}</option>
                            @endforeach
                        </optgroup>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="note" placeholder="请填写" class="form-control" rows="3">{{$community->note}}</textarea>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>

        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassign.assigngriduser')}}', $('#form-edit').serialize());
            }
        }
    </script>
@stop

