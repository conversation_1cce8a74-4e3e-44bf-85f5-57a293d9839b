@extends('admin.layout.form')

@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">下派方式：</label>
            <div class="col-sm-8">
                <label class="radio-box"><input type="radio" @if(in_array($info['leader_publish_type'], [1,3])) checked @endif value="1"  name="leader_publish_type" class="leader_publish_type">直接下派</label>
                <label class="radio-box"><input type="radio" @if($info['leader_publish_type'] == 2) checked @endif value="2"  name="leader_publish_type" class="leader_publish_type">城运中心下派</label>
            </div>
        </div>
        <div id="direct" @if(!in_array($info['leader_publish_type'], [1,3])) style="display: none" @endif>
            <div class="form-group">
                <label class="col-sm-3 control-label">抄送科室：</label>
                <div class="col-sm-8">
                    <select name="carbon_copy_structs" id="carbon_copy_structs" xm-select="carbon_copy_structs" xm-select-search>
                        @foreach($structs as $id=>$name)
                            <option value="{{$id}}" @if(in_array($id, $info['carbon_copy_structs'])) selected @endif >{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">下派社区：</label>
                <div class="col-sm-8">
                    <select name="cids" id="cids" xm-select="select3" required xm-select-search>
                        @foreach($community as $id=>$name)
                            <option value="{{$id}}" @if(in_array($id, $info['cids'])) selected @endif >{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="fg_note" placeholder="请填写" class="form-control" rows="3"></textarea>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {
            $(document).on("ifChanged", ".leader_publish_type", function () {
                let value = $('input[name=leader_publish_type]:checked').val();
                if (value == 1) {
                    $('#direct').show();
                } else {
                    $('#direct').hide();
                }
            });
        });
        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassign.assign')}}', $('#form-edit').serialize());
            }
        }
    </script>
@stop

