@extends('admin.layout.form')

@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">cc：</label>
            <div class="col-sm-8">
                <select name="cc_struct_user" id="cc_struct_user" xm-select="cc_struct_user" required xm-select-search>
                    @foreach($structs as $item)
                        <optgroup label="{{$item['name']}}">
                            @if (isset($item['list']))
                                @foreach($item['list'] as $user)
                                    <option value="{{$user['id']}}"
                                            @if(in_array($user['id'], $info['cc_struct_user'])) selected @endif >{{$user['username']}}</option>
                                @endforeach
                            @endif
                        </optgroup>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="note" placeholder="请填写" class="form-control" rows="3"></textarea>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>

        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassign.structcc')}}', $('#form-edit').serialize());
            }
        }
    </script>
@stop

