@extends('admin.layout.form')

@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
{{--        <div class="form-group">--}}
{{--            <label class="col-sm-3 control-label">内容：</label>--}}
{{--            <div class="col-sm-8">--}}
{{--                <textarea type="text" name="city_centre_content" rows="3" class="form-control" required autocomplete="off">{{$info['city_centre_content']}}</textarea>--}}
{{--            </div>--}}
{{--        </div>--}}
        <div class="form-group">
            <label class="col-sm-3 control-label">抄送科室：</label>
            <div class="col-sm-8">
                <select name="carbon_copy_structs" id="carbon_copy_structs" xm-select="carbon_copy_structs" xm-select-search>
                    @foreach($structs as $id=>$name)
                        <option value="{{$id}}" @if(in_array($id, $info['carbon_copy_structs'])) selected @endif >{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">下派社区：</label>
            <div class="col-sm-8">
                <select name="cids" id="cids" xm-select="select1" required xm-select-search>
                    @foreach($community as $id=>$name)
                        <option value="{{$id}}" @if(in_array($id, $info['cids'])) selected @endif >{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="cy_note" placeholder="请填写" class="form-control" rows="3"></textarea>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {
            //选择组织架构
            $("#treeName").click(function () {
                var treeId=$("#treeId").val();
                var url = urlcreate("{{route('system.struct.tree')}}","id="+treeId+"&parent=0&ismult=1&type=1");
                var options = {
                    title: '科室选择',
                    width: "380",
                    url: url,
                    callBack: doSubmit
                };
                $.modal.openOptions(options);
            });
        });
        function doSubmit(index, layero){
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
        }
        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassign.assigncommunity')}}', $('#form-edit').serialize());
            }
        }
    </script>
@stop

