@extends('admin.layout.layout')

@section('content')
    <div class="mt10" style="background-color: #fff;padding: 10px">
        <div class="bs-example bs-example-tabs b5navtab" data-example-id="togglable-tabs">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                @foreach($community as $key=>$item)
                <li class="nav-item @if(!$key) active @endif " role="presentation">
                    <a class="nav-link" data-toggle="tab" href="javascript:;" data-id="{{$item['id']}}" role="tab" aria-controls="detail" aria-selected="true">{{$item['cname']}} <span style="font-size: 14px;"> {{$item['status_text']}}</span></a>
                </li>
                @endforeach
            </ul>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane active" id="jiaban" role="tabpanel" aria-labelledby="detail-tab">
                    <iframe class="RuoYi_iframe" id="myIframe" src="{{route('grid.workassigncommunity.detail')}}?id={{$community[0]['id']}}" frameborder="0" width="100%" height="750px"></iframe>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            $('a[data-toggle="tab"]').on('click', function (e) {
                e.preventDefault(); // 阻止默认的锚点跳转行为
                var id = $(this).data('id');
                var iframe = $('#myIframe'); // 获取 iframe 元素
                iframe.attr('src', '{{route('grid.workassigncommunity.detail')}}?id='+id);
                // 激活 Bootstrap 标签
                $(this).parent().addClass('active').siblings().removeClass('active');
            });
        });
    </script>
@stop

