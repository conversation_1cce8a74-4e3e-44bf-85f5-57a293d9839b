@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <style>
        .fixed-table-toolbar {
            display: none
        }
    </style>
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="create_time" id="create_time" value="" placeholder="上报时间"></li>
                    @if(C_ID == 0)
                        <li>
                            <select name="cid" class="select2">
                                <option value="">所有社区</option>
                                @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                                    <option value="{{$type}}">{{$name}}</option>
                                @endforeach
                            </select>
                        <li>
                            @else
                                <input type="hidden" name="cid" value="{{C_ID}}"/>
                    @endif
                    <li>
                        <select name="where[is_synchronization]" class="select2">
                            <option value="">是否同步</option>
                            <option value="0">未同步</option>
                            <option value="1">已同步</option>
                        </select>
                    <li>
                    <li>
                        <select name="where[is_instructions]" class="select2">
                            <option value="">是否有批示</option>
                            <option value="0">无批示</option>
                            <option value="1">有批示</option>
                        </select>
                    <li>
                    <li><input type="text" name="user_name" placeholder="搜索"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        {{--@hasPerm("grid:watchman:export")
                        <a class="btn btn-warning btn-sm" onclick="$.table.exportExcel()"><i class="fa fa-download"></i>
                            导出</a>
                        @endhasPerm--}}
                        @hasPerm("system:{$app}:import")
                        <a href="javascript:void(0)" class="btn btn-warning" onclick="importExcel()">
                            <i class="fa fa-upload"></i> 导入
                        </a>
                        @endhasPerm
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#create_time'
                , type: 'month'
                , max: '{{date('Y-m')}}'
            });
        });
        $(function () {
            var options = {
                modalName: "上报记录",
                sortName: 'id',
                sortOrder: "desc",
                detailUrl: '{{route('grid.report.detail')}}?id=%id%',
                columns: [
                    {field: 'c_name', title: '社区', align: 'left'},
                    {field: 'userinfo.username', title: '网格员', align: 'left'},
                    {field: 'create_time', title: '上报时间', align: 'center', sortable: true},
                    {
                        field: 'title', title: '上报内容', align: 'left', formatter: function (value, row, index) {
                            let htmls = '';
                            row.reportvalue.forEach((element, index) => {
                                if (index < 2) {
                                    if(element.item_type == 'radio' || element.item_type == 'checkbox'){
                                        htmls += "<p>" + element.template_item_title + ': ' + element.item_text + "</p>";
                                    }else{
                                        htmls += "<p>" + element.template_item_title + ': ' + element.item_value + "</p>";
                                    }
                                }
                            });
                            return htmls;
                        }
                    },
                    {
                        field: 'is_synchronization',
                        title: '是否同步',
                        align: 'left',
                        formatter: function (value, row, index) {
                            if (value == 0) {
                                return "<p>未同步</p>";
                            }
                            if (value == 1) {
                                return "<p>已同步</p>";
                            }
                        }
                    },
                    {
                        field: 'is_instructions',
                        title: '是否有批示',
                        align: 'left',
                        formatter: function (value, row, index) {
                            if (value == 0) {
                                return "<p>无批示</p>";
                            }
                            if (value == 1) {
                                return "<p>有批示</p>";
                            }
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("grid:watchman:detail")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="operateview(\'' + row.id + '\')"> 详情</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });

        function operateview(id) {
            $.operate.detail(id, "1200");
        }

        function importExcel() {
            $.modal.layerinit(function (layer) {
                layer.open({
                    type: 1,
                    area: ['400px', '260px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: '导入',
                    content: $('#importTpl').html(),
                    btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    btn1: function (index, layero) {
                        var file = layero.find('#file').val();
                        if (file == '' && !$.common.endWith(file, '.xlsx', 'i')) {
                            $.modal.msgWarning("请选择后缀为 xlsx”的文件。");
                            return false;
                        }
                        var index = layer.load(2, {shade: false});
                        $.modal.disable();
                        var formData = new FormData(layero.find('form')[0]);
                        $.ajax({
                            url: '{{route("grid.{$app}.import")}}',
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            type: 'POST',
                            success: function (result) {
                                if (result.code == web_status.SUCCESS) {
                                    $.modal.closeAll();
                                    if (result.data.errorNum > 0) {
                                        $.modal.layerinit(function (layer) {
                                            layer.alert(result.msg, {
                                                icon: $.modal.icon('warning'),
                                                title: "系统提示",
                                                btn: ['下载', '取消'],
                                                btnclass: ['btn btn-primary'],
                                            },function (index) {
                                                window.open(result.data.error_url)
                                            });
                                        });
                                    } else {
                                        $.modal.alertSuccess(result.msg);
                                        $.table.refresh();
                                    }
                                } else if (result.code == web_status.WARNING) {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertWarning(result.msg)
                                } else {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertError(result.msg);
                                }
                            },
                            complete: function () {
                                // layero.find("#file").val("")
                            }
                        });
                    }
                });
            });
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <!-- <a download href="{{asset('template/政府工作人员.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p> -->
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop

