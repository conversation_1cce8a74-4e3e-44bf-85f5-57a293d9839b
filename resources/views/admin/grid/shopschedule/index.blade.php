@extends('admin.layout.layout')
@include('widget.asset.viewer')
@include('widget.asset.select2')
@section('content')
    <style>
        .fixed-table-toolbar{display: none}
    </style>
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="watch_mouth" id="watch_mouth" value="{{date('Y-m')}}" placeholder="选择月份"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#watch_mouth'
                ,type: 'month'
                ,max:'{{date('Y-m')}}'
            });
        });
        $(function () {
            var options = {
                modalName: "商铺走访",
                pagination:false,
                detailView: true,
                onExpandRow : function(index, row, $detail) {
                    initChildTable(index, row, $detail);
                },
                columns: [
                    {field: 'id', title: 'ID',  sortable: true,visible:false},
                    {field:'name',title: '社区',align: "left"},
                    {field: 'mubiao',title: '当月目标走访商铺数',align: 'left',sortable: true},
                    {field: 'count',title: '当月走访商铺数',align: 'left',sortable: true},
                    {field: 'mcount',title: '累计走访商铺数',align: 'left',sortable: true},
                    {field: 'status',title: '当月完成状态',align: 'left',sortable: true},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("grid:holdshop:index")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="watchman(\'' + row.id + '\')">详情</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ],
            };
            $.table.init(options);
        });
        initChildTable = function(index, row, $detail) {
            var childTable = $detail.html('<table style="table-layout:fixed;font-size: 12px"></table>').find('table');
            $(childTable).bootstrapTable({
                url: '{{route('grid.shopschedule.userlist')}}',
                method: 'post',
                sidePagination: "server",
                contentType: "application/x-www-form-urlencoded",
                queryParams : {
                    cid: row.id,
                    mouth: $("#watch_mouth").val()
                },
                columns: [
                    {field: 'id', title: 'ID',  sortable: true,visible:false},
                    {field:'username',title: '网格员',align: "left"},
                    {field: 'shop_in_plan_num',title: '当月目标走访商铺数',align: 'left',sortable: true},
                    {field: 'shop_in_mouth_num',title: '当月走访商铺数',align: 'left',sortable: true},
                    {field: 'all_count',title: '累计走访商铺数',align: 'left',sortable: true},
                    {field: 'status',title: '当月完成状态',align: 'left',sortable: true},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("grid:holdhouse:index")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="watchmangrid(\'' + row.id + '\')">详情</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }]
            });
        };
        function watchman(id){
            $.modal.openTab("商铺走访记录", '{{route('grid.holdshop.index')}}?type=c&id='+id,true);
        }
        function watchmangrid(id){
            $.modal.openTab("商铺走访记录", '{{route('grid.holdshop.index')}}?type=g&id='+id,true);
        }
    </script>
@stop

