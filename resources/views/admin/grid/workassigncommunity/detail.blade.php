@extends('admin.layout.form')
@include("widget.asset.select2")

@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }

        .b5uploadimgbox .b5uploadlistbox img{
            cursor: zoom-in;
        }
        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            /*background-color: #f7f8fa;*/
            /*padding: 20px 5px;*/
        }

        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding-right: 20px;
        }
        #flow {
            border-left: 1px solid #e5e5e5;
        }
        .btn-xs{
            font-size: 14px;
        }

        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
            font-weight: bold;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 24px;
            height: 24px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color:#f1f8fe ;
            margin: -6px;
            padding: 5px;
            border-radius: 4px;
        }
        .tooltip-inner {
            /*padding: 6px 11px;*/
            margin: 2px;
        }
        #base-info .col-sm-2 {
            width: auto;
        }
        .form-horizontal .row .form-group {
            margin-right: 0;
            margin-left: 0;
        }
        .block {
            width: 100%;
            background-color: #f2f2f2;
            border-radius: 6px;
            margin-bottom: 10px;
            padding: 10px;
            position: relative;
        }
        .block-item{
            padding-bottom: 10px;
        }
        .block.active {
            background-color: #deeafd;
        }
        .block.pointer {
            cursor: pointer;
        }
        .block-item:last-child{
            padding-bottom: 0;
        }
        .block:hover .chevron-right i {
            display: inline;
        }
        .annex {
            display: flex;
            flex-wrap: wrap;
            padding-top: 5px;
        }
        .annex-item {
            display: inline-block;
            padding: 5px;
            margin: 5px;
            background-color: #FFFFFF;
            border-radius: 10px;
        }
        .user-label span {
            background-color: #fff;
            padding: 5px 10px;
            border-radius: 5px;
            margin-left: 10px;
        }
        .chevron-right {
            width: 30px;
            text-align: right;
        }
        .chevron-right i {
            display: none;
        }
        .progress {
            background-color: #fff;
            margin:10px 0;
        }
        .progress .progress-bar {
            background-color: #aaaaaa;
        }
        .data-status {
            position: absolute;
            right: 5px;
            top: -28px;
        }
        .data-status img{
            width: 100px;
            height: 100px;
        }
    </style>
@append
@section('content')
    <div class="row detail-box">
        <div class="col-sm-6 arrange" id="base-info">
            <div class="item">
                <form action="" class="form-horizontal" style="padding-bottom: 10px;">
                    <div>
                        <div class="row">
                            <div class="item-head-title">社区总体情况</div>
                        </div>
                        <div class="row">
                            @if ($info['audit_status'])
                                <div class="block pointer active" data-type="community">
                                    <div class="block-item">{{$info['report_note']}}</div>
                                    @if ($info['report_annex'])
                                    <div class="block-item">
                                        <div class="annex">
                                            @foreach ($info['report_annex'] as $key => $value)
                                                <a class="annex-item" href="{{$value['url']}}" target="_blank">{{$value['originName']}}</a>
                                            @endforeach
                                        </div>
                                    </div>
                                    @endif
                                    <div class="block-item">
                                        {{$info['report_time']}}
                                        <div class="pull-right chevron-right"><i class="fa fa-chevron-right"></i></div>
                                    </div>
                                    <div class="clearfix"></div>
                                    @if ($info['audit_status'] == 1)
                                    <div class="data-status">
                                        <img src="{{asset('static/images/shz.png')}}">
                                    </div>
                                    @elseif ($info['status'] == 3)
                                    <div class="data-status">
                                        <img src="{{asset('static/images/ywc.png')}}">
                                    </div>
                                    @endif
                                </div>
                            @else
                                <div class="block">
                                    未上报
                                </div>
                            @endif
                        </div>
                        <div class="row">
                            <div class="item-head-title">网格员完成情况</div>
                        </div>
                        <div class="row">
                            @if ($info['grid'])
                                @foreach ($info['grid'] as $key => $grid)
                                    <div class="block pointer" data-type="grid" data-id="{{$grid['id']}}">
                                        <div class="block-item">
                                            {{$grid['grid_user']['username'] ?? '未知'}}

                                            <div class="pull-right user-label">
                                                <span>{{$grid['status_text']}}</span>
                                                @if ($grid['is_timeout'] == 2)
                                                    <span>超时</span>
                                                @elseif ($grid['is_timeout'] == 1)
                                                    <span>临期</span>
                                                @endif
                                                <div class="pull-right chevron-right">
                                                    <i class="fa fa-chevron-right"></i>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="clearfix"></div>
                                        @if($info['work_assign']['work_type'] == 2)
                                            <div class="block-item">
                                                <div style="display: flex">
                                                    <div class="progress" style="display: inline-block;width: 100%">
                                                        <div class="progress-bar" role="progressbar" aria-valuenow="{{$grid['completed_rate']}}" aria-valuemin="0" aria-valuemax="100" style="width: {{$grid['completed_rate']}}%;"></div>
                                                    </div>
                                                    <div style="display: inline-block;width: 60px;margin:10px 0;padding-left: 10px;">{{$grid['completed_rate']}}%</div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                               @endforeach
                            @else
                                <div class="block">
                                    未下派网格员
                                </div>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="col-sm-6 arrange" id="flow">
            <div class="item">
                <div style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-bottom: 10px;">
                    <strong class="item-head-title">详情</strong>
                </div>
                <div id="detail">
                    {!! $audit !!}
                </div>
            </div>

        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            $(document).on('click', '#switch-flow .tree-switch-btn',function (){
                let value = $(this).data('value');
                let hideValue = value === 'tree' ? 'table' : 'tree';
                $(this).addClass('active');
                $('#switch-flow .tree-switch-btn[data-value='+hideValue+']').removeClass('active');
                $('.flow-'+value).show();
                $('.flow-'+hideValue).hide();
            });
            $('.block.pointer').click(function () {
               let type = $(this).data('type');
                $('.block.active').removeClass('active');
                $(this).addClass('active');
                $.modal.loading('加载中...');
                var url = '';
               if (type === 'grid') {
                   let id = $(this).data('id');
                   url = '{{route('grid.workassigngrid.detail')}}?id='+id;
               } else {
                   url = '{{route('grid.workassigncommunity.auditdetail')}}?id={{$info['id']}}';
               }
               $.ajax({
                   url: url,
                   type: 'GET',
                   success: function (res) {
                       $('#detail').html(res);
                       $.modal.closeLoading();
                   }
               });
            });
        });
    </script>
@stop
