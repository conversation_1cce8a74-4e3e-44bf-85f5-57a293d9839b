@extends('admin.layout.layout')
@include("widget.asset.select2")
@section('css_common')
    <style>
        .cycle-item {
            margin-top: 10px;
            margin-left: 10px;
            margin-right: 10px;
            padding-top: 5px;
            padding-bottom: 13px;
            box-shadow: rgb(0 0 0 / 20%) 3px 3px 20px;
            background: rgb(255, 255, 255);
            border-radius: 6px;
            height: 100%;
            overflow: auto;
        }

        .remove_index:hover {
            color: red;
        }
        .tuchu {
            color: #000000;
            font-weight: bold;
        }
        .flow-item {
            background-color:#f1f8fe ;
            margin: -6px;
            padding: 5px;
            border-radius: 4px;
        }
        .mbig-btn {
            background-color: #fff;
            border: 1px solid #2256FF;
            color: #2256FF !important;
            padding: 8px 5px;
            border-radius: 7px;
            margin: 0 5px;
            display: inline-block;
            min-width: 96px;
            text-align: center;
        }
        .mbig-btn:hover{
            background-color: #2256FF;
            border-color: #2256FF;
            color: #FFFFFF !important;
        }
    </style>
@append

@section('content')
    <div class="row">
        <div class="col-sm-6 cycle-item">
            <div class="h4">社区总体完成情况 <a href="javascript:;" onclick="workassigncommunitydetail({{$info['id']}})" class="btn btn-warning">详情</a></div>
            <div class="form-group">
                <div class="col-sm-11">
                    <div>{{$info['report_note']}}</div>
                    <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                        <div class="b5uploadlistbox annex_filelist">
                            @foreach($info['report_annex'] as $item)
                                <div class="b5upload_li" style="width:100%; padding: 5px" title="{{$item['originName']}}">
                                    <div class="b5upload_filename" style="padding-left: 5px;display:inline;word-break: keep-all;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{$item['originName']}}</div>
                                    <div class="b5upload_fileop" style="height: auto !important;">
                                        <a href="{{$item['url']}}" target="_blank">
                                            <i class="fa fa-hand-o-right"></i>查看
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <div class="form-group" style="margin-top: 15px;margin-bottom: 0">
                <div class="h4" style="">审核</div>
            </div>
            <form class="form-horizontal m" id="form-audit">
                <input type="hidden" name="id" value="{{$info['id']}}">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">审核结果：</label>
                    <div class="col-sm-8">
                        <label class="radio-box"><input type="radio" value="3" checked  name="status" class="status">通过</label>
                        <label class="radio-box"><input type="radio" value="2"  name="status" class="status">回退</label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核意见：</label>
                    <div class="col-sm-8">
                        <textarea type="text" name="note" rows="3" class="form-control" autocomplete="off"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label"></label>
                    <div class="col-sm-8">
                        <a href="javascript:void(0)" onclick="submitHandler()" class="mbig-btn">审核</a>
                    </div>
                </div>
            </form>
        </div>
        <style>
            .vertical-timeline-content {
                padding: 6px;
            }

            .vertical-timeline-content p {
                color: #7F7F7F;
            }

            #vertical-timeline {
                margin-top: 0;
                margin-bottom: 0;
            }
        </style>
        <div class="col-sm-5 cycle-item">
            <div class="h4">审核记录</div>
            <ul class="layui-timeline">
                @foreach($auditList as $item)
                    <li class="layui-timeline-item">
                        <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                        <div class="layui-timeline-content layui-text">
                            <div class="flow-item">
                                <p class="tuchu">{{$item['audit_time']}}</p>
                                <div style="display: inline-block">
                                    <p class="butuchu">{{$item['audit_type_text']}}-{{$item['audit_username']}}</p>
                                </div>
                                <div style="display: inline-block;position: relative">
                                    <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                        <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                                    </div>
                                </div>
                                @if($item['note'] != '') <p class="tuchu">{{$item['note']}}</p> @endif
                                @if($item['signature'] != '')
                                    <p>
                                        电子签名：<img style="height: 50px" src="{{$item['signature']}}" alt="">
                                    </p>
                                @endif
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            $(document).on("ifChanged", ".status", function () {
                let value = $('input[name=status]:checked').val();
                if (value == 3) {
                    $('.defen').show();
                    $('.handle_status').show();
                } else {
                    $('.handle_status').hide();
                }
            });
        });

        $("#form-audit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassigncommunity.audit')}}', $('#form-audit').serialize(), function (result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg, function () {
                            $.modal.close();
                        })
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }

                });
            }
        }
        function workassigncommunitydetail(id) {
            $.modal.openCloseFull("社区总体情况", '{{route('grid.workassigncommunity.detail')}}?id='+id, 1000);
        }
    </script>
@stop
