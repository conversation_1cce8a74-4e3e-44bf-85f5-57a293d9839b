@extends('admin.layout.form')
@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">选择回退网格员：</label>
        <div class="col-sm-8">
            <div>
                <label class="check-box">
                    <input type="checkbox" id="selectAll">全选
                </label>
            </div>
            @foreach($info->grid as $grid)
                <div>
                    <label class="check-box">
                        <input type="checkbox" value="{{$grid->id}}" required class="grid_id" name="wag_ids[]">{{$grid->gridUser->username ?? ''}}
                    </label>
                    @hasPerm("grid:workassigngrid:detail")
                    <a href="javascript:;" onclick="work_assign_grid({{$grid->id}})">工作详情</a>
                    @endhasPerm
                </div>
            @endforeach
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">备注：</label>
        <div class="col-sm-8">
            <textarea name="note" rows="3" class="form-control"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function (){
            $(document).on("ifChanged", "#selectAll", function (event) {
                if ($('#selectAll').is(':checked')) {
                    $('.grid_id').iCheck('check');
                } else {
                    $('.grid_id').iCheck('uncheck');
                }
                event.stopPropagation();
            });
            $(document).on('ifChanged', '.grid_id', function (event) {
                if ($('.grid_id:checked').length === $('.grid_id').length) {
                    $('#selectAll').iCheck('check');
                } else {
                    $('#selectAll').prop('checked',false);
                }
                $('#selectAll').iCheck('update');
                event.stopPropagation();
            });
        });
        $("#form-edit").validate();

        {{--$("#XX").val(["{{$info['xx']}}"]).trigger("change");--}}

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('grid.workassigncommunity.backgrid')}}', $('#form-edit').serialize());
            }
        }
        function work_assign_grid(id) {
            $.modal.open("工作详情", '{{route('grid.workassigngrid.detail')}}?view=detail&id='+id, 1000);
        }
    </script>
@stop
