@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <style>
        .fixed-table-toolbar {
            display: none
        }
    </style>
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <input type="hidden" name="type" value="{{$input['type'] ?? ''}}"/>
            <input type="hidden" name="id" value="{{$input['id'] ?? 0}}"/>
            <div class="select-list">
                <ul>
                    @if(C_ID == 0)
                        <li>
                            <select name="where[cid]" class="select2">
                                <option value="">所有社区</option>
                                @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                                    <option value="{{$type}}">{{$name}}</option>
                                @endforeach
                            </select>
                        <li>
                    @endif
                    <li><input type="text" name="where[watch_mouth]" id="watch_mouth" value="{{date('Y-m')}}"
                               placeholder="选择月份"></li>
                    <li>
                        <select name="where[yx_status]" class="select2">
                            <option value="">是否有效</option>
                            <option value="1">有效</option>
                            <option value="0">无效</option>
                        </select>
                    </li>
                    <li><input type="text" name="like[user_name]" placeholder="姓名"></li>
                    <li><input type="text" name="like[mobile]" placeholder="手机号"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#watch_mouth'
                , type: 'month'
                , max: '{{date('Y-m')}}'
            });
        });
        $(function () {
            var options = {
                modalName: "巡访记录",
                sortName: 'id',
                sortOrder: "desc",
                detailUrl: '{{route('grid.watchman.detail')}}?id=%id%',
                columns: [
                    {field: 'c_name', title: '社区', align: 'left'},
                    {field: 'watch_date', title: '巡防日期', align: 'center', sortable: true},
                    {field: 'user_name', title: '网格员', align: 'left'},
                    {field: 'mobile', title: '手机号', align: 'left'},
                    {field: 'start_time', title: '开始时间', align: 'left'},
                    {field: 'end_time', title: '结束时间', align: 'left'},
                    {
                        field: 'status', title: '状态', align: 'left', formatter: function (value, row, index) {
                            if (value == 1) {
                                return "巡防中";
                            }
                            if (value == 2) {
                                return "巡防结束";
                            }
                        }
                    },
                    {
                        field: 'long_time', title: '巡防时长', align: 'left', formatter: function (value, row, index) {
                            if (value != 0 && row.end_time) {
                                return value
                            } else {
                                if (row.status == 2) {
                                    return "系统自动结束"
                                }
                            }

                        }
                    },
                    {
                        field: 'yx_status', title: '是否有效', align: 'left', formatter: function (value, row, index) {
                            if (value == 0) {
                                return "无效";
                            }
                            if (value == 1) {
                                return "有效";
                            }
                        }
                    },

                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("grid:watchman:detail")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="operateview(\'' + row.id + '\')"> 轨迹</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });

        function operateview(id) {
            $.operate.detailgj(id, "90%");
        }
    </script>
@stop

