<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>巡防轨迹</title>
    <script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
    <script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
    <style>
        html, body, #container {
            height: 100%;
            width: 100%;
        }
        .amap-icon img {
            width: 30px;
            height: 30px;
        }
    </style>
</head>
<body>
<div id="container">

</div>
<div class="input-card" style="width: 30rem;position: sticky;margin-left: 15px;margin-top: 15px;font-family: auto;">
    <h4>{{$info['user_name']}}</h4>
    <div style="line-height: 60px"><span style="float: left">巡防日期：{{$info['watch_date']}}</span>   <span style="float: right;padding-right: 50px">巡防时长：{{$info['long_time']}}</span></div>
    <p> 巡防时间：{{$info['start_time']}} ~ {{$info['end_time']}}</p>
</div>
@if($info['trajectory'])
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key={{env('GAODE_KEY')}}"></script>
<script>
    // JSAPI2.0 使用覆盖物动画必须先加载动画插件
    AMap.plugin('AMap.MoveAnimation', function(){
        var marker, lineArr = @json($info['trajectory']);

        var map = new AMap.Map("container", {
            resizeEnable: true,
            center: @json($info['trajectory'][0]),
            zoom: 17
        });

        marker = new AMap.Marker({
            map: map,
            position: @json($info['trajectory'][0]),
            icon: "",
            //offset: new AMap.Pixel(-13, -26),
        });

        // 绘制轨迹
        var polyline = new AMap.Polyline({
            map: map,
            path: lineArr,
            showDir:true,
            strokeColor: "#2256ff",  //线颜色
            strokeOpacity: 1,     //线透明度
            strokeWeight: 3,      //线宽
            strokeStyle: "solid"  //线样式
        });

        var passedPolyline = new AMap.Polyline({
            map: map,
            strokeColor: "#AF5",  //线颜色
            strokeWeight: 6,      //线宽
        });


        marker.on('moving', function (e) {
            passedPolyline.setPath(e.passedPath);
            map.setCenter(e.target.getPosition(),true)
        });

        map.setFitView();

        window.startAnimation = function startAnimation () {
            marker.moveAlong(lineArr, {
                // 每一段的时长
                duration: 500,//可根据实际采集时间间隔设置
                // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
                autoRotation: true,
            });
        };

        window.pauseAnimation = function () {
            marker.pauseMove();
        };

        window.resumeAnimation = function () {
            marker.resumeMove();
        };

        window.stopAnimation = function () {
            marker.stopMove();
        };
    });
</script>
@else
    <script>
        $("#container").html('<div style="text-align: center;line-height: 200px;font-size: 16px;color: red">巡防期间未采集到网格员轨迹</div>')
    </script>
@endif
</body>
</html>
