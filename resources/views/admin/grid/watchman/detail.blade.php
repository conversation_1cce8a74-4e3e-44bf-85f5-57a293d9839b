<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>轨迹</title>
    <script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
    <script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
    <script type="text/javascript" src="{{asset('static/plugins/supermap-iclient/dist/leaflet/include-leaflet.js')}}"></script>
    <style>
        html, body, #map {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
        }
        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
            background-color: transparent;
            background-image: none;
            color: #25A5F7;
            border-color: #25A5F7;
            padding: .25rem .5rem;
            line-height: 1.5;
            border-radius: 1rem;
            -webkit-appearance: button;
            cursor: pointer;
        }
        .btn:hover {
            text-decoration: none;
        }
        .btn:hover {
            color: #fff;
            background-color: #25A5F7;
            border-color: #25A5F7;
        }
    </style>
</head>
<body>
<div id="map" style="width: 100%;height: 100vh">

</div>

@if($info['trajectory'])
    <script src="{{asset('static/plugins/layui/layui.js')}}"></script>
    <script src="{{asset('static/plugins/blockUI/jquery.blockUI.js')}}"></script>
    <script src="{{asset('static/admin/js/common.js')}}"></script>
    <script src="{{asset('static/admin/js/iframe-ui.js')}}"></script>
    <script src="{{asset('static/plugins/supermap-iclient/libs/trackplayer/leaflet-trackplayer.umd.cjs')}}"></script>
    <script>
        var map,
            mapUrl = "{{env('YZT_STREET_URL')}}",
            url = "{{env('YZT_DISTRICT_URL')}}";
        let track;
        @php($center = explode(',', env('YZT_CENTER_POINT','104.0638303756715,30.659859309043362')))
        L.supermap.initMap(url,{
            mapOptions: {
                target: 'map',
                center: L.latLng({{$center[1]}}, {{$center[0]}}),
                zoom: 12
            }
        }).then((res) => {
            map = res.map
            var overlayLayer = new L.supermap.TiledMapLayer(mapUrl, {
                transparent: true,  // 叠加层透明，显示底图
                opacity: 0.4        // 可选：调整不透明度
            });
            overlayLayer.addTo(map);

            var control = L.control({position: 'topleft'});
            control.onAdd = function (map) {
                var popup = L.DomUtil.create('div');
                popup.style.width = '350px';
                popup.style.background = '#fff';
                popup.innerHTML = '<div style="position: sticky;margin-left: 15px;margin-top: 15px;font-family: auto;">' +
                    '<div style="line-height: 0px;"><h3 style="float: left;width: 40%">{{$info['user_name']}}</h3></div>'+
                    '<div style="clear: both"></div><div style="line-height: 40px"><span style="float: left">巡防日期：{{$info['watch_date']}}</span>   <span style="float: right;padding-right: 50px">巡防时长：{{$info['long_time']}}</span></div>'+
                    '<div style="clear: both"><div style="line-height: 40px"><span style="float: left">巡防时间：{{$info['start_time']}} ~ {{$info['end_time']}}</span> </div>'+
                    '</div>';
                handleMapEvent(popup, this._map);
                return popup;
            };
            control.addTo(map);
            var control1 = L.control({position: 'bottomright'});
            control1.onAdd = function (map) {
                var popup = L.DomUtil.create('div');
                popup.innerHTML = '<div >'+
                    '<input type="button" class="btn" value="开始动画" id="start" onclick="startAnimation()">'+
                    '<input type="button" class="btn" value="暂停动画" id="pause" onclick="pauseAnimation()">'+
                    '</div>';
                handleMapEvent(popup, this._map);
                return popup;
            };
            control1.addTo(map);

            var latlngs = $.common.reverseLngLat(Object.values(@json($info['trajectory'])));
            map.setView(latlngs[0], 16);
            track = new L.TrackPlayer(latlngs, {
                markerIcon: L.icon({
                    iconSize: [46, 46],
                    iconUrl: "{{asset('static/admin/images/icon/wg.png')}}",
                    iconAnchor: [23, 46],
                }),
                markerRotation: false,
                // notPassedLineColor:'#0000ff',
                // passedLineColor:'#25A5F7',
            }).addTo(map);
        });
        function startAnimation(){
            track.start();
        }
        function pauseAnimation(){
            track.pause();
        }
        function handleMapEvent(div, map) {
            if (!div || !map) {
                return;
            }
            div.addEventListener('mouseover', function () {
                map.dragging.disable();
                map.scrollWheelZoom.disable();
                map.doubleClickZoom.disable();
            });
            div.addEventListener('mouseout', function () {
                map.dragging.enable();
                map.scrollWheelZoom.enable();
                map.doubleClickZoom.enable();
            });
        }
    </script>
@else
    <script>
        $("#map").html('<div style="text-align: center;line-height: 200px;font-size: 16px;color: red">未采集到轨迹</div>')
    </script>
@endif
</body>
</html>
