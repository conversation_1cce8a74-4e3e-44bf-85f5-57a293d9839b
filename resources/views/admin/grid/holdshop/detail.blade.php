@extends('admin.layout.form')
@include("widget.asset.select2")

@section('content')
    <style>
        .bg-thead{background-color: aliceblue;}
    </style>
    <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">基本信息</h3>
    <form class="form-horizontal m">
        <div class="form-group">
            <label class="col-sm-1 control-label is-required">描述：</label>
            <div class="col-sm-10" style="padding-top: 8px;">
                {{$info['content']}}
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-1 control-label is-required">图片：</label>
            <div class="col-sm-10" style="padding-top: 8px;">
                @if($info['img_file'])
                    @foreach($info['img_file'] as $img)
                        <a href="{{$img['path']}}" target="_blank"> <img src="{{$img['path']}}" height="60px"></a>
                    @endforeach
                @endif
            </div>
        </div>
    </form>
    @if(!empty($info['update']))
    <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">修改信息</h3>
    <table class="table table-hover" style="margin-top: 20px;">
        <thead class="bg-thead">
        <tr>
            <th style="text-align: left; " ><div class="th-inner " style="font-weight: normal;">修改字段</div><div class="fht-cell"></div></th>
            <th style="text-align: left; " ><div class="th-inner " style="font-weight: normal;">修改前</div><div class="fht-cell"></div></th>
            <th style="text-align: left; " ><div class="th-inner " style="font-weight: normal;">修改后</div><div class="fht-cell"></div></th>
        </tr>
        </thead>
        <tbody>
        @foreach($info['update'] as $itemSec)
            @if($itemSec['type'] == 1)
                <tr>
                    <td style="text-align: left; ">{{$itemSec['field_name']}}</td>
                    <td style="text-align: left; ">{{$itemSec['old_value']}}</td>
                    <td style="text-align: left; ">{{$itemSec['new_value']}}</td>
                </tr>
            @else
                <tr>
                    <td colspan="3" style="text-align: center; ">{{$itemSec['type_text']}}了商铺【{{$item['name']}}】</td>
                </tr>
            @endif
        @endforeach
        </tbody>
    </table>
    @endif
@stop

