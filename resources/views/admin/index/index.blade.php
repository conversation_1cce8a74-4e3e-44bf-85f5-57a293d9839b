<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{$system_name}}</title>
    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;url=/ie.html"/>
    <![endif]-->
    <link href="{{asset('static/plugins/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/contextMenu/jquery.contextMenu.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/fontawesome/css/font-awesome.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/layui/css/layui.css')}}" rel="stylesheet">
    <link href="{{asset('static/plugins/animate/animate.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/style.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/skins.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/iframe-ui.css')}}" rel="stylesheet"/>
    <script>
        if (window !== top) top.location.replace(location.href);
        var _M_ = '{{$group}}'
        var _C_ = '{{$app}}';
        var _A_ = '{{$act}}';
        var rootUrl ="/";
        var mUrl = rootUrl+_M_;
        var cUrl = mUrl+"/" + _C_;
    </script>
</head>
<body class="fixed-sidebar full-height-layout gray-bg theme-dark skin-blue" style="overflow: hidden">
<div style="">

    <div style="">
        <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0;margin-bottom:0px">
            <div class="navbar navbar-static-top" style="float: left;margin-bottom:0px;">
                <div class="nav-close">
                    <i class="fa fa-times-circle"></i>
                </div>
                <div class="logo hidden-xs" style="line-height: 74px;font-size: 22px;height: 74px;width:auto">
                    <img src="{{asset('static/admin/images/profile.png')}}"  class="user-image" width="50px">
                    <span class="logo-lg" style="text-align: center;float: right;padding-left: 10px">{{$system_name}}</span>
                </div>
            </div>
            <div class="navbar-header">
                <a class="navbar-minimalize minimalize-styl-2" style="color:#FFF;" href="#" title="收起菜单">
                    <i class="fa fa-bars"></i>
                </a>
            </div>
            <ul class="nav navbar-top-links navbar-right welcome-message" style="padding-right: 5px;margin-right: 0px">
                <li><a data-toggle="tooltip" data-trigger="hover" data-placement="bottom" title="清除缓存" href="javascript:clearCacheAll();"><i class="fa fa-question-circle"></i> 清除缓存</a></li>
                <li><a data-toggle="tooltip" data-trigger="hover" data-placement="bottom" title="全屏显示" href="#" id="fullScreen"><i class="fa fa-arrows-alt"></i> 全屏</a></li>
                <li class="dropdown user-menu">
                    <a href="javascript:;" class="dropdown-toggle" data-hover="dropdown">
                        <span class="hidden-xs">{{$user_info['info']['name']}}    <i class="fa fa-angle-down" style="font-size: 18px;padding-left: 5px;"></i></span>
                    </a>
                    <ul class="dropdown-menu" style="min-width: 350px;">
                        <li>
                            <a style="white-space: pre-wrap;">{{$user_info['info']['depnames'] ?? ''}}</a>
                        </li>
                        <li>
                            <a onclick="toggleMenu()"><i class="fa fa-toggle-off"></i> 横向菜单</a>
                        </li>
                        <li>
                            <a href="{{route('admin.logout')}}"><i class="fa fa-sign-out"></i> 退出登录</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>
</div>
<div id="wrapper">
    <!--左侧导航开始-->
    <nav class="navbar-default navbar-static-side" role="navigation">

        <div class="sidebar-collapse">
            <ul class="nav" id="side-menu">
                <!--- 菜单 -->
                <li style="height: 39px">

                </li>
                {!! $menuHtml !!}
            </ul>
        </div>
    </nav>
    <!--左侧导航结束-->

    <!--右侧部分开始-->
    <div id="page-wrapper" class="gray-bg dashbard-1">


        <div class="row content-tabs">
            <button class="roll-nav roll-left tabLeft">
                <i class="fa fa-backward"></i>
            </button>
            <nav class="page-tabs menuTabs">
                <div class="page-tabs-content">

                </div>
            </nav>
            <button class="roll-nav roll-right tabRight">
                <i class="fa fa-forward"></i>
            </button>
            <a href="javascript:;" class="roll-nav roll-right tabReload"><i class="fa fa-refresh"></i> 刷新</a>
        </div>

        <a id="ax_close_max" class="ax_close_max" href="#" title="关闭全屏"> <i class="fa fa-times-circle-o"></i> </a>

        <div class="row mainContent" id="content-main">
            <iframe class="RuoYi_iframe" name="iframe0" width="100%" height="100%" data-id="/admin/home"
                    src="{{route('admin.home')}}" frameborder="0" seamless></iframe>
        </div>

        <div class="footer">
            <div class="pull-right">© 2025 Copyright </div>
        </div>
    </div>
    <!--右侧部分结束-->
</div>
<script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
<script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
<script src="{{asset('static/plugins/bootstrap/js/bootstrap.min.js')}}"></script>
<script src="{{asset('static/plugins/metismenu/jquery.metisMenu.js')}}"></script>
<script src="{{asset('static/plugins/slimscroll/jquery.slimscroll.min.js')}}"></script>
<script src="{{asset('static/plugins/contextMenu/jquery.contextMenu.min.js')}}"></script>
<script src="{{asset('static/plugins/blockUI/jquery.blockUI.js')}}"></script>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script src="{{asset('static/admin/js/iframe-ui.js')}}"></script>
<script src="{{asset('static/admin/js/common.js')}}"></script>
<script src="{{asset('static/admin/js/index.js')}}"></script>
<script src="{{asset('static/plugins/fullscreen/jquery.fullscreen.js')}}"></script>
<script src="{{asset('static/admin/js/shuiyin.js')}}"></script>

<script>
    // history（表示去掉地址的#）否则地址以"#"形式展示
    var mode = "history";
    // 历史访问路径缓存
    var historyPath = storage.get("historyPath");
    // 是否页签与菜单联动
    var isLinkage = true;
    watermark({ "watermark_txt": "{{$user_info['info']['name']}}  {{$user_info['info']['username']}}" });
    /** 刷新时访问路径页签 */
    function applyPath(url) {
        $('a[href$="' + decodeURI(url) + '"]').click();
        if (!$('a[href$="' + url + '"]').hasClass("noactive")) {
            $('a[href$="' + url + '"]').parent("li").addClass("selected").parents("li").addClass("active").end().parents("ul").addClass("in");
        }
    }
    $(function() {
        if ($.common.equals("history", mode) && window.performance.navigation.type == 1) {
            var url = storage.get('publicPath');
            if ($.common.isNotEmpty(url)) {
                applyPath(url);
            }
        } else {
            var hash = location.hash;
            if ($.common.isNotEmpty(hash)) {
                var url = hash.substring(1, hash.length);
                applyPath(url);
            } else {
                if ($.common.equals("history", mode)) {
                    storage.set('publicPath', "");
                }
            }
        }
        $("[data-toggle='tooltip']").tooltip();
    });


    /* 修改密码 */
    function rePass() {
        $.modal.open("修改密码", "{{route('admin.repass')}}", '770', '380');
    }

    function clearCacheAll() {
        $.operate.b5get("{{route('admin.cacheclear')}}");
    }

    // 皮肤缓存
    var skin = storage.get("skin");
    // 本地主题优先，未设置取系统配置
    if($.common.isNotEmpty(skin)){
        $("body").addClass(skin.split('|')[0]);
        $("body").addClass(skin.split('|')[1]);
    } else {
        $("body").addClass("theme-blue");
        $("body").addClass("skin-blue");
    }
</script>
</body>
</html>


