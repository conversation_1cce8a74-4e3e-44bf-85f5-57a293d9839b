<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{$system_name}}</title>
    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;url=/ie.html"/>
    <![endif]-->
    <link href="{{asset('static/plugins/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/contextMenu/jquery.contextMenu.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/fontawesome/css/font-awesome.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/layui/css/layui.css')}}" rel="stylesheet">
    <link href="{{asset('static/plugins/animate/animate.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/style.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/skins.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/iframe-ui.css')}}" rel="stylesheet"/>
    <style>
        .wait-pagination {
            margin-bottom: 10px;
            padding: 5px;
            text-align: center;
            background-color: #fff;
        }
    </style>
    <script>
        if (window !== top) top.location.replace(location.href);
        var _M_ = '{{$group}}'
        var _C_ = '{{$app}}';
        var _A_ = '{{$act}}';
        var rootUrl ="/";
        var mUrl = rootUrl+_M_;
        var cUrl = mUrl+"/" + _C_;
    </script>
</head>
<body class="fixed-sidebar full-height-layout gray-bg theme-dark skin-blue" style="overflow: hidden">
<div >
        <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
            <div class="navbar navbar-static-top" style="float: left;margin-bottom:0px;">
                <div class="nav-close">
                    <i class="fa fa-times-circle"></i>
                </div>
                <div class="logo hidden-xs" style="line-height: 74px;font-size: 22px;height: 74px;width:auto">
                    <img src="{{asset('static/admin/images/profile.png')}}"  class="user-image" width="50px">
                    <span class="logo-lg" style="text-align: center;float: right;padding-left: 10px">{{$system_name}}</span>
                </div>
            </div>
            <div class="navbar-header">
                <a class="navbar-minimalize minimalize-styl-2" style="color:#FFF;" href="#" title="收起菜单">
                    <i class="fa fa-bars"></i>
                </a>
            </div>

            {{--顶部菜单--}}
            <div id="navMenu">
                <ul class="nav navbar-toolbar nav-tabs navbar-left hidden-xs">
                    <!-- 顶部菜单列表 -->
                    @foreach($menuList as $menu)
                        <li role="presentation" id="tab_{{$menu['id']}}">
                            @if($menu['type'] == 'C')
                                <a data-toggle="tab" href="{{$menu['url']}}" class="@if($menu['target'] == '1') menuBlank @endif">
                                    <i class="{{$menu['icon']}}"></i> <span>{{$menu['name']}}</span>
                                </a>
                            @elseif($menu['child'])
                                <a data-toggle="tab" href="#menu_{{$menu['id']}}" >
                                    <i class="{{$menu['icon']}}"></i> <span>{{$menu['name']}}</span>
                                </a>
                            @endif
                        </li>
                    @endforeach
                </ul>
            </div>
            <ul class="nav navbar-top-links navbar-right welcome-message">
                <li><a data-toggle="tooltip" data-trigger="hover" data-placement="bottom" href="javascript:toggleDaiban();"><div class="icon-wrapper"><i class="fa fa-bell-o"></i> <span class="icon-badge" id="my-wait-handle" style="@if($waitHandleCount == 0) display: none; @endif min-width:21px;min-height: 15px; text-align: center">{{$waitHandleCount > 99 ? '99+' : $waitHandleCount}}</span></div> 我的待办</a></li>
                <li><a data-toggle="tooltip" data-trigger="hover" data-placement="bottom" title="清除缓存" href="javascript:clearCacheAll();"><i class="fa fa-question-circle"></i> 清除缓存</a></li>
                <li class="dropdown user-menu">
                    <a href="javascript:;" class="dropdown-toggle" data-hover="dropdown">
                        <span class="hidden-xs">{{$user_info['info']['name']}}<i class="fa fa-angle-down" style="font-size: 18px;padding-left: 5px;"></i></span>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a style="white-space: pre-wrap;">{{$user_info['info']['depnames'] ?? ''}}</a>
                        </li>
                        <li>
                            <a href="javascript:rePass();"><i class="fa fa-key"></i> 修改密码</a>
                        </li>
                        <li class="divider"></li>
                        <li>
                            <a href="{{route('admin.logout')}}"><i class="fa fa-sign-out"></i> 退出登录</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>
<div id="wrapper">
    <!--左侧导航开始-->
    <nav class="navbar-default navbar-static-side" role="navigation">
        <div class="sidebar-collapse tab-content" id="side-menu">
            <!--- 菜单 -->
            @foreach($menuList as $menu)
                @if($menu['type']!='C' && $menu['child'])
                <div class="tab-pane fade height-full" id="menu_{{$menu['id']}}">
                    <ul class="nav">
                        @foreach($menu['child'] as $menu1)
                            <li>
                                @if($menu1['type'] =='C')
                                    <a class="menu-content {{$menu1['target'] == '1'?'menuBlank':'menuItem'}}" href="{{$menu1['url']}}" data-refresh="{{$menu1['is_refresh']=='1'}}">
                                        <i class="{{$menu1['icon']}} fa-fw"></i>
                                        <span class="nav-label">{{$menu1['name']}}</span>
                                    </a>
                                @elseif($menu1['child'])
                                    <a href="javascript:;">
                                        <i class="{{$menu1['icon']}} fa-fw"></i>
                                        <span class="nav-label">{{$menu1['name']}}</span>
                                        <span class="fa arrow"></span>
                                    </a>
                                    <ul class="nav nav-second-level collapse">
                                        @foreach($menu1['child'] as $menu2)
                                            <li>
                                            @if($menu2['type'] =='C')
                                                <a class="{{$menu2['target'] == '1'?'menuBlank':'menuItem'}}" href="{{$menu2['url']}}" data-refresh="{{$menu2['is_refresh']=='1'}}">
                                                    <i class="{{$menu2['icon']}} fa-fw"></i>
                                                    <span class="nav-label">{{$menu2['name']}}</span>
                                                </a>
                                            @elseif($menu2['child'])
                                                <a href="javascript:;">
                                                    <i class="{{$menu2['icon']}} fa-fw"></i>
                                                    <span class="nav-label">{{$menu2['name']}}</span>
                                                    <span class="fa arrow"></span>
                                                </a>
                                                <ul class="nav nav-third-level collapse">
                                                    @foreach($menu2['child'] as $menu3)
                                                        @if($menu3['type'] =='C')
                                                        <li>
                                                            <a class="{{$menu3['target'] == '1'?'menuBlank':'menuItem'}}" href="{{$menu3['url']}}" data-refresh="{{$menu2['is_refresh']=='1'}}">
                                                                <i class="{{$menu3['icon']}} fa-fw"></i>
                                                                <span class="nav-label">{{$menu3['name']}}</span>
                                                            </a>
                                                        </li>
                                                        @endif
                                                    @endforeach
                                                </ul>
                                            @endif
                                            </li>
                                        @endforeach
                                    </ul>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                </div>
                @endif
            @endforeach
            <div class="tab-pane fade height-full" id="index">
                <ul class="nav">
                    <li>
                        <a class="menuItem" href="{{route('admin.home')}}"><i class="fa fa-home"></i> <span class="nav-label">首页</span></a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <!--左侧导航结束-->

    <!--右侧部分开始-->
    <div id="page-wrapper" class="gray-bg dashbard-1">


        <div class="row content-tabs">
            <button class="roll-nav roll-left tabLeft">
                <i class="fa fa-backward"></i>
            </button>
            <nav class="page-tabs menuTabs">
                <div class="page-tabs-content">
                    <a href="javascript:;" class="active menuTab" data-id="{{route('admin.home')}}">首页</a>
                </div>
            </nav>
            <button class="roll-nav roll-right tabRight">
                <i class="fa fa-forward"></i>
            </button>
            <a href="javascript:;" class="roll-nav roll-right tabReload"><i class="fa fa-refresh"></i> 刷新</a>
        </div>

        <a id="ax_close_max" class="ax_close_max" href="#" title="关闭全屏"> <i class="fa fa-times-circle-o"></i> </a>

        <div class="row mainContent" id="content-main">
            <iframe class="RuoYi_iframe" name="iframe0" width="100%" height="100%" data-id="{{route('admin.home')}}" src="{{route('admin.home')}}" frameborder="0" seamless></iframe>
        </div>

        <!--我的待办开始-->
        <!-- Control Sidebar -->
        <aside id="daiban" class="control-sidebar control-sidebar-dark" style="position: fixed; max-height: 100%; overflow: auto;">
            <!-- Create the tabs -->
            <ul class="nav nav-tabs nav-justified control-sidebar-tabs" id="wait-handle-item">
                @foreach(\App\Models\System\AdminWaitHandle::$itemMap as $key=>$name)
                <li @if($key == \App\Models\System\AdminWaitHandle::ITEM_RW) class="active" @endif data-item="{{$key}}"><a href="#wait{{$key}}" data-toggle="tab" aria-expanded="true">{{$name}} <span id="wait-handle-{{$key}}">0</span></a></li>
                @endforeach
            </ul>
            <!-- Tab panes -->
            <div class="tab-content">
                @foreach(\App\Models\System\AdminWaitHandle::$itemMap as $key=>$name)
                    <div class="tab-pane @if($key == \App\Models\System\AdminWaitHandle::ITEM_RW) active @endif " id="wait{{$key}}">
                        <div class="input-group mt20 mb20">
                            <input type="text" class="form-control keywords" placeholder="搜索" style="height: 34px;">
                            <span class="input-group-btn">
                            <button type="button" class="btn btn-success wait-handle-search">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                        </div>
                        <div class="input-group mb20" @if(count(\App\Models\System\AdminWaitHandle::$itemType[$key]) == 1) style="display: none" @endif>
                            @foreach(\App\Models\System\AdminWaitHandle::$itemType[$key] as $k=>$gr)
                            <a class="btn btn-default btn-rounded btn-outline btn-sm mr20 wait-group @if(count(\App\Models\System\AdminWaitHandle::$itemType[$key]) == 1) active @endif" data-group="{{$gr}}" href="javascript:;">{{\App\Models\System\AdminWaitHandle::$groupMap[$gr]}} <span>0</span></a>
                            @endforeach
                        </div>
                        <div class="input-group group-type"></div>
                        <!--卡片列表-->
                        <div class="card-list mt20 input-group" style="width: 100%;color: #2f4650;">

                        </div>
                    </div>
                @endforeach
            </div>
        </aside>
        <!-- /.control-sidebar -->
        <!--我的待办结束-->
        <div class="footer">
            <div class="pull-right">© 2025 Copyright </div>
        </div>
    </div>
    <!--右侧部分结束-->
</div>
<script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
<script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
<script src="{{asset('static/plugins/bootstrap/js/bootstrap.min.js')}}"></script>
<script src="{{asset('static/plugins/metismenu/jquery.metisMenu.js')}}"></script>
<script src="{{asset('static/plugins/slimscroll/jquery.slimscroll.min.js')}}"></script>
<script src="{{asset('static/plugins/contextMenu/jquery.contextMenu.min.js')}}"></script>
<script src="{{asset('static/plugins/blockUI/jquery.blockUI.js')}}"></script>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script src="{{asset('static/admin/js/iframe-ui.js')}}"></script>
<script src="{{asset('static/admin/js/common.js')}}"></script>
<script src="{{asset('static/admin/js/index.js')}}"></script>
<script src="{{asset('static/admin/js/resize-tabs.js')}}"></script>
<script src="{{asset('static/plugins/fullscreen/jquery.fullscreen.js')}}"></script>
<script src="{{asset('static/admin/js/shuiyin.js')}}"></script>
<script>
    // history（表示去掉地址的#）否则地址以"#"形式展示
    var mode = "history";
    // 历史访问路径缓存
    var historyPath = storage.get("historyPath");
    // 是否页签与菜单联动
    var isLinkage = true;
    watermark({ "watermark_txt": "{{$user_info['info']['name']}}，{{$user_info['info']['mobile']}}" });
    /** 刷新时访问路径页签 */
    function applyPath(url) {
        var $dataObj = $('a[href$="' + decodeURI(url) + '"]');
        $dataObj.click();
        if (!$dataObj.hasClass("noactive")) {
            $dataObj.parent("li").addClass("selected").parents("li").addClass("active").end().parents("ul").addClass("in");
        }
        // 顶部菜单同步处理
        var tabStr = $dataObj.parents(".tab-pane").attr("id");
        if ($.common.isNotEmpty(tabStr)) {
            var sepIndex = tabStr.lastIndexOf('_');
            var menuId = tabStr.substring(sepIndex + 1, tabStr.length);
            $("#tab_" + menuId + " a").click();
        }
    }
    $(function() {
        var lockPath = storage.get('lockPath');
        if($.common.equals("history", mode) && window.performance.navigation.type == 1) {
            var url = storage.get('publicPath');
            if ($.common.isNotEmpty(url)) {
                applyPath(url);
            } else {
                $(".navbar-toolbar li a").eq(0).click();
            }
        } else if($.common.isNotEmpty(lockPath)) {
            applyPath(lockPath);
            storage.remove('lockPath');
        } else {
            var hash = location.hash;
            if ($.common.isNotEmpty(hash)) {
                var url = hash.substring(1, hash.length);
                applyPath(url);
            } else {
                if($.common.equals("history", mode)) {
                    storage.set('publicPath', "");
                }
                $(".navbar-toolbar li a").eq(0).click();
            }
        }
        $("[data-toggle='tooltip']").tooltip();
        $('.wait-group').click(function () {
            $(this).parent().find('a').removeClass('active');
            $(this).addClass('active');
            $(this).parent().next().html('');
            waitHandleRefresh();
        });
        $(document).on('click', '.group-type a', function () {
            $(this).parent().find('a').removeClass('active');
            $(this).addClass('active');
            waitHandleRefresh();
        })
        $('.wait-handle-search').click(function () {
            waitHandleRefresh();
        });
        $('#daiban .nav-tabs a').on('shown.bs.tab', function (e) {
            waitHandleRefresh();
        });

        @if (!empty($user_info['info']['edit_pwd']))
        forceEdit();
        @endif
    });


    /* 修改密码 */
    function rePass() {
        $.modal.open("修改密码", "{{route('admin.repass')}}", '770', '380');
    }
    /* 修改初始密码 */
    function forceEdit() {
        $.modal.layerinit(function (layer) {
            var index = layer.open({
                type: 2,
                area: ['770px', '380px'],
                closeBtn:0,
                fix: false,
                //不固定
                maxmin: false,
                shade: 0.9,
                title: '重置密码',
                content: '{{route('admin.repass')}}',
                btnAlign: 'c',
                btn: ['确定','切换账号登录'],
                resize: false,
                // 弹层外区域关闭
                shadeClose: false,
                // scrollbar:false,
                yes: function (index, layero) {
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                },
                btn2: function(index, layero){
                    window.parent.location.href = '{{route('admin.logout')}}';
                },
                cancel: function (index) {
                    return false;
                }
            });
        });
    }

    function clearCacheAll() {
        $.operate.b5get("{{route('admin.cacheclear')}}");
    }
    var waitHandleRefreshInterval = null;
    function toggleDaiban(){
        $('#daiban').toggleClass('control-sidebar-open');
        if($('#daiban').hasClass('control-sidebar-open')){
            waitHandleRefresh();
            // waitHandleRefreshInterval = setInterval(function () {
            //     waitHandleRefresh(false);
            // }, 10000);
        } else {
            clearInterval(waitHandleRefreshInterval);
        }
    }
    setInterval(waitHandle, 20000);
    setInterval(function () {
        $.each($('.deadline_second'), function (i, item) {
            var seconds = $(item).data('value');
            $(item).data('value', seconds - 1);
            $(item).find('span').html(waitHandleTime(seconds));
        })
    }, 1000);
    function waitHandleTime(seconds) {
        if (seconds <= 0) {
            return '已超期';
        }
        var time = [];
        if (seconds >= 3600) {
            time.push(Math.floor(seconds/3600))
            seconds = seconds%3600;
        }
        if (seconds >= 60) {
            time.push(Math.floor(seconds/60))
            seconds = seconds%60;
        } else {
            time.push(0)
        }
        time.push(seconds)
        return time.join(':');
    }
    function waitHandle() {
        {{--$.ajax({--}}
        {{--    url: "{{route('waithandle.count')}}",--}}
        {{--    type: "get",--}}
        {{--    success: function(result) {--}}
        {{--        if (result.code == 0) {--}}
        {{--            var count = result.data.count;--}}
        {{--            let obj = $('#my-wait-handle');--}}
        {{--            if (count > 0) {--}}
        {{--                obj.show();--}}
        {{--                obj.html(count > 99 ? '99+' : count);--}}
        {{--            } else {--}}
        {{--                obj.hide();--}}
        {{--            }--}}
        {{--        }--}}
        {{--    }--}}
        {{--});--}}
        var xhr = new XMLHttpRequest();
        xhr.open("GET", "{{route('waithandle.count')}}", true);
        xhr.setRequestHeader('x-requested-with', 'XMLHttpRequest');
        xhr.onreadystatechange = function () {
            if (xhr.readyState == 4 && xhr.status == 200) {
                var result = JSON.parse(xhr.responseText);
                if (result.code == 0) {
                    var count = result.data.count;
                    let obj = $('#my-wait-handle');
                    if (count > 0) {
                        obj.show();
                        obj.html(count > 99 ? '99+' : count);
                    } else {
                        obj.hide();
                    }
                }
            }
        };
        xhr.send();
    }
    function waitHandleRefresh(loading = true) {
        let item = $('#wait-handle-item li.active').data('item');
        getWaitHandleByGroup(item, loading);
    }
    function getWaitHandleByGroup(item, loading = true) {
        if (loading) {
            $.modal.loading("正在加载...");
        }
        let data = {};
        data.item = item;
        let tabId = 'wait'+item;
        data.group = $('#'+tabId+' .wait-group.active').data('group');
        data.group_type = $('#'+tabId+' .group-type a.active').data('type');
        data.keywords = $('#'+tabId+' .keywords').val();
        getWaitHandleByGroupData("{{route('waithandle.list')}}", data);
    }
    function getWaitHandleByGroupData(url, data = {}) {
        let item = $('#wait-handle-item li.active').data('item');
        let tabId = 'wait'+item;
        data.group_type = data.group_type || $('#'+tabId+' .group-type a.active').data('type');
        $.ajax({
            url: url,
            type: "get",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    $.each(result.data.item_count, function (i, item) {
                        $('#wait-handle-'+item.item).html(item.count);
                    })

                    $.each(result.data.group_count, function (i, item) {
                        $('#'+tabId+' .wait-group[data-group='+item.group+'] span').html(item.count);
                    })
                    let groupType = '';
                    $.each(result.data.group_type_count, function (i, item) {
                        var active = '';
                        if (data.group_type == item.group_type) {
                            active = 'active';
                        }
                        groupType += '<a class="btn btn-default btn-rounded btn-outline btn-sm mr20 '+active+'" data-type="'+item.group_type+'" href="javascript:;">'+item.name+' <span>'+item.count+'</span></a>'
                    })
                    $('#'+tabId+' .group-type').html(groupType);
                    $('#'+tabId+' .card-list').html(result.data.html);
                    if (result.data.page !== '') {
                        $('#'+tabId+' .card-list').append('<div class="wait-pagination">'+result.data.page+'</div>')
                    }

                }
                $.modal.closeLoading();
            }
        });
    }
    document.addEventListener('click', function(event) {
        var target = event.target;
        if (!target.closest('#daiban')) {
            //$('#daiban').toggleClass('control-sidebar-open');
        }
    });
    $(document).ready(function() {
        $(document).on('click', '.wait-pagination a', function(e) {
            e.preventDefault();
            getWaitHandleByGroupData('{{route('waithandle.list')}}'+$(this).attr('href'));
        });
    });
    // 皮肤缓存
    var skin = storage.get("skin");
    // 本地主题优先，未设置取系统配置
    if($.common.isNotEmpty(skin)){
        $("body").addClass(skin.split('|')[0]);
        $("body").addClass(skin.split('|')[1]);
    } else {
        $("body").addClass("theme-dark");
        $("body").addClass("skin-blue");
    }

    // websocket连接
    var websocket_connected_count = 0;
    var onclose_connected_count = 0;
    function newWebSocket(){
        var websocket = null;
        // 判断当前环境是否支持websocket
        if(window.WebSocket){
            if(!websocket){
                var ws_url = '';
                if (window.location.protocol === 'https:') {
                    ws_url = 'wss://'+window.location.hostname+'/ws';
                } else {
                    ws_url = 'ws://'+window.location.hostname+'/ws';
                }
                websocket = new WebSocket(ws_url);
            }
        } else {
            console.log("not support websocket");
        }

        // 连接成功建立的回调方法
        websocket.onopen = function(e){
            heartCheck.reset().start();   // 成功建立连接后，重置心跳检测
            var data = {
                action: 'login',
                user_type: '1',
                source: 1,
                user_id: '{{\App\Extends\Helpers\Admin\LoginAuth::admin()->id}}'
            }
            websocket.send(JSON.stringify(data));
            console.log("connected successfully");
        }
        // 连接发生错误，连接错误时会继续尝试发起连接（尝试5次）
        websocket.onerror = function() {
            console.log("onerror连接发生错误")
            websocket_connected_count++;
            if(websocket_connected_count <= 5){
                newWebSocket();
                heartCheck.reset();
            }
        }
        // 接受到消息的回调方法
        websocket.onmessage = function(e){
            console.log("接受到消息了")
            heartCheck.reset().start();    // 如果获取到消息，说明连接是正常的，重置心跳检测
            var message = e.data;
            if(message){
                //执行接收到消息的操作，一般是刷新ＵＩ
            }
        }

        // 接受到服务端关闭连接时的回调方法
        websocket.onclose = function(){
            console.log("onclose断开连接");
        }
        // 监听窗口事件，当窗口关闭时，主动断开websocket连接，防止连接没断开就关闭窗口，server端报错
        window.onbeforeunload = function(){
            websocket.close();
        }

        // 心跳检测, 每隔一段时间检测连接状态，如果处于连接中，就向server端主动发送消息，来重置server端与客户端的最大连接时间，如果已经断开了，发起重连。
        var heartCheck = {
            timeout: 300000,        // 5分钟发一次心跳，比server端设置的连接时间稍微小一点，在接近断开的情况下以通信的方式去重置连接时间。
            serverTimeoutObj: null,
            reset: function(){
                // clearTimeout(this.timeoutObj);
                clearTimeout(this.serverTimeoutObj);
                return this;
            },
            start: function(){
                var self = this;
                this.serverTimeoutObj = setInterval(function(){
                    if(websocket.readyState == 1){
                        console.log("连接状态，发送消息保持连接");
                        websocket.send(JSON.stringify({
                            action: 'ping',
                        }));
                        heartCheck.reset().start();    // 如果获取到消息，说明连接是正常的，重置心跳检测
                    }else{
                        console.log("断开状态，尝试重连");
                        newWebSocket();
                        heartCheck.reset();
                    }
                }, this.timeout)
            }
        }
    }
    // newWebSocket();
</script>
</body>
</html>


