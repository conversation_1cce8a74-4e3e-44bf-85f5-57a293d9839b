@extends('admin.layout.form')

@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('css_common')
    <style>
        .form-group .col-xs-3{
            padding-right:0;
            width: auto;
        }
        .select-user {
            display: none;
        }
        .select-user.active {
            display: inherit;
        }
    </style>
@append
@section('content')
<script>
        var annexIndex = 0;
    </script>
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <input type="hidden" name="report_id" value="{{$info['report_id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">批示意见：</label>
            <div class="col-sm-8">
                <textarea type="text" name="opinion" rows="3" class="form-control" required autocomplete="off">{{$info['opinion']}}</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">附件：</label>
            <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                <div class="col-sm-8">
                    <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                        <!-- <button type="button" class="btn-b5upload btn btn-primary btn-sm" id="annex" data-exts=""  data-multi="9"  data-cat="work" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 最多上传9个文件</span>
                        <div class="b5uploadlistbox annex_filelist" id="annex_filelist"></div> -->
                        @if(!empty($info['file'])) 
                        @foreach($info['file'] as $itemSec)
                            <div class="b5upload_li">
                            <input type="hidden" name="file['+annexIndex+']{{$itemSec['url']}}" value="{{$itemSec['url']}}">
                            <input type="hidden" name="file['+annexIndex+']{{$itemSec['originName']}}" value="'+{{$itemSec['originName']}}+'">
                            <input type="hidden" name="file['+annexIndex+']{{$itemSec['ext']}}" value="'+ext+'">
                            <div class="b5upload_filetype '+classname+'"></div>
                            <div class="b5upload_filename">
                            </div>
                                <div class="b5upload_fileop">
                                    <a href="{{$itemSec['url']}}" target="_blank"><i class="fa fa-hand-o-right"></i>查看</a>
                                </div>
                            </div>
                        @endforeach
                        @else
                        <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                            <div class="col-sm-8">
                                <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                                    <button type="button" class="btn-b5upload btn btn-primary btn-sm" id="annex" data-exts=""  data-multi="9"  data-cat="work" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 最多上传9个文件</span>
                                    <div class="b5uploadlistbox annex_filelist" id="annex_filelist"></div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </form>
@stop

@section('script')
<script>
        $(function () {
            b5uploadfileinit('annex',uploadAfter);
            dragula([annex_filelist]);
   
        })
        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save('{{route('grid.report.instructions')}}', $('#form-edit').serialize());
            }
        }

        function uploadAfter(id, data) {
            annexIndex++;
            let ext = data.ext;
            let url = data.url;
            let filename = data.originName;
            if(!filename){
                filename = getFileName(url);
            }
            var classname = getExtClass(url);
            var html='<div class="b5upload_li">' +
                '           <input type="hidden" name="file['+annexIndex+'][url]" value="'+url+'">' +
                '           <input type="hidden" name="file['+annexIndex+'][originName]" value="'+filename+'">' +
                '           <input type="hidden" name="file['+annexIndex+'][ext]" value="'+ext+'">' +
                '           <div class="b5upload_filetype '+classname+'"></div>' +
                '           <div class="b5upload_filename">'+filename+
                '        </div>' +
                '               <div class="b5upload_fileop">' +
                '                   <a href="javascript:;" onclick="b5uploadImgRemove(this)"><i class="fa fa-trash-o"></i>删除</a>' +
                '                  <a href="'+url+'" target="_blank"><i class="fa fa-hand-o-right"></i>查看</a>' +
                '               </div>' +
                '      </div>';
            b5uploadhtmlshow(id,html);
        }
    </script>
@stop