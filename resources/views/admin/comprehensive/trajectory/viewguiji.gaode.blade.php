<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>轨迹</title>
    <script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
    <script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
    <style>
        html, body, #container {
            height: 100%;
            width: 100%;
        }
        .amap-icon img {
            width: 30px;
            height: 30px;
        }
    </style>
</head>
<body>
<div id="container">

</div>
<div class="input-card" style="width: 30rem;position: sticky;margin-left: 15px;margin-top: 15px;font-family: auto;">
    <div style="line-height: 0px;"><h3 style="float: left;width: 40%">{{$info['username']}}</h3><h4 style="float: right;width: 60%">{{$date_range}}</h4></div>
    <div style="line-height: 40px"><span style="float: left">步数：{{$info['step_num']}}</span>   <span style="float: right;padding-right: 50px">停留次数：{{$info['stay_num']}}</span></div>

</div>
@if($info['trajectory'])
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key={{env('GAODE_KEY')}}"></script>
    <script>
        // JSAPI2.0 使用覆盖物动画必须先加载动画插件
        AMap.plugin('AMap.MoveAnimation', function(){
            var marker, lineArr =  @json($info['trajectory']);

            var map = new AMap.Map("container", {
                resizeEnable: true,
                center: @json($info['trajectory'][0]),
                zoom: 17
            });
            map.clearMap();  // 清除地图覆盖物
            var markers = @json($info['analysis']);

            // 添加一些分布不均的点到地图上,地图上添加三个点标记，作为参照
            markers.forEach(function(marker) {
                new AMap.Marker({
                    map: map,
                    icon: marker.icon,
                    position: [marker.position[0], marker.position[1]]
                });
            });
            // 绘制轨迹
            new AMap.Polyline({
                map: map,
                path: lineArr,
                showDir:true,
                strokeColor: "#2256ff",  //线颜色
                strokeOpacity: 1,     //线透明度
                strokeWeight: 3,      //线宽
                strokeStyle: "solid"  //线样式
            });
            new AMap.Polyline({
                map: map,
                strokeColor: "#AF5",  //线颜色
                strokeWeight: 6,      //线宽
            });
        });
    </script>
@else
    <script>
        $("#container").html('<div style="text-align: center;line-height: 200px;font-size: 16px;color: red">未采集到定位信息</div>')
    </script>
@endif
</body>
</html>
