@extends('admin.layout.form')
@include('widget.asset.select2')
@section('css_common')
    <style>
        .panel-body {
            display: flex;
            justify-content: left;
            flex-wrap: wrap;
        }
        .item {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            min-width: 50px;
            max-width: 200px;
            /*align-items: center;*/
            /*justify-content: center;*/
        }
        .item-title {
            margin-top: 5px;
        }
    </style>
    <style>
        .group {
            display: flex;
            flex-wrap: wrap;
        }

        .boder {
            border: 1px solid #d7d7d7;
            border-radius: 10px;
        }

        .boder+.boder {
            margin-left: 20px;
        }

        .boder_heade {
            background: #f2f2f2;
            padding: 10px;
            box-sizing: border-box;
        }

        .boder_body {
            display: flex;
            flex-wrap: wrap;
            padding: 10px 0;
            box-sizing: border-box;
        }

        .item {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            padding: 0 10px;
            box-sizing: border-box;
            position: relative;

        }

        .u-flex {
            display: flex;
        }

        .dropdown-link {
            cursor: pointer;
            outline: none;
        }



        .dropdown-menu {
            display: none;
            position: absolute;
            background-color: #fff;
            border: 1px solid #e4e7ed;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
            margin: 0;
            padding: 10px;
            box-sizing: border-box;
            left: 0;
            z-index: 10;
            top: 100%;

        }

        .dropdown-arrow {
            position: absolute;
            display:none;
            top: 100%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #ffffff;
            z-index: 12;

        }
    </style>
@append
@section('content')
<div class="col-sm-12 search-collapse" style="box-shadow: 0 0 0;padding-left: 0">
    <form id="role-form">
        <input type="hidden" name="user_type" value="{{\App\Models\System\Users::TYPE_WGY}}">
        <div class="select-list">
            <ul>
                <li>月份：
                    <input type="text" value="{{date('Y-m')}}" name="month" id="month" placeholder="" readonly autocomplete="off">
                </li>
                <li style="display: none">发生时间：
                    <span id="selecttime">
                        <input type="text" value="{{date('Y-m-01')}}" name="start_time" id="startTime" placeholder="开始时间" readonly autocomplete="off">
                        <span>-</span>
                        <input type="text"  value="{{date('Y-m-t')}}" name="end_time" id="endTime" placeholder="结束时间" readonly autocomplete="off">
                    </span>
                </li>
                <li>辖区范围：
                    <select name="cid" class="select2">
                        @if (\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys == \App\Extends\Helpers\Functions::getCommunity())
                        <option value="">{{$street_name}}</option>
                        @endif
                        @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                <li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="searchStats()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-8">
        <div class="panel panel-default" id="kqdk">
            <div class="panel-heading">考勤打卡</div>
            <div class="panel-body">
                <div class="item">
                    <div id="kqdk_zrs">-</div>
                    <div class="item-title">总人数</div>
                </div>
                <div class="item">
                    <div id="kqdk_zc">-</div>
                    <div class="item-title">正常</div>
                </div>
                <div class="item">
                    <div id="kqdk_cd">-</div>
                    <div class="item-title">迟到</div>
                </div>
                <div class="item">
                    <div id="kqdk_zt">-</div>
                    <div class="item-title">早退</div>
                </div>
                <div class="item">
                    <div id="kqdk_sbqk">-</div>
                    <div class="item-title">上班缺卡</div>
                </div>
                <div class="item">
                    <div id="kqdk_xbqk">-</div>
                    <div class="item-title">下班缺卡</div>
                </div>
                <div class="item">
                    <div id="kqdk_qj">-</div>
                    <div class="item-title">请假</div>
                </div>
                <div class="item">
                    <div id="kqdk_wc">-</div>
                    <div class="item-title">外出</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-4">
        <div class="panel panel-default" id="rwxp">
            <div class="panel-heading">任务下派</div>
            <div class="panel-body">
                <div class="item">
                    <div id="rwxp_zs">-</div>
                    <div class="item-title">总数</div>
                </div>
                <div class="item">
                    <div id="rwxp_wks">-</div>
                    <div class="item-title">未开始</div>
                </div>
                <div class="item">
                    <div id="rwxp_jxz">-</div>
                    <div class="item-title">进行中</div>
                </div>
                <div class="item">
                    <div id="rwxp_ywc">-</div>
                    <div class="item-title">已完成</div>
                </div>
                <div class="item">
                    <div id="rwxp_yqx">-</div>
                    <div class="item-title">已取消</div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-4">
        <div class="panel panel-default" id="xf">
            <div class="panel-heading">巡防</div>
            <div class="panel-body">
                <div class="item">
                    <div id="xf_dbrs">-</div>
                    <div class="item-title">达标人数</div>
                </div>
                <div class="item">
                    <div id="xf_wdbrs">-</div>
                    <div class="item-title">未达标人数</div>
                </div>
                <div class="item">
                    <div id="xf_jd">-</div>
                    <div class="item-title">进度</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-4">
        <div class="panel panel-default" id="rh">
            <div class="panel-heading">入户</div>
            <div class="panel-body">
                <div class="item">
                    <div id="rh_dbrs">-</div>
                    <div class="item-title">达标人数</div>
                </div>
                <div class="item">
                    <div id="rh_wdbrs">-</div>
                    <div class="item-title">未达标人数</div>
                </div>
                <div class="item">
                    <div id="rh_jd">-</div>
                    <div class="item-title">进度</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-4">
        <div class="panel panel-default" id="rsp">
            <div class="panel-heading">入商铺</div>
            <div class="panel-body">
                <div class="item">
                    <div id="rsp_dbrs">-</div>
                    <div class="item-title">达标人数</div>
                </div>
                <div class="item">
                    <div id="rsp_wdbrs">-</div>
                    <div class="item-title">未达标人数</div>
                </div>
                <div class="item">
                    <div id="rsp_jd">-</div>
                    <div class="item-title">进度</div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-4">
        <div class="panel panel-default" id="sj">
            <div class="panel-heading">事件</div>
            <div class="panel-body">
                <div class="item">
                    <div id="sj_jczl">-</div>
                    <div class="dropdown-link item-title">基层治理 <i class="fa fa-angle-down"></i></div>
                    <div class="dropdown-arrow"></div>
                    <div class="dropdown-menu" style="padding: 5px;">
                        <div class="u-flex">
                            <div>
                                <div id="sj_jczl_czz">-</div>
                                <div style=" white-space: nowrap;">处置中</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_jczl_yja">-</div>
                                <div style=" white-space: nowrap;">已结案</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_jczl_yht">-</div>
                                <div style=" white-space: nowrap;">已回退</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_jczl_yqx">-</div>
                                <div style=" white-space: nowrap; ">已取消</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div id="sj_dld">-</div>
                    <div class="dropdown-link item-title">大联动 <i class="fa fa-angle-down"></i></div>
                    <div class="dropdown-arrow"></div>
                    <div class="dropdown-menu" style="padding: 5px;">
                        <div class="u-flex">
                            <div>
                                <div id="sj_dld_czz">-</div>
                                <div style=" white-space: nowrap;">处置中</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_dld_yja">-</div>
                                <div style=" white-space: nowrap;">已结案</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_dld_yzf">-</div>
                                <div style=" white-space: nowrap;">已作废</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div id="sj_qdz">-</div>
                    <div class="dropdown-link item-title">清单制 <i class="fa fa-angle-down"></i></div>
                    <div class="dropdown-arrow"></div>
                    <div class="dropdown-menu" style="padding: 5px;">
                        <div class="u-flex">
                            <div>
                                <div id="sj_qdz_wzg">-</div>
                                <div style=" white-space: nowrap;">未整改</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_qdz_jjyq">-</div>
                                <div style=" white-space: nowrap;">即将逾期</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_qdz_yqwzg">-</div>
                                <div style=" white-space: nowrap;">逾期未整改</div>
                            </div>
                            <div style="margin-left: 20px;">
                                <div id="sj_qdz_yzg">-</div>
                                <div style=" white-space: nowrap; ">已整改</div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-8">
        <div class="panel panel-default" id="zdysb">
            <div class="panel-heading">自定义上报</div>
            <div class="panel-body">
                <div class="item">
                    <div>-</div>
                    <div class="item-title">总数</div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
    <script>
        $(function () {
            layui.use('laydate', function() {
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#month'
                    ,type: 'month'
                    ,max:'{{date('Y-m')}}'
                    ,btns: ['confirm']
                    ,change: function(value, date, endDate){
                        var lastday = getLastDayOfMonth(date.year, date.month);
                        $('#startTime').val(value+'-01');
                        $('#endTime').val(value+'-'+lastday);
                    }
                });
                // laydate.render({
                //     elem: '#selecttime'
                //     ,range: ['#startTime', '#endTime']
                //     ,btns: ['confirm']
                //     ,close: function(value, date, endDate){
                //         // searchStats();
                //     }
                // });
            });
            searchStats();
        });

        function getLastDayOfMonth(year, month) {
            return new Date(year, month, 0).getDate();
        }
        function searchStats() {
            getBaseData('kqdk');
            getBaseData('rwxp');
            getBaseData('xf');
            getBaseData('rh');
            getBaseData('rsp');
            zdysb();
            sj();
        }
        function getBaseData(id) {
            $.each($('#'+id+' .panel-body .item'), function (index, item) {
                $(item).children(':first').html('-');
            });
            var params = $.common.formToJSON('role-form');
            $.ajax({
                url: _M_+_C_+'/ajax'+id,
                data: params,
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    if (result.code == 0) {
                        $.each(result.data, function (key, val) {
                            $('#'+id+'_'+key).html(val);
                        });
                    }
                }
            });
        }

        function zdysb() {
            $.each($('#zdysb .panel-body .item'), function (index, item) {
                $(item).children(':first').html('-');
            });
            var params = $.common.formToJSON('role-form');
            $.ajax({
                url: _M_+_C_+'/ajaxzdysb',
                data: params,
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    if (result.code == 0) {
                        var html = '';
                        $.each(result.data, function (key, val) {
                            html += '<div class="item"> ' +
                                '<div>'+val.count+'</div> ' +
                                '<div class="item-title">'+val.title+'</div> ' +
                                '</div>';

                        });
                        console.log(html);
                        $('#zdysb .panel-body').html(html);
                    }
                }
            });
        }
        function sj() {
            $.each($('#sj .panel-body .item'), function (index, item) {
                $(item).children(':first').html('-');
                $.each($(item).find('.u-flex div'), function (index, item) {
                    $(item).children(':first').html('-');
                });
            });
            var params = $.common.formToJSON('role-form');
            $.ajax({
                url: _M_+_C_+'/ajaxsj',
                data: params,
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    if (result.code == 0) {
                        $.each(result.data, function (key, val) {
                            $('#sj_'+key).html(val);
                        });
                    }
                }
            });
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownItems = document.querySelectorAll('.item');
            dropdownItems.forEach(function(dropdownItem) {
                const dropdownLink = dropdownItem.querySelector('.dropdown-link');
                const dropdownMenu = dropdownItem.querySelector('.dropdown-menu');
                const dropdownArrow = dropdownItem.querySelector('.dropdown-arrow');
                const icon = $(dropdownLink).find('i');

                if (dropdownLink && dropdownMenu) {
                    // 鼠标移入显示菜单
                    // dropdownItem.addEventListener('click', function() {
                    //     dropdownMenu.style.display = 'block';
                    //     dropdownArrow.style.display = 'block';
                    //     icon.removeClass('fa-angle-down');
                    //     icon.addClass('fa-angle-up');
                    // });
                    // 鼠标移出隐藏菜单
                    // dropdownItem.addEventListener('mouseleave', function() {
                    //     dropdownMenu.style.display = 'none';
                    //     dropdownArrow.style.display = 'none';
                    // });
                    // 点击其他地方隐藏
                    document.addEventListener('click', function(e) {
                        if (!dropdownLink.contains(e.target)) {
                            dropdownMenu.style.display = 'none';
                            dropdownArrow.style.display = 'none';
                            icon.removeClass('fa-angle-up');
                            icon.addClass('fa-angle-down');
                        }
                    });

                    dropdownLink.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡到document
                        dropdownMenu.style.display = (dropdownMenu.style.display === 'none' ||
                            dropdownMenu.style.display === '') ? 'block' : 'none';
                        dropdownArrow.style.display = dropdownMenu.style.display;
                        if (dropdownArrow.style.display === 'none') {
                            icon.removeClass('fa-angle-up');
                            icon.addClass('fa-angle-down');
                        } else {
                            icon.removeClass('fa-angle-down');
                            icon.addClass('fa-angle-up');
                        }
                    });
                }
            });
        });
    </script>
@stop
