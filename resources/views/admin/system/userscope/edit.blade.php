<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>范围划分</title>
    <meta name="renderer" content="webkit">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="{{asset('static/plugins/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/fontawesome/css/font-awesome.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/style.css')}}" rel="stylesheet"/>
    <link rel="stylesheet" href="{{asset('static/plugins/supermap-iclient/libs/leaflet/plugins/leaflet-geoman/2.14.2/leaflet-geoman.css')}}">
</head>
<body style=" margin: 0;overflow: hidden;background: #fff;width: 100%;height:100%;position: absolute;top: 0;">
<div id="map" style="margin:0 auto;width: 100%;height: 100%"></div>
<script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
<script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
<script src="{{asset('static/plugins/bootstrap/js/bootstrap.min.js')}}"></script>
<script src="{{asset('static/plugins/three-js/three.min.js')}}"></script>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script src="{{asset('static/plugins/blockUI/jquery.blockUI.js')}}"></script>
<script src="{{asset('static/admin/js/common.js')}}"></script>
<script src="{{asset('static/admin/js/iframe-ui.js')}}"></script>
<script type="text/javascript" src="{{asset('static/plugins/supermap-iclient/dist/leaflet/include-leaflet.js')}}"></script>
<script type="text/javascript" src="{{asset('static/plugins/supermap-iclient/libs/leaflet/plugins/leaflet-geoman/2.14.2/leaflet-geoman.min.js')}}"></script>
<script type="text/javascript">
    var map,resultLayer, geo,
        mapUrl = "{{env('YZT_STREET_URL')}}",
        url = "{{env('YZT_DISTRICT_URL')}}";
    @php($center = explode(',', env('YZT_CENTER_POINT','104.0638303756715,30.659859309043362')))
    L.supermap.initMap(url,{
        mapOptions: {
            target: 'map',
            center: L.latLng({{$center[1]}}, {{$center[0]}}),
            zoom: 16
        }
    }).then((res) => {
        map = res.map
        new L.supermap.TiledMapLayer(url).addTo(map);
        new L.supermap.TiledMapLayer(mapUrl, {
            transparent: true,  // 叠加层透明，显示底图
            opacity: 0.4        // 可选：调整不透明度
        }).addTo(map);

        var control = L.control({position: 'topright'});
        control.onAdd = function (map) {
            var popup = L.DomUtil.create('div');
            popup.innerHTML = "<a type='button' id='decodeBtn' onclick='restMap()' class='btn btn-primary'  style='padding: 5px 10px;font-size: 14px;line-height: 1.5;border-radius: 6px;background-color: #ffffff;border-color: #2256ff;color: #2256ff;cursor: pointer;' >重置范围</a>";
            handleMapEvent(popup, this._map);
            return popup;
        };
        control.addTo(map);
        map.pm.setLang("zh");

        map.pm.setGlobalOptions({
            pinning: true,
        });
        @if($region)
        var lnglats = $.common.reverseLngLat(Object.values(@json($region)));
        map.setView(lnglats[0]);
        geo = L.polygon(lnglats).addTo(map);
        // 启用编辑功能
        geo.pm.enable();
        @else
        map.pm.enableDraw('Polygon');
        @endif
        map.on('pm:create', function(e) {
            geo = e.layer;
            geo.pm.enable();
        });
    });
    function restMap(e) {
        stopPropagation(e);
        map.pm.disableDraw();
        if (geo) {
            geo.pm.remove();
            geo = null;
        }
        map.pm.enableDraw('Polygon');
    }
    function stopPropagation(e) {
        e = e || window.event;
        if(e.stopPropagation) {
            e.stopPropagation(); //W3C阻止冒泡方法
        } else {
            e.cancelBubble = true; //IE阻止冒泡方法
        }
    }
    function handleMapEvent(div, map) {
        if (!div || !map) {
            return;
        }
        div.addEventListener('mouseover', function () {
            map.dragging.disable();
            map.scrollWheelZoom.disable();
            map.doubleClickZoom.disable();
        });
        div.addEventListener('mouseout', function () {
            map.dragging.enable();
            map.scrollWheelZoom.enable();
            map.doubleClickZoom.enable();
        });
    }
    function submitHandler() {
        if (!geo) {
            $.modal.msgError('请绘制区域');
            return false;
        }
        let FenceRegion = [];
        geo.toGeoJSON().geometry.coordinates[0].forEach(function (item, index) {
            FenceRegion.push([item[0], item[1]]);
        });
        $.operate.save('{{route("system.{$app}.edit")}}', {scope:FenceRegion,id:{{$info['id']}}});
    }
</script>
</body>
</html>