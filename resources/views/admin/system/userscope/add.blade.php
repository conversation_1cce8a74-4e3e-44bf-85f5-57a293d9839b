@extends('admin.layout.form')
@include("widget.asset.formSelects")
@section('css_common')
{{--    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>--}}
    <style type="text/css" media="screen">
        #map{
            width: 100%;
            height: 82vh;
        }
    </style>
<link rel="stylesheet" href="{{asset('static/plugins/supermap-iclient/libs/leaflet/plugins/leaflet-geoman/2.14.2/leaflet-geoman.css')}}">
@append
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">{{$user_type_text}}：</label>
            <div class="col-sm-8">
                <select name="user_id" id="user_id" xm-select="user_id" xm-select-search xm-select-radio required>
                    @foreach($gridUsers as $user)
                        <option value="{{$user['id']}}">{{$user['username']}}（{{$user['mobile']}}）</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <div id="map"></div>
        </div>
    </form>
@stop

@section('script')
    <script type="text/javascript" src="{{asset('static/plugins/supermap-iclient/dist/leaflet/include-leaflet.js')}}"></script>
    <script type="text/javascript" src="{{asset('static/plugins/supermap-iclient/libs/leaflet/plugins/leaflet-geoman/2.14.2/leaflet-geoman.min.js')}}"></script>
    <script type="text/javascript">
        var map,resultLayer, geo,
            mapUrl = "{{env('YZT_STREET_URL')}}",
            url = "{{env('YZT_DISTRICT_URL')}}";
        @php($center = explode(',', env('YZT_CENTER_POINT','104.0638303756715,30.659859309043362')))
        L.supermap.initMap(url,{
            mapOptions: {
                target: 'map',
                center: L.latLng({{$center[1]}}, {{$center[0]}}),
                zoom: 16
            }
        }).then((res) => {
            map = res.map
            new L.supermap.TiledMapLayer(url).addTo(map);
            new L.supermap.TiledMapLayer(mapUrl, {
                transparent: true,  // 叠加层透明，显示底图
                opacity: 0.4        // 可选：调整不透明度
            }).addTo(map);

            var control = L.control({position: 'topright'});
            control.onAdd = function (map) {
                var popup = L.DomUtil.create('div');
                popup.innerHTML = "<a type='button' id='decodeBtn' onclick='restMap()' class='btn btn-primary'  style='padding: 5px 10px;font-size: 14px;line-height: 1.5;border-radius: 6px;background-color: #ffffff;border-color: #2256ff;color: #2256ff;cursor: pointer;' >重置范围</a>";
                handleMapEvent(popup, this._map);
                return popup;
            };
            control.addTo(map);
            map.pm.setLang("zh");

            map.pm.setGlobalOptions({
                pinning: true,
            });
            @if(!empty($region))
                var lnglats = $.common.reverseLngLat(Object.values(@json($region)));
                geo = L.polygon(lnglats).addTo(map);
                // 启用编辑功能
                geo.pm.enable();
            @else
                map.pm.enableDraw('Polygon');
            @endif

            map.on('pm:create', function(e) {
                geo = e.layer;
                geo.pm.enable();
            });
        });


        function restMap(e) {
            stopPropagation(e);
            map.pm.disableDraw();
            if (geo) {
                geo.pm.remove();
                geo = null;
            }
            map.pm.enableDraw('Polygon');
        }
        function stopPropagation(e) {
            e = e || window.event;
            if(e.stopPropagation) {
                e.stopPropagation(); //W3C阻止冒泡方法
            } else {
                e.cancelBubble = true; //IE阻止冒泡方法
            }
        }
        function handleMapEvent(div, map) {
            if (!div || !map) {
                return;
            }
            div.addEventListener('mouseover', function () {
                map.dragging.disable();
                map.scrollWheelZoom.disable();
                map.doubleClickZoom.disable();
            });
            div.addEventListener('mouseout', function () {
                map.dragging.enable();
                map.scrollWheelZoom.enable();
                map.doubleClickZoom.enable();
            });
        }
        function submitHandler() {
            let user_id = layui.formSelects.value('user_id', 'valStr');
            if (!user_id) {
                $.modal.msgError('请选择{{$user_type_text}}');
                return false;
            }
            if (!geo) {
                $.modal.msgError('请绘制区域');
                return false;
            }
            let FenceRegion = [];
            geo.toGeoJSON().geometry.coordinates[0].forEach(function (item, index) {
                FenceRegion.push([item[0], item[1]]);
            });
            $.operate.save(oasUrl, {scope:FenceRegion, user_id:user_id});
        }
    </script>
@stop
