@extends('admin.layout.form')
@include("widget.asset.formSelects")
@section('css_common')
{{--    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>--}}
    <style type="text/css" media="screen">
        .button-group {position: absolute;top: 100px;right: 20px;font-size: 12px;padding: 10px; }
        #container{
            width: 100%;
            height: 500px;
        }
        .button-group {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 12px;
            padding: 10px;
        }

        .button-group .button {
            height: 28px;
            line-height: 28px;
            background-color: #0D9BF2;
            color: #FFF;
            border: 0;
            outline: none;
            padding-left: 5px;
            padding-right: 5px;
            border-radius: 3px;
            margin-bottom: 4px;
            cursor: pointer;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">{{$user_type_text}}：</label>
            <div class="col-sm-8">
                <select name="user_id" id="user_id" xm-select="user_id" xm-select-search xm-select-radio required>
                    @foreach($gridUsers as $user)
                        <option value="{{$user['id']}}">{{$user['username']}}（{{$user['mobile']}}）</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <div id="container"></div>
            <div class="button-group">
                <input type="button" class="button" value="重置范围" onClick="restMap()"/>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script src="https://webapi.amap.com/maps?v=1.4.4&key={{env('GAODE_KEY')}}&plugin=AMap.PolyEditor"></script>
    <script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
    <script>
        var storage = window.localStorage;
        var editorTool, map = new AMap.Map("container", {
            resizeEnable: true,
            center: ['104.065861','30.657401'],//地图中心点
            zoom: 13 //地图显示的缩放级别
        });
        var beginNum = 0;
        var clickListener;
        var beginPoints = [];//存坐标的数组
        var beginMarks = [];
        var polygonEditor;
        var resPolygon = [];
        var polygon;
        var resNum = 0;
        init();
        function restMap(){
            map.clearMap();
            init()
        }
        function init() {
            beginPoints = [];
            beginMarks = [];
            beginNum = 0;
            polygonEditor = '';
            clickListener = AMap.event.addListener(map, "click", mapOnClick);
        }

        function mapOnClick(e) {
            beginMarks.push(addMarker(e.lnglat));
            beginPoints.push(e.lnglat);
            beginNum++;
            if (beginMarks.length >= 3) {
                AMap.event.removeListener(clickListener);
                createPolygon(beginPoints);
                polygonEditor = createEditor(polygon);
                clearMarks();
            }
        }

        /*创建多边形*/
        function createPolygon(arr) {
             polygon = new AMap.Polygon({
                map: map,
                path: arr,
                strokeColor: "#009cff",
                strokeOpacity: 1,
                strokeWeight: 3,
                fillColor: "#009cff",
                fillOpacity: 0.35
            });
        }

        function createEditor(polygon) {
            var polygonEditor = new AMap.PolyEditor(map, polygon);
            polygonEditor.open();
            AMap.event.addListener(polygonEditor, 'end', polygonEnd);
            return polygonEditor;
        }

        function submitHandler() {
            let user_id = layui.formSelects.value('user_id', 'valStr');
            console.log(user_id);
            if (!user_id) {
                $.modal.msgError('请选择网格员');
                return false;
            }
            if (!polygonEditor || beginPoints.length < 3) {
                $.modal.msgError('请至少选择3个点');
                return false;
            }
            let FenceRegion = [];
            beginPoints.forEach(function (item, index) {
                var lng = parseFloat(item['lng']);//经
                var lat = parseFloat(item['lat']);//纬
                FenceRegion.push([lng, lat]);
            });
            // let newRegion = ObjectToJson(FenceRegion);
            $.operate.save(oasUrl, {scope:FenceRegion, user_id:user_id});
        }

        function polygonEnd(res) {
            resPolygon.push(res.target);
            alert(resPolygon[resNum].contains([104.008014, 30.680541]));
            appendHideHtml(resNum, res.target.getPath());
            resNum++;
            init();
        }

        function appendHideHtml(index, arr) {
            var strify = JSON.stringify(arr);
            var html = '<input type="hidden" id="index' + index + '" name="paths[]" value="' + strify + '">';
            $('body').append(html);
            console.log(html);
        }

        function clearMarks() {
            map.remove(beginMarks);
        }

        function JsonToArray(json) {
            var arr = JSON.parse(json);
            var res = [];
            for (var i = 0; i < arr.length; i++) {
                var line = [];
                line.push(arr[i].lng);
                line.push(arr[i].lat);
                res.push(line);
            }
            return res;
        }

        // 实例化点标记
        function addMarker(lnglat) {
            var marker = new AMap.Marker({
                position: lnglat
            });
            marker.setMap(map);
            return marker;
        }

    </script>
@stop
