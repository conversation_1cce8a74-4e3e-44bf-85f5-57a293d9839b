@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.upload")

@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">APP类型：</label>
        <div class="col-sm-8">
            <label class="radio-box"><input type="radio" name="app_type" value="1" @if ($info['app_type'] == 1) checked @endif/> 业务端</label>
            <label class="radio-box"><input type="radio" name="app_type" value="2" @if ($info['app_type'] == 2) checked @endif/> 调度端</label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">版本号：</label>
        <div class="col-sm-8">
            <input type="text" name="version" value="{{$info['version']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">更新说明：</label>
        <div class="col-sm-8">
            <textarea rows="6" name="note" class="form-control" required autocomplete="off">{{$info['note']}}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">安装包地址：</label>
        <div class="col-sm-8">
            <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                <button type="button" class="btn-b5upload btn btn-warning btn-sm" id="annex" data-exts="apk"  data-multi="1" data-maxsize="100"  data-cat="apk" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                <div style="padding-top: 10px;">
                    <input type="text" name="apkurl" id="apkurl" value="{{$info['apkurl']}}" class="form-control" required autocomplete="off"/>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请上传apk文件或填写文件地址</span>
            </div>
        </div>
    </div>
    @if ($info['publish_status'])
        <div class="form-group publish_time">
            <label class="col-sm-3 control-label is-required">发布时间：</label>
            <div class="col-sm-8">
                <div class="form-control-static">{{$info['publish_time']}}</div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 已发布的版本不能修改发布时间</span>
            </div>
        </div>
    @else
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">发布类型：</label>
        <div class="col-sm-8">
            <label class="radio-box"><input type="radio" name="publish_type" value="1" @if ($info['publish_type'] == 1) checked @endif /> 立即发布</label>
            <label class="radio-box"><input type="radio" name="publish_type" value="2" @if ($info['publish_type'] == 2) checked @endif /> 定时发布</label>
        </div>
    </div>
    <div class="form-group publish_time" @if ($info['publish_type'] == 1) style="display: none" @endif>
        <label class="col-sm-3 control-label is-required">发布时间：</label>
        <div class="col-sm-8">
            <input type="text" name="publish_time" value="{{$info['publish_time']}}" class="form-control time-input" required autocomplete="off" data-type="datetime"/>
        </div>
    </div>
    @endif
</form>
@stop

@section('script')
    <script>
        $(function () {
            b5uploadfileinit('annex',uploadAfter);
            $('input[name=publish_type]').on('ifChecked',function(e){
                if (this.value == 1) {
                    $('.publish_time').hide();
                } else {
                    $('.publish_time').show();
                }
            })
        })
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
        function uploadAfter(id, data) {
            $('#apkurl').val(data.url);
        }
    </script>
@stop
