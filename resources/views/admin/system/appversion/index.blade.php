@extends('admin.layout.layout')
@include("widget.asset.select2")
@section('content')
<div class="col-sm-12 search-collapse">
    <form id="role-form">
        <div class="select-list">
            <ul>
                <li>
                    <select name="app_type" class="select2">
                        <option value="">APP类型</option>
                        <option value="1">业务端</option>
                        <option value="2">调度端</option>
                    </select>
                </li>
                <li><input type="text" name="like[version]" placeholder="版本号" autocomplete="off"></li>
                <li><input type="text" name="like[note]" placeholder="更新说明" autocomplete="off"></li>
                <li class="select-time">
                    <input type="text" name="between[create_time][start]" id="startTime" placeholder="创建时间开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="between[create_time][end]" id="endTime" placeholder="创建时间结束时间" readonly>
                </li>
                <li class="select-time">
                    <input type="text" name="between[publish_time][start]" class="time-input" placeholder="发布时间开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="between[publish_time][end]" class="time-input" placeholder="发布时间结束时间" readonly>
                </li>
                <li>
                    <select name="publish_status" class="select2">
                        <option value="">发布状态</option>
                        <option value="1">已发布</option>
                        <option value="0">未发布</option>
                    </select>
                </li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("system:appversion:add")
    <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
    @endhasPerm
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "APP版本管理",
                sortName:'publish_time',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', sortable: true,visible: false},
                    {field: 'app_type_text', title: 'APP类型'},
                    {field: 'version', title: '版本号'},
                    {
                        field: 'note',
                        title: '更新说明',
                        formatter: function (value, row, index) {
                            return $.table.tooltip(value,25, 'open');
                        }
                    },
                    {field: 'create_time', title: '创建时间',  sortable: true},
                    {field: 'publish_time', title: '发布时间',  sortable: true},
                    {field: 'publish_status', title: '发布状态', formatter: function (value, row, index) {
                            return $.view.statusShow(row,false,['未发布','已发布'], 'publish_status');
                        }},
                    {field: 'update_time', title: '更新时间',  sortable: true,visible: false},
                @if(hasPerm("system:appversion:edit") || hasPerm("system:appversion:drop"))
                    {
                        title: '操作',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("system:appversion:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')">编辑</a> ');
                        @endhasPerm

                        @hasPerm("system:appversion:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                    @endif
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

