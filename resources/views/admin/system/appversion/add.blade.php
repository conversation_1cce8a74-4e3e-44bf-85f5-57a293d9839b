@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.upload")

@section('content')
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">APP类型：</label>
        <div class="col-sm-8">
            <label class="radio-box"><input type="radio" name="app_type" value="1" checked/> 业务端</label>
            <label class="radio-box"><input type="radio" name="app_type" value="2" /> 调度端</label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">版本号：</label>
        <div class="col-sm-8">
            <input type="text" name="version" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">更新说明：</label>
        <div class="col-sm-8">
            <textarea rows="6" name="note" class="form-control" required autocomplete="off"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">安装包地址：</label>
        <div class="col-sm-8">
            <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                <button type="button" class="btn-b5upload btn btn-warning btn-sm" id="annex" data-exts="apk"  data-multi="1" data-maxsize="100"  data-cat="apk" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                <div style="padding-top: 10px;">
                    <input type="text" name="apkurl" id="apkurl" value="" class="form-control" required autocomplete="off"/>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请上传apk文件或填写文件地址</span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">发布类型：</label>
        <div class="col-sm-8">
            <label class="radio-box"><input type="radio" name="publish_type" value="1" checked/> 立即发布</label>
            <label class="radio-box"><input type="radio" name="publish_type" value="2" /> 定时发布</label>
        </div>
    </div>
    <div class="form-group publish_time" style="display: none">
        <label class="col-sm-3 control-label is-required">发布时间：</label>
        <div class="col-sm-8">
            <input type="text" name="publish_time" value="" class="form-control time-input" required autocomplete="off" data-type="datetime"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            b5uploadfileinit('annex',uploadAfter);
            $('input[name=publish_type]').on('ifChecked',function(e){
                if (this.value == 1) {
                    $('.publish_time').hide();
                } else {
                    $('.publish_time').show();
                }
            })
        })
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
        function uploadAfter(id, data) {
            $('#apkurl').val(data.url);
        }
    </script>
@stop
