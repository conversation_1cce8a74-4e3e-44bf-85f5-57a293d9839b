@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="like[name]" placeholder="字典名称"></li>
                    <li><input type="text" name="like[type]" placeholder="字典标识"></li>
                    <li>
                        <select name="where[status]" class="select2">
                            <option value="">字典状态</option>
                            <option value="1">正常</option>
                            <option value="0">停用</option>
                        </select>
                    <li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:dicttype:add")
            <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "字典",
                sortName:'listsort',
                type: 0,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: '字典ID',  sortable: true},
                    {field: 'name', title: '字典名称'},
                    {
                        field: 'type',
                        title: '字典标识',
                        sortable: true,
                        formatter: function(value, row, index) {
                            return '<a href="javascript:void(0)" onclick="detail_mine(\'' + row.type + '\')">' + value + '</a>';
                        }},
                    {field: 'listsort', title: '显示顺序',align: 'left', sortable: true},
                    {
                        title: '状态',
                        field: 'status',
                        align: 'left',
                        sortable: true,
                        formatter: function (value, row, index) {
                            return $.view.statusShow(row,false);
                        }
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {field: 'note', title: '备注',visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:dicttype:edit")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')">编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:dictdata:index")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="detail_mine(\'' + row.type + '\')">列表</a> ');
                            @endhasPerm
                            @hasPerm("system:dicttype:drop")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                            @endhasPerm

                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        /*字典列表-详细*/
        function detail_mine(type) {
            var url = '{{route('system.dictdata.index')}}?type=' + type;
            $.modal.openTab("字典数据", url);
        }
    </script>
@stop

