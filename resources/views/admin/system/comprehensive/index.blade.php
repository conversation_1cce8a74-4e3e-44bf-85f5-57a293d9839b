@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
        .relevance {
            padding-bottom: 10px;
        }
        #relevance-struct.no-setting {
            color: #aab2c8;
        }
    </style>
@append
@section('content')
    <div class="btn-group-sm" id="toolbar" role="group">
        <div class="pull-right relevance">
            <span>关联科室：</span>
            <span id="relevance-struct" @if(!$relevanceStruct) class="no-setting" @endif>@if($relevanceStruct){{$relevanceStruct['name']}}@else未设置@endif</span>
            @hasPerm("system:comprehensive:relevance")
            <a class="" href="javascript:void(0);" onclick="relevance()">设置</a>
            @endhasPerm
        </div>
        <div class="clearfix"></div>
        @hasPerm("system:comprehensive:add")
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("system:{$app}:import")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.importExcel()">
            <i class="fa fa-upload"></i> 导入
        </a>
        @endhasPerm
        @hasPerm("system:{$app}:export")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.exportExcel()">
            <i class="fa fa-download"></i> 导出
        </a>
        @endhasPerm
        <div class="pull-right">
            <form id="role-form">
                <div class="select-list">
                    <ul>
                        <li><input type="text" name="keywords" value="" placeholder="姓名电话" autocomplete="off"></li>
                        <li>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "综治人员",
                sortName:'id',
                sortOrder: "desc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'username',
                        title: '姓名',
                    },
                    {
                        field: 'mobile',
                        title: '手机号',
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:comprehensive:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                            @endhasPerm

                            @hasPerm("system:comprehensive:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            @endhasPerm
                                return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function relevance()
        {
            $.modal.open('综治人员关联科室','{{route('system.comprehensive.relevance')}}', '500', '500');
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <a download href="{{asset('template/消防队员综治人员.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop

