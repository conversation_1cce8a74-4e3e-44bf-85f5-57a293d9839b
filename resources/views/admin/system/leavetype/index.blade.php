@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.formSelects")
@section('css_common')
    <style>
        .form-group .col-sm-1{
            padding-right:0;
            /*width: auto;*/
        }
        .form-group .col-sm-3{
            padding-left:0;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-config-site-site">
        @foreach($leaveTypes as $item)
        <div class="form-group">
            <label class="col-sm-1 control-label">{{$item['name']}}：</label>
            <div class="col-sm-10">
                @foreach($dateTypes as $key=>$value)
                    <label class="radio-inline">
                        <input type="radio" name="data[{{$item['id']}}][date_type]" value="{{$key}}" @if($item['date_type'] == $key) checked @endif> {{$value}}
                    </label>
                @endforeach
                <input type="text" name="data[{{$item['id']}}][desc]" value="{{$item['desc']}}" placeholder="备注" class="form-control" style="width: 200px;display: inline-block" autocomplete="off">
            </div>
        </div>
        @endforeach
        <div class="form-group" style="padding-top: 20px;">
            <label class="col-sm-1 control-label"></label>
            <div class="col-sm-10">
                <a href="javascript:;" class="btn btn-success btn-mid b5submit_btn" data-target="form-config-site-site" data-title="确定提交配置信息">保存</a>
            </div>
        </div>
    </form>
@stop