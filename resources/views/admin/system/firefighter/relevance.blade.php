@extends('admin.layout.form')
@include('widget.asset.select2')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <div class="form-group">
            <label class="col-xs-3 control-label is-required" style="text-align: right;padding-top: 7px;">关联科室：</label>
            <div class="col-xs-8">
                <select name="relevance" class="select2">
                    <option value="">请选择</option>
                    @foreach($structs as $struct)
                        <option value="{{$struct['id']}}" @if(isset($relevanceStruct['id']) && $relevanceStruct['id'] == $struct['id']) selected @endif>{{$struct['name']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save('{{route('system.firefighter.relevance')}}', $('#form-edit').serialize(), function (res) {
                    if (res.code == web_status.SUCCESS) {
                        $.modal.close();
                        var obj = window.parent.document.getElementById('relevance-struct');
                        $(obj).html(res.data.struct);
                        $(obj).removeClass('no-setting');
                        var parent = window.parent;
                        parent.$.modal.msgSuccess(res.msg);
                    } else if (res.code == web_status.WARNING) {
                        $.modal.alertWarning(res.msg)
                    }  else {
                        $.modal.alertError(res.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                });
            }
        }
    </script>
@stop
