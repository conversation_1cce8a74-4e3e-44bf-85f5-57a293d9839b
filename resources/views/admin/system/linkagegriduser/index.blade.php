@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
    </style>
@append
@section('content')
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:{$app}:import")
        <a class="btn btn-warning" onclick="importExcel()">导入</a>
        @endhasPerm
        <div class="pull-right">
            <form id="role-form">
                <div class="select-list">
                    <ul>
                        <li>
                            <select name="cid" id="cid" class="select2">
                                <option value="">所属社区</option>
                                @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                                    <option value="{{$id}}">{{$name}}</option>
                                @endforeach
                            </select>
                        </li>
                        <li>
                            <select name="is_dld_bind" id="is_dld_bind" class="select2">
                                <option value="">是否绑定大联动账号</option>
                                <option value="1">是</option>
                                <option value="0">否</option>
                            </select>
                        </li>
                        <li><input type="text" name="keywords" value="" placeholder="姓名电话" autocomplete="off"></li>
                        <li>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "大联动账号-网格员",
                sortName:'id',
                sortOrder: "desc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'username',
                        title: '姓名',
                    },
                    {
                        field: 'mobile',
                        title: '手机号',
                    },
                    {
                        field: 'grid.community.name',
                        title: '所属社区',
                        formatter: function (value, row, index) {
                            return value ? value : '街道级';
                        }
                    },
                    {
                        field: 'grid.name',
                        title: '所属网格',
                        formatter: function (value, row, index) {
                            return value ? value : '消防';
                        }
                    },
                    {
                        field: 'is_dld_bind',
                        title: '是否绑定账号(大联动)',
                        formatter: function (value, row, index) {
                            return value == 1 ? '是' : '否';
                        }
                    },
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:{$app}:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" data-width="800" data-height="500" onclick="$.operate.edit(\'' + row.id + '\', this)">编辑</a> ');
                            @endhasPerm
                                return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });

        function importExcel() {
            $.modal.layerinit(function (layer) {
                layer.open({
                    type: 1,
                    area: ['400px', '260px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: '导入',
                    content: $('#importTpl').html(),
                    btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    btn1: function (index, layero) {
                        var file = layero.find('#file').val();
                        if (file == '' && !$.common.endWith(file, '.xlsx', 'i')) {
                            $.modal.msgWarning("请选择后缀为 xlsx”的文件。");
                            return false;
                        }
                        var index = layer.load(2, {shade: false});
                        $.modal.disable();
                        var formData = new FormData(layero.find('form')[0]);
                        $.ajax({
                            url: '{{route("system.{$app}.import")}}',
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            type: 'POST',
                            success: function (result) {
                                if (result.code == web_status.SUCCESS) {
                                    $.modal.closeAll();
                                    if (result.data.errorNum > 0) {
                                        $.modal.layerinit(function (layer) {
                                            layer.alert(result.msg, {
                                                icon: $.modal.icon('warning'),
                                                title: "系统提示",
                                                btn: ['下载', '取消'],
                                                btnclass: ['btn btn-primary'],
                                            },function (index) {
                                                window.open(result.data.error_url)
                                            });
                                        });
                                    } else {
                                        $.modal.alertSuccess(result.msg);
                                        $.table.refresh();
                                    }
                                } else if (result.code == web_status.WARNING) {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertWarning(result.msg)
                                } else {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertError(result.msg);
                                }
                            },
                            complete: function () {
                                layero.find("#file").val("")
                            }
                        });
                    }
                });
            });
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10 ml5">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
            </div>
            <div class="col-xs-offset-1 mt10" style="color: red;">
                <p>1、点击下载模板：
                    <a download href="{{asset('template/网格员大联动账号导入模板.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                <p>2、数据将从第三行开始读取，请确保数据格式正确。</p>
                <p>3、请确保导入的网格员姓名、电话与系统中一致。</p>
            </div>
        </form>
    </script>
@stop

