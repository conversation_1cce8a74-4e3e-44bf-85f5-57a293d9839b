@extends('admin.layout.form')

@include("widget.asset.select2")
@section('css_common')
    <style>
        .form-group .col-xs-3{
            padding-right:0;
            width: auto;
        }
        .select-user {
            display: none;
        }
        .select-user.active {
            display: inherit;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <div class="col-xs-3">审批人类型：</div>
        </div>
        @if ($user_type == \App\Models\System\Users::TYPE_WGY)
        <div class="form-group">
            <div class="col-xs-3">
                <label class="radio-box">
                    <input type="radio" name="approver_type" value="1" @if($info['approver_type'] == 1) checked @endif /> 选择社区人员
                </label>
            </div>
            <div class="col-xs-8 select-user @if($info['approver_type'] == 1) active @endif">
                @foreach($communities as $cid=>$community)
                    <div class="form-group">
                        <label class="col-xs-3" style="padding-top: 7px;">{{$community['name']}}：</label>
                        <div class="col-xs-9">
                            <select name="community_user[{{$cid}}]" class="select2" required>
                                <option value="">请选择</option>
                                @foreach($community['users'] as $user)
                                    <option value="{{$user['id']}}" @if(isset($info['community_user'][$cid]) && $info['community_user'][$cid] == $user['id']) selected @endif>{{$user['username']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        @endif
        <div class="form-group">
            <div class="col-xs-3">
                <label class="radio-box">
                    <input type="radio" name="approver_type" value="2" @if($info['approver_type'] == 2) checked @endif /> 选择街道人员
                </label>
            </div>
            <div class="col-xs-8 select-user @if($info['approver_type'] == 2) active @endif">
                <select name="street_user_id" class="select2" required>
                    <option value="">请选择</option>
                    @foreach($streetUsers as $user)
                        <option value="{{$user['id']}}" @if($info['street_user_id'] == $user['id']) selected @endif>{{$user['username']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        @if ($user_type == \App\Models\System\Users::TYPE_WGY)
        <div class="form-group">
            <div class="col-xs-3">
                <label class="radio-box">
                    <input type="radio" name="approver_type" value="3" @if($info['approver_type'] == 3) checked @endif /> 社区负责人
                </label>
            </div>
        </div>
        @else
        <div class="form-group">
            <div class="col-xs-3">
                <label class="radio-box">
                    <input type="radio" name="approver_type" value="4" @if($info['approver_type'] == 4) checked @endif /> 关联科室负责人
                </label>
            </div>
        </div>
        @endif
        <div class="form-group">
            <div class="col-xs-3">
                <label class="radio-box">
                    <input type="radio" name="approver_type" value="5" @if($info['approver_type'] == 5) checked @endif /> 分管领导
                </label>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {
            $('input[name=approver_type]').on('ifChecked', function () {
                $('.select-user').removeClass('active');
                $(this).parents('.form-group').find('.select-user').addClass('active');
            });
        })
        $("#form-add").validate();
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize(),function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgReload('保存成功', modal_status.SUCCESS);
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    }  else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                });
            }
        }
    </script>
@stop
