@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}
@section('css_common')
    <style>

        .form-horizontal h4{
            /*line-height: 40px;*/
            position: relative;
            padding-left: 15px; /* 确保文字与竖线有间距 */
            margin-bottom: 10px;
            font-size: 15px;
            font-weight: normal;
        }
        h4::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px; /* 竖线宽度 */
            background-color: #0076F6; /* 竖线颜色 */
        }

        .liuchengtu{
            margin: 20px 10px;
            display: flex;
            align-items: center;
        }
        .liuchengtu .block {
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #DCDFE6;
            /*background-color: #F2F2F2;*/
        }
        .glyphicon{
            font-size: 20px;
        }
        .approval {
            width: 250px;
        }
        .approval:hover .approval-header-icon{
            display: block;
        }
        .approval-header-icon {
            display: none;
        }
        .approval-header-icon a{
            color: black;
        }
        .approval-body {
            display: flex;
            flex-wrap: wrap;
            padding-top: 5px;
        }
        .approval-body-item {
            display: inline-block;
            padding: 5px;
            margin: 5px;
            background-color: #ECF5FF;
            border: 1px solid #409EFF;
            border-radius: 4px;
            color: #409EFF;
        }
    </style>
@append
@section('content')

<form class="form-horizontal m" id="form-add">
    @foreach(\App\Extends\Services\System\ApprovalProcessService::$requestTypes as $request_type=>$request_type_text)
    <div class="form-group">
        <h4>{{$request_type_text}}</h4>
        <div class="liuchengtu">
            <div class="block">提交申请</div>
            <div class="glyphicon glyphicon-arrow-right mr20 ml20"></div>
            @foreach($list[$request_type] ?? [] as $key=>$item)
                <div class="block approval">
                    <div class="approval-header">
                        <div class="approval-header-title pull-left">{{\App\Extends\Helpers\Functions::numToChinese($key+1)}}级审批</div>
                        <div class="approval-header-icon pull-right">
                            @hasPerm("system:{$app}:edit")
                            <a href="javascript:;" onclick="editApproval({{$item['id']}}, '{{$request_type_text}}')"><i class="fa fa-edit"></i></a>
                            @endhasPerm
                            @hasPerm("system:{$app}:drop")
                            <a href="javascript:;" onclick="removeApproval({{$item['id']}}, '{{$request_type_text}}')"><i class="fa fa-remove"></i></a>
                            @endhasPerm
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <div class="approval-body">
                        @if ($item['approver_type'] == 1)
                            @foreach($item['community_user_info'] as $c)
                                <div class="approval-body-item">{{$c['cname']}} {{$c['username']}}</div>
                            @endforeach
                        @elseif ($item['approver_type'] == 2)
                            <div class="approval-body-item">{{$item->streetUser['username'] ?? ''}}</div>
                        @else
                            <div class="approval-body-item">{{$item['approver_type_text']}}</div>
                        @endif
                    </div>
                </div>
                <div class="glyphicon glyphicon-arrow-right mr20 ml20"></div>
            @endforeach
            @hasPerm("system:{$app}:add")
            <a class="btn btn-sm btn-warning" onclick="addApproval({{$request_type}}, '{{$request_type_text}}');">添加</a>
            @endhasPerm
        </div>
    </div>
    @endforeach
</form>
@stop

@section('script')
    <script>
        function addApproval(request_type, request_type_text) {
            var title = '添加'+request_type_text+'审批层级';
            var url = '{{route("system.{$app}.add")}}?request_type='+request_type;
            $.modal.open(title, url,600,500);
        }
        function editApproval(id, request_type_text) {
            var title = '编辑'+request_type_text+'审批层级';
            var url = '{{route("system.{$app}.edit")}}?id='+id;
            $.modal.open(title, url,600,500);
        }
        function removeApproval(id, request_type_text) {
            $.modal.confirm("确定删除该"+request_type_text+"审批层级吗？", function() {
                $.operate.submit('{{route("system.{$app}.drop")}}', "post", "json", { "id": id },function (result) {
                    if (result.code == web_status.SUCCESS) {
                        result.msg && $.modal.msgSuccess(result.msg, function () {
                            location.reload();
                        })
                    }  else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    }  else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                });
            });
        }
    </script>
@stop
