@extends('admin.layout.form')

@include("widget.asset.select2")

@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">字典类型：</label>
            <div class="col-sm-8">
                <select name="type" class="select2" required>
                    <option value="">请选择字典类型</option>
                    @foreach($typeList as $type=>$name)
                        <option value="{{$type}}" @if(isset($input['id']) && $type == $input['id']) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">数据名称：</label>
            <div class="col-sm-8">
                <input type="text" name="name" value="" class="form-control" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">数据值：</label>
            <div class="col-sm-8">
                <input type="text" name="value" value="" class="form-control" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 该项为数据名称的筛选值，请根据已有的数据值类型设置，如果为字母请设置为字母，如果为数字请设置为数字</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">显示顺序：</label>
            <div class="col-sm-8">
                <input type="number" name="listsort" value="99" class="form-control" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">数据状态：</label>
            <div class="col-sm-8">
                <label class="radio-box">
                    <input type="radio" name="status" value="0"/> 停用
                </label>
                <label class="radio-box">
                    <input type="radio" name="status" value="1" checked/> 启用
                </label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">扩展字段：</label>
            <div class="col-sm-8">
                <label class="radio-box">
                    <input type="radio" name="is_true" value="0"/> 否
                </label>
                <label class="radio-box">
                    <input type="radio" name="is_true" value="1" checked/> 是
                </label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="note" class="form-control" placeholder="请输入备注"></textarea>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
