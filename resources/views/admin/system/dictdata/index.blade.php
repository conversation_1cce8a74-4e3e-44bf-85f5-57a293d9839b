@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li>
                        <select name="where[type]" id="dictType" class="select2">
                            <option value="">字典类型</option>
                            @foreach($typeList as $type=>$name)
                                <option value="{{$type}}" @if(isset($input['type']) && $type == $input['type']) selected @endif>{{$name}}</option>
                            @endforeach
                        </select>
                    <li>
                    <li><input type="text" name="like[name]" placeholder="数据名称"></li>
                    <li>
                        <select name="where[status]" class="select2">
                            <option value="">数据状态</option>
                            <option value="1">正常</option>
                            <option value="0">停用</option>
                        </select>
                    <li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:dictdata:add")
            <a class="btn btn-warning" onclick="addDictData()"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("system:dictdata:edit")
            <a class="btn btn-warning single disabled" onclick="$.operate.edit('',this)"> 修改</a>
        @endhasPerm
        <a class="btn btn-warning" onclick="closeItem()">
            <i class="fa fa-reply-all"></i> 关闭
        </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "数据",
                sortName:'listsort',
                columns: [
                    {checkbox: true},
                    {field: 'id', title: '数据ID',  sortable: true},
                    {field: 'name', title: '数据名称'},
                    {field: 'value', title: '数据值'},
                    {field: 'listsort', title: '显示顺序',align: 'left', sortable: true},
                    {
                        title: '状态',
                        field: 'status',
                        align: 'left',
                        sortable: true,
                        formatter: function (value, row, index) {
                            return $.view.statusShow(row,false);
                        }
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {field: 'note', title: '备注',visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:dictdata:edit")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')">编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:dictdata:drop")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                            @endhasPerm

                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);


        });
        //新增字典数据-传入当前类型
        function addDictData() {
            var dictType = $("#dictType option:selected").val();
            $.operate.add('', dictType);
        }
    </script>
@stop

