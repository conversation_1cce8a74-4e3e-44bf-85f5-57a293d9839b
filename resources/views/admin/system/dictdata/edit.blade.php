@extends('admin.layout.form')

@include('widget.asset.select2')

@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">字典类型：</label>
            <div class="col-sm-8">
                <select name="type" class="select2" required>
                    <option value="">请选择字典类型</option>
                    @foreach($typeList as $type=>$name)
                        <option value="{{$type}}" @if($type == $info['type']) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">数据名称：</label>
            <div class="col-sm-8">
                <input type="text" name="name" value="{{$info['name']}}" class="form-control" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">数据值：</label>
            <div class="col-sm-8">
                <input type="text" name="value" value="{{$info['value']}}" class="form-control" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 该项为数据名称的筛选值，请根据已有的数据值类型设置，如果为字母请设置为字母，如果为数字请设置为数字</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">显示顺序：</label>
            <div class="col-sm-8">
                <input type="number" name="listsort" value="{{$info['listsort']}}" class="form-control" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">数据状态：</label>
            <div class="col-sm-8">
                <label class="radio-box">
                    <input type="radio" name="status" value="0" @if(!$info['status']) checked @endif /> 停用
                </label>
                <label class="radio-box">
                    <input type="radio" name="status" value="1" @if($info['status']) checked @endif /> 启用
                </label>
            </div>
        </div>
        @if($info['type'] == 'danger_problem' || $info['type'] == 'danger_cg')
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">扩展字段：</label>
                <div class="col-sm-8">
                    <label class="radio-box">
                        <input type="radio" name="is_true" value="0" @if($info['is_true'] == 0) checked @endif/> 否
                    </label>
                    <label class="radio-box">
                        <input type="radio" name="is_true" value="1" @if($info['is_true'] == 1) checked @endif/> 是
                    </label>
                </div>
            </div>
        @endif
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="note" class="form-control" placeholder="请输入备注">{{$info['note']}}</textarea>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
