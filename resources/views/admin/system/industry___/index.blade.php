@extends('admin.layout.layout')
@include('widget.asset.treetable')
@section('content')
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm('system:industry:add')
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        <a class="btn btn-info" id="expendinfobtn"><i class="fa fa-exchange"></i> 展开/折叠</a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-tree-table"></table>
    </div>
@stop
@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "经营类型",
                columns: [
                    {field: 'selectItem', radio: true},
                    {title: '经营类型名称', field: 'name'},
                    {title: '排序', field: 'listsort',},
                    {
                        title: '操作',
                        formatter: function (value, row, index) {
                            var actions = [];
                            @hasPerm('system:industry:extend')
                            if (row.parent_id){
                                actions.push('<a class="btn btn-primary btn-xs" href="javascript:;" onclick="industryExtend(\'' + row.id + '\')">扩展字段</a> ');
                            }
                            @endhasPerm
                            @hasPerm('system:industry:edit')
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                            @endhasPerm

                            @hasPerm('system:industry:drop')
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }]
            };
            $.treeTable.init(options);
        });
        function industryExtend(id) {
            $.modal.openFull('经营类型扩展字段','{{route('system.industry.extend')}}?id='+id);
        }
    </script>
@stop
