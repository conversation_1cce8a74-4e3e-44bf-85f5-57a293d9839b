@extends('admin.layout.form')

@section('content')
<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="parent_id" id="treeId" value="0">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">上级类别：</label>
        <div class="col-sm-8">
            <div class="input-group">
                <input type="text" id="treeName" value="上级类别" class="form-control" placeholder="请选择上级类别" readonly autocomplete="off"/>
                <span class="input-group-addon"><i class="fa fa-search"></i></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">类别名称：</label>
        <div class="col-sm-8">
            <input type="text" name="name" value="" class="form-control" placeholder="请输入类别名称" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group mb0">
        <label class="col-sm-3 control-label is-required">显示顺序：</label>
        <div class="col-sm-3 mb15">
            <input type="number" name="listsort" value="255" class="form-control" placeholder="请输入显示顺序" required autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            //选择菜单树
            $("#treeName").click(function () {
                var treeId = $("#treeId").val();
                var url = urlcreate("{{ route('system.industry.tree') }}","id=" + treeId + "&root=1") ;
                var options = {
                    title: '上级分类选择',
                    width: "380",
                    url: url,
                    callBack: doSubmit
                };
                $.modal.openOptions(options);
            });
        });
        function doSubmit(index, layero){
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
        }
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
