@extends('admin.layout.layout')
@section('content')
<div class="col-sm-12 search-collapse">
    <form id="role-form">
        <div class="select-list">
            <ul>
                <li><input type="text" name="keywords" style="width: 300px" value="" placeholder="搜索姓名、电话、部门"></li>
                <li>
                    <a class="btn btn-success btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "工作人员库",
                sortName:'id',
                rememberSelected:true,//翻页时保留所选行
                maintainSelected:true,//翻页时保留所选行
                columns: [
                    {checkbox: true, visible:false},
                    {field: '',title:'序号',formatter:function (value, row, index){
                            return $.table.serialNumber(index);
                        }},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {field: 'qyuser_id', title: '原始ID', align: 'left', visible: false},
                    {field: 'username', title: '姓名', align: 'left',formatter:function (value, row, index){
                            if (row.join_depot){
                                return "<span style='color: #ff0000'>"+value+"</span>"
                            }else{
                                return value;
                            }
                        }},
                    {field: 'depNames', title: '所属部门',align: 'left'},
                    {field: 'mobile', title: '联系电话', align: 'left'},
                    {
                        title: '状态',
                        field: 'status',
                        align: 'left',
                        visible: false,
                        formatter: function (value, row, index) {
                            return $.view.statusUserShow(row,false);
                        }
                    },
                    {
                        title: '逻辑删除',
                        field: 'delFlag',
                        align: 'left',
                        visible: false,
                        formatter: function (value, row, index) {
                            return $.view.statusUserShow(row,false,['有效数据','无效数据'],'delFlag');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

