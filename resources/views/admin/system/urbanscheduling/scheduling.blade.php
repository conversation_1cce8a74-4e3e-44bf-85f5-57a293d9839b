@extends('admin.layout.layout')

@section('content')
    <style>
        th {
            text-align: center;
        }
        td {
            text-align: center;
        }
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        .bootstrap-table thead th {
            width: calc((100%)/8);
        }
        .date-status-1 {
            background-color: #dcd9d9 !important;
        }
        .date-status-2 {
            background-color: #dcd9d9!important;
        }
        .date-status-4 {
            /*background-color: #f89c9c !important;*/
        }
        @if ($info->scheduling_type == 2)
        .date-status-3 {
            cursor: pointer;
        }
        @endif
    </style>
{{--    <div class="col-sm-12 alert alert-warning alert-dismissable" style="margin: 10px 0 0 0">--}}
{{--        <p><span style="width: 100px;height: 30px " class="date-status-4">为周末或节假日</span> </p>--}}
{{--    </div>--}}
        <div class="col-sm-12 select-table table-striped table-hover">
            <div class="bootstrap-table bootstrap3">
                <div class="fixed-table-toolbar">
                    <h3>排班-{{$info['name']}}</h3>
                    <div class="bs-bars pull-left">
                        <div class="btn-group-sm" id="toolbar" role="group">
                            @if ($info->scheduling_type == 2)
                                @hasPerm("system:urbanscheduling:schedulingimport")
                                <a href="javascript:void(0)" class="btn btn-warning" onclick="importExcel()">
                                    <i class="fa fa-upload"></i> 导入
                                </a>
                                @endhasPerm
                            @endif
                            @hasPerm("system:urbanscheduling:schedulingexport")
                            <a href="javascript:void(0)" class="btn btn-warning" onclick="exportExcel()">
                                <i class="fa fa-download"></i> 导出
                            </a>
                            @endhasPerm
                                <div class="pull-right">
                                    <form id="role-form">
                                        <div class="select-list">
                                            <input type="hidden" name="group_id" value="{{$info->id}}">
                                            <ul>
                                                <li>时间：<input type="text" name="date_range" value="{{$week[0]}}~{{$week[1]}}" id="date_range" style="width: 190px;" placeholder="请选择" readonly></li>
                                                <li>
                                                    <a data-value="{{$upWeek[0]}}~{{$upWeek[1]}}" href="javascript:;" class="btn btn-warning week_up_down"><i style="font-size: 18px" class="fa fa-angle-left"></i>上周</a>
                                                    <a data-value="{{$downWeek[0]}}~{{$downWeek[1]}}" href="javascript:;" class="btn btn-warning week_up_down">下周 <i class="fa fa-angle-right" style="font-size: 18px"></i></a>
                                                </li>
                                            </ul>
                                        </div>
                                    </form>
                                </div>
                        </div>

                    </div>

                <div class="fixed-table-container" style="padding-bottom: 0px;">
                    <div class="fixed-table-header" style="display: none;"><table></table></div>
                    <div class="fixed-table-body">
                        <div class="fixed-table-loading table table-hover" style="top: 35px;">
                          <span class="loading-wrap">
                          <span class="loading-text" style="font-size: 13px;">正在努力地加载数据中，请稍候</span>
                          <span class="animation-wrap"><span class="animation-dot"></span></span>
                          </span>
                        </div>
                        <table id="bootstrap-table" class="table select-table table-striped">
                            <thead class="">
                            <tr style="width:">
                                <th>
                                    <div class="th-inner ">姓名</div>
                                    <div class="fht-cell"></div>
                                </th>
                                @foreach($allDays as $date)
                                    <th class="">
                                        <div class="th-inner ">{{$date}}{{$allDaysStatus[$date]['week']}} @if (!in_array($date, $work_days)) (非工作日) @endif</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                @endforeach
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($info->users as $user)
                                <tr>
                                    <td>{{$user->username}} <br/> （{{$user->identity_text}}）</td>
                                    @foreach($allDays as $date)
                                        <td class="date-status-{{$allDaysStatus[$date]['status']}}">
                                            <div @if($allDaysStatus[$date]['status'] == 3 && $info->scheduling_type == 2)
                                                 data-pk="{{$user->id}}"
                                                 @if (isset($user->scheduling[$date]))
                                                     @if($user->scheduling[$date]['type'] == 2)
                                                         @if (isset($customTimes[$user->scheduling[$date]['value']]))
                                                            data-value="{{$user->scheduling[$date]['value']}}"
                                                         @else
                                                             data-value=""
                                                         @endif
                                                     @elseif($user->scheduling[$date]['type'] == 3)
                                                        data-value="{{$user->scheduling[$date]['value']}}"
                                                     @else
                                                        data-value=""
                                                     @endif
                                                 @else
                                                    data-value=""
                                                 @endif
                                                 data-date="{{$date}}"
                                                 class="editable"
                                                @endif >

                                                @if (empty($user->scheduling[$date]))
                                                        未排班
                                                @elseif($user->scheduling[$date]['type'] == 1)
                                                    {{$user->scheduling[$date]['value']}}
                                                @elseif($user->scheduling[$date]['type'] == 2)
                                                    @if (isset($customTimes[$user->scheduling[$date]['value']]))
                                                        {{$customTimes[$user->scheduling[$date]['value']]}}
                                                    @else
                                                        {{$user->scheduling[$date]['name']}}
                                                    @endif
                                                @elseif($user->scheduling[$date]['type'] == 3)
                                                    {{$user->scheduling[$date]['name']}}
                                                @endif
                                            <div>
                                        </td>

                                    @endforeach
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
            <div class="clearfix"></div>
        </div>
@stop

@section('script')
    <script src="{{asset('static/plugins/bootstrap-table/extensions/editable/bootstrap-editable.min.js')}}"></script>
    <script>
        $(function () {
            $('.week_up_down').click(function () {
                $('#date_range').val($(this).data('value'));
                submitHandler();
            });
            $('.editable').editable({
                type:'select',
                source: [
                    {value: '', text: '未排班'}
                    @foreach($customTimes as $id=>$value)
                    ,{value: {{$id}}, text: '{{$value}}'}
                    @endforeach
                    @foreach($leave as $item)
                    ,{value: 'leave_{{$item['leave_type']}}', text: '{{$item['name']}}'}
                    @endforeach
                ],
                emptytext: "未排班",
                mode: "inline",
                showbuttons:false,
                url: '{{route('system.urbanscheduling.scheduling')}}',
                ajaxOptions: {
                    type: 'post',
                    dataType: 'json'
                },
                params: function(params) {
                    //originally params contain pk, name and value
                    params.date = $(this).data('date');
                    params.group_id = '{{$info->id}}';
                    return params;
                },
                success: function(result, value) {
                    if (result.code != web_status.SUCCESS) {
                        return result.msg;
                    }
                },
            });
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#date_range',
                    // theme: '#da7606',
                    type: 'date',
                    max: '2099-12-31',
                    format: "yyyy-MM-dd~yyyy-MM-dd",
                    weekStart: 1,
                    isPreview: false,
                    btns: ['now', 'confirm'],
                    done: function (value, date, endDate) {
                        if (value != "" && value.length > 0) {
                            let today = new Date(value.substring(0, 10));
                            let weekday = today.getDay();
                            let monday;
                            let sunday;
                            if (weekday == 0) {
                                monday = new Date(1000 * 60 * 60 * 24 * (weekday - 6) + today.getTime());
                            } else {
                                monday = new Date(1000 * 60 * 60 * 24 * (1 - weekday) + today.getTime());
                            }
                            if (weekday == 0) {
                                sunday = today;
                            } else {
                                sunday = new Date(1000 * 60 * 60 * 24 * (7 - weekday) + today.getTime());
                            }
                            let month = monday.getMonth() + 1;
                            if (month < 10) {
                                month = "0" + month;
                            }
                            let day1 = monday.getDate();
                            if (day1 < 10) {
                                day1 = "0" + day1;
                            }
                            let start = "" + monday.getFullYear() + "-" + month + "-" + day1;
                            let month2 = sunday.getMonth() + 1;
                            if (month2 < 10) {
                                month2 = "0" + month2;
                            }
                            let day2 = sunday.getDate();
                            if (day2 < 10) {
                                day2 = "0" + day2;
                            }
                            let end = "" + sunday.getFullYear() + "-" + month2 + "-" + day2;
                            $('#date_range').val(start + "~" + end);
                            submitHandler();
                        } else {
                            $('#date_range').val('');
                        }
                    }
                });
            });
        });
        function submitHandler() {
            $('#role-form').submit();
        }
        function exportExcel() {
            if ($.validate.form()) {
                $.modal.loading('正在处理中，请稍后...');
                $.modal.disable();
                var url = '{{route('system.urbanscheduling.schedulingexport')}}';
                var xhr = new XMLHttpRequest();
                xhr.open('POST', url, true);    // 也可以使用POST方式，根据接口
                xhr.responseType = "blob";    // 返回类型blob
                xhr.setRequestHeader("X-CSRF-TOKEN", $('meta[name="csrf-token"]').attr('content'));
                xhr.setRequestHeader('content-type', 'application/json');
                xhr.onload = function () {
                    // 请求完成
                    if (this.status === 200) {
                        // 返回200
                        var response = this.response;
                        let contentType = xhr.getResponseHeader('Content-Type');
                        contentType = contentType != '' ? contentType : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                        let blob = new Blob([response], {type: contentType});
                        const reader = new FileReader();
                        if (contentType === 'application/json' || contentType === 'application/javascript') {
                            reader.readAsText(blob, 'utf-8');
                            reader.onload = (e) => {
                                const resp = JSON.parse(reader.result);
                                if (resp.hasOwnProperty('code') && resp.code != web_status.SUCCESS) {
                                    $.modal.alertError(resp.msg);
                                } else {
                                    $.modal.alertError('导出失败');
                                }
                                $.modal.enable();
                            };
                        } else {
                            const disposition = xhr.getResponseHeader('Content-Disposition');
                            let fileName;
                            let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
                            let matches = filenameRegex.exec(disposition)
                            if (matches != null && matches[1]) {
                                fileName = matches[1].replace(/['"]/g, '')
                            }
                            // 通过 URLEncoder.encode(pFileName, StandardCharsets.UTF_8.name()) 加密编码的, 使用decodeURI(fileName) 解密
                            fileName = decodeURI(fileName)
                            var urlCreator = window.URL || window.webkitURL;
                            var url = urlCreator.createObjectURL(blob); //这个函数的返回值是一个字符串，指向一块内存的地址。
                            //以下代码保存我的excel导出文件
                            var link = document.createElement('a'); //创建事件对象
                            link.setAttribute('href', url);
                            link.setAttribute("download", fileName);
                            var event = document.createEvent("MouseEvents"); //初始化事件对象
                            event.initMouseEvent("click", true, true, document.defaultView, 0, 0, 0, 0, 0, false, false, false, false, 0, null); //触发事件
                            link.dispatchEvent(event);
                            $.modal.close();
                            $.modal.enable();
                        }
                    } else {
                        $.modal.alertError('导出失败');
                        $.modal.close();
                        $.modal.enable();
                    }
                    $.modal.closeLoading();
                };
                // 发送ajax请求
                xhr.send(JSON.stringify($.common.formToJSON('role-form')));
            }
        }
        
        function importExcel() {
            $.modal.layerinit(function (layer) {
                layer.open({
                    type: 1,
                    area: ['400px', '260px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: '导入排班数据',
                    content: $('#importTpl').html(),
                    btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    btn1: function (index, layero) {
                        var file = layero.find('#file').val();
                        if (file == '' && !$.common.endWith(file, '.xlsx', 'i')) {
                            $.modal.msgWarning("请选择后缀为 xlsx”的文件。");
                            return false;
                        }
                        var index = layer.load(2, {shade: false});
                        $.modal.disable();
                        var formData = new FormData(layero.find('form')[0]);
                        $.ajax({
                            url: '{{route('system.urbanscheduling.schedulingimport')}}',
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            type: 'POST',
                            success: function (result) {
                                if (result.code == web_status.SUCCESS) {
                                    $.modal.closeAll();
                                    $.modal.alertSuccess(result.msg);
                                    submitHandler();
                                } else if (result.code == web_status.WARNING) {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertWarning(result.msg)
                                } else {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertError(result.msg);
                                }
                            },
                            complete: function () {
                                layero.find("#file").val("")
                            }
                        });
                    }
                });
            });
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                {{--                <input type="file" id="file" name="file" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>--}}
                <input type="file" id="file" name="file"
                       accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <input type="hidden" name="group_id" value="{{$input['group_id']}}">
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <a download href="{{asset('template/城管排班导入模板.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop

