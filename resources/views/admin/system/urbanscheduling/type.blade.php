@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}


@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">排班方式：</label>
        <div class="col-sm-9">
            @foreach(\App\Models\System\UrbanGroup::$schedulingType as $type=>$name)
                <label class="radio-box">
                    <input type="radio" @if($type == $info['scheduling_type']) checked @endif  value="{{$type}}" required name="scheduling_type">{{$name}}
                </label>
            @endforeach
        </div>
    </div>
    <div class="form-group" id="gdsxb" @if($info['scheduling_type'] == 2) style="display: none" @endif >
        <label class="col-sm-3 control-label is-required">上下班打卡时间：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control sxb-time" readonly name="regular_time" id="regular_time" value="{{$info['scheduling_type']==1 && $info['regular_time'] ? ($info['regular_time'][0] ?? '') .' - '. ($info['regular_time'][1] ?? '') : ''}}" placeholder="请选择" required style="width: 150px;">
        </div>
    </div>
    <div class="form-group" id="zdysxb" @if($info['scheduling_type'] == 1) style="display: none" @endif >
        <label class="col-sm-3 control-label is-required">排班时间：</label>
        <div class="col-sm-8" id="inputs_box">
            <div class="uploadimg_link inputs_box_add" style="margin-bottom: 5px;max-width:100%;">
                <a class="btn btn-sm btn-warning" onclick="add()">
                    添加
                </a>
            </div>
            @foreach($info['scheduling'] as $key=>$custom_time)
                <div class="uploadimg_link inputs_box_del" style="margin-bottom: 5px;max-width:100%;">
                    <input type="hidden" name="custom_time[{{$key}}][id]" value="{{$custom_time['id']}}" >
                    <input type="text" class="form-control sxb-time" placeholder="请选择" readonly name="custom_time[{{$key}}][time]" value="{{$custom_time['start'].' - '.$custom_time['end']}}"  style="width: 150px;display: inline" required autocomplete="off"/>
                    <a href="javascript:;" class="btn btn-sm btn-warning" onclick="del(this)">
                       删除
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        let custom_time_index = {{count($info['scheduling'])}};
        $(function () {
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '.sxb-time'
                    ,range: true
                    ,type: 'time'
                    ,format: 'HH:mm'
                });
            });
            $('input[name=scheduling_type]').on('ifChecked', function () {
                if (this.value === '1') {
                    $('#gdsxb').show();
                    $('#zdysxb').hide();
                } else {
                    $('#gdsxb').hide();
                    $('#zdysxb').show();
                }
            })
        });
        $("#form-edit").validate();

        {{--$("#XX").val(["{{$info['xx']}}"]).trigger("change");--}}

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save('{{route('system.urbanscheduling.type')}}', $('#form-edit').serialize());
            }
        }
        function add() {
            var html = '<div class="uploadimg_link inputs_box_del" style="margin-bottom: 5px;max-width:100%;">'+
                '<input type="text" class="form-control sxb-time" placeholder="请选择" readonly name="custom_time['+custom_time_index+'][time]" style="width: 150px;display: inline" required autocomplete="off"/> '+
                '<a href="javascript:;" class="btn btn-sm btn-warning" onclick="del(this)">删除</a>'+
                '</div>';
            custom_time_index++;
            $("#inputs_box").append(html);
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '.sxb-time'
                    ,range: true
                    ,type: 'time'
                    ,format: 'HH:mm'
                });
            });
        }
        function del(obj) {
            $(obj).parent().remove();
        }
    </script>
@stop
