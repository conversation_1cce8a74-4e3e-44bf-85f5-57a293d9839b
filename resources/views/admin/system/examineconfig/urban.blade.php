@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.formSelects")
@section('css_common')
    <style>
        .item-header {
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 15px;
            position: relative;
            padding-left: 15px;
        }
        .item-header:before{
            content: "";
            background-color: #2256ff;
            height: 17px;
            width: 7px;
            position: absolute;
            left: 0;
            top: -15px;
            bottom: 0;
            margin: auto;
            border-radius: 5px;
        }
        .form-group .col-sm-1{
            padding-right:0;
            width: 120px;
        }
        .form-group .col-sm-3{
            padding-left:0;

        }
        .patrol-item {
            border-bottom: 1px solid #e5e6e7;
            padding-top: 15px;
        }
        .target_type_row,.watch_num,.watch_field {
            display: none;
        }
        .group-setting {
            max-width: 500px;
            display: none;
            padding-top: 10px;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-config-site-site">
        <div class="item-header">轨迹</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">停留时长：</label>
            <div class="col-sm-3">
                <div class="input-group">
                    <input type="number" name="urban_stop_minute" min="1" step="1" value="{{$config['urban_stop_minute'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
                    <div class="input-group-addon">分钟</div>
                </div>
            </div>
        </div>
        <div class="item-header">城管巡查</div>
        <div id="patrol">
            @foreach($patrolRelevance as $key=>$item)
                <div class="patrol-item patrol-item{{$key+1}}" data-index="{{$key+1}}">
                    <div class="form-group template-row">
                        <label class="col-sm-1 control-label">巡查表单：</label>
                        <div class="col-sm-3">
                            <div style="display: inline-block;width: 90%">
                                <select name="urban_patrol_form[{{$key+1}}][template_id]" class="patrol_template_id" xm-select="urban_patrol_form{{$key+1}}" xm-select-search xm-select-radio>
                                </select>
                            </div>
                            <a style="float: right;padding-top: 7px;" onclick="removePatrolItem(this)" href="javascript:;"><i class="fa fa-remove"></i></a>
                        </div>
                    </div>
                    <div class="form-group target_row">
                        <label class="col-sm-1 control-label">是否设置目标：</label>
                        <div class="col-sm-10">
                            <div>
                                <label class="radio-box">
                                    <input type="radio" class="target" @if($item['target'] == 1) checked @endif value="1" required name="urban_patrol_form[{{$key+1}}][target]">是
                                </label>
                                <label class="radio-box">
                                    <input type="radio" class="target" @if($item['target'] == 0) checked @endif value="0" required name="urban_patrol_form[{{$key+1}}][target]">否
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group target_type_row" @if($item['target'] == 1) style="display: inherit" @endif>
                        <label class="col-sm-1 control-label">目标类型：</label>
                        <div class="col-sm-10">
                            <label class="radio-box">
                                <input type="radio" value="1" @if($item['target_type'] == 1) checked @endif class="target_type" required name="urban_patrol_form[{{$key+1}}][target_type]">每月巡查次数
                            </label>
                            <label class="radio-box">
                                <input type="radio" value="2" @if($item['target_type'] == 2) checked @endif class="target_type" required name="urban_patrol_form[{{$key+1}}][target_type]">每天巡查次数
                            </label>
                            <label class="radio-box">
                                <input type="radio" value="3" @if($item['target_type'] == 3) checked @endif class="target_type" required name="urban_patrol_form[{{$key+1}}][target_type]">目标字段
                            </label>
                            <div class="watch_num" @if(in_array($item['target_type'], [1, 2])) style="display: inline-block" @endif >
                                <input type="text" name="urban_patrol_form[{{$key+1}}][watch_num]" value="{{$item['watch_num_filed']}}" class="form-control"  @if($item['target_type'] == 1) placeholder="请输入每月巡查次数" @elseif($item['target_type'] == 2) placeholder="请输入每天巡查次数" @endif>
                            </div>
                            <div class="watch_field" @if($item['target_type'] == 3) style="display: inline-block" @endif>
                                <div style="display: inline-block">
                                    <select name="urban_patrol_form[{{$key+1}}][watch_field]" xm-select="urban_patrol_watch_field{{$key+1}}" xm-select-search xm-select-radio>
                                    </select>
                                </div>
                                <div style="display: inline-block">
                                    <label class="radio-box">
                                        <input type="checkbox" value="1" @if($item['watch_field_group'] == 1) checked @endif class="watch_field_group" required name="urban_patrol_form[{{$key+1}}][watch_field_group]">分组配置
                                    </label>
                                </div>

                            </div>
                            <div class="group-setting" @if($item['watch_field_group'] == 1) style="display: inherit" @endif>
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th>分组名称</th>
                                        <th>选择项</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($urbanList as $urban)
                                        <tr>
                                            <td>{{$urban['name']}}</td>
                                            <td>
                                                <select class="watch_group" name="urban_patrol_form[{{$key+1}}][watch_group][{{$urban['id']}}]" xm-select="urban_patrol_watch_group_{{$urban['id']}}_{{$key+1}}"></select>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="form-group">
            <label class="col-sm-1 control-label">
                <a href="javascript:;" class="btn btn-success btn-mid" onclick="addPatrol()" >添加</a>
            </label>
        </div>
        <div class="item-header">城管上报</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">上报表单：</label>
            <div class="col-sm-3">
                <select name="urban_report_form"  xm-select="urban_report_form" xm-select-search>
                </select>
            </div>
        </div>
        <div class="form-group" style="padding-left:15px;">
            <a href="javascript:;" class="btn btn-success btn-mid b5submit_btn" data-target="form-config-site-site" data-title="确定提交配置信息">保存</a>
        </div>
    </form>
@stop
@section('script_before')
    <script>
        var patrol_item_index = {{count($patrolRelevance) + 1}};
        var formSelects = layui.formSelects;
        var patrolForm = @json($patrolForm);
        var watchGroup = [];
        $(function () {
            init();
            $(document).on('ifChecked', '.target', function () {
                var target = $(this).val();
                var target_type_row = $(this).parents('.patrol-item').find('.target_type_row');
                if (target == 1) {
                    target_type_row.show();
                } else {
                    target_type_row.hide();
                }
            });
            $(document).on('ifChecked', '.target_type', function () {
                var target_type = $(this).val();
                var watch_num = $(this).parents('.target_type_row').find('.watch_num');
                var watch_field = $(this).parents('.target_type_row').find('.watch_field');
                var group_setting = $(this).parents('.target_type_row').find('.group-setting');
                if (target_type == 1) {
                    watch_num.find('input').attr('placeholder', '请输入每月巡查次数');
                    watch_num.css('display', 'inline-block')
                    watch_field.hide();
                    group_setting.hide();
                } else if (target_type == 2){
                    watch_num.find('input').attr('placeholder', '请输入每天巡查次数');
                    watch_num.css('display', 'inline-block')
                    watch_field.hide();
                    group_setting.hide();
                } else {
                    watch_num.hide();
                    watch_field.css('display', 'inline-block');
                    if (watch_field.find('.watch_field_group').is(':checked')) {
                        group_setting.show();
                    } else {
                        group_setting.hide();
                    }
                }
            });
            $(document).on('ifChecked', '.watch_field_group', function () {
                $(this).parents('.target_type_row').find('.group-setting').show();
            });
            $(document).on('ifUnchecked', '.watch_field_group', function () {
                $(this).parents('.target_type_row').find('.group-setting').hide();
            });
        });
        function addPatrol() {
            var html = $('#patrol-item-template').html();
            html = html.replace(/_patrol_item_index_/g, patrol_item_index)
            $('#patrol').append(html);
            $(".patrol-item"+patrol_item_index+" .check-box:not(.noicheck),.radio-box:not(.noicheck)").each(function () {
                $(this).iCheck({
                    checkboxClass: 'icheckbox-blue',
                    radioClass: 'iradio-blue',
                })
            });
            formSelects.render('urban_patrol_form'+patrol_item_index);
            formSelects.data('urban_patrol_form'+patrol_item_index, 'local', {arr:patrolForm});
            formSelects.on('urban_patrol_form'+patrol_item_index, patrolFormChange);
            formSelects.render('urban_patrol_watch_field'+patrol_item_index);
            $.each($('.patrol-item'+patrol_item_index+' .watch_group'), function (index, group) {
                formSelects.render($(group).attr('xm-select'));
            });
            patrol_item_index++;
        }
        function getPatrolForm() {
            let i;
            let select = [];
            $.each($('.patrol_template_id'), function (index, template) {
                $.each(formSelects.value($(template).attr('xm-select'), 'val'), function (j, item){
                    select.push(parseInt(item));
                });
            });
            for (i in patrolForm) {
                patrolForm[i].disabled = select.indexOf(parseInt(patrolForm[i].value)) >= 0;
            }
        }
        function getWatchGroup(index) {
            let i;
            let select = [];
            $.each($('.patrol-item'+index+' .watch_group'), function (i, group) {
                $.each(formSelects.value($(group).attr('xm-select'), 'val'), function (j, item){
                    select.push(parseInt(item));
                });
            });
            for (i in watchGroup[index]) {
                watchGroup[index][i].disabled = select.indexOf(parseInt(watchGroup[index][i].value)) >= 0;
            }
        }
        function resetPatrolForm() {
            getPatrolForm();
            $.each($('.patrol_template_id'), function (index, template) {
                const thisId = $(template).attr('xm-select');
                let tmp = JSON.parse(JSON.stringify(patrolForm));
                $.each(formSelects.value(thisId, 'val'), function (j, item){
                    for (var k in tmp) {
                        if (tmp[k].value == item) {
                            tmp[k].disabled = false;
                            tmp[k].selected = true;
                            break;
                        }
                    }
                });
                formSelects.data(thisId, 'local', {arr:tmp});
            });
        }
        function patrolFormChange(id, vals, val, isAdd, isDisabled) {
            getPatrolForm();
            if (isAdd) {
                $.each(formSelects.value(id, 'val'), function (j, item){
                    for (var k in patrolForm) {
                        if (patrolForm[k].value == item) {
                            patrolForm[k].disabled = false;
                            break;
                        }
                    }
                });
                $.each(patrolForm, function (j, item){
                    if (val.value == item.value) {
                        patrolForm[j].disabled = true;
                    }
                });
            } else {
                $.each(patrolForm, function (j, item){
                    if (val.value == item.value) {
                        patrolForm[j].disabled = false;
                    }
                });
            }
            $.each($('.patrol_template_id'), function (index, template) {
                var thisId = $(template).attr('xm-select');
                if (thisId !== id) {
                    let tmp = JSON.parse(JSON.stringify(patrolForm));
                    $.each(formSelects.value(thisId, 'val'), function (j, item){
                        for (var k in tmp) {
                            if (tmp[k].value == item) {
                                tmp[k].disabled = false;
                                tmp[k].selected = true;
                                break;
                            }
                        }
                    });
                    formSelects.data(thisId, 'local', {arr:tmp});
                }
            });
            var index = id.replace(/urban_patrol_form/g, '');
            var selectId = 'urban_patrol_watch_field'+index;
            formSelects.render(selectId);
            var patrol_id = val.value;
            if (isAdd) {
                formSelects.data(selectId, 'server', {url: "{{route('system.patrol.ajaxmultiple')}}?id="+patrol_id});
                formSelects.on(selectId, watchFieldChange);
            } else {
                formSelects.data(selectId, 'local',{arr:{}});
            }
            $.each($('.patrol-item'+index+' .watch_group'), function (i, group) {
                var groupId = $(group).attr('xm-select');
                formSelects.render(groupId);
                formSelects.data(groupId, 'local',{arr:{}});
            });
            //id:           点击select的id

            //vals:         当前select已选中的值

            //val:          当前select点击的值

            //isAdd:        当前操作选中or取消

            //isDisabled:   当前选项是否是disabled
            // console.log(id, vals, val, isAdd, isDisabled);
            return true;
        }
        function watchFieldChange(id, vals, val, isAdd, isDisabled) {
            var index = id.replace(/urban_patrol_watch_field/g, '');
            var patrol_id = formSelects.value('urban_patrol_form'+index, 'valStr');
            if (isAdd) {
                $.ajax({
                    url: "{{route('system.patrol.ajaxmultipleindex')}}?id="+patrol_id+'&index='+val.value,
                    type: 'get',
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            watchGroup[index] = res.data;
                            $.each($('.patrol-item'+index+' .watch_group'), function (index, group) {
                                var groupId = $(group).attr('xm-select');
                                formSelects.render(groupId);
                                formSelects.data(groupId, 'local',{arr:res.data});
                                formSelects.on(groupId, watchGroupChange);
                            });
                        }
                    }
                })
            } else {
                $.each($('.patrol-item'+index+' .watch_group'), function (index, group) {
                    var groupId = $(group).attr('xm-select');
                    formSelects.render(groupId);
                    formSelects.data(groupId, 'local',{arr:{}});
                });
            }
        }
        function watchGroupChange(id, vals, val, isAdd, isDisabled) {
            var arr = id.split('_');
            var index = arr[arr.length - 1];
            getWatchGroup(index);
            console.log(index,isAdd, watchGroup[index])
            $.each(watchGroup[index], function (j, item){
                if (val.value == item.value) {
                    watchGroup[index][j].disabled = isAdd;
                }
            });
            $.each($('.patrol-item'+index+' .watch_group'), function (i, group) {
                var thisId = $(group).attr('xm-select');
                if (thisId !== id) {
                    let tmp = JSON.parse(JSON.stringify(watchGroup[index]));
                    $.each(formSelects.value(thisId, 'val'), function (j, item){
                        for (var k in tmp) {
                            if (tmp[k].value == item) {
                                tmp[k].disabled = false;
                                tmp[k].selected = true;
                                break;
                            }
                        }
                    });
                    formSelects.data(thisId, 'local', {arr:tmp});
                }
            });
            return true;
        }
        function removePatrolItem(obj) {
            $(obj).parents('.patrol-item').remove();
            resetPatrolForm();
        }
        function init() {
            formSelects.render('urban_report_form');
            formSelects.data('urban_report_form', 'local', {arr:@json($reportForm)});
            @foreach($patrolRelevance as $key => $item)
            formSelects.render('urban_patrol_form{{$key+1}}');
            formSelects.data('urban_patrol_form{{$key+1}}', 'local', {arr:@json($item['patrol_list'])});
            formSelects.on('urban_patrol_form{{$key+1}}', patrolFormChange);
            formSelects.render('urban_patrol_watch_field{{$key+1}}');
            formSelects.on('urban_patrol_watch_field{{$key+1}}', watchFieldChange);
            formSelects.data('urban_patrol_watch_field{{$key+1}}', 'local', {arr:@json($item['columns'])});
            $.each($('.patrol-item{{$key+1}} .watch_group'), function (index, group) {
                formSelects.render($(group).attr('xm-select'));
            });
            @if($item['watch_field_group'] == 1)
                @foreach($item['groups'] as $urbanId=>$json)
                formSelects.data('urban_patrol_watch_group_{{$urbanId}}_{{$key+1}}','local', {arr:@json($json)});
                formSelects.on('urban_patrol_watch_group_{{$urbanId}}_{{$key+1}}', watchGroupChange);
                @endforeach
            @endif
            @endforeach
        }
    </script>
    <script id="patrol-item-template" type="text/template">
        <div class="patrol-item patrol-item_patrol_item_index_" data-index="_patrol_item_index_">
            <div class="form-group template-row">
                <label class="col-sm-1 control-label">巡查表单：</label>
                <div class="col-sm-3">
                    <div style="display: inline-block;width: 90%">
                        <select name="urban_patrol_form[_patrol_item_index_][template_id]" class="patrol_template_id" xm-select="urban_patrol_form_patrol_item_index_" xm-select-search xm-select-radio>
                        </select>
                    </div>
                    <a style="float: right;padding-top: 7px;" onclick="removePatrolItem(this)" href="javascript:;"><i class="fa fa-remove"></i></a>
                </div>
            </div>
            <div class="form-group target_row">
                <label class="col-sm-1 control-label">是否设置目标：</label>
                <div class="col-sm-10">
                    <div>
                        <label class="radio-box">
                            <input type="radio" class="target" value="1" required name="urban_patrol_form[_patrol_item_index_][target]">是
                        </label>
                        <label class="radio-box">
                            <input type="radio" class="target" value="0" required name="urban_patrol_form[_patrol_item_index_][target]">否
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group target_type_row">
                <label class="col-sm-1 control-label">目标类型：</label>
                <div class="col-sm-10">
                    <label class="radio-box">
                        <input type="radio" value="1" class="target_type" required name="urban_patrol_form[_patrol_item_index_][target_type]">每月巡查次数
                    </label>
                    <label class="radio-box">
                        <input type="radio" value="2" class="target_type" required name="urban_patrol_form[_patrol_item_index_][target_type]">每天巡查次数
                    </label>
                    <label class="radio-box">
                        <input type="radio" value="3" class="target_type" required name="urban_patrol_form[_patrol_item_index_][target_type]">目标字段
                    </label>
                    <div class="watch_num" >
                        <input type="text" name="urban_patrol_form[_patrol_item_index_][watch_num]" class="form-control">
                    </div>
                    <div class="watch_field">
                        <div style="display: inline-block">
                            <select name="urban_patrol_form[_patrol_item_index_][watch_field]" xm-select="urban_patrol_watch_field_patrol_item_index_" xm-select-search xm-select-radio>
                            </select>
                        </div>
                        <div style="display: inline-block">
                            <label class="radio-box">
                                <input type="checkbox" value="1" class="watch_field_group" required name="urban_patrol_form[_patrol_item_index_][watch_field_group]">分组配置
                            </label>
                        </div>

                    </div>
                    <div class="group-setting">
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>分组名称</th>
                                <th>选择项</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($urbanList as $urban)
                                <tr>
                                    <td>{{$urban['name']}}</td>
                                    <td>
                                        <select class="watch_group" name="urban_patrol_form[_patrol_item_index_][watch_group][{{$urban['id']}}]" xm-select="urban_patrol_watch_group_{{$urban['id']}}__patrol_item_index_"></select>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </script>
@append