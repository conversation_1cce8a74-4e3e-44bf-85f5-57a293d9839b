@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.formSelects")
@section('css_common')
    <style>
        .item-header {
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 15px;
            position: relative;
            padding-left: 15px;
        }
        .item-header:before{
            content: "";
            background-color: #2256ff;
            height: 17px;
            width: 7px;
            position: absolute;
            left: 0;
            top: -15px;
            bottom: 0;
            margin: auto;
            border-radius: 5px;
        }
        .form-group .col-sm-1{
            padding-right:0;
            width: auto;
        }
        .form-group .col-sm-3{
            padding-left:0;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-config-site-site">
        <div class="item-header">综治上报</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">上报表单：</label>
            <div class="col-sm-3">
                <select name="comprehensive_report_form" xm-select="comprehensive_report_form" xm-select-search>
                    @foreach($reportForm as $id=>$name)
                        <option value="{{$id}}" @if(in_array($id, $relevanceFormIds)) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group" style="padding-left:15px;">
            <a href="javascript:;" class="btn btn-success btn-mid b5submit_btn" data-target="form-config-site-site" data-title="确定提交配置信息">保存</a>
        </div>
    </form>
@stop