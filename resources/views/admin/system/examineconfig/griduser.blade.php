@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.formSelects")
@section('css_common')
    <style>
        .item-header {
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 15px;
            position: relative;
            padding-left: 15px;
        }
        .item-header:before{
            content: "";
            background-color: #2256ff;
            height: 17px;
            width: 7px;
            position: absolute;
            left: 0;
            top: -15px;
            bottom: 0;
            margin: auto;
            border-radius: 5px;
        }
        .form-group .col-sm-1{
            padding-right:0;
            width: 156px;
            /*text-align: justify;*/
            /*text-align-last: justify;*/
        }
        .form-group .col-sm-3{
            padding-left:0;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-config-site-site">
        <div class="item-header">网格巡防</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">巡防有效时长：</label>
            <div class="col-sm-3">
                <div class="input-group">
                    <input type="number" name="grid_user_watchman_minute" min="1" step="1" value="{{$config['grid_user_watchman_minute'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
                    <div class="input-group-addon">分钟</div>
                </div>
{{--                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 城管人员如果超过两天未进行巡查，该项配置的人可收到通知消息 </span>--}}
            </div>
            <label class="col-sm-2 control-label">工作日每天巡防次数：</label>
            <div class="col-sm-3">
                <input type="number" name="grid_user_workday_watchman_num" min="1" step="1" value="{{$config['grid_user_workday_watchman_num'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
            </div>
        </div>
        @hasPerm('system:griduserwatchscope:index')
        <div class="form-group">
            <label class="col-sm-1 control-label">巡防范围：</label>
            <div class="col-sm-8">
                <div class="form-control-static">默认为所属网格的范围，可自定义设置  <a href="javascript:;" onclick="function griduserscope() {
                    $.modal.openTab(
                        '自定义设置巡防范围',
                        '{{route('system.griduserwatchscope.index')}}',
                    );
                }
                griduserscope()" style="padding-left: 20px;">设置</a></div>
            </div>
        </div>
        @endhasPerm
        <div class="item-header">入户</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">每月入户总数：</label>
            <div class="col-sm-3">
                <input type="number" name="grid_user_month_in_house" min="1" step="1" value="{{$config['grid_user_month_in_house'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
            </div>
            <label class="col-sm-2 control-label">红色标签每月入户数：</label>
            <div class="col-sm-3">
                <input type="number" name="grid_user_month_red_in_house" min="1" step="1" value="{{$config['grid_user_month_red_in_house'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-1 control-label">黄色标签每月入户数：</label>
            <div class="col-sm-3">
                <input type="number" name="grid_user_month_yellow_in_house" min="1" step="1" value="{{$config['grid_user_month_yellow_in_house'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
            </div>
            <label class="col-sm-2 control-label">蓝色标签每月入户数：</label>
            <div class="col-sm-3">
                <input type="number" name="grid_user_month_blue_in_house" min="1" step="1" value="{{$config['grid_user_month_blue_in_house'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
            </div>
        </div>
        <div class="item-header">入商铺</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">每月入商铺总数：</label>
            <div class="col-sm-3">
                <input type="number" name="grid_user_month_in_shop" min="1" step="1" value="{{$config['grid_user_month_in_shop'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
            </div>
        </div>
        <div class="item-header">网格上报</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">上报表单：</label>
            <div class="col-sm-3">
                <select name="grid_user_report_form" xm-select="grid_user_report_form" xm-select-search>
                    @foreach($reportForm as $id=>$name)
                        <option value="{{$id}}" @if(in_array($id, $relevanceFormIds)) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="item-header">任务</div>
        <div class="form-group">
            <label class="col-sm-1 control-label">临期规则：</label>
            <div class="col-sm-3">
                <div style="display: flex">
                    <div class="form-control-static">距完成时间</div>
                    <input type="number"  name="grid_user_work_advent_day" min="1" step="1" value="{{$config['grid_user_work_advent_day'] ?? ''}}" placeholder="请输入" class="form-control" style="width: 100px;margin: 0 10px;" autocomplete="off">
                    <div class="form-control-static">天</div>
                </div>
            </div>
        </div>
        <div class="form-group" style="padding-left:15px;">
            <a href="javascript:;" class="btn btn-success btn-mid b5submit_btn" data-target="form-config-site-site" data-title="确定提交配置信息">保存</a>
        </div>
    </form>
@stop