@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.formSelects")
@section('css_common')
    <style>
        .item-header {
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 15px;
            position: relative;
            padding-left: 15px;
        }
        .item-header:before{
            content: "";
            background-color: #2256ff;
            height: 17px;
            width: 7px;
            position: absolute;
            left: 0;
            top: -15px;
            bottom: 0;
            margin: auto;
            border-radius: 5px;
        }
        .form-group .col-sm-1{
            padding-right:0;
            width: auto;
            /*text-align: justify;*/
            /*text-align-last: justify;*/
        }
        .form-group .col-sm-3{
            padding-left:0;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-config-site-site">
        <div class="form-group">
            <label class="col-sm-1 control-label">临期规则：</label>
            <div class="col-sm-3">
                <div style="display: flex">
                    <div class="form-control-static">距办结时间</div>
                    <input type="number"  name="event_advent_day" min="0" step="1" value="{{$config['event_advent_day'] ?? ''}}" placeholder="请输入" class="form-control" style="width: 100px;margin: 0 10px;" autocomplete="off">
                    <div class="form-control-static">小时</div>
                </div>
            </div>
        </div>
        <div class="form-group" style="padding-left:15px;">
            <a href="javascript:;" class="btn btn-success btn-mid b5submit_btn" data-target="form-config-site-site" data-title="确定提交配置信息">保存</a>
        </div>
    </form>
@stop