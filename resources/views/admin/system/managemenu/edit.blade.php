@extends('admin.layout.form')
@include('widget.asset.upload')
@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <input type="hidden" name="parent_id" id="treeId" value="{{$info['parent_id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">上级菜单：</label>
        <div class="col-sm-8">
            <div class="input-group">
                <input type="text" id="treeName" value="{{$info['parent_name']}}" class="form-control" placeholder="请选择上级菜单" readonly autocomplete="off"/>
                <span class="input-group-addon"><i class="fa fa-search"></i></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">菜单名称：</label>
        <div class="col-sm-8">
            <input type="text" name="name" value="{{$info['name']}}" class="form-control" placeholder="请输入菜单名称" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">菜单类型：</label>
        <div class="col-sm-3">
            @foreach($typeList as $type=>$name)
                <label class="radio-box">
                    <input type="radio" name="type" @if($info['type'] == $type) checked @endif value="{{$type}}" required/> {{$name}}
                </label>
            @endforeach
        </div>
        <label class="col-sm-2 control-label">权限标识：</label>
        <div class="col-sm-3 ">
            <input type="text" name="perms" value="{{$info['perms']}}" class="form-control" placeholder="请输入权限标识" autocomplete="off"/>
        </div>
    </div>
    <div id="menu-show" @if($info['type'] != 'C') style="display: none" @endif>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">菜单图标：</label>
            <div class="col-sm-8">
                <input type="hidden" name="icon" value="{{$info['icon']}}" id="img" required>
                <x-upload type="img" name="img_upload" :extend="['cat'=>'menu','tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M','data'=>$info['icon']]"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">跳转类型：</label>
            <div class="col-sm-8 ">
                <label class="radio-box">
                    <input type="radio" name="path_type" @if($info['path_type'] == 1) checked @endif value="1" required/> APP内页
                </label>
                <label class="radio-box">
                    <input type="radio" name="path_type" @if($info['path_type'] == 2) checked @endif value="2" required/> H5
                </label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">跳转地址：</label>
            <div class="col-sm-8">
                <input type="text" name="path" value="{{$info['path']}}" class="form-control" placeholder="跳转地址" required autocomplete="off"/>
            </div>
        </div>
    </div>

    <div class="form-group mb0">
        <label class="col-sm-3 control-label is-required">菜单状态：</label>
        <div class="col-sm-3">
            <label class="radio-box">
                <input type="radio" name="status" @if($info['status'] == 0) checked @endif value="0"/> 隐藏
            </label>
            <label class="radio-box">
                <input type="radio" name="status" @if($info['status'] == 1) checked @endif value="1" /> 显示
            </label>
        </div>
        <label class="col-sm-2 control-label is-required">显示顺序：</label>
        <div class="col-sm-3 mb15">
            <input type="number" name="listsort" value="{{$info['listsort']}}" class="form-control" placeholder="请输入显示顺序" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label ">备注：</label>
        <div class="col-sm-8">
            <textarea name="note" class="form-control" placeholder="请输入备注">{{$info['note']}}</textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            //选择菜单树
            $("#treeName").click(function () {
                var treeId = $("#treeId").val();
                var url = urlcreate("{{ route('system.managemenu.tree') }}","id=" + treeId + "&root=1") ;
                var options = {
                    title: '菜单选择',
                    width: "380",
                    url: url,
                    callBack: doSubmit
                };
                $.modal.openOptions(options);
            });
            $('input[name=type]').on('ifChecked',function () {
                var type = $(this).val();
                if(type === 'C'){
                    $('#menu-show').show();
                } else {
                    $('#menu-show').hide();
                }
            });
        });
        function doSubmit(index, layero){
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
        }
        function submitHandler() {
            var img = '';
            if($("input[name='img_upload[]']").length>0){
                $("input[name='img_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval){
                        img = imgval;
                    }
                })
            }
            $("#img").val(img);
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
