@extends('admin.layout.full')

@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }

    </style>
@append
@section('content')
    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <div class="btn-group-sm" id="toolbar" role="group" style="width: 100%">
                    <div class="pull-right">
                        <form id="role-form">
                            <div class="select-list">
                                <ul>
                                    <li>
                                        <select name="where[struct_type]" class="select2">
                                            <option value="">科室类型</option>
                                            @foreach($structTypes as $id=>$name)
                                                <option value="{{$id}}">{{$name}}</option>
                                            @endforeach
                                        </select>
                                    </li>
                                    <li>
                                        <select name="is_operator" class="select2">
                                            <option value="">是否设置经办人</option>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </li>
                                    <li><input type="text" name="like[name]" value="" placeholder="科室名称" autocomplete="off"></li>
                                    <li>
                                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                    </li>
                                </ul>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            getUserList();
        });

        function getUserList() {
            var options = {
                modalName: "经办人",
                sortName:'listsort',
                sortOrder:'asc',
                showToolbar: false,
                columns: [
                    {field: 'id', title: '科室ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'name',
                        align: 'left',
                        title: '科室/社区名称',
                    },
                    {
                        field: 'struct_type_text',
                        align: 'left',
                        title: '科室类型'
                    },
                    {
                        field: 'operators',
                        align: 'left',
                        title: '经办人',
                    },

                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        export:false,
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:eventoperator:setting")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="setting(\'' + row.id + '\', \'' + row.name + '\')"> 设置</a> ');
                            @endhasPerm
                                return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        }
        function setting(id) {
            $.modal.open("设置经办人", "{{route('system.eventoperator.setting')}}?id="+id, '800', '500');
        }
    </script>
@stop

