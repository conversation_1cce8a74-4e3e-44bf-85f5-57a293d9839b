@extends('admin.layout.form')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input name="id" type="hidden" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label">科室名称：</label>
            <div class="col-sm-8">
                <div class="form-control-static">{{$info['name']}}</div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">经办人：</label>
            <div class="col-sm-8">
                <select name="operator" id="operator" xm-select="operator" xm-select-search required>
                    <option value="">请选择</option>
                    @foreach($users as $id=>$name)
                        <option value="{{$id}}" @if(in_array($id, $operators)) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save('{{route('system.eventoperator.setting')}}', $('#form-edit').serialize());
            }
        }
    </script>
@stop
