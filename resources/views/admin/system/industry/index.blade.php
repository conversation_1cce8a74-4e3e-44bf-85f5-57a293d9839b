@extends('admin.layout.layout')
@include('widget.asset.treetable')
@section('content')
    <div class="btn-group-sm" id="toolbar" role="group">
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        <a class="btn btn-warning" id="expendinfobtn"><i class="fa fa-exchange"></i> 展开/折叠</a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-tree-table"></table>
    </div>
@stop
@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "行业类别",
                columns: [
                    {field: 'selectItem', radio: true},
                    {title: '类别名称', field: 'name'},
                    {title: '排序', field: 'listsort',},
                    {
                        title: '操作',
                        formatter: function (value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')">编辑</a> ');
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                            return actions.join('');
                        }
                    }]
            };
            $.treeTable.init(options);
        });
    </script>
@stop
