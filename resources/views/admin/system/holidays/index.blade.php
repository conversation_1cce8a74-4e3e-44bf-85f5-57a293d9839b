@extends('admin.layout.full')
@include("widget.asset.select2")
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }

    </style>
@append
@section('content')
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 select-table table-striped">
                <div id="calendar"></div>
            </div>
        </div>
    </div>
@stop
@section('script')
<script>

    $(function() {
        initDate('', 0, @json($holidays['holidays']),@json($holidays['workday']))
    });
    function initDate(value, type, holidays, workday) {
        layui.use('laydate', function () {
            var ins1 = layui.laydate.render({
                elem: '#calendar'
                ,type: 'date'
                ,range: '~'
                ,show: true //直接显示
                ,calendar: true
                ,position: 'static'
                ,btns: ['confirm']
                ,value: value
                ,holidays: [
                    holidays,
                    workday
                ]
                ,ready: function(date){
                    $('.layui-laydate-preview').after(
                        '<select id="date-type">' +
                        '<option value="0">无</option>' +
                        '<option value="1" '+(type == 1 ? 'selected' : '')+'>补班日</option>' +
                        '<option value="2" '+(type == 2 ? 'selected' : '')+'>法定节假日</option>' +
                        '</select>'
                    );
                    console.log(ins1);
                }
                ,done: function(value, date, endDate){
                    var date_type = $('#date-type option:selected').val();
                    var text = $("#date-type option:selected").text();
                    $.modal.confirm("确定设置" + value + "日期为"+text+"安排吗？", function() {
                        $.modal.loading("正在处理数据，请稍后...");
                        $.post('{{route('system.holidays.index')}}', {date:value,type:date_type}, function(result) {
                            if (result.code == web_status.SUCCESS) {
                                $.modal.msgSuccess('操作成功', function (){
                                    $('.select-table').html('');
                                    $('.select-table').html('<div id="calendar"></div>');
                                    initDate(value, date_type, result.data.holidays,result.data.workday);
                                });
                            } else if (result.code == web_status.WARNING) {
                                $.modal.alertWarning(result.msg)
                            } else {
                                $.modal.alertError(result.msg);
                            }
                            $.modal.closeLoading();
                        });
                    });
                }
            });
        });
    }
</script>
@stop


