@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}
@section('css_common')
    <style>
        .label-item{
            padding-bottom: 45px;
        }
    </style>
@append
@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-xs-4 control-label is-required" style="padding-top: 7px;padding-right:0;text-align: right;">分组名称：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <input type="text" name="name" value="{{$info['name']}}" placeholder="请输入" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-4 control-label is-required" style="padding-top: 7px;padding-right:0;text-align: right;">人员标签：</label>
        <div class="col-xs-7" style="padding-left: 0;">
            <div id="labels">
                @foreach($info['labels'] as $key=>$label)
                    <div class="label-item">
                        <div class="col-xs-9" style="padding-left: 0;">
                            <input type="hidden" name="labels[{{$key}}][id]" value="{{$label['id']}}">
                            <input type="text" name="labels[{{$key}}][label]" value="{{$label['name']}}" class="form-control" placeholder="请输入" required autocomplete="off"/>
                        </div>
                        <div class="col-xs-2">
                            <a class="btn btn-xs" href="javascript:;" onclick="removeLabel(this)"><i class="fa fa-remove"></i></a>
                        </div>
                    </div>
                @endforeach
            </div>
            <div class="label-item form-control-static">
                <div class="col-xs-5" style="padding-left: 0;">
                    <a class="btn btn-xs" href="javascript:;" onclick="addLabel()"><i class="fa fa-plus"></i>添加</a>
                </div>
            </div>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        var labelIndex = {{$info->labels->count()}};
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
        function addLabel() {
            labelIndex++;
            $('#labels').append(
                '<div class="label-item"> ' +
                '<div class="col-xs-9" style="padding-left: 0;"> ' +
                '<input type="text" name="labels['+labelIndex+'][label]" value="" class="form-control" placeholder="请输入" required autocomplete="off"/> ' +
                '</div> ' +
                '<div class="col-xs-2"> ' +
                '<a class="btn btn-xs" href="javascript:;" onclick="removeLabel(this)"><i class="fa fa-remove"></i></a> ' +
                '</div> ' +
                '</div>'
            );
        }
        function removeLabel(obj) {
            $(obj).parents('.label-item').remove();
        }
    </script>
@stop
