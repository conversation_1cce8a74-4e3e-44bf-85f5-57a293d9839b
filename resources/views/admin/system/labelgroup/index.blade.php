@extends('admin.layout.layout')

@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
    </style>
@append
@section('content')
<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("system:labelgroup:add")
    <a class="btn btn-warning" data-width="500" data-height="500" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
    @endhasPerm
    <div class="pull-right">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="like[name]" value="" placeholder="分组名称" autocomplete="off"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "居民标签",
                sortName:'id',
                sortOrder: "asc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {
                        title: '序号',
                        align: "center",
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'name',
                        title: '标签分组',
                    },
                    {
                        field: 'labels',
                        title: '标签',
                        formatter: function (value, row, index) {
                            var html = ''
                            $.each(value,function (k,v) {
                                switch (v.name) {
                                    case '红':
                                        html += '<span class="badge badge-primary" style="background:#FFE6E6;color:#FF9999;border:1px solid #FF9999;font-weight:normal">'+v.name+'</span>';
                                        break;
                                    case '黄':
                                        html += '<span class="badge badge-primary" style="background:#FFFFE0;color:#FFD699;border:1px solid #FFD699;font-weight:normal">'+v.name+'</span>';
                                    break;
                                    case '蓝':
                                        html += '<span class="badge badge-primary" style="background:#E0E9FF;color:#99C6FF;border:1px solid #99C6FF;font-weight:normal">'+v.name+'</span>';
                                    break;
                                    default:
                                        html += '<span class="badge badge-primary" style="background:#ffffff;color:#c0c0c0;border:1px solid #c0c0c0;font-weight:normal">'+v.name+'</span>';
                                        break;
                                }
                            });
                            return html;
                        }
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            if(row.id === 1) {
                                return '';
                            }
                        @hasPerm("system:labelgroup:edit")
                            actions.push('<a class="btn  btn-xs" data-width="500" data-height="500" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\',this)"> 编辑</a> ');
                        @endhasPerm

                        @hasPerm("system:labelgroup:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

