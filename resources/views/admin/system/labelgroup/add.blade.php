@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}
@section('css_common')
    <style>
        .label-item{
            padding-bottom: 45px;
        }
    </style>
@append
@section('content')
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <label class="col-xs-4 control-label is-required" style="padding-top: 7px;padding-right:0;text-align: right;">分组名称：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <input type="text" name="name" value="" placeholder="请输入" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-4 control-label is-required" style="padding-top: 7px;padding-right:0;text-align: right;">人员标签：</label>
        <div class="col-xs-7" style="padding-left: 0;">
            <div id="labels">
                <div class="label-item">
                    <div class="col-xs-9" style="padding-left: 0;">
                        <input type="text" name="labels[0][label]" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
                    </div>
                    <div class="col-xs-2">
                        <a class="btn btn-xs" href="javascript:;" onclick="removeLabel(this)"><i class="fa fa-remove"></i></a>
                    </div>
                </div>
            </div>
            <div class="label-item form-control-static">
                <div class="col-xs-5" style="padding-left: 0;">
                    <a class="btn btn-xs" href="javascript:;" onclick="addLabel()"><i class="fa fa-plus"></i>添加</a>
                </div>
            </div>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        var labelIndex = 0;
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
        function addLabel() {
            labelIndex++;
            $('#labels').append(
                '<div class="label-item"> ' +
                '<div class="col-xs-9" style="padding-left: 0;"> ' +
                '<input type="text" name="labels['+labelIndex+'][label]" value="" class="form-control" placeholder="请输入" required autocomplete="off"/> ' +
                '</div> ' +
                '<div class="col-xs-2"> ' +
                '<a class="btn btn-xs" href="javascript:;" onclick="removeLabel(this)"><i class="fa fa-remove"></i></a> ' +
                '</div> ' +
                '</div>'
            );
        }
        function removeLabel(obj) {
            $(obj).parents('.label-item').remove();
        }
    </script>
@stop
