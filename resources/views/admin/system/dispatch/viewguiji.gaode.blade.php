<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>轨迹回放</title>
    <script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
    <script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
    <link href="{{asset('static/plugins/layui/css/layui.css')}}" rel="stylesheet">
    <style>
        html, body, #container {
            height: 100%;
            width: 100%;
        }

        .amap-icon img {
            width: 30px;
            height: 30px;
        }
    </style>
</head>
<body>
<div id="container"></div>
<div class="input-card" style="width: 30rem;">
    <h4>轨迹回放控制[最长时间24小时]</h4>
    <div class="input-item">
        <input type="text" class="layui-input" id="test16" value="{{$date}}" placeholder="开始时间">
    </div>
    <div class="input-item">
        <select id="selectValue" onchange="stoppoints(this)">
            <option value="1">1分钟</option>
            <option value="3">3分钟</option>
            <option value="5">5分钟</option>
            <option value="10">10分钟</option>
            <option value="15">15分钟</option>
            <option value="20">20分钟</option>
            <option value="30">30分钟</option>
            <option value="45">45分钟</option>
            <option value="60">1小时</option>
        </select>
    </div>
    <div class="input-item">
        <input type="button" class="btn" value="开始动画" id="start" onclick="startAnimation()"/>
        <input type="button" class="btn" value="暂停动画" id="pause" onclick="pauseAnimation()"/>
        <input type="button" class="btn" value="继续动画" id="resume" onclick="resumeAnimation()"/>
        <input type="button" class="btn" value="停止动画" id="stop" onclick="stopAnimation()"/>
    </div>
</div>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key={{env('GAODE_KEY')}}"></script>
<script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
<script>
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#test16'
            , type: 'date'
            , format: 'yyyy-MM-dd'
            , done: function (value, date) {
                window.location = '{{route('system.dispatch.viewguiji')}}?id={{$id}}&date='+value;
            }
        });
    });
    var result = null;
    var trid = null;
    $.ajax({
        url: '{{ route('system.dispatch.ajaxguijipoints') }}',
        type: 'POST',
        headers: {'X-CSRF-TOKEN': '{{ csrf_token() }}'},
        data: {id: '{{$id}}', date: '{{$date}}'},
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.code == 0) {
                result = data.data.guiji;
                trid = data.data.trid
            }
        }
    });

    var marker, lineArr = result;
    if(result){
        center = result[0];
    }else{
        center = [104.065861, 30.657401];
    }
    var map = new AMap.Map("container", {
        resizeEnable: true,
        center: center,
        zoom: 12
    });
    var markerArr = [];
    map.clearMap()
    stoppoints();
    function stoppoints(obj) {
        var m = $(obj).val();
        if (!m) {
            m = 1;
        }
        console.log(markerArr)
        if (markerArr.length > 0){
            for (var i = 0; i < markerArr.length; i++){
                map.remove(markerArr[i]);
            }
            markerArr = []
        }
        console.log(result)
        $.ajax({
            url: '{{ route('system.dispatch.ajaxstoppoints')}}',
            type: 'POST',
            headers: {'X-CSRF-TOKEN': '{{ csrf_token() }}'},
            data: {id: '{{$id}}', trid: trid, m: m,date:'{{$date}}'},
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.code == 0) {
                    var lnglats = data.data;
                    var infoWindow = new AMap.InfoWindow({offset: new AMap.Pixel(0, -30)});
                    for (var i = 0; i < lnglats.length; i++) {
                        var marker = new AMap.Marker({
                            position: lnglats[i].location,
                            map: map,
                        });
                        var content = "";
                        content += "<div class='input-card'>";
                        content += "<p class='input-item height'>停留时长：" + lnglats[i].duration + "</p>";
                        content += "<p class='input-item height'>开始时间：" + lnglats[i].startTime + "</p>";
                        content += "<p class='input-item height'>结束时间：" + lnglats[i].endTime + "</p>";
                        content += "<p class='input-item height'>停留位置：" + lnglats[i].address + "</p>";
                        content += "</div>";
                        marker.content = content;
                        marker.on('click', markerClick);
                        markerArr.push(marker)
                    }
                    function markerClick(e) {
                        infoWindow.setContent(e.target.content);
                        infoWindow.open(map, e.target.getPosition());
                    }
                }
            }
        });
    }

    if (result) {
        AMap.plugin('AMap.MoveAnimation', function () {
            marker = new AMap.Marker({
                map: map,
                position: center,
                icon: "/static/admin/images/wguser.png",
                offset: new AMap.Pixel(-13, -30)
            });
            // 绘制轨迹
            var polyline = new AMap.Polyline({
                map: map,
                path: lineArr,
                showDir: true,
                strokeColor: "#119c41",  //线颜色
                strokeOpacity: 1,     //线透明度
                strokeWeight: 6,      //线宽
                strokeStyle: "solid"  //线样式
            });
            var passedPolyline = new AMap.Polyline({
                map: map,
                strokeColor: "#d6ecdd",  //线颜色
                strokeWeight: 6,      //线宽
            });
            marker.on('moving', function (e) {
                passedPolyline.setPath(e.passedPath);
                map.setCenter(e.target.getPosition(), true)
            });
            window.startAnimation = function startAnimation() {
                marker.moveAlong(lineArr, {
                    duration: 1000,
                    autoRotation: true,
                });
            };
            window.pauseAnimation = function () {
                marker.pauseMove();
            };
            window.resumeAnimation = function () {
                marker.resumeMove();
            };
            window.stopAnimation = function () {
                marker.stopMove();
            };
        });
    }
    map.setFitView();
</script>
</body>
</html>
