<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="{{asset('static/plugins/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/bootstrap-table/bootstrap-table.min.css')}}" rel="stylesheet"/>
    <script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
    <script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
    <link href="{{asset('static/plugins/layui/css/layui.css')}}" rel="stylesheet">
    <!-- 引入样式 -->
    <style>
        .table > tbody > tr > td, .table > tbody > tr > th, .table > thead > tr > td, .table > thead > tr > th {
            vertical-align: inherit;
        }
    </style>
    <title>工作详情</title>
</head>
<body>
<div class="layui-form" style="padding-right: 45px;padding-top: 10px;float: right;">
    <div class="layui-form-item">
        <div class="layui-inline">
            <div class="layui-input-inline">
                <input type="text" name="where[clock_date]" class="form-control" id="test31" value="{{$date}}" placeholder="选择日期">
            </div>
        </div>
    </div>
</div>
<div class="container flex" style="flex-direction:column;width: 95%;">
    <div>
        @foreach($list as $work)
            <table id="bootstrap-table" class="table table-bordered">
                <caption style="padding-top: 15px;padding-bottom: 15px;color:#000000;text-align: center;font-weight: bold">
                    {{$work['name']}}【共计{{count($work['list'])}}条】
                </caption>
                <thead>
                <tr>
                    @foreach($work['fields'] as $field)
                    <th>{{$field}}</th>
                    @endforeach
                </tr>
                </thead>
                <tbody>
                @if($work['list'])
                    @foreach($work['list'] as $item)
                        <tr>
                            @foreach($work['fields'] as $key=>$field)
                                <td>{{$item[$key] ?? ''}}</td>
                            @endforeach
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="4" style="text-align: center">暂无数据</td>
                    </tr>
                @endif
                </tbody>
            </table>
        @endforeach
    </div>
</div>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script>
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#test31'
            // , theme: 'grid'
            ,done: function(value, date){
                window.location = '{{route('system.dispatch.viewwork')}}?id={{$id}}&date='+value;
            }
        });
    })
</script>
</body>
</html>