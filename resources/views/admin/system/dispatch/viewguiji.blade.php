<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>轨迹</title>
    <link href="{{asset('static/plugins/layui/css/layui.css')}}" rel="stylesheet">
    <script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
    <script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
    <script type="text/javascript" src="{{asset('static/plugins/supermap-iclient/dist/leaflet/include-leaflet.js')}}"></script>
    <style>
        html, body, #map {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
        }
        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
            background-color: transparent;
            background-image: none;
            color: #25A5F7;
            border-color: #25A5F7;
            padding: .25rem .5rem;
            line-height: 1.5;
            border-radius: 1rem;
            -webkit-appearance: button;
            cursor: pointer;
        }
        .btn:hover {
            text-decoration: none;
        }
        .btn:hover {
            color: #fff;
            background-color: #25A5F7;
            border-color: #25A5F7;
        }
    </style>
</head>
<body>
<div id="map" style="width: 100%;height: 100vh">

</div>

<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script src="{{asset('static/plugins/blockUI/jquery.blockUI.js')}}"></script>
<script src="{{asset('static/admin/js/common.js')}}"></script>
<script src="{{asset('static/admin/js/iframe-ui.js')}}"></script>
<script src="{{asset('static/plugins/supermap-iclient/libs/trackplayer/leaflet-trackplayer.umd.cjs')}}"></script>
<script>
    var map,
        mapUrl = "{{env('YZT_STREET_URL')}}",
        url = "{{env('YZT_DISTRICT_URL')}}";
    let track;
    var decodeMarkers = [];
    @php($center = explode(',', env('YZT_CENTER_POINT','104.0638303756715,30.659859309043362')))
    L.supermap.initMap(url,{
        mapOptions: {
            target: 'map',
            center: L.latLng({{$center[1]}}, {{$center[0]}}),
            zoom: 12,
        }
    }).then((res) => {
        map = res.map
        var overlayLayer = new L.supermap.TiledMapLayer(mapUrl, {
            transparent: true,  // 叠加层透明，显示底图
            opacity: 0.4        // 可选：调整不透明度
        });
        overlayLayer.addTo(map);

        var control = L.control({position: 'topleft'});
        control.onAdd = function (map) {
            var popup = L.DomUtil.create('div');
            popup.style.width = '200px';
            popup.innerHTML = '<div style="background-color: #fff;padding: 10px;">' +
                    '<div class="input-item" style="margin-bottom: 10px;">'+
                        '<input type="text" class="layui-input" id="test16" style="width: 100%" value="{{$date}}" placeholder="日期">'+
                    '</div>' +
                    '<div class="input-item"  style="text-align: center">'+
                        '<input type="button" class="btn" value="开始动画" id="start" onclick="startAnimation()" style="margin-right: 10px;">'+
                        '<input type="button" class="btn" value="暂停动画" id="pause" onclick="pauseAnimation()">'+
                    '</div>' +
                '</div>';
            handleMapEvent(popup, this._map);
            return popup;
        };
        control.addTo(map);
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#test16'
                , type: 'date'
                , format: 'yyyy-MM-dd'
                , done: function (value, date) {
                    window.location = '{{route('system.dispatch.viewguiji')}}?id={{$id}}&date='+value;
                }
            });
        });
        @if ($points)
        var latlngs = $.common.reverseLngLat(Object.values(@json($points)));
        map.setView(latlngs[0], 18);
        track = new L.TrackPlayer(latlngs, {
            markerIcon: L.icon({
                iconSize: [46, 46],
                iconUrl: "{{asset('static/admin/images/icon/wg.png')}}",
                iconAnchor: [23, 46],
            }),
            markerRotation: false,
            // notPassedLineColor:'#0000ff',
            // passedLineColor:'#25A5F7',
        }).addTo(map);
        stoppoints();
        @endif
    });
    function startAnimation(){
        track.start();
    }
    function pauseAnimation(){
        track.pause();
    }
    function handleMapEvent(div, map) {
        if (!div || !map) {
            return;
        }
        div.addEventListener('mouseover', function () {
            map.dragging.disable();
            map.scrollWheelZoom.disable();
            map.doubleClickZoom.disable();
        });
        div.addEventListener('mouseout', function () {
            map.dragging.enable();
            map.scrollWheelZoom.enable();
            map.doubleClickZoom.enable();
        });
    }
    var markerArr = [];
    function stoppoints() {
        $.ajax({
            url: '{{ route('system.dispatch.ajaxstoppoints')}}',
            type: 'POST',
            headers: {'X-CSRF-TOKEN': '{{ csrf_token() }}'},
            data: {id: '{{$id}}',date:'{{$date}}'},
            dataType: "json",
            async: false,
            success: function (res) {
                if (res.code === 0) {
                    for (var i = 0; i < res.data.length; i++) {
                        var item = res.data[i];
                        var content = "";
                        content += "<div class='input-card'>";
                        content += "<p class='input-item height'>停留时长：" + item.duration + "</p>";
                        content += "<p class='input-item height'>开始时间：" + item.start_time + "</p>";
                        content += "<p class='input-item height'>结束时间：" + item.end_time + "</p>";
                        content += "<p class='input-item height'>停留位置：" + item.address + "</p>";
                        content += "</div>";

                        var marker = L.marker([item.location[1], item.location[0]]);
                        decodeMarkers.push(marker);
                        marker.bindPopup(content);
                    }
                    for (var i = 0; i < decodeMarkers.length; i++) {
                        decodeMarkers[i].addTo(map);
                    }
                }
            }
        });
    }
</script>
</body>
</html>
