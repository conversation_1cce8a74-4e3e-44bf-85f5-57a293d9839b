@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <style>
        .map{
           min-height: 85%;
            width: 100%;
        }
        .panel-title a {
            width: 450px;
            display: inline-block;
            text-align: left;
            /* Add more styles if needed */
        }
        .toggle-text {
            display: inline-block;
            float: right;
        }
        .filter{
            /*display: ruby-text;*/
            display: flex;
            padding-bottom: 10px;
            justify-content: space-around;
        }
        .select-table{
            padding-top: 0 !important;
        }
        .icon{
            width: 24px;
            height: 24px;
        }
        .map-txt{
            text-align: center;
        }
        .map-txt img{
            width: 42px;
        }
        .map-txt-xb{
            border: solid 1px red;
            color: #a5a2a2;
            float: left;
            width: 80px;
            background-color: rgba(255,0,0,0.1)
        }
        .line-content{
            line-height: 32px;
        }
        .line-content a{
            margin-left: 8px;
        }
        .q-info{
            background-color: #E0E0E0;
            border-radius: 8px;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            text-align: center;
            margin-top: 8px;
        }
        .q-info .content{
            display: flex;
            flex-direction: column;
            padding: 0 15px 0 15px;
        }
        .q-info .bl{
            border-left: 1px solid silver;
        }
        .connectList .active{
            background: silver;
        }
        .agile-list li:hover {
            cursor: pointer;
            background: silver !important;
        }
        .sortable-list{
            height: 460px;
            overflow: auto;
            padding-bottom: 80px;
        }
        .sortable-list::-webkit-scrollbar {
            width: 2px; /* 设置滚动条宽度 */
        }
        .sortable-list::-webkit-scrollbar-track {
            /*background: #f1f1f1; !* 设置滚动条轨道颜色 *!*/
        }

        .sortable-list::-webkit-scrollbar-thumb {
            /*background: #888; !* 设置滚动条滑块颜色 *!*/
        }

        .sortable-list::-webkit-scrollbar-thumb:hover {
            /*background: #555; !* 鼠标悬浮时滚动条滑块颜色 *!*/
        }
    </style>
    <div class="col-sm-12 search-collapse mb20">
        <div class="ibox-title">
            <div class="pull-left">
                <h3>指挥调度</h3>
            </div>
            <div class="pull-right user-types" style="display: inline-flex">
                <div class="">
                    <label class="check-box">
                        <input type="checkbox" name="user_type" checked value="1"/> 网格员 <image class="icon" src="{{asset('static/admin/images/icon/wg.png')}}" />
                    </label>
                </div>
                <div class="ml20">
                    <label class="check-box">
                    <input type="checkbox" name="user_type" checked value="2"/> 城管人员 <image class="icon" src="{{asset('static/admin/images/icon/cg.png')}}" />
                    </label>
                </div>
                <div class="ml20">
                    <label class="check-box">
                    <input type="checkbox" name="user_type" checked value="3"/> 消防队员 <image class="icon" src="{{asset('static/admin/images/icon/xf.png')}}" />
                    </label>
                </div>
                <div class="ml20">
                    <label class="check-box">
                    <input type="checkbox" name="user_type" checked value="4"/> 综治人员 <image class="icon" src="{{asset('static/admin/images/icon/zz.png')}}" />
                    </label>
                </div>
            </div>
        </div>
    </div>


    <div id="map" class="map select-table table-striped">
        <div class="panel panel-default" style="position: absolute;z-index: 1;">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">
                        人员
                        <span class="toggle-text">收起 <i class="fa fa-angle-up"></i> </span>
                    </a>
                </h4>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
                    <aside class="control-sidebar-light" style="width: 450px;position: static;border-left: 0px;">
                        <ul class="nav nav-tabs nav-justified control-sidebar-tabs">
                            <li class="active"><a href="#1" data-toggle="tab" aria-expanded="true">网格员</a></li>
                            <li><a href="#2" data-toggle="tab">城管队员</a></li>
                            <li><a href="#3" data-toggle="tab">消防队员</a></li>
                            <li><a href="#4" data-toggle="tab">综治人员</a></li>
                        </ul>

                        <div class="tab-content">
                            <div class="input-group mt10 mb10" style="width: 94%;margin-left: 14px;">
                                <input id="kw" type="text" class="form-control" placeholder="姓名电话" style="height: 34px;">
                                <span class="input-group-btn">
                                        <button type="button" class="btn btn-success" onclick="searchkw()">
                                            <i class="fa fa-search"></i>
                                        </button>
                                    </span>
                            </div>
                            <div class="tab-pane active" id="1">
                                <div class="filter">
                                    <select class="select2" id="cid">
                                        <option value="">所有社区</option>
                                        @foreach(\App\Extends\Helpers\Functions::getCommunity() as $id=>$name)
                                            <option value="{{$id}}">{{$name}}</option>
                                        @endforeach
                                    </select>
                                    <select class="select2" id="grid_id">
                                        <option value="">所属网格</option>
                                    </select>
                                </div>
                                <div class="user-list">
                                    <ul id="user_list_1" class="sortable-list connectList agile-list ui-sortable">
                                    </ul>
                                </div>
                            </div>
                            <div class="tab-pane" id="2">
                                <div class="filter">
                                    <select class="select2" id="gid">
                                        <option value="">所有分组</option>
                                        @foreach($urbanGroup as $item)
                                            <option value="{{$item['id']}}">{{$item['name']}}</option>
                                        @endforeach
                                    </select>
                                    <select class="select2" id="identity">
                                        <option value="">身份</option>
                                        @foreach($urbanIdentity as $key=>$item)
                                            <option value="{{$key}}">{{$item}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="user-list">
                                    <ul id="user_list_2" class="sortable-list connectList agile-list ui-sortable">
                                    </ul>
                                </div>
                            </div>
                            <div class="tab-pane" id="3">
                                <div class="user-list">
                                    <ul id="user_list_3" class="sortable-list connectList agile-list ui-sortable">
                                    </ul>
                                </div>
                            </div>
                            <div class="tab-pane" id="4">
                                <div class="user-list">
                                    <ul id="user_list_4" class="sortable-list connectList agile-list ui-sortable">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </aside>

                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" data-backdrop="static" data-keyboard="false" style="padding-top: 170px;" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" >
        <div class="modal-dialog" style="width: 260px;background-color: rgb(208,208,208);" role="document">
            <div class="modal-content" style="height: 560px;background-color: #f1f1f1">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalLabel">语音通话</h4>
                </div>
                <div class="modal-body" style="height: 426px;text-align: center;line-height: 80px;font-size: 26px;">
                    <p id="phone-username"></p>
                    <p id="phone-mobile"></p>
                    <p id="phone-status"></p>
                </div>
                <div class="modal-footer" style="text-align: center;">
                    <button type="button" class="btn btn-warning on-hook" data-dismiss="modal">结束通话</button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode:'{{env('GAODE_SECRET')}}',
        }
    </script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key={{env('GAODE_KEY')}}"></script>
    <script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
    <script type="text/javascript">
        //初始化地图对象，加载地图
        var map = new AMap.Map("map", {resizeEnable: true,zoom:14});
        var infoWindow = new AMap.InfoWindow({offset: new AMap.Pixel(0, -30)});
        var markers = [];
        //获取标点的用户
        function getUsers(user_types){
            $.ajax({
                url: '{{route('dispatch.getUserList')}}',
                type: 'post',
                dataType: 'json',
                data: {
                    user_types:user_types
                },
                success: function (res) {
                    map.clearMap()
                    if (res.code === 0) {
                        timer(res.data);
                    }
                }
            });
        }
        //获取单个用户的详情
        function getUserInfo(e){
            var user_id = e.target.getOptions().extData.uid;
            $.ajax({
                url: '{{route('dispatch.getUserInfo')}}',
                type: 'post',
                dataType: 'json',
                data: {
                    id:user_id,
                },
                success: function (res) {
                    if (res.code === 0) {
                        // console.log(res)
                        var data = res.data;
                        var templet = '<div class="line-content"><span>'+data.username+'</span> <a class="yuyin" data-mobile="'+data.mobile+'" data-username="'+data.username+'" href="javascript:;"><i class="fa fa-phone-square" aria-hidden="true"></i></a> <a><i class="fa fa-video-camera" aria-hidden="true"></i></a></div>' +
                            '<div class="line-content"><span>位置：'+(data.address ? data.address : '未知')+'</span></div>' +
                            '<div class="line-content"><span>定位时间：'+data.send_time+'</span></div>' +
                            '<div class="line-content"><span>手机：'+data.mobile+'</span></div>';
                        if(data.user_type===1){
                            //网格员
                            templet += '<div class="line-content">' +
                                '<span>网格：'+data.grid_info.community.name + data.grid_info.name+'</span>' +
                                '</div>' +
                                '<div class="line-content"><span>小区：'+data.quarters.join(',')+'</span></div>' +
                                '<ul class="q-info">' +
                                '<li class="content"><p>'+data.loudong_num+'</p><p>楼栋</p></li>' +
                                '<li class="content bl"><p>'+data.danyuan_num+'</p><p>单元</p></li>' +
                                '<li class="content bl"><p>'+data.hu_num+'</p><p>户</p></li>' +
                                '<li class="content bl"><p>'+data.juming_num+'</p><p>居民</p></li>' +
                                '</ul>';
                        }
                        if(data.user_type===2){
                            //城管
                            var identity_text = '队员';
                            switch (data.identity) {
                                case 1:
                                    identity_text = '组长';
                                    break;
                                case 2:
                                    identity_text = '副组长';
                                    break;
                            }
                            templet += '<div class="line-content"><span>分组：'+data.group.name+'</span></div>' +
                                '<div class="line-content"><span>身份：'+identity_text+'</span></div>';
                        }
                        templet += "<div class='line-content'>" +
                            @hasPerm("system:dispatch:viewwork")
                            "【<span style='color: #059fe7;cursor: pointer' onclick='viewwork("+data.id+")' >查看工作</span>】" +
                            @endhasPerm
                            @hasPerm("system:dispatch:viewguiji")
                            "【<span style='color: #059fe7;cursor: pointer' onclick='viewguiji("+data.id+")' >查看轨迹</span>】" +
                            @endhasPerm
                            "</div>";

                        infoWindow.setContent(templet);
                        infoWindow.open(map, e.target.getPosition());
                    }
                }
            });
        }
        map.setFitView();
        function timer(data) {
            map.clearMap()
            markers = [];
            $.each(data,function (index,item) {
                var position = [104.062414, 30.662651];
                if (item.position){
                    const arr = item.position.split(",");
                    if(arr.length===2){
                        position = arr;
                    }
                }
                var cls = 'map-txt';
                var icon = item.is_online ? '{{asset('static/admin/images/icon/wg.png')}}' :  '{{asset('static/admin/images/icon/wg1.png')}}';
                switch (item.user_type){
                    case 2:
                        icon = item.is_online ? '{{asset('static/admin/images/icon/cg.png')}}' :  '{{asset('static/admin/images/icon/cg1.png')}}';
                        break;
                    case 3:
                        icon = item.is_online ? '{{asset('static/admin/images/icon/xf.png')}}' :  '{{asset('static/admin/images/icon/xf1.png')}}';
                        break;
                    case 4:
                        icon = item.is_online ? '{{asset('static/admin/images/icon/zz.png')}}' :  '{{asset('static/admin/images/icon/zz1.png')}}';
                        break;
                }
                var marker = new AMap.Marker({
                    // map: map,
                    position: [position[0],position[1]],
                    icon: icon,
                    offset: new AMap.Pixel(-13, -30),
                    content:'<div class="'+cls+'"><img src="'+icon+'" alt="">'+item.username+'</div>',
                    extData:{uid : item.id},
                });
                markers.push(marker);
                marker.on('click', markerClick);
            })
            map.add(markers);
        }

        function markerClick(e) {
            // console.log(e)
            // 获取单个用户信息
            getUserInfo(e);
        }
        function clearMarker() {
            infoWindow.close();
            markers.forEach(function(marker) {
                marker.setMap(null);
            });
            markers = [];
        }
    </script>
    <script>
        var user_type = 1; // 网格员
        var user_types = '1,2,3,4';
        var params = {}; // 搜索参数
        var intervalIndex = null;
        intervalIndex = setInterval(function () {
            getUsers(user_types);
        }, 45000);
        infoWindow.on('open', function() {
            clearInterval(intervalIndex);
        });
        infoWindow.on('close', function() {
            getUsers(user_types);
            intervalIndex = setInterval(function () {
                getUsers(user_types);
            }, 45000);
        });
        $(function () {

            $('.panel-title a').on('click', function(e) {
                var $this = $(this);
                // console.log($this)
                var $toggleText = $this.find('.toggle-text');
                if ($('#collapseOne').hasClass('in')) {
                    $toggleText.html('展开 <i class="fa fa-angle-down"></i>');
                } else {
                    $toggleText.html('收起 <i class="fa fa-angle-up"></i>');
                }
            });

            // 社区changge
            $('#cid').on("change", function() {
                let communityId = $(this).val();
                let grid = $('#grid_id');
                grid.select2('destroy').empty();
                grid.select2({data:[{id:"",text:"所属网格"}],width:200});
                if (communityId) {
                    $.ajax({
                        url: '{{route('admin.getgridbycid')}}',
                        type: 'get',
                        dataType: 'json',
                        data: {
                            cid: communityId
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                grid.select2({data:res.data,width:200});
                            }
                        }
                    });
                }

                params['cid'] = $('#cid').val();
                getUserList(user_type,params);
            });
            // 网格change
            $('#grid_id').on("change", function() {
                params['cid'] = $('#cid').val();
                params["where[group_id]"] = $('#grid_id').val();
                getUserList(user_type,params);
            });
            // 分组change
            $('#gid').on("change", function() {
                params["where[group_id]"] = $('#gid').val();
                getUserList(user_type,params);
            });
            // 身份change
            $('#identity').on("change", function() {
                params["where[identity]"] = $('#identity').val();
                getUserList(user_type,params);
            });

            $('a[data-toggle="tab"]').on('click', function (e) {
                e.preventDefault(); // 阻止默认的锚点跳转行为
                var tab = $(this).attr('href');
                if (tab === '#1') {
                    user_type = 1;
                    params = {};
                }
                if (tab === '#2') {
                    user_type = 2;
                    params = {};
                }
                if (tab === '#3') {
                    user_type = 3;
                    params = {};
                }
                if (tab === '#4') {
                    user_type = 4;
                    params = {};
                }
                getUserList(user_type,params);
            });

            // 初始化数据
            getUserList(user_type,params);
            getUsers(user_types);

            //过滤人员类型
            $('input[name=user_type]').on('ifChanged', function(){
                var _user_types = '';
                $('input[name=user_type]:checkbox').each(function (){
                    if(true===$(this).is(':checked')){
                        _user_types+=$(this).val()+',';
                    }
                })
                if(_user_types.substr(_user_types.length-1)===','){
                    _user_types = _user_types.substr(0,_user_types.length-1);
                }
                user_types = _user_types;
                // console.log(user_types);
                getUsers(user_types);
            })
        });
        // 获取左侧列表中的用户
        function getUserList(user_type,params){
            var url = '{{route('griduser.index')}}'
            if(user_type === 1){
                url = '{{route('griduser.index')}}'
            }
            if(user_type === 2){
                url = '{{route('urban.index')}}'
            }
            if(user_type === 3){
                url = '{{route('firefighter.index')}}'
            }
            if(user_type === 4){
                url = '{{route('comprehensive.index')}}'
            }
            params['isTree']=1;
            $.ajax({
                url: url,
                type: 'post',
                dataType: 'json',
                data: params,
                success: function (res) {
                    if (res.code === 0) {
                        // console.log(res);
                        var html = '';
                        if(user_type===1){
                            for (var i=0;i<res.data.length;i++){
                                html = html + '<li class="info-element" data-id="'+res.data[i].id+'">'+res.data[i].username+'<div class="agile-detail">'+res.data[i].grid.community.name+res.data[i].grid.name+'</div></li>'
                            }
                        }
                        if(user_type===2){
                            for (var i=0;i<res.data.length;i++){
                                html = html + '<li class="info-element" data-id="'+res.data[i].id+'">'+res.data[i].username+'<div class="agile-detail"><a class="pull-right btn btn-xs btn-white">'+res.data[i].identity_text+'</a>'+res.data[i].urban_group.name+'</div></li>'
                            }
                        }
                        if(user_type===3){
                            for (var i=0;i<res.data.length;i++){
                                html = html + '<li class="info-element" data-id="'+res.data[i].id+'">'+res.data[i].username+'</li>'
                            }
                        }
                        if(user_type===4){
                            for (var i=0;i<res.data.length;i++){
                                html = html + '<li class="info-element" data-id="'+res.data[i].id+'">'+res.data[i].username+'</li>'
                            }
                        }
                        $('#user_list_'+user_type).html(html);

                        //选中列表中的某个人
                        $('.info-element').on('click',function (){
                            if($(this).hasClass('active')){
                                $(this).removeClass('active');
                                clearMarker();
                                getUsers(user_types);
                            }else{
                                //先移除所有active
                                $('.info-element').removeClass('active');
                                $(this).addClass('active');

                                //获取当前用户的信息
                                var user_id = $(this).data('id');
                                $.ajax({
                                    url: '{{route('dispatch.getUserInfo')}}',
                                    type: 'post',
                                    dataType: 'json',
                                    data: {
                                        id:user_id,
                                    },
                                    success: function (res) {
                                        if (res.code === 0) {
                                            // console.log(res)
                                            var data = res.data;
                                            var templet = '<div class="line-content"><span>'+data.username+'</span> <a class="yuyin" data-mobile="'+data.mobile+'" data-username="'+data.username+'" href="javascript:;"><i class="fa fa-phone-square" aria-hidden="true"></i></a> <a><i class="fa fa-video-camera" aria-hidden="true"></i></a></div>' +
                                                '<div class="line-content"><span>位置：'+(data.address ? data.address : '未知')+'</span></div>' +
                                                '<div class="line-content"><span>定位时间：'+data.send_time+'</span></div>' +
                                                '<div class="line-content"><span>手机：'+data.mobile+'</span></div>';
                                            if(data.user_type===1){
                                                //网格员
                                                templet += '<div class="line-content">' +
                                                    '<span>网格：'+data.grid_info.community.name + data.grid_info.name+'</span>' +
                                                    '</div>' +
                                                    '<div class="line-content"><span>小区：'+data.quarters.join(',')+'</span></div>' +
                                                    '<ul class="q-info">' +
                                                    '<li class="content"><p>'+data.loudong_num+'</p><p>楼栋</p></li>' +
                                                    '<li class="content bl"><p>'+data.danyuan_num+'</p><p>单元</p></li>' +
                                                    '<li class="content bl"><p>'+data.hu_num+'</p><p>户</p></li>' +
                                                    '<li class="content bl"><p>'+data.juming_num+'</p><p>居民</p></li>' +
                                                    '</ul>';
                                            }
                                            if(data.user_type===2){
                                                //城管
                                                var identity_text = '队员';
                                                switch (data.identity) {
                                                    case 1:
                                                        identity_text = '组长';
                                                        break;
                                                    case 2:
                                                        identity_text = '副组长';
                                                        break;
                                                }
                                                templet += '<div class="line-content"><span>分组：'+data.group.name+'</span></div>' +
                                                    '<div class="line-content"><span>身份：'+identity_text+'</span></div>';
                                            }
                                            templet += "<div class='line-content'>" +
                                                    @hasPerm("system:dispatch:viewwork")
                                                        "【<span style='color: #059fe7;cursor: pointer' onclick='viewwork("+data.id+")' >查看工作</span>】" +
                                                    @endhasPerm
                                                    @hasPerm("system:dispatch:viewguiji")
                                                        "【<span style='color: #059fe7;cursor: pointer' onclick='viewguiji("+data.id+")' >查看轨迹</span>】" +
                                                    @endhasPerm
                                                        "</div>";
                                            //遍历marks中是否有那个用户了
                                            var flag = false;
                                            markers.forEach(marker=>{
                                                if(marker._originOpts.extData.uid === user_id){
                                                    flag = true;
                                                    infoWindow.close();
                                                    position = marker._position
                                                    var _position = new AMap.LngLat(position[0], position[1]);
                                                    map.setCenter(_position);
                                                    setTimeout(function () {
                                                        infoWindow.setContent(templet);
                                                        infoWindow.open(map, position);
                                                    },800);
                                                    return false;
                                                }
                                            });
                                            if(flag){
                                                return false;
                                            }
                                            //重新标点
                                            var position = [104.062414, 30.662651];
                                            if (data.position)
                                            {
                                                const arr = data.position.split(",");
                                                if(arr.length===2){
                                                    position = arr;
                                                }
                                            }
                                            var cls = 'map-txt';
                                            var icon = data.is_online ? '{{asset('static/admin/images/icon/wg.png')}}' :  '{{asset('static/admin/images/icon/wg1.png')}}';
                                            switch (data.user_type){
                                                case 2:
                                                    icon = data.is_online ? '{{asset('static/admin/images/icon/cg.png')}}' :  '{{asset('static/admin/images/icon/cg1.png')}}';
                                                    break;
                                                case 3:
                                                    icon = data.is_online ? '{{asset('static/admin/images/icon/xf.png')}}' :  '{{asset('static/admin/images/icon/xf1.png')}}';
                                                    break;
                                                case 4:
                                                    icon = data.is_online ? '{{asset('static/admin/images/icon/zz.png')}}' :  '{{asset('static/admin/images/icon/zz1.png')}}';
                                                    break;
                                            }
                                            var marker = new AMap.Marker({
                                                // map: map,
                                                position: [position[0],position[1]],
                                                icon: icon,
                                                offset: new AMap.Pixel(-13, -30),
                                                content:'<div class="'+cls+'"><img src="'+icon+'" alt="">'+data.username+'</div>',
                                                extData:{uid : data.id},
                                            });
                                            //clearMarker();
                                            infoWindow.close();
                                            var _position = new AMap.LngLat(position[0], position[1]);
                                            map.setCenter(_position);
                                            marker.on('click', markerClick);
                                            map.add(marker)

                                            //这个要发生鬼畜，暂不知原因，暂时用延时弹框
                                            // map.on('moveend', function() {
                                            //     infoWindow.setContent(templet);
                                            //     infoWindow.open(map, position);
                                            // });

                                            setTimeout(function () {
                                                infoWindow.setContent(templet);
                                                infoWindow.open(map, position);
                                            },800)
                                        }
                                    }
                                });
                            }
                        })
                    }
                }
            });

        }
        // 关键词搜索
        function searchkw(){
            var kw = $('#kw').val().trim();
            params.keywords = kw;
            getUserList(user_type,params);
        }
        $(document).on("click",".yuyin",function (){
            var mobile = $(this).data('mobile');
            var username = $(this).data('username');
            $.modal.confirm("您确定要拨打电话" + mobile + "("+username+")吗？", function() {
                $("#phone-username").html(username);
                $("#phone-mobile").html(mobile);
                $("#exampleModal").modal("toggle");
                websocket = new WebSocket("wss://jszhdd.cdqingyang.gov.cn/wss");
                websocket.onopen = function () {
                    msg1 = {
                        "Action":"Login",
                        "GongHao":"18000",
                        "FenJi":"8002",
                        "PlatFormCode":"Default"};
                    websocket.send(JSON.stringify(msg1));
                    msg = {
                        "Action":"DialOut",
                        "GongHao":"18000",
                        "FenJi":"8002",
                        "PlatFormCode":"Default",
                        "Params":mobile};
                    websocket.send(JSON.stringify(msg));
                }
                websocket.onmessage = (event) => {
                    result = event.data.match(/\((.*?)\)/)
                    if (!result[1]){
                        $("#phone-status").html("通话失败");
                    }
                    resultJosn = JSON.parse(result[1]);
                    console.log(resultJosn)
                    if(resultJosn['Action'] == 'Login'){
                        $("#phone-status").html("登录CTI......");
                    }
                    if(resultJosn['Action'] == 'RegNumberState'){
                        $("#phone-status").html("坐席排队中......");
                    }
                    if(resultJosn['Action'] == 'DialOut'){
                        $("#phone-status").html("正在呼叫......");
                    }
                    if(resultJosn['Action'] == 'BeginTalking'){
                        $("#phone-status").html("通话中......");
                    }
                    if(resultJosn['Action'] == 'UserRingEnd'){
                        $("#phone-status").html("未接挂机");
                    }
                    if(resultJosn['Action'] == 'TalkingEnd'){
                        $("#phone-status").html("通话结束");
                    }
                }
                websocket.onerror = (error) => {
                    console.log('WebSocket 出错:', error);
                };
            });
        });
        $(document).on("click",".on-hook",function (){
            websocket = new WebSocket("wss://jszhdd.cdqingyang.gov.cn/wss");
            websocket.onopen = function () {
                msg = {
                    "Action":"HangUp",
                    "GongHao":"18000",
                    "FenJi":"8002",
                    "PlatFormCode":"Default",
                    "Params":""};
                websocket.send(JSON.stringify(msg));
            }
            websocket.onmessage = (event) => {
                result = event.data.match(/\((.*?)\)/)
                if (!result[1]){
                    $("#phone-status").html("异常");
                }
                resultJosn = JSON.parse(result[1]);
                console.log(resultJosn)
                if(resultJosn['Action'] == 'HangUp'){
                    $("#phone-status").html("挂机");
                }
            }
            websocket.onerror = (error) => {
                console.log('WebSocket 出错:', error);
            };

        });
        function viewwork(id) {
            $.modal.openOptions({
                title: '查看工作',
                url: '{{route('system.dispatch.viewwork')}}?id='+id,
                full:true,
                btn:['关闭'],
                yes:function(index, layero){
                    layer.close(index);
                }
            });
        }
        function viewguiji(id) {
            $.modal.openOptions({
                title: '查看轨迹',
                url: '{{route('system.dispatch.viewguiji')}}?id='+id,
                btn:['关闭'],
                full:true,
                yes:function(index, layero){
                    layer.close(index);
                }
            });
        }
    </script>
@stop

