@extends('admin.layout.form')
@include('widget.asset.select2')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">姓名：</label>
            <div class="col-sm-8">
                <input type="text" name="username" value="{{$info['username']}}" class="form-control" placeholder="请输入" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">手机号：</label>
            <div class="col-sm-8">
                <input type="text" name="mobile" value="{{$info['mobile']}}" class="form-control" placeholder="请输入" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 手机号为app的登录账号</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">所属社区：</label>
            <div class="col-sm-8">
                <select name="cid" id="cid" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                        <option value="{{$id}}" @if($id == ($info['microgrid']['cid'] ?? 0)) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">所属网格：</label>
            <div class="col-sm-8">
                <select name="grid_id" id="grid_id" class="select2" required>
                    <option value="">请选择</option>
                    @foreach($gridList ?? [] as $item)
                        <option value="{{$item['id']}}" @if($item['id'] == ($info['microgrid']['grid_id'] ?? 0)) selected @endif>{{$item['name']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">所属网格：</label>
            <div class="col-sm-8">
                <select name="group_id" id="microgrid_id" class="select2" required>
                    <option value="">请选择</option>
                    @foreach($microgridList ?? [] as $item)
                        <option value="{{$item['id']}}" @if($item['id'] == $info['group_id']) selected @endif>{{$item['name']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {
            $('#cid').on("change", function() {
                let communityId = $(this).val();
                let grid = $('#grid_id');
                let microgrid = $('#microgrid_id');
                grid.select2('destroy').empty();
                microgrid.select2('destroy').empty();
                grid.select2({data:[{id:"",text:"请选择"}],width:200});
                microgrid.select2({data:[{id:"",text:"请选择"}],width:200});
                if (communityId) {
                    $.ajax({
                        url: '{{route('admin.getgridbycid')}}',
                        type: 'get',
                        dataType: 'json',
                        data: {
                            cid: communityId
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                grid.select2({data:res.data,width:200});
                            }
                        }
                    });
                }
            });
            $('#grid_id').on("change", function() {
                let grid_id = $(this).val();
                let microgrid = $('#microgrid_id');
                microgrid.select2('destroy').empty();
                microgrid.select2({data:[{id:"",text:"请选择"}],width:200});
                if (grid_id) {
                    $.ajax({
                        url: '{{route('admin.getmicrogridbygridid')}}',
                        type: 'get',
                        dataType: 'json',
                        data: {
                            grid_id: grid_id
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                microgrid.select2({data:res.data,width:200});
                            }
                        }
                    });
                }
            });
        });
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
