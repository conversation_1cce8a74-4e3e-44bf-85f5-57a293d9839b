@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
    </style>
@append
@section('content')
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:griduser:add")
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("system:{$app}:import")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.importExcel()">
            <i class="fa fa-upload"></i> 导入
        </a>
        @endhasPerm
        @hasPerm("system:{$app}:export")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.exportExcel()">
            <i class="fa fa-download"></i> 导出
        </a>
        @endhasPerm
        <div class="pull-right">
            <form id="role-form">
                <div class="select-list">
                    <ul>
                        <li>
                            <select name="cid" id="cid" class="select2">
                                <option value="">所属社区</option>
                                @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                                    <option value="{{$id}}">{{$name}}</option>
                                @endforeach
                            </select>
                        </li>
                        <li>
                            <select name="grid_id" id="grid_id" class="select2">
                                <option value="">所属网格</option>
                            </select>
                        </li>
                        <li>
                            <select name="where[group_id]" id="microgrid_id" class="select2">
                                <option value="">所属微网格</option>
                            </select>
                        </li>
                        <li><input type="text" name="keywords" value="" placeholder="姓名电话" autocomplete="off"></li>
                        <li>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "微网格员",
                sortName:'id',
                sortOrder: "desc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'username',
                        title: '姓名',
                    },
                    {
                        field: 'mobile',
                        title: '手机号',
                    },
                    {
                        field: 'microgrid.community.name',
                        title: '所属社区',
                    },
                    {
                        field: 'microgrid.grid.name',
                        title: '所属网格',
                    },
                    {
                        field: 'microgrid.name',
                        title: '所属微网格',
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:microgriduser:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                            @endhasPerm

                            @hasPerm("system:microgriduser:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            @endhasPerm
                                return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);

            $('#cid').on("change", function() {
                let communityId = $(this).val();
                let grid = $('#grid_id');
                let microgrid = $('#microgrid_id');
                grid.select2('destroy').empty();
                microgrid.select2('destroy').empty();
                grid.select2({data:[{id:"",text:"所属网格"}],width:200});
                microgrid.select2({data:[{id:"",text:"所属微网格"}],width:200});
                if (communityId) {
                    $.ajax({
                        url: '{{route('admin.getgridbycid')}}',
                        type: 'get',
                        dataType: 'json',
                        data: {
                            cid: communityId
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                grid.select2({data:res.data,width:200});
                            }
                        }
                    });
                }
            });
            $('#grid_id').on("change", function() {
                let grid_id = $(this).val();
                let microgrid = $('#microgrid_id');
                microgrid.select2('destroy').empty();
                microgrid.select2({data:[{id:"",text:"所属微网格"}],width:200});
                if (grid_id) {
                    $.ajax({
                        url: '{{route('admin.getmicrogridbygridid')}}',
                        type: 'get',
                        dataType: 'json',
                        data: {
                            grid_id: grid_id
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                microgrid.select2({data:res.data,width:200});
                            }
                        }
                    });
                }
            });
        });
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <a download href="{{asset('template/微网格员.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop

