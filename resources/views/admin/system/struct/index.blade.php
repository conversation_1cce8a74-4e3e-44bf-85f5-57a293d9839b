@extends('admin.layout.layout')

@include('widget.asset.treetable')
@include('widget.asset.select2')

@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li>
                        科室名称 ：<input type="text" name="like[name]" value="" autocomplete="off">
                    </li>
                    <li>科室类型：
                        <select name="where[struct_type]" class="select2">
                            <option value="">全部</option>
                            @foreach($structTypes as $key=>$value)
                                <option value="{{$key}}">{{$value}}</option>
                            @endforeach
                        </select>
                    <li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.treeTable.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:struct:add")
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("system:struct:import")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="importExcel()">
            <i class="fa fa-upload"></i> 导入
        </a>
        @endhasPerm
        @hasPerm("system:struct:export")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="exportExcel()">
            <i class="fa fa-download"></i> 导出
        </a>
        @endhasPerm
        <a class="btn btn-warning" id="expendinfobtn"><i class="fa fa-exchange"></i> 展开/折叠</a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-tree-table"></table>
    </div>
@stop

@section('script')
    <script>
        var datas = '';
        $(function() {
            var options = {
                modalName: "组织架构",
                expandAll:true,
                columns: [{
                    field: 'selectItem',
                    radio: true
                },
                    {
                        title: '科室名称',
                        field: 'name',
                    },
                    {
                        field: 'struct_type_text',
                        title: '科室类型'
                    },
                    {
                        field: 'listsort',
                        title: '排序'
                    },
                    {field: 'create_time', title: '创建时间', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间',sortable: true,visible: false},
                    {
                        title: '操作',
                        width: '20',
                        widthUnit: '%',
                        align: "left",
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:struct:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:struct:drop")
                            if(row.if_drop){
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            }
                            @endhasPerm
                            return actions.join('');
                        }
                    }]
            };
            $.treeTable.init(options);
        });
        function exportExcel() {
            $.modal.loading('正在处理中，请稍后...');
            var dataParam = new FormData(document.querySelector('#role-form'));
            var url = '{{route("system.{$app}.export")}}';
            var xhr = new XMLHttpRequest();
            xhr.open('POST', url, true);    // 也可以使用POST方式，根据接口
            xhr.responseType = "blob";    // 返回类型blob
            xhr.setRequestHeader("X-CSRF-TOKEN", $('meta[name="csrf-token"]').attr('content'));
            // xhr.setRequestHeader('content-type', 'application/json');
            xhr.onload = function () {
                // 请求完成
                if (this.status === 200) {
                    // 返回200
                    var response = this.response;
                    let contentType = xhr.getResponseHeader('Content-Type');
                    contentType = contentType != '' ? contentType : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    let blob = new Blob([response], {type: contentType});
                    const reader = new FileReader();
                    if (contentType === 'application/json' || contentType === 'application/javascript') {
                        reader.readAsText(blob, 'utf-8');
                        reader.onload = (e) => {
                            const resp = JSON.parse(reader.result);
                            if (resp.hasOwnProperty('code') && resp.code != web_status.SUCCESS) {
                                $.modal.alertError(resp.msg);
                            } else {
                                $.modal.alertError('导出失败');
                            }
                        };
                    } else {
                        const disposition = xhr.getResponseHeader('Content-Disposition');
                        let fileName;
                        let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
                        let matches = filenameRegex.exec(disposition)
                        if (matches != null && matches[1]) {
                            fileName = matches[1].replace(/['"]/g, '')
                        }
                        // 通过 URLEncoder.encode(pFileName, StandardCharsets.UTF_8.name()) 加密编码的, 使用decodeURI(fileName) 解密
                        fileName = decodeURI(fileName)
                        var urlCreator = window.URL || window.webkitURL;
                        var url = urlCreator.createObjectURL(blob); //这个函数的返回值是一个字符串，指向一块内存的地址。
                        //以下代码保存我的excel导出文件
                        var link = document.createElement('a'); //创建事件对象
                        link.setAttribute('href', url);
                        link.setAttribute("download", fileName);
                        var event = document.createEvent("MouseEvents"); //初始化事件对象
                        event.initMouseEvent("click", true, true, document.defaultView, 0, 0, 0, 0, 0, false, false, false, false, 0, null); //触发事件
                        link.dispatchEvent(event);
                    }
                } else {
                    $.modal.alertError('导出失败');
                }
                $.modal.closeLoading();
            };
            // 发送ajax请求
            xhr.send(dataParam);
        }
        function importExcel() {
            $.modal.layerinit(function (layer) {
                layer.open({
                    type: 1,
                    area: ['400px', '260px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: '导入科室',
                    content: $('#importTpl').html(),
                    btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    btn1: function (index, layero) {
                        var file = layero.find('#file').val();
                        if (file == '' && !$.common.endWith(file, '.xlsx', 'i')) {
                            $.modal.msgWarning("请选择后缀为 xlsx”的文件。");
                            return false;
                        }
                        var index = layer.load(2, {shade: false});
                        $.modal.disable();
                        var formData = new FormData(layero.find('form')[0]);
                        $.ajax({
                            url: '{{route("system.{$app}.import")}}',
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            type: 'POST',
                            success: function (result) {
                                if (result.code == web_status.SUCCESS) {
                                    $.modal.closeAll();
                                    if (result.data.errorNum > 0) {
                                        $.modal.layerinit(function (layer) {
                                            layer.alert(result.msg, {
                                                icon: $.modal.icon('warning'),
                                                title: "系统提示",
                                                btn: ['下载', '取消'],
                                                btnclass: ['btn btn-primary'],
                                            },function (index) {
                                                window.open(result.data.error_url)
                                            });
                                        });
                                    } else {
                                        $.modal.alertSuccess(result.msg);
                                        $.table.refresh();
                                    }
                                } else if (result.code == web_status.WARNING) {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertWarning(result.msg)
                                } else {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertError(result.msg);
                                }
                            },
                            complete: function () {
                                layero.find("#file").val("")
                            }
                        });
                    }
                });
            });
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <a download href="{{asset('template/科室.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop
