@extends('admin.layout.form')
@include('widget.asset.select2')
@section('content')
<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="parent_id" id="treeId" value="{{$root_id}}">

    <div class="form-group">
        <label class="col-sm-3 control-label is-required">上级组织：</label>
        <div class="col-sm-8">
            <div class="input-group">
                <input type="text" id="treeName" value="{{$root_name}}" class="form-control" placeholder="请选择上级组织" readonly autocomplete="off"/>
                <span class="input-group-addon"><i class="fa fa-search"></i></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">科室名称：</label>
        <div class="col-sm-8">
            <input type="text" name="name" value="" class="form-control" placeholder="请输入科室名称" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">科室类型：</label>
        <div class="col-sm-8">
            @foreach($structTypes as $k=>$v)
                <label class="radio-box">
                    <input type="radio" value="{{$k}}" required name="struct_type">{{$v}}
                </label>
            @endforeach
        </div>
    </div>
    <div class="form-group" style="display:none;" id="community_code">
        <label class="col-sm-3 control-label is-required">社区编码：</label>
        <div class="col-sm-8">
            <input type="text" name="code" value="" class="form-control" placeholder="请输入社区编码" required autocomplete="off"/>
            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 同步清单制需要</span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">显示顺序：</label>
        <div class="col-sm-8">
            <input type="number" name="listsort" value="0" class="form-control" placeholder="请输入显示顺序" required autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            //选择组织树
            $("#treeName").click(function () {
                var treeId = $("#treeId").val();
                var url=urlcreate("{{route('system.struct.tree')}}","id=" + treeId);
                var options = {
                    title: '组织选择',
                    width: "380",
                    url: url,
                    callBack: doSubmit
                };
                $.modal.openOptions(options);
            });
            $('input[name=struct_type]').on('ifChecked', function () {
                if (this.value == {{\App\Extends\Services\System\StructService::TYPE_COMMUNITY}}) {
                    $('#community_code').show();
                } else {
                    $('#community_code').hide();
                }
            })
        });
        function doSubmit(index, layero){
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
        }
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
