@extends('admin.layout.layout')

@section('content')
<div class="col-sm-12 search-collapse">
    <form id="role-form">
        <div class="select-list">
            <ul>
                <li>ID：<input type="text" name="where[id]" value=""></li>
                <li class="select-time">
                    <label>创建时间： </label>
                    <input type="text" name="between[create_time][start]" id="startTime" placeholder="开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="between[create_time][end]" id="endTime" placeholder="结束时间" readonly>
                </li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("system:kaoqinapprovallevels:add")
    <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
    @endhasPerm

    @hasPerm("system:kaoqinapprovallevels:edit")
    <a class="btn btn-primary single disabled" onclick="$.operate.edit('',this)"> 修改</a>
    @endhasPerm

    @hasPerm("system:kaoqinapprovallevels:dropall")
    <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll(this)"><i class="fa fa-trash"></i> 批量删除</a>
    @endhasPerm
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "考勤审批层级设计",
                sortName:'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true},
                    {field: 'type', title: '考勤人员类型:1=网格员,2=城管队员,3=消防队员4=综治人员', align: 'center'},
                    {field: 'request_type', title: '申请类型:1=请假,2=加班,3=外出', align: 'center'},
                    {
                        field: 'name',
                        title: '名称',
                        sortable: true,
                        formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }
                    },
                    {field: 'description', title: '描述', align: 'center'},
                    {field: 'approver_ids', title: '审批人Ids', align: 'center'},
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("system:kaoqinapprovallevels:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                        @endhasPerm

                        @hasPerm("system:kaoqinapprovallevels:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

