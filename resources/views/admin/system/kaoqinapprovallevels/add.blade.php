@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.select2")

@section('content')
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">考勤人员类型:1=网格员,2=城管队员,3=消防队员4=综治人员</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" value="{{$type}}">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">审批人类型</label>
        <div class="col-sm-8">
            <select name="approver_type" id="approver_type" class="select2 form-control" required>
                <option value="">请选择审批人类型</option>
                <option value="1">选择社区人员</option>
                <option value="2">选择街道人员</option>
                <option value="3">选择社区负责人</option>
                <option value="4">选择分管领导</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">审批人员</label>
        <div class="col-sm-8">
            <select name="approver_ids" id="approver_ids" class="select2" required multiple>

            </select>
        </div>
    </div>

</form>
@stop

@section('script')
    <script>
        $(function(){
            let approver_ids = $('#approver_ids');
            approver_ids.select2({data:[],width:530});
            $('#approver_type').on("change", function() {
                let approver_type = $(this).val();
                let url = '{{route('admin.getgridbycid')}}';
                approver_ids.select2('destroy').empty();
                approver_ids.select2({data:[],width:530});
                if (approver_type) {
                    $.ajax({
                        url: url,
                        type: 'get',
                        dataType: 'json',
                        data: {
                            cid: approver_type
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                approver_ids.select2({data:res.data,width:530});
                            }
                        }
                    });
                }
            });
        });

        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
