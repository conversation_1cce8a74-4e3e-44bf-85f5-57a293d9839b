@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}

@section('content')
<style>

    h4{
        /*line-height: 40px;*/
        position: relative;
        padding-left: 15px; /* 确保文字与竖线有间距 */
        margin-bottom: 10px;
        font-size: 15px;
        font-weight: normal;
    }
    h4::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px; /* 竖线宽度 */
        background-color: #0076F6; /* 竖线颜色 */
    }

    .liuchengtu{
        margin: 20px 10px;
        display: flex;
        align-items: center;
    }
    .liuchengtu .block {
        padding: 10px;
        border-radius: 5px;
        background-color: #F2F2F2;
    }
    .glyphicon{
        font-size: 20px;
    }
</style>
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <h4>加班</h4>
        <div class="liuchengtu">
            <div class="block">提交申请</div>
            <div class="glyphicon glyphicon-arrow-right mr20 ml20"></div>
            <a class="btn btn-xs" onclick="add(1);">添加</a>
        </div>
    </div>
    <div class="form-group">
        <h4>外出</h4>
        <div class="liuchengtu">
            <div class="block">提交申请</div>
            <div class="glyphicon glyphicon-arrow-right mr20 ml20"></div>
            <a class="btn btn-xs">添加</a>
        </div>
    </div>
    <div class="form-group">
        <h4>请假</h4>
        <div class="liuchengtu">
            <div class="block">提交申请</div>
            <div class="glyphicon glyphicon-arrow-right mr20 ml20"></div>
            <a class="btn btn-xs">添加</a>
        </div>
    </div>

</form>
@stop

@section('script')
    <script>
        function add(request_type) {
            var request_type_text = '';
            switch (request_type){
                case 1:
                    request_type_text = '加班';
                    break;
                case 2:
                    request_type_text = '外出';
                    break;
                case 3:
                    request_type_text = '请假';
                    break;
            }
            $.modal.open('添加审批层级-'+request_type_text,'{{route('system.kaoqinapprovallevels.add')}}?request_type='+request_type+'&type={{$type}}',600,400,function () {
                // 刷新父页面
            })
        }
    </script>
@stop
