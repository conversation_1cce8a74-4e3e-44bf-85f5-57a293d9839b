@extends('admin.layout.layout')

@section('content')
    <div class="mt10" style="background-color: #fff;padding: 10px">
        <div class="bs-example bs-example-tabs b5navtab" data-example-id="togglable-tabs">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item active" role="presentation">
                    <a class="nav-link" id="home-tab" data-toggle="tab" href="#1" role="tab" aria-controls="detail" aria-selected="true">网格员</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#2" role="tab" aria-controls="log" aria-selected="false">城管队员</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#3" role="tab" aria-controls="log" aria-selected="false">消防队员</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#4" role="tab" aria-controls="log" aria-selected="false">综治人员</a>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane active" id="jiaban" role="tabpanel" aria-labelledby="detail-tab">
                    <iframe class="RuoYi_iframe" id="myIframe" src="{{route('system.kaoqinapprovallevels.config')}}?type=1" frameborder="0" width="100%" height="750px"></iframe>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            $('a[data-toggle="tab"]').on('click', function (e) {
                e.preventDefault(); // 阻止默认的锚点跳转行为
                var tab = $(this).attr('href'); // 获取被点击的标签的 href
                var iframe = $('#myIframe'); // 获取 iframe 元素

                // 根据不同的标签设置不同的 iframe src
                if (tab === '#1') {
                    iframe.attr('src', '{{route('system.kaoqinapprovallevels.config')}}?type=1');
                } else if (tab === '#2') {
                    iframe.attr('src', '{{route('system.kaoqinapprovallevels.config')}}?type=2');
                } else if (tab === '#3') {
                    iframe.attr('src', '{{route('system.kaoqinapprovallevels.config')}}?type=3');
                } else if (tab === '#4') {
                    iframe.attr('src', '{{route('system.kaoqinapprovallevels.config')}}?type=4');
                }
                // 激活 Bootstrap 标签
                $(this).tab('show');
            });
        });
    </script>
@stop

