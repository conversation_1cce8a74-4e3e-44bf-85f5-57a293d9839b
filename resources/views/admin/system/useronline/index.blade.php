@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <style>
        .select-list li li{
            margin: 0px 15px -4px 0px;
        }
        .nav_item{
            margin-left: 12px;
            display: flex;
            flex-shrink: 0;
            flex-direction: row;
            background-color: #F1F8FE;
            /*border: 1px solid  #ddd;*/
            color: #444;
            border-radius: 6px;
            align-items: center;
            padding: 5px  10px;
            box-sizing: border-box;
        }
        .select-change {
            color: #2256ff;
            background-color: #DEE6FF;
            border: 1px solid #2256ff;
        }
    </style>
<div class="col-sm-12 search-collapse">
    <form id="role-form">
        <div class="select-list">
            <ul>
                <li> <select name="date_type" class="select2" id="date_type">
                        <option value="1" selected>按天</option>
                        <option value="2">按周</option>
                        <option value="3">按月</option>
                        <option value="4">自定义</option>
                    </select></li>
                <li class="date_type" id="date_type_1">
                    <input type="text" name="date_type_1" id="data_type_day" readonly value="{{date('Y-m-d')}}"/>
                </li>
                <li class="date_type" id="date_type_2" style="display: none">
                    <input type="text" name="date_type_2" id="data_type_week"  readonly value="{{date('Y-m-d', strtotime('monday this week'))}} ~ {{date('Y-m-d', strtotime('sunday this week'))}}" style="width: 200px;" />
                </li>
                <li class="date_type" id="date_type_3" style="display: none">
                    <input type="text" name="date_type_3" id="data_type_month"  readonly value="{{date('Y-m')}}" />
                </li>
                <li class="date_type" id="date_type_4" style="display: none">
                    <div class="layui-inline" id="test6">
                        <div class="layui-input-inline">
                            <input type="text" name="data_type_start" value="{{date('Y-m-d', strtotime('-1 month'))}}" autocomplete="off" id="test-startDate-1" class="layui-input" placeholder="开始日期">
                        </div>
                        <div class="layui-input-inline">
                            <input type="text" name="data_type_end" value="{{date('Y-m-d')}}" autocomplete="off" id="test-endDate-1" class="layui-input" placeholder="结束日期">
                        </div>
                    </div>
                </li>
                <li>
                    <select name="source" id="source"  class="select2" multiple data-width="300px">
                        <option value="1" selected>PC端</option>
                        <option value="2" selected>调度端</option>
                        <option value="3" selected>业务端</option>
                    </select>
                <li>
                <li>
                    <select name="struct_id" class="select2">
                        <option value="">全部</option>
                        @foreach($structs as $k=>$v)
                            <option value="{{$k}}">{{$v}}</option>
                        @endforeach
                    </select>
                <li>
                <li><input type="text" name="keywords" value="" placeholder="搜索社区、科室名称" autocomplete="off"></li>

                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("system:{$app}:export")
    <a href="javascript:void(0)" class="btn btn-warning" onclick="exportExcel()">
        <i class="fa fa-download"></i> 导出
    </a>
    @endhasPerm
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            layui.use('laydate', function() {
                var laydate = layui.laydate;
                //日期范围
                laydate.render({
                    elem: '#test6'
                    ,max:'{{date('Y-m-d')}}'
                    ,btns: ['confirm']
                    //设置开始日期、日期日期的 input 选择器
                    //数组格式为 2.6.6 开始新增，之前版本直接配置 true 或任意分割字符即可
                    ,range: ['#test-startDate-1', '#test-endDate-1']
                });
            })
            layui.use('laydate', function(){
                var laydate = layui.laydate;

                laydate.render({
                    elem: '#data_type_day',
                    max:'{{date('Y-m-d')}}',
                    weekStart: 1,
                    isPreview: false,
                    btns: ['now', 'confirm'],
                });
                laydate.render({
                    elem: '#data_type_week',
                    // theme: '#da7606',
                    type: 'date',
                    max: '{{date('Y-m-d')}}',
                    format: "yyyy-MM-dd ~ yyyy-MM-dd",
                    weekStart: 1,
                    isPreview: false,
                    btns: ['now', 'confirm'],
                    done: function (value, date, endDate) {
                        if (value != "" && value.length > 0) {
                            let today = new Date(value.substring(0, 10));
                            let weekday = today.getDay();
                            let monday;
                            let sunday;
                            if (weekday == 0) {
                                monday = new Date(1000 * 60 * 60 * 24 * (weekday - 6) + today.getTime());
                            } else {
                                monday = new Date(1000 * 60 * 60 * 24 * (1 - weekday) + today.getTime());
                            }
                            if (weekday == 0) {
                                sunday = today;
                            } else {
                                sunday = new Date(1000 * 60 * 60 * 24 * (7 - weekday) + today.getTime());
                            }
                            let month = monday.getMonth() + 1;
                            if (month < 10) {
                                month = "0" + month;
                            }
                            let day1 = monday.getDate();
                            if (day1 < 10) {
                                day1 = "0" + day1;
                            }
                            let start = "" + monday.getFullYear() + "-" + month + "-" + day1;
                            let month2 = sunday.getMonth() + 1;
                            if (month2 < 10) {
                                month2 = "0" + month2;
                            }
                            let day2 = sunday.getDate();
                            if (day2 < 10) {
                                day2 = "0" + day2;
                            }
                            let end = "" + sunday.getFullYear() + "-" + month2 + "-" + day2;
                            $('#data_type_week').val(start + " ~ " + end);
                        } else {
                            $('#data_type_week').val('');
                        }
                    }
                });
                laydate.render({
                    elem: '#data_type_month',
                    type: 'month',
                    max: '{{date('Y-m')}}',
                    weekStart: 1,
                    isPreview: false,
                    btns: ['now', 'confirm'],
                });
            });
            $('#date_type').change(function () {
                var type = $(this).val();
                $('.date_type').hide();
                $('#date_type_'+type).show();
            });
            var extend = {user_count:0,login_num:0,online_time:0,online_time_avg:0,avg_user_count:0,avg_login_num:0}
            var options = {
                modalName: "用户数据",
                sortName:'user_count',
                sortOrder: "desc",
                showFooter: true,
                columns: [
                    {title: '序号', align: 'left', formatter: function(value, row, index) {
                            return $.table.serialNumber(index);
                        }},
                    {field: 'name', title: '科室/社区名称', align: 'left',footerFormatter:function (value,a,b,c,d) {
                            return "累计";
                        }},
                    {field: 'user_count', title: '登录用户数', align: 'left', sortable: true,footerFormatter:function (value) {
                            return extend.user_count;
                        }},
                    {field: 'login_num', title: '登录次数', align: 'left', sortable: true,footerFormatter:function (value) {
                            return extend.login_num;
                        }},
                    {field: 'avg_user_count', title: '日均登录用户数', align: 'left', sortable: true,footerFormatter:function (value) {
                            return extend.avg_user_count;
                        }},
                    {field: 'avg_login_num', title: '日均登录次数', align: 'left', sortable: true,footerFormatter:function (value) {
                            return extend.avg_login_num;
                        }},
                    {field: 'online_time', title: '总在线时长(小时)', align: 'left', sortable: true,visible: false,footerFormatter:function (value) {
                            return extend.online_time;
                        }},
                    {field: 'online_time_avg', title: '平均每人在线时长(小时)', align: 'left', sortable: true,visible: false,footerFormatter:function (value) {
                            return extend.online_time_avg;
                        }},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:useronline:detail")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="detail(\'' + row.id + '\')"> 详细数据</a> ');
                            @endhasPerm
                            return actions.join('');
                        },
                        footerFormatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:useronline:detail")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="detail()"> 详细数据</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ],
                responseHandler: function(res) {
                    if (res.code == web_status.SUCCESS) {
                        extend  = res.extend;
                    } else {
                        extend = {user_count:0,login_num:0,online_time:0,online_time_avg:0,avg_user_count:0,avg_login_num:0}
                    }
                }
            };
            $.table.init(options);
        });
        function tableSearchReset(){
            $("#source").val([1,2,3]).trigger('change');
        }
        function detail(id = '') {
            var dataParam = $.common.formToJSON("role-form")
            var params = [];
            $.each(dataParam, function (key, value) {
                params.push(key + '=' + value);
            });
            return $.modal.openTab("详细数据", '{{route('system.useronline.detail')}}?id=' + id + '&' + params.join('&'));
        }
        function exportExcel() {
            $.modal.open("导出用户数据", '{{route('system.useronline.export')}}', '500', '500');
        }
    </script>

@stop

