@extends('admin.layout.form')
@include('widget.asset.select2')
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-3 col-xs-3 control-label">月份：</label>
            <div class="col-sm-8 col-xs-8">
                <input type="text" name="month" readonly value="{{date('Y-m')}}" class="form-control time-input" data-btn="now|confirm" data-type="month" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 col-xs-3 control-label">数据范围：</label>
            <div class="col-sm-8 col-xs-8">
                <label class="radio-box"><input type="radio" name="data_range" required value="1" checked/> 累计</label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3  col-xs-3 control-label">数据指标：</label>
            <div class="col-sm-8 col-xs-8">
                <label class="check-box"><input type="checkbox" checked name="target[]" value="1" required>登录用户数</label>
                <label class="check-box"><input type="checkbox" checked name="target[]" value="2" required>登录次数</label>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {

        });
        function submitHandler() {
            if ($.validate.form()) {
                $.modal.loading('正在处理中，请稍后...');
                $.modal.disable();
                var url = '{{route('system.useronline.export')}}';
                var xhr = new XMLHttpRequest();
                xhr.open('POST', url);    // 也可以使用POST方式，根据接口
                xhr.responseType = "blob";    // 返回类型blob
                xhr.setRequestHeader("X-CSRF-TOKEN", $('meta[name="csrf-token"]').attr('content'));
                xhr.setRequestHeader('x-requested-with', 'XMLHttpRequest');
                xhr.onload = function () {
                    // 请求完成
                    if (this.status === 200) {
                        // 返回200
                        var response = this.response;
                        let contentType = xhr.getResponseHeader('Content-Type');
                        contentType = contentType != '' ? contentType : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                        let blob = new Blob([response], {type: contentType});
                        const reader = new FileReader();
                        if (contentType === 'application/json' || contentType === 'application/javascript') {
                            reader.readAsText(blob, 'utf-8');
                            reader.onload = (e) => {
                                const resp = JSON.parse(reader.result);
                                if (resp.hasOwnProperty('code') && resp.code != web_status.SUCCESS) {
                                    $.modal.alertError(resp.msg);
                                } else {
                                    $.modal.alertError('导出失败');
                                }
                                $.modal.enable();
                            };
                        } else {
                            const disposition = xhr.getResponseHeader('Content-Disposition');
                            let fileName;
                            let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
                            let matches = filenameRegex.exec(disposition)
                            if (matches != null && matches[1]) {
                                fileName = matches[1].replace(/['"]/g, '')
                            }
                            // 通过 URLEncoder.encode(pFileName, StandardCharsets.UTF_8.name()) 加密编码的, 使用decodeURI(fileName) 解密
                            fileName = decodeURI(fileName)
                            var urlCreator = window.URL || window.webkitURL;
                            var url = urlCreator.createObjectURL(blob); //这个函数的返回值是一个字符串，指向一块内存的地址。
                            //以下代码保存我的excel导出文件
                            var link = document.createElement('a'); //创建事件对象
                            link.setAttribute('href', url);
                            link.setAttribute("download", fileName);
                            var event = document.createEvent("MouseEvents"); //初始化事件对象
                            event.initMouseEvent("click", true, true, document.defaultView, 0, 0, 0, 0, 0, false, false, false, false, 0, null); //触发事件
                            link.dispatchEvent(event);
                            $.modal.close();
                            $.modal.enable();
                        }
                    } else {
                        $.modal.alertError('导出失败');
                        $.modal.close();
                        $.modal.enable();
                    }
                    $.modal.closeLoading();
                };
                var formData = new FormData(document.getElementById('form-add'));
                // 发送ajax请求
                xhr.send(formData);
            }
        }
    </script>
@stop
