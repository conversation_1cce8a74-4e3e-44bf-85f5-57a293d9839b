@extends('admin.layout.full')
@include('widget.asset.select2')
@section('content')

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="role-form">
                    <input type="hidden" name="structId" id="structId" value="">
                    <div class="select-list">
                        <ul>
                            <li><input type="text" placeholder="请输入姓名" name="like[username]" value="" autocomplete="off"></li>
                            <li><input type="text" placeholder="请输入电话" name="like[username]" value="" autocomplete="off"></li>
                            <li>
                                <select name="where[user_type]" style="min-width: 100px" class="select2">
                                    <option value="">用户类型</option>
                                    @foreach($userTypes as $user_typ=>$type)
                                        <option value="{{$user_typ}}">{{$type}}</option>
                                    @endforeach
                                </select>
                            <li>
                            <li>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">

            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
    <script>
        var ids = "{{$user_ids}}";
        if(ids) ids = ids.split(',');
        $(function () {
            getUserList();
        });
        function getUserList() {
            var options = {
                modalName: "人员",
                url:"{{route('system.user.ajaxtreelist')}}",
                sortName:'id',
                sortOrder:'asc',
                showToolbar:false,
                singleSelect : @if($mult == '0') false @else true @endif,
                columns: [
                    {
                        checkbox: true,
                        formatter: function (value, row, index) {
                            if(ids && ids.indexOf(row.id.toString())>-1){
                                return {
                                    checked: true//设置选中
                                };
                            }
                            return value;
                        }
                    },
                    {field: 'id', title: '用户编号', align: 'left', visible: false},
                    {field: 'username', align: 'left', title: '姓名'},
                    {field: 'mobile', align: 'left', title: '电话'},
                    {field: 'user_type_text', align: 'left', title: '身份'},
                ]
            };
            $.table.init(options);
        }

        //点击行选中
        $("#bootstrap-table").on("click-row.bs.table",function(e,row,ele){
            $(this).bootstrapTable('check', ele.data('index'))
        });

        //获取选择的行，数组形式
        function getCheckRows(){
            return $('#bootstrap-table').bootstrapTable('getSelections');
        }
    </script>
@stop

