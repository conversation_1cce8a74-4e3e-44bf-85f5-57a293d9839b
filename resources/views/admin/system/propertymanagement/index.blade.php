@extends('admin.layout.layout')

@section('content')
<div class="col-sm-12 search-collapse">
    <form id="role-form">
        <div class="select-list">
            <ul>
                <li>名称：<input type="text" name="like[name]" value=""></li>
                <li class="select-time">
                    <label>创建时间： </label>
                    <input type="text" name="between[create_time][start]" id="startTime" placeholder="开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="between[create_time][end]" id="endTime" placeholder="结束时间" readonly>
                </li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("system:propertymanagement:add")
    <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
    @endhasPerm

    @hasPerm("system:propertymanagement:edit")
    <a class="btn btn-primary single disabled" onclick="$.operate.edit('',this)"> 修改</a>
    @endhasPerm

</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "物业公司",
                sortName:'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true},
                    {field: 'create_time', title: '添加时间', align: 'left', sortable: true},
                    {
                        field: 'name',
                        title: '物业公司名称',
                        sortable: true,
                        formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }
                    },
                    {field: 'user_count', title: '绑定人员数', align: 'left',formatter: function (value, row, index) {
                            @hasPerm("user:usersproperty:index")
                                value = '<a href="javascript:;" onclick="usersProperty('+row.id+')">'+value+'</a>';
                            @endhasPerm
                                return value;
                        }},
                    {
                        field: 'note',
                        title: '备注',
                        sortable: true,
                        formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }
                    },
                    {field: 'listsort', title: '排序值', align: 'left', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("system:propertymanagement:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                        @endhasPerm

                        @hasPerm("system:propertymanagement:drop")
                            if (row.user_count == 0) {
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            }
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function usersProperty(id) {
            $.modal.openTab('物业人员','{{route('user.usersproperty.index')}}?property_id='+id, true);
        }
    </script>
@stop

