@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}


@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">名称：</label>
        <div class="col-sm-8">
            <input type="text" name="name" value="{{$info['name']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
{{--    <div class="form-group">--}}
{{--        <label class="col-sm-3 control-label is-required">状态：</label>--}}
{{--        <div class="col-sm-8">--}}
{{--            <label class="radio-box">--}}
{{--                <input type="radio" name="status" required value="0" @if(0 == $info['status']) checked @endif/> 禁用--}}
{{--            </label>--}}
{{--            <label class="radio-box">--}}
{{--                <input type="radio" name="status" required value="1" @if(1 == $info['status']) checked @endif /> 正常--}}
{{--            </label>--}}
{{--        </div>--}}
{{--    </div>--}}
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">排序值：</label>
        <div class="col-sm-8">
            <input type="number" name="listsort" value="{{$info['listsort']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">备注：</label>
        <div class="col-sm-8">
            <input type="text" name="note" value="{{$info['note']}}" class="form-control" autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-edit").validate();

        {{--$("#XX").val(["{{$info['xx']}}"]).trigger("change");--}}

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
