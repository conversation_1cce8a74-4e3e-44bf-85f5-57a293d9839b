@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}
@section('css_common')
    <style>
        .label-item{
            padding-bottom: 45px;
        }
    </style>
@append
@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    @if ($info['parent_id'] == 0)
    <div class="form-group">
        <label class="col-xs-4 control-label is-required" style="padding-top: 7px;padding-right:0;text-align: right;">一级类别：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <input type="text" name="name" value="{{$info['name']}}" placeholder="请输入" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    @else
        <div class="form-group">
            <label class="col-xs-4 control-label" style="padding-top: 7px;padding-right:0;text-align: right;">一级类别：</label>
            <div class="col-xs-5" style="padding-left: 0;">
                <div class="form-control-static">{{$info['parent']['name'] ?? ''}}</div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-4 control-label is-required" style="padding-top: 7px;padding-right:0;text-align: right;">二级类别：</label>
            <div class="col-xs-5" style="padding-left: 0;">
                <input type="text" name="name" value="{{$info['name']}}" placeholder="请输入" class="form-control" required autocomplete="off"/>
            </div>
        </div>
    @endif
</form>
@stop

@section('script')
    <script>
        $("#form-edit").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
