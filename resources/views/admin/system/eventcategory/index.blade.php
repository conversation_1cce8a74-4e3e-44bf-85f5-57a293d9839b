@extends('admin.layout.layout')

@include('widget.asset.treetable')
@include('widget.asset.select2')

@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li>
                        类别名称 ：<input type="text" name="like[name]" value="" autocomplete="off">
                    </li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.treeTable.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:eventcategory:add")
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        <a class="btn btn-warning" id="expendinfobtn"><i class="fa fa-exchange"></i> 展开/折叠</a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-tree-table"></table>
    </div>
@stop

@section('script')
    <script>
        var datas = '';
        $(function() {
            var options = {
                modalName: "事件类别",
                expandAll:true,
                columns: [{
                    field: 'selectItem',
                    radio: true
                },
                    {
                        title: '类别名称',
                        field: 'name',
                    },
                    {field: 'create_time', title: '创建时间', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间',sortable: true,visible: false},
                    {
                        title: '操作',
                        align: "left",
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:eventcategory:edit")
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:eventcategory:add")
                                if (!row.parent_id) {
                                    actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.add(this, \'' + row.id + '\')"> 新增</a> ');
                                }
                            @endhasPerm
                            @hasPerm("system:eventcategory:drop")
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }]
            };
            $.treeTable.init(options);
        });
    </script>
@stop
