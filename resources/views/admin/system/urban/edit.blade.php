@extends('admin.layout.form')
@include('widget.asset.select2')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">姓名：</label>
            <div class="col-sm-8">
                <input type="text" name="username" value="{{$info['username']}}" class="form-control" placeholder="请输入" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">手机号：</label>
            <div class="col-sm-8">
                <input type="text" name="mobile" value="{{$info['mobile']}}" class="form-control" placeholder="请输入" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 手机号为app的登录账号</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">登录密码：</label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="password" name="password" value="" class="form-control" style="border-right: none !important;" placeholder="请输入"  autocomplete="off"/>
                    <div class="input-group-addon">
                        <img class="pwdeye" style="cursor: pointer;height: 16px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUdwTNHR0dra2tPT09HR0dHR0dHR0dPT09HR0dLS0tHR0dLS0tHR0dDQ0HhB6yAAAAANdFJOUwDnCzD0osoddluJRLXFpER1AAABoUlEQVRIx+1Uv0vDQBQ+U7TFdrAURIRAoVLQqS2uLjqpBFyqU0ALokuhi4KKoIuD4uIiHQQVQZdCB0HH/gMOwbTa0u9/8S65H4lNMjk45Bsu73v33bt3792FkBgxYvwp0rfR80dtOlzb7cgYxjcddaxHiV5h0bGOqFBpAz36WQRWw0V3wAr9JAzgLEyTAuwSM8pANx+s0Uwg51gJHdgLFh0CVkmaduCGKUMt1z6AYWNUk2R+mUgRwNpIWtoTddckpSuAmV8q7Zx5h4KOAzR3zPp2TD5QlwVscN4Cmkw1vFGaeRbdagIDWbAuWTBY8P0313VywZj9SExRaFr5DiEHcDBdfbms7rj2FiEZoO+0EOixnI91+GA9s+zrQNttcE2lIcETLLoNNmHxY415RVP8kDpNmIly/ERLGLxvZg07u3vVwid3lh1RWbSQqBmm51ZCZzG0ipipiz3Yzj1hFnydSAL3wp4EGmG3UF72dNhtnYCtiIHtQNGpcw4OE18hD6MfQoh38bIic96wPlFHkUyISFVV1jDgkVW8rJCPf7gx/jt+AKid4q/Um1olAAAAAElFTkSuQmCC">
                    </div>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 不填则表示不修改密码，密码应该包含大写字母、小写字母、数字和特殊符号中的至少三种种类,并且密码长度6-15位！</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">所属分组：</label>
            <div class="col-sm-8">
                <select name="group_id" required class="select2">
                    <option value="">请选择</option>
                    @foreach($urbanGroup as $item)
                        <option value="{{$item['id']}}" @if($item['id'] == $info['group_id']) selected @endif >{{$item['name']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">身份：</label>
            <div class="col-sm-8">
                @foreach($urbanIdentity as $key=>$item)
                    <label class="radio-box">
                        <input type="radio" value="{{$key}}" required name="identity" @if($key == $info['identity']) checked @endif>{{$item}}
                    </label>
                @endforeach
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {
            $('.pwdeye').click(function (){
                var pwd = $(this).parents('.input-group').find('input');
                var t = pwd.attr('type');
                if (t == 'password') {
                    pwd.attr('type','text');
                    $(this).attr('src', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADUAAAAuCAMAAAC/KaEaAAAAOVBMVEVHcEzNzc3Nzc3Nzc3Q0NDNzc3MzMzNzc3Nzc3V1dXNzc3Nzc3MzMz////r6+vh4eHx8fH+/v7S0tLiR/+pAAAADHRSTlMAYZKBI63xxt4NPlNOUVuQAAABOklEQVRIx+VVyZaEIAwUZNXI9v8fO6O20wQC+ui5dd30pbJUQjJN3wO7cqmNAmU0Z+IRRcwaEAxf7jirBAKa9TiLgQZMkyckdKDpApmCLtRMkDgySd6FEJxP+U9pS7GRcClsF0LO07ZDcnHL4HJRUHFIh7BhhAaN90iY9k5yRekdhjE67/0r1Zgnqa+i8t6miIp5+cglmYmiTjP/9+0rReAYywU1KpZGh5uIpsuWoXyV0JFy9IBzFFCxAlSaIpayxSS5yuR0hAr7DWZuHFPhzWAsUbOIulxR15iGg/0am43JKmIOzyQTMYecmvnT+ZbPPBL1/VTk8/dl7Idvubc3YntvVDvq4kW8o8R/7MN998INZnrP64E9vzeueVNU7xZZpuk7ZO9uJS8CKrk8O7KMS7MPp9Ly4V3+avwAZ4tEuL3lSDsAAAAASUVORK5CYII=');
                } else {
                    pwd.attr('type','password');
                    $(this).attr('src', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUdwTNHR0dra2tPT09HR0dHR0dHR0dPT09HR0dLS0tHR0dLS0tHR0dDQ0HhB6yAAAAANdFJOUwDnCzD0osoddluJRLXFpER1AAABoUlEQVRIx+1Uv0vDQBQ+U7TFdrAURIRAoVLQqS2uLjqpBFyqU0ALokuhi4KKoIuD4uIiHQQVQZdCB0HH/gMOwbTa0u9/8S65H4lNMjk45Bsu73v33bt3792FkBgxYvwp0rfR80dtOlzb7cgYxjcddaxHiV5h0bGOqFBpAz36WQRWw0V3wAr9JAzgLEyTAuwSM8pANx+s0Uwg51gJHdgLFh0CVkmaduCGKUMt1z6AYWNUk2R+mUgRwNpIWtoTddckpSuAmV8q7Zx5h4KOAzR3zPp2TD5QlwVscN4Cmkw1vFGaeRbdagIDWbAuWTBY8P0313VywZj9SExRaFr5DiEHcDBdfbms7rj2FiEZoO+0EOixnI91+GA9s+zrQNttcE2lIcETLLoNNmHxY415RVP8kDpNmIly/ERLGLxvZg07u3vVwid3lh1RWbSQqBmm51ZCZzG0ipipiz3Yzj1hFnydSAL3wp4EGmG3UF72dNhtnYCtiIHtQNGpcw4OE18hD6MfQoh38bIic96wPlFHkUyISFVV1jDgkVW8rJCPf7gx/jt+AKid4q/Um1olAAAAAElFTkSuQmCC');
                }
            });
        });
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
