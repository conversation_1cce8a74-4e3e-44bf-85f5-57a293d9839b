@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li>角色名称：<input type="text" name="like[name]"></li>
                    <li>状态：
                        <select name="where[status]" class="select2">
                            <option value="">全部</option>
                            <option value="1">启用</option>
                            <option value="0">停用</option>
                        </select>
                    <li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:managerole:add")
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        var root_id = @json($systemRoleIds);
        $(function () {
            var options = {
                modalName: "业务端角色",
                sortName:'listsort',
                sortOrder:'asc',
                columns: [
                    {
                        checkbox: true,
                        visible: false,
                        formatter:function(value,row,index){
                            if($.inArray(row.id, root_id) !== -1) return {disabled : true};
                        }
                    },
                    {field: 'id', title: '角色ID', align: 'left', sortable: true, visible: false},
                    {field: 'name', title: '角色名称'},
                    {field: 'user_count', title: '人数', formatter: function (value, row, index) {
                            @hasPerm("system:appuserrole:index")
                            if($.inArray(row.id, root_id) !== -1) {
                                return value;
                            } else {
                                return '<a href="javascript:void(0)" onclick="adminRole(\'' + row.id + '\')">' + value + '</a>'
                            }
                            @endhasPerm
                                return value;
                        }},
                    {field: 'listsort', title: '显示顺序',align: 'left', sortable: true},
                    {
                        title: '状态',
                        field: 'status',
                        align: 'left',
                        sortable: true,
                        formatter: function (value, row, index) {
                            @hasPerm("system:approle:setstatus")
                            if($.inArray(row.id, root_id) !== -1) {
                                return $.view.statusShow(row,false, ['停用','启用']);
                            } else {
                                return $.view.statusTools(row,true, ['停用','启用']);
                            }
                            @endhasPerm
                            return $.view.statusShow(row,false, ['停用','启用']);
                        }
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {field: 'note', title: '备注',visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:approle:edit")
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:approle:drop")
                                if($.inArray(row.id, root_id) === -1){
                                    actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                                }
                            @endhasPerm
                            @hasPerm("system:approle:auth")
                                actions.push("<a class='btn btn-xs' href='javascript:;' onclick='authMenu(" + row.id + ")'> 设置权限</a> ");
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        /* 角色管理-菜单授权 */
        function authMenu(roleId) {
            var url = "{{route('system.approle.auth')}}";
            url=urlcreate(url,'role_id=' + roleId);
            $.modal.open("菜单授权", url);
        }
        function adminRole(roleId){
            var url = "{{route('system.appuserrole.index')}}";
            url=urlcreate(url,'role_id=' + roleId);
            $.modal.openOptions({
                title:'分配用户',
                url:url,
                full:true,
                btn:['关闭'],
                yes:function(index, layero){
                    layer.close(index);
                }
            });
        }

    </script>
@stop

