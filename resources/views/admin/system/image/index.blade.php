@extends('admin.layout.layout')

@include('widget.asset.viewer')
@include('widget.asset.fixed-columns')

@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li>图片位置：
                        <select name="where[position]">
                            <option value="">全部</option>
                            @foreach($position as $type=>$name)
                                <option value="{{$type}}" >{{$name}}</option>
                            @endforeach
                        </select>
                    <li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:image:add")
        <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("system:image:edit")
        <a class="btn btn-primary single disabled" onclick="$.operate.edit('',this)"> 编辑</a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "图片",
                sortName:'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID',  sortable: true,visible:false},
                    {
                        title: '序号',
                        align: "center",
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'title',
                        title: '标题',
                        align: 'left'
                    },
                    {
                        field: 'position',
                        title: '图片位置',
                        align: 'left'
                    },
                    {
                        field: 'img_url',
                        title: '图片',
                        formatter: function (value, row, index) {
                            return $.table.imageView(row,'img_url');
                        }
                    },
                    {
                        title: '跳转地址',
                        field: 'jump_url',
                        align: 'left'
                    },
                    {
                        title: '排序值',
                        field: 'listsort',
                        align: 'left',
                        sortable: true
                    },
                    {
                        title: '状态',
                        field: 'status',
                        align: 'left',
                        sortable: true,
                        formatter: function (value, row, index) {
                            return $.view.statusTools(row,true);
                        }
                    },
                    {field: 'create_time', title: '上传时间', align: 'left', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("system:image:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                        @endhasPerm
                        @hasPerm("system:image:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ],
            };
            $.table.init(options);
        });
    </script>
@stop

