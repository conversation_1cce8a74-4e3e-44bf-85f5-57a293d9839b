@extends('admin.layout.form')
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')

@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">图片位置：</label>
            <div class="col-sm-8">
                <select name="type" class="select2" required>
                    <option value="">请选择图片位置</option>
                    @foreach($position as $type=>$name)
                        <option value="{{$type}}" @if($type == $info['position']) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">标题：</label>
            <div class="col-sm-8">
                <input type="text" name="title" value="{{$info['title']}}" class="form-control" autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">图片：</label>
            <div class="col-sm-8">
                <input type="hidden" name="crop" value="" id="crop" required>
                <x-upload type="img" name="crop_upload" :extend="['cat'=>'demo','link'=>1,'multi'=>1,'crop'=>1,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M；','data'=>$info['img_url']]"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">跳转地址：</label>
            <div class="col-sm-8">
                <input type="text" name="jump_url" value="{{$info['jump_url']}}" class="form-control" autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">排序值：</label>
            <div class="col-sm-2">
                <input type="text" name="listsort" value="{{$info['listsort']}}" class="form-control" autocomplete="off"/>
            </div>
            <label class="col-sm-3 control-label is-required">状态：</label>
            <div class="col-sm-3">
                <label class="radio-box">
                    <input type="radio" name="status" value="1" @if($info["status"] == "0") checked @endif/> 停用
                </label>
                <label class="radio-box">
                    <input type="radio" name="status" value="1" @if($info["status"] == "1") checked @endif/> 启用
                </label>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            var crop = [];
            if($("input[name='crop_upload[]']").length>0){
                $("input[name='crop_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval){
                        crop.push(imgval)
                    }
                })
            }
            $("#crop").val(crop.join(','));
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
