@extends('admin.layout.form')
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')

@section('content')
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">图片位置：</label>
        <div class="col-sm-8">
            <select name="position" class="select2" required>
                <option value="">请选择图片位置</option>
                @foreach($position as $type=>$name)
                    <option value="{{$type}}">{{$name}}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">标题：</label>
        <div class="col-sm-8">
            <input type="text" name="title" value="" class="form-control" autocomplete="off" placeholder="请输入标题"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">图片：</label>
        <div class="col-sm-8">
            <input type="hidden" name="crop" value="" id="crop" required>
            <x-upload type="img" name="crop_upload" :extend="['cat'=>'demo','link'=>1,'multi'=>1,'crop'=>1,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M']"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">跳转地址：</label>
        <div class="col-sm-8">
            <input type="text" name="jump_url" value="" class="form-control" autocomplete="off" placeholder="请输入跳转地址"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">排序值：</label>
        <div class="col-sm-2">
            <input type="text" name="listsort" value="255" class="form-control" autocomplete="off"/>
        </div>
        <label class="col-sm-3 control-label is-required">状态：</label>
        <div class="col-sm-3">
            <label class="radio-box">
                <input type="radio" name="status" value="0"/> 停用
            </label>
            <label class="radio-box">
                <input type="radio" name="status" value="1" checked/> 启用
            </label>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        function submitHandler() {
            var crop = [];
            if($("input[name='crop_upload[]']").length>0){
                $("input[name='crop_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval)
                        crop.push(imgval)
                })
            }
            $("#crop").val(crop.join(','));
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
