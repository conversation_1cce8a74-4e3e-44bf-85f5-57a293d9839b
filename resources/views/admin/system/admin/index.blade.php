@extends('admin.layout.full')

@include('widget.asset.select2')
@include('widget.asset.jquery-layout')
@include('widget.asset.ztree')
@include('widget.asset.export')

@section('content')
<div class="ui-layout-west">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-grid"></i> 组织部门
            </div>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" title="组织架构管理" onclick="$.modal.openTab('组织架构', struct_indexUrl);"></button>
                <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                <button type="button" class="btn btn-box-tool" title="刷新组织" onclick="getStructList()"><i class="fa fa-refresh"></i></button>
            </div>
        </div>
        <div class="ui-layout-content">
            <div id="tree" class="ztree"></div>
        </div>
    </div>
</div>

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="role-form">
                    <input type="hidden" name="structId" id="structId" value="">
                    <div class="select-list">
                        <ul>
                            <li><input type="text" name="like[username]" value="" placeholder="人员姓名"></li>
                            <li><input type="text" name="like[mobile]" value="" placeholder="手机号码"></li>
                            <li>
                                <select name="position_id" class="select2">
                                    <option value="">所有职位</option>
                                    @foreach($posList as $id=>$value)
                                        <option value="{{$id}}">{{$value}}</option>
                                    @endforeach
                                </select>
                            </li>
                            <li>
                                <select name="where[position_level_id]" class="select2">
                                    <option value="">所有职级</option>
                                    @foreach($levelList as $id=>$value)
                                        <option value="{{$id}}">{{$value}}</option>
                                    @endforeach
                                </select>
                            </li>
                            <li>
                                <select name="where[is_struct_leader]" class="select2">
                                    <option value="">是否为科室/社区负责人</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            <li>
                            <li>
                                <select name="contains" class="select2">
                                    <option value="1">包含子部门</option>
                                    <option value="0">不包含子部门</option>
                                </select>
                            <li>
                            <li>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">
                @hasPerm("system:admin:add")
                <a class="btn btn-warning" data-width="1200" data-height="600" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
                @endhasPerm
                @hasPerm("system:{$app}:import")
                <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.importExcel()">
                    <i class="fa fa-upload"></i> 导入
                </a>
                @endhasPerm
                @hasPerm("system:{$app}:export")
                <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.exportExcel()">
                    <i class="fa fa-download"></i> 导出
                </a>
                @endhasPerm
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
    <script>
        var root_id ="{{$root_id}}";
        var struct_indexUrl="{{route('system.struct.index')}}";
        var struct_treeUrl="{{route('system.struct.tree')}}";
        $(function () {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 200,togglerContent_open:"<i class='fa fa-caret-left'></i>",togglerContent_closed:"<i class='fa fa-caret-right'></i>" });
            getStructList();
            getUserList();

            $('#btnExpand').click(function() {
                $._tree.expandAll(true);
                $(this).hide();
                $('#btnCollapse').show();
            });

            $('#btnCollapse').click(function() {
                $._tree.expandAll(false);
                $(this).hide();
                $('#btnExpand').show();
            });
        });
        function getStructList() {
            var options = {
                url: struct_treeUrl,
                expandLevel: 2,
                onClick : zOnClick
            };
            $.tree.init(options);
            $("#structId").val('');
            $.table.search();
            function zOnClick(event, treeId, treeNode) {
                $("#structId").val(treeNode.id);
                $.table.search();
            }
        }
        function getUserList() {
            var options = {
                modalName: "人员",
                sortName:'id',
                sortOrder:'asc',
                showExport: true,
                exportOptions:{
                    ignoreColumn:[0]
                },
                columns: [
                    {
                        checkbox: true,
                        formatter:function(value,row,index){
                            if(row.id == root_id) return {disabled : true};
                        }
                    },
                    {field: 'id', title: '用户ID', align: 'left', sortable: true},
                    {
                        field: 'username',
                        align: 'left',
                        title: '姓名',
                    },
                    {
                        field: 'mobile',
                        align: 'left',
                        title: '手机号码'
                    },
                    {
                        field: 'struct.name',
                        title: '科室/社区',
                        formatter:function (value, row, index) {
                            value = row.struct ? row.struct.parent_name +'/'+row.struct.name : null;
                            return $.table.tooltip(value,15);
                        }
                    },
                    {
                        field: 'is_struct_leader',
                        title: '是否为科室/社区负责人',
                        formatter:function (value, row, index) {
                            return value ? '是' : '否';
                        }
                    },
                    {
                        field: 'position_names',
                        title: '职位',
                        formatter:function (value, row, index) {
                            return $.table.tooltip(value,15);
                        }
                    },
                    {
                        field: 'position_level.name',
                        title: '职务',

                    },
                    {
                        field: 'status',
                        title: '状态',
                        formatter: function (value, row, index) {
                            return $.view.statusTools(row,true);
                        }
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {field: 'note', title: '备注', visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        export:false,
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:admin:edit")
                            actions.push('<a class="btn btn-xs" href="javascript:;" data-width="1200" data-height="600" onclick="$.operate.edit(\'' + row.id + '\', this)">编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:admin:drop")
                            if(row.id != root_id) {
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                            }
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        }

        //显示选择人员弹窗
        function userTree(){
            var treeId= $("#checkUserList").val();
            //mult 1多选 0 单选
            var url = urlcreate("{{route('system.admin.tree')}}","ids="+treeId+"&mult=1");
            var options = {
                title: '人员选择',
                width: "800",
                url: url,
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }
        function tableSearchReset() {
            $('#structId').val('');
            $._tree.cancelSelectedNode();
        }
        function doSubmit(index, layero){
            // var body = layer.getChildFrame('body', index);
            var iframeWin = window[layero.find('iframe')[0]['name']];//得到iframe页的窗口对象，执行iframe页的方法：
            var list = iframeWin.getCheckRows();
            var idList = [];
            if(list.length>0){
                for (let i = 0; i < list.length; i++) {
                    idList.push(list[i].id)
                }
            }
            $("#checkUserList").val(idList.join(','))
            layer.close(index);
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <a download href="{{asset('template/政府工作人员.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop

