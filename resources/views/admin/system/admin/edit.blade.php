@extends('admin.layout.form')

@include('widget.asset.select2')

@section('content')
<div class="main-content">
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">姓名：</label>
            <div class="col-sm-4">
                <input type="text" name="username" value="{{$info['username']}}" class="form-control" placeholder="请输入姓名" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">手机号：</label>
            <div class="col-sm-4">
                <input type="text" name="mobile" value="{{$info['mobile']}}" class="form-control" placeholder="请输入手机号" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 手机号为管理后台和app的登录账号</span>
            </div>
            <label class="col-sm-2 control-label">登录密码：</label>
            <div class="col-sm-4">
                <div class="input-group">
                    <input type="password" name="password" value="" class="form-control" style="border-right: none !important;" placeholder="请输入"  autocomplete="off"/>
                    <div class="input-group-addon">
                        <img class="pwdeye" style="cursor: pointer;height: 16px;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUdwTNHR0dra2tPT09HR0dHR0dHR0dPT09HR0dLS0tHR0dLS0tHR0dDQ0HhB6yAAAAANdFJOUwDnCzD0osoddluJRLXFpER1AAABoUlEQVRIx+1Uv0vDQBQ+U7TFdrAURIRAoVLQqS2uLjqpBFyqU0ALokuhi4KKoIuD4uIiHQQVQZdCB0HH/gMOwbTa0u9/8S65H4lNMjk45Bsu73v33bt3792FkBgxYvwp0rfR80dtOlzb7cgYxjcddaxHiV5h0bGOqFBpAz36WQRWw0V3wAr9JAzgLEyTAuwSM8pANx+s0Uwg51gJHdgLFh0CVkmaduCGKUMt1z6AYWNUk2R+mUgRwNpIWtoTddckpSuAmV8q7Zx5h4KOAzR3zPp2TD5QlwVscN4Cmkw1vFGaeRbdagIDWbAuWTBY8P0313VywZj9SExRaFr5DiEHcDBdfbms7rj2FiEZoO+0EOixnI91+GA9s+zrQNttcE2lIcETLLoNNmHxY415RVP8kDpNmIly/ERLGLxvZg07u3vVwid3lh1RWbSQqBmm51ZCZzG0ipipiz3Yzj1hFnydSAL3wp4EGmG3UF72dNhtnYCtiIHtQNGpcw4OE18hD6MfQoh38bIic96wPlFHkUyISFVV1jDgkVW8rJCPf7gx/jt+AKid4q/Um1olAAAAAElFTkSuQmCC">
                    </div>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 不填则表示不修改密码，密码应该包含大写字母、小写字母、数字和特殊符号中的至少三种种类,并且密码长度6-15位！</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">科室/社区：</label>
            <div class="col-sm-4">
                <div class="input-group">
                    <input type="hidden" id="treeId" name="struct_id" value="{{$info['struct_id']}}">
                    <input type="text" id="treeName" value="{{$struct['name'] ?? ''}}" class="form-control" required placeholder="请选择" readonly autocomplete="off"/>
                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                </div>
            </div>
            <label class="col-sm-2 control-label is-required">科室/社区负责人：</label>
            <div class="col-sm-4">
                <label class="radio-box">
                    <input type="radio" name="is_struct_leader" required value="0" @if($info['is_struct_leader'] == 0) checked @endif/> 否
                </label>
                <label class="radio-box">
                    <input type="radio" name="is_struct_leader" required value="1" @if($info['is_struct_leader'] == 1) checked @endif/> 是
                </label>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 每个科室/社区的负责人只有一个</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">状态：</label>
            <div class="col-sm-8">
                <label class="radio-box">
                    <input type="radio" name="status" required value="0" @if($info['status'] == 0) checked @endif/> 禁用
                </label>
                <label class="radio-box">
                    <input type="radio" name="status" required value="1" @if($info['status'] == 1) checked @endif/> 启用
                </label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">职级：</label>
            <div class="col-sm-4">
                <select class="select2" data-width="90%" name="position_level_id">
                    <option value="">选择职级</option>
                    @foreach($levelList as $id=>$name)
                        <option value="{{$id}}" @if($info['position_level_id'] == $id) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
            <label class="col-sm-2 control-label">职位：</label>
            <div class="col-sm-4">
                <select class="select2" data-width="100%" multiple name="position_id[]">
                    <option value="">选择职位</option>
                    @foreach($posList as $id=>$name)
                        <option value="{{$id}}" @if(in_array($id, $posIds)) selected @endif >{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div id="struct_type_leader" @if(!$info['leader_type']) style="display: none" @endif>
            <div class="form-group ">
                <label class="col-sm-2 control-label is-required">领导类型：</label>
                <div class="col-sm-4">
                    <label class="radio-box">
                        <input type="radio" name="leader_type" @if($info['leader_type'] == 1) checked @endif required value="1"/> 主要领导
                    </label>
                    <label class="radio-box">
                        <input type="radio" name="leader_type" @if($info['leader_type'] == 2) checked @endif required value="2" /> 分管领导
                    </label>
                </div>
            </div>
            <div class="form-group" id="leader_type_fg" @if($info['leader_type'] != 2) style="display: none" @endif>
                <label class="col-sm-2 control-label is-required">分管科室/社区：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input type="hidden" id="fgTreeId" name="manage_struct" value="{{$manageStructIds}}">
                        <input type="text" id="fgTreeName" value="{{$manageStruct}}" class="form-control" required placeholder="请选择" readonly autocomplete="off"/>
                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@stop

@section('script')
    <script>
        var structType = @json($structType);
        $(function () {
            $('.pwdeye').click(function (){
                var pwd = $(this).parents('.input-group').find('input');
                var t = pwd.attr('type');
                if (t == 'password') {
                    pwd.attr('type','text');
                    $(this).attr('src', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADUAAAAuCAMAAAC/KaEaAAAAOVBMVEVHcEzNzc3Nzc3Nzc3Q0NDNzc3MzMzNzc3Nzc3V1dXNzc3Nzc3MzMz////r6+vh4eHx8fH+/v7S0tLiR/+pAAAADHRSTlMAYZKBI63xxt4NPlNOUVuQAAABOklEQVRIx+VVyZaEIAwUZNXI9v8fO6O20wQC+ui5dd30pbJUQjJN3wO7cqmNAmU0Z+IRRcwaEAxf7jirBAKa9TiLgQZMkyckdKDpApmCLtRMkDgySd6FEJxP+U9pS7GRcClsF0LO07ZDcnHL4HJRUHFIh7BhhAaN90iY9k5yRekdhjE67/0r1Zgnqa+i8t6miIp5+cglmYmiTjP/9+0rReAYywU1KpZGh5uIpsuWoXyV0JFy9IBzFFCxAlSaIpayxSS5yuR0hAr7DWZuHFPhzWAsUbOIulxR15iGg/0am43JKmIOzyQTMYecmvnT+ZbPPBL1/VTk8/dl7Idvubc3YntvVDvq4kW8o8R/7MN998INZnrP64E9vzeueVNU7xZZpuk7ZO9uJS8CKrk8O7KMS7MPp9Ly4V3+avwAZ4tEuL3lSDsAAAAASUVORK5CYII=');
                } else {
                    pwd.attr('type','password');
                    $(this).attr('src', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUdwTNHR0dra2tPT09HR0dHR0dHR0dPT09HR0dLS0tHR0dLS0tHR0dDQ0HhB6yAAAAANdFJOUwDnCzD0osoddluJRLXFpER1AAABoUlEQVRIx+1Uv0vDQBQ+U7TFdrAURIRAoVLQqS2uLjqpBFyqU0ALokuhi4KKoIuD4uIiHQQVQZdCB0HH/gMOwbTa0u9/8S65H4lNMjk45Bsu73v33bt3792FkBgxYvwp0rfR80dtOlzb7cgYxjcddaxHiV5h0bGOqFBpAz36WQRWw0V3wAr9JAzgLEyTAuwSM8pANx+s0Uwg51gJHdgLFh0CVkmaduCGKUMt1z6AYWNUk2R+mUgRwNpIWtoTddckpSuAmV8q7Zx5h4KOAzR3zPp2TD5QlwVscN4Cmkw1vFGaeRbdagIDWbAuWTBY8P0313VywZj9SExRaFr5DiEHcDBdfbms7rj2FiEZoO+0EOixnI91+GA9s+zrQNttcE2lIcETLLoNNmHxY415RVP8kDpNmIly/ERLGLxvZg07u3vVwid3lh1RWbSQqBmm51ZCZzG0ipipiz3Yzj1hFnydSAL3wp4EGmG3UF72dNhtnYCtiIHtQNGpcw4OE18hD6MfQoh38bIic96wPlFHkUyISFVV1jDgkVW8rJCPf7gx/jt+AKid4q/Um1olAAAAAElFTkSuQmCC');
                }
            });
            //选择组织架构
            $("#treeName").click(function () {
                var treeId=$("#treeId").val();
                var url = urlcreate("{{route('system.struct.tree')}}","id="+treeId+"&parent=0");
                var options = {
                    title: '科室/社区选择',
                    width: "380",
                    url: url,
                    callBack: function (index, layero){
                        var body = layer.getChildFrame('body', index);
                        var structId = body.find('#treeId').val();
                        $("#treeId").val(structId);
                        $("#treeName").val(body.find('#treeName').val());
                        if(structType[structId] === 4){
                            $("#struct_type_leader").show();
                        } else {
                            $("#struct_type_leader").hide();
                        }
                        layer.close(index);
                    }
                };
                $.modal.openOptions(options);
            });
            $("#fgTreeName").click(function () {
                var treeId=$("#fgTreeId").val();
                var url = urlcreate("{{route('system.struct.tree')}}","id="+treeId+"&parent=0&ismult=1");
                var options = {
                    title: '科室/社区选择',
                    width: "380",
                    url: url,
                    callBack: function (index, layero){
                        var body = layer.getChildFrame('body', index);
                        $("#fgTreeId").val(body.find('#treeId').val());
                        $("#fgTreeName").val(body.find('#treeName').val());
                        layer.close(index);
                    }
                };
                $.modal.openOptions(options);
            });
            $('input[name=leader_type]').on('ifChecked', function(event){
                if($(this).val() === '2'){
                    $("#leader_type_fg").show();
                } else {
                    $("#leader_type_fg").hide();
                }
            })
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
