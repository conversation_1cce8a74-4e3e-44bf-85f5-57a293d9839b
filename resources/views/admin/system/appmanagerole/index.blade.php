@extends('admin.layout.full')

@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }

    </style>
@append
@section('content')
    <div class="col-sm-12 alert alert-warning alert-dismissable" style="margin: 10px 0 0 0">
        <div>
            角色名称：{{$role['name']}}
        </div>
    </div>
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="btn-group-sm" id="toolbar" role="group" style="width: 100%">
                @hasPerm("system:appmanagerole:add")
                <a class="btn btn-warning" data-width="1200" data-height="600" onclick="userTree()"><i class="fa fa-plus"></i> 新增</a>
                @endhasPerm
                @hasPerm("system:appmanagerole:drop")
                <a class="btn btn-warning multiple disabled" onclick="remove()"><i class="fa fa-trash"></i> 批量删除</a>
                @endhasPerm
                <div class="pull-right">
                    <form id="role-form">
                        <input type="hidden" name="role_id" value="{{$role['id']}}">
                        <div class="select-list">
                            <ul>
                                <li><input type="text" name="keywords" value="" placeholder="姓名手机号" autocomplete="off"></li>
                                <li>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
    <script>
        $(function () {
            getUserList();
        });

        function getUserList() {
            var options = {
                modalName: "人员",
                sortName:'id',
                sortOrder:'asc',
                showExport: true,
                showToolbar: false,
                exportOptions:{
                    ignoreColumn:[0]
                },
                columns: [
                    {
                        checkbox: true,
                    },
                    {field: 'id', title: '用户ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'username',
                        align: 'left',
                        title: '姓名',
                    },
                    {
                        field: 'mobile',
                        align: 'left',
                        title: '手机号码'
                    },
                    {
                        field: 'struct.name',
                        title: '科室/社区',
                        formatter:function (value, row, index) {
                            value = row.struct ? row.struct.parent_name +'/'+row.struct.name : null;
                            return $.table.tooltip(value,15);
                        }
                    },
                    {
                        field: 'is_struct_leader',
                        title: '科室/社区',
                        formatter:function (value, row, index) {
                            return value ? '是' : '否';
                        }
                    },
                    {
                        field: 'position_names',
                        title: '职位',
                        formatter:function (value, row, index) {
                            return $.table.tooltip(value,15);
                        }
                    },
                    {
                        field: 'position_level.name',
                        title: '职务',

                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {field: 'note', title: '备注', visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        export:false,
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:appmanagerole:drop")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="remove(\'' + row.id + '\')">删除</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        }

        //显示选择人员弹窗
        function userTree(){
            var treeId= $("#checkUserList").val();
            //mult 1多选 0 单选
            var url = urlcreate("{{route('system.admin.tree')}}","ids="+treeId+"&mult=0");
            var options = {
                title: '人员选择',
                width: "800",
                url: url,
                callBack: function (index, layero){
                    // var body = layer.getChildFrame('body', index);
                    var iframeWin = window[layero.find('iframe')[0]['name']];//得到iframe页的窗口对象，执行iframe页的方法：
                    var list = iframeWin.getCheckRows();
                    var idList = [];
                    if(list.length>0){
                        for (let i = 0; i < list.length; i++) {
                            idList.push(list[i].id)
                        }
                    }
                    if (idList.length>0) {
                        var data = { "ids": idList.join(),'role_id':{{$role['id']}} };
                        $.operate.submit('{{route('system.appmanagerole.add')}}', "post", "json", data);
                    }
                    layer.close(index);
                }
            };
            $.modal.openOptions(options);
        }

        function remove(id) {
            table.set();
            var rows
            if($.common.isEmpty(id)){
                rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
                var column = '';
                if($.common.isNotEmpty(column)){
                    rows = $.table.selectColumns(column);
                }
                if (rows.length == 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
            } else {
                rows = [id];
            }
            $.modal.confirm("确认要删除这" + rows.length + "条数据吗?", function() {
                var url = table.options.removeUrl;
                var data = { "ids": rows.join(),'role_id':{{$role['id']}} };
                $.operate.submit(url, "post", "json", data);
            });
        }
    </script>
@stop

