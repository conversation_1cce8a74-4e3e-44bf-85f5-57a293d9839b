@extends('admin.layout.layout')

@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
    </style>
@append
@section('content')

<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("work:workassignlevel:add")
    <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
    @endhasPerm
    <div class="pull-right">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li>等级名称：<input type="text" name="like[title]" value=""></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "工作等级",
                sortName:'listsort',
                sortOrder: "asc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'center', sortable: true},
                    {
                        field: 'title',
                        title: '等级名称',
                        formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }
                    },
                    {field: 'listsort', title: '排序值', align: 'center', sortable: true},
                    {field: 'create_time', title: '创建时间', align: 'center', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'center', sortable: true,visible: false},
                @if(hasPerm("work:workassignlevel:edit") || hasPerm("work:workassignlevel:drop"))
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("work:workassignlevel:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')">编辑</a> ');
                        @endhasPerm

                        @hasPerm("work:workassignlevel:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                    @endif
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

