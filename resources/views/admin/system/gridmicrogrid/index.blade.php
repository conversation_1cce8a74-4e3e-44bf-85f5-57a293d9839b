@extends('admin.layout.full')

@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }

    </style>
@append
@section('content')
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="btn-group-sm" id="toolbar" role="group" style="width: 100%">
                @hasPerm("system:gridgrid:add")
                <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
                @endhasPerm
                @hasPerm("system:{$app}:import")
                <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.importExcel()">
                    <i class="fa fa-upload"></i> 导入
                </a>
                @endhasPerm
                @hasPerm("system:{$app}:export")
                <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.exportExcel()">
                    <i class="fa fa-download"></i> 导出
                </a>
                @endhasPerm
                <div class="pull-right">
                    <form id="role-form">
                        <div class="select-list">
                            <ul>
                                <li>
                                    <select name="where[grid_microgrid.cid]" id="cid" class="select2">
                                        <option value="">所属社区</option>
                                        @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                                            <option value="{{$id}}">{{$name}}</option>
                                        @endforeach
                                    </select>
                                </li>
                                <li>
                                    <select name="where[grid_microgrid.grid_id]" id="grid_id" class="select2">
                                        <option value="">所属网格</option>
                                    </select>
                                </li>
                                <li><input type="text" name="like[grid_microgrid.name]" value="" placeholder="微网格名称" autocomplete="off"></li>
                                <li>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
    <script>
        $(function () {
            getUserList();
            $('#cid').on("change", function() {
                let communityId = $(this).val();
                let grid = $('#grid_id');
                grid.select2('destroy').empty();
                grid.select2({data:[{id:"",text:"所属网格"}],width:200});
                if (communityId) {
                    $.ajax({
                        url: '{{route('admin.getgridbycid')}}',
                        type: 'get',
                        dataType: 'json',
                        data: {
                            cid: communityId
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                grid.select2({data:res.data,width:200});
                            }
                        }
                    });
                }
            });
        });

        function getUserList() {
            var options = {
                modalName: "微网格",
                sortName:'id',
                sortOrder:'desc',
                showToolbar: false,
                columns: [
                    {field: 'id', title: '微网格ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'name',
                        align: 'left',
                        title: '微网格名称',
                    },
                    {
                        field: 'community.name',
                        align: 'left',
                        title: '所属社区'
                    },
                    {
                        field: 'grid.name',
                        align: 'left',
                        title: '所属网格'
                    },

                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        export:false,
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:gridgrid:edit")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\', \'' + row.name + '\')"> 编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:gridgrid:drop")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\', \'' + row.name + '\')"> 删除</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <a download href="{{asset('template/微网格.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop

