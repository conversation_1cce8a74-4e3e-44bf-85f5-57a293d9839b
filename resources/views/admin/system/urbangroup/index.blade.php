@extends('admin.layout.layout')

@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
    </style>
@append
@section('content')
<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("system:urbangroup:add")
    <a class="btn btn-warning" data-width="500" data-height="500" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
    @endhasPerm
    <div class="pull-right">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="like[name]" value="" placeholder="分组名称" autocomplete="off"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "城管分组",
                sortName:'id',
                sortOrder: "desc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'name',
                        title: '分组名称',
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            if(row.id === 1) {
                                return '';
                            }
                        @hasPerm("system:urbangroup:edit")
                            actions.push('<a class="btn  btn-xs" data-width="500" data-height="500" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\', this)"> 编辑</a> ');
                        @endhasPerm

                        @hasPerm("system:urbangroup:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

