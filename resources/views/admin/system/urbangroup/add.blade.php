@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}
@section('css_common')
    <style>
        .label-item{
            padding-bottom: 45px;
        }
    </style>
@append
@section('content')
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <label class="col-xs-4 control-label is-required" style="padding-top: 7px;padding-right:0;text-align: right;">分组名称：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <input type="text" name="name" value="" placeholder="请输入" class="form-control" required autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
