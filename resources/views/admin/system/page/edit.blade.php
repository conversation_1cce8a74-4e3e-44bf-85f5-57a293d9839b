@extends('admin.layout.form')
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@include('widget.asset.summernote')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">单页类型：</label>
            <div class="col-sm-8">
                <select name="page_type" class="select2" required>
                    <option value="">请选择单页类型</option>
                    @foreach($position as $type=>$name)
                        <option value="{{$type}}" @if($type == $info['page_type']) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">内容：</label>
            <div class="col-sm-8">
                <textarea class="summernote_content hide" id="content" name="content">{{$info['content']}}</textarea>
                <div class="summernote" data-place="请输入内容" id="contentEditor"></div>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                if($("#contentEditor").summernote('isEmpty')){
                    $.modal.msgError('请填写内容')
                    return false;
                }
                var sHTML = $('#contentEditor').summernote('code');
                $("#content").val(sHTML);
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
