@extends('admin.layout.layout')

@include('widget.asset.viewer')
@include('widget.asset.fixed-columns')

@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li>单页类型：
                        <select name="where[position]">
                            <option value="">全部</option>
                            @foreach($position as $type=>$name)
                                <option value="{{$type}}" >{{$name}}</option>
                            @endforeach
                        </select>
                    <li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:page:add")
        <a class="btn btn-warning" onclick="$.operate.addFull()"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("system:page:edit")
        <a class="btn btn-primary single disabled" onclick="$.operate.editFull('',this)"> 编辑</a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "单页",
                sortName:'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID',  sortable: true,visible:false},
                    {
                        title: '序号',
                        align: "center",
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'page_type',
                        title: '单页类型',
                        align: 'left'
                    },
                    {field: 'create_time', title: '上传时间', align: 'left', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("system:page:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.editFull(\'' + row.id + '\')"> 编辑</a> ');
                        @endhasPerm
                        @hasPerm("system:page:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ],
            };
            $.table.init(options);
        });
    </script>
@stop

