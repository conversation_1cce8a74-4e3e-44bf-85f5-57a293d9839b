@extends('admin.layout.form')
@include('widget.asset.select2')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">所属社区：</label>
            <div class="col-sm-8">
                <div class="form-control-static">{{$admin['struct']['name'] ?? ''}}</div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">社区管理员：</label>
            <div class="col-sm-8">
                <div class="form-control-static">{{$admin['username']}}</div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">大联动账号：</label>
            <div class="col-sm-8">
                <input type="text" name="dld_account" value="{{$info['dld_account']}}" class="form-control" placeholder="请输入" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">大联动密码：</label>
            <div class="col-sm-8">
                <input type="password" name="dld_pwd" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 不填则表示不修改密码</span>

            </div>
        </div>

    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
