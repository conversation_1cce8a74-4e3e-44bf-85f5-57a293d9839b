@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
    </style>
@append
@section('content')
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:{$app}:add")
        <a class="btn btn-warning" onclick="$.operate.add(this)"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        <div class="pull-right">
            <form id="role-form">
                <div class="select-list">
                    <ul>
                        <li><input type="text" name="keywords" value="" placeholder="姓名电话" autocomplete="off"></li>
                        <li>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "大联动账号-社区管理员",
                sortName:'id',
                sortOrder: "desc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'dld_account_relevance.id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'username',
                        title: '姓名',
                    },
                    {
                        field: 'mobile',
                        title: '手机号',
                    },
                    {
                        field: 'community_name',
                        title: '所属社区',
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:{$app}:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')"> 编辑</a> ');
                            @endhasPerm

                            @hasPerm("system:{$app}:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

