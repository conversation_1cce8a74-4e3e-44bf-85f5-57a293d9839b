@extends('admin.layout.full')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.select2")
@section('css_common')
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
    <style type="text/css" media="screen">
        .button-group {position: absolute;top: 0px;right: 20px;font-size: 12px;padding: 10px; }
    </style>
@append
@section('content')
    <div id="container"></div>
    <div class="button-group">
        <input type="button" class="button" value="重置范围" onClick="restMap()"/>
    </div>
@stop

@section('script')
    <script src="https://webapi.amap.com/maps?v=1.4.4&key={{env('GAODE_KEY')}}&plugin=AMap.PolyEditor"></script>
    <script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
    <script>
        var storage = window.localStorage;
        var editorTool, map = new AMap.Map("container", {
            resizeEnable: true,
            center: [{{$region[0][0] ?? '104.065861'}},{{$region[0][1] ?? '30.657401'}}],//地图中心点
            zoom: 13 //地图显示的缩放级别
        });
        var beginNum = 0;
        var clickListener;
        var beginPoints = [];//存坐标的数组
        var beginMarks = [];
        var polygonEditor;
        var resPolygon = [];
        var resNum = 0;
        init();
        function restMap(){
            location.href = '{{route('system.structregion.region')}}?id={{$info['id']}}&reset=1';
        }
        function init() {
            beginPoints = [];
            beginMarks = [];
            beginNum = 0;
            polygonEditor = '';
            @if(empty($input['reset']) && $region)
                beginPoints = @json($region);
                var polygon = createPolygon(beginPoints);
                polygonEditor = createEditor(polygon);//如果是要不可编辑状态去掉此行
            @else
                clickListener = AMap.event.addListener(map, "click", mapOnClick);
            @endif
        }

        function mapOnClick(e) {
            beginMarks.push(addMarker(e.lnglat));
            beginPoints.push(e.lnglat);
            beginNum++;
            if (beginMarks.length >= 3) {
                AMap.event.removeListener(clickListener);
                var polygon = createPolygon(beginPoints);
                polygonEditor = createEditor(polygon);
                clearMarks();
            }
        }

        /*创建多边形*/
        function createPolygon(arr) {
            var polygon = new AMap.Polygon({
                map: map,
                path: arr,
                strokeColor: "#009cff",
                strokeOpacity: 1,
                strokeWeight: 3,
                fillColor: "#009cff",
                fillOpacity: 0.35
            });
            return polygon;
        }

        function createEditor(polygon) {
            var polygonEditor = new AMap.PolyEditor(map, polygon);
            polygonEditor.open();
            AMap.event.addListener(polygonEditor, 'end', polygonEnd);
            return polygonEditor;
        }

        function submitHandler() {
            if (!polygonEditor || beginPoints.length < 3) {
                $.modal.msgError('请至少选择3个点');
                return false;
            }
            let FenceRegion = [];
            beginPoints.forEach(function (item, index) {
                var lng = parseFloat(item['lng']);//经
                var lat = parseFloat(item['lat']);//纬
                FenceRegion.push([lng, lat]);
            });
            // let newRegion = ObjectToJson(FenceRegion);
            $.operate.save('{{route('system.structregion.region')}}?id={{$info['id']}}', {region:FenceRegion});
        }

        function polygonEnd(res) {
            resPolygon.push(res.target);
            alert(resPolygon[resNum].contains([104.008014, 30.680541]));
            appendHideHtml(resNum, res.target.getPath());
            resNum++;
            init();
        }

        function appendHideHtml(index, arr) {
            var strify = JSON.stringify(arr);
            var html = '<input type="hidden" id="index' + index + '" name="paths[]" value="' + strify + '">';
            $('body').append(html);
            console.log(html);
        }

        function clearMarks() {
            map.remove(beginMarks);
        }

        function JsonToArray(json) {
            var arr = JSON.parse(json);
            var res = [];
            for (var i = 0; i < arr.length; i++) {
                var line = [];
                line.push(arr[i].lng);
                line.push(arr[i].lat);
                res.push(line);
            }
            return res;
        }

        // 实例化点标记
        function addMarker(lnglat) {
            var marker = new AMap.Marker({
                position: lnglat
            });
            marker.setMap(map);
            return marker;
        }

    </script>
@stop
