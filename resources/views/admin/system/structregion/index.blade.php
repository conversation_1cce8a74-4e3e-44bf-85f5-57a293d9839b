@extends('admin.layout.layout')

@section('content')
<div class="col-sm-12 search-collapse" style="display: none">
    <form id="role-form">
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "街道社区区域",
                sortName:'id',
                sortOrder: "asc",
                showToolbar: false,
                columns: [
                    // {checkbox: true},
                    {field: 'id', title: '编号', align: 'left', sortable: true,visible: false},
                    {field: 'name', title: '名称', align: 'left'},
                    {
                        field: 'is_region',
                        title: '是否设置区域',
                        formatter: function(value, row, index) {
                            return $.view.statusShow(row,false,['否','是'], 'is_region');
                        }
                    },
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:structregion:region")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="region(\'' + row.id + '\', \'' + row.name + '\')"> 区域</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function region(id, title) {
            $.modal.openFull("地图区域-"+title, '{{route('system.structregion.region')}}?id='+id);
        }
    </script>
@stop

