@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@section('css_common')
    <style>
        .form-group .col-sm-1{
            padding-right:0;
            width: auto;
        }
        .form-group .col-sm-3{
            padding-left:0;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-config-site-site">
        <div class="form-group">
            <label class="col-sm-1 control-label">上下班打卡时间：</label>
            <div class="col-sm-3">
                <input type="text" name="clock_comprehensive" id="clock_user" readonly value="{{$config['clock_comprehensive'] ?? ''}}" placeholder="请选择" class="form-control" autocomplete="off">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-1 control-label">上班提前打卡时间：</label>
            <div class="col-sm-3">
                <div class="input-group">
                    <input type="number" name="clock_comprehensive_before" min="1" step="1" value="{{$config['clock_comprehensive_before'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
                    <div class="input-group-addon">小时</div>
                </div>            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-1 control-label">下班延迟打卡时间：</label>
            <div class="col-sm-3">
                <div class="input-group">
                    <input type="number" name="clock_comprehensive_after" min="1" step="1" value="{{$config['clock_comprehensive_after'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
                    <div class="input-group-addon">小时</div>
                </div>
            </div>
        </div>
        @hasPerm('system:comprehensiveclockscope:index')
        <div class="form-group">
            <label class="col-sm-1 control-label">打卡范围：</label>
            <div class="col-sm-8">
                <div class="form-control-static">打卡范围：默认为街道的区域范围，可自定义设置  <a href="javascript:;" onclick="$.modal.openTab('自定义设置打卡范围','{{route('system.comprehensiveclockscope.index')}}',);" style="padding-left: 20px;">设置</a></div>
            </div>
        </div>
        @endhasPerm
        <div class="form-group" style="padding-left:15px;">
            <a href="javascript:;" class="btn btn-success btn-mid b5submit_btn" data-target="form-config-site-site" data-title="确定提交配置信息">保存</a>
        </div>
    </form>
@stop
@section('script')
    <script>
        $(function () {
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#clock_user'
                    ,type: 'time'
                    ,format: 'HH:mm'
                    ,range: true
                });
            });
        });
    </script>
@append