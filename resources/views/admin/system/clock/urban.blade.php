@extends('admin.layout.full')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@section('css_common')
    <style>
        .form-group .col-sm-1{
            padding-right:0;
            width: auto;
        }
        .form-group .col-sm-3{
            padding-left:0;
        }
        .table-striped {
             min-height: 0 !important;
        }
        .setting {
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-top: 10px;
            padding-top: 10px;
            padding-bottom: 13px;
            box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
        }
    </style>
@append
@section('content')
    <div class="container-div">
        <div class="row">
            @hasPerm('system:urbanscheduling:index')
            <div class="btn-group-sm" id="toolbar" role="group" style="width: 100%">
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
            @endhasPerm
            <div class="col-sm-12 setting">
                <form class="form-horizontal m" id="form-config-site-site">
                    <div class="form-group">
                        <label class="col-sm-1 control-label">上班提前打卡时间：</label>
                        <div class="col-sm-3">
                            <div class="input-group">
                                <input type="number" name="clock_urban_before" min="1" step="1" value="{{$config['clock_urban_before'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
                                <div class="input-group-addon">小时</div>
                            </div>            </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-1 control-label">下班延迟打卡时间：</label>
                        <div class="col-sm-3">
                            <div class="input-group">
                                <input type="number" name="clock_urban_after" min="1" step="1" value="{{$config['clock_urban_after'] ?? ''}}" placeholder="请输入" class="form-control" autocomplete="off">
                                <div class="input-group-addon">小时</div>
                            </div>
                        </div>
                    </div>
                    @hasPerm('system:urbanclockscope:index')
                    <div class="form-group">
                        <label class="col-sm-1 control-label">打卡范围：</label>
                        <div class="col-sm-8">
                            <div class="form-control-static">默认为街道的区域范围，可自定义设置  <a href="javascript:;" onclick="
                                        $.modal.openTab(
                                        '自定义设置打卡范围',
                                        '{{route('system.urbanclockscope.index')}}',
                                        );" style="padding-left: 20px;">设置</a></div>
                        </div>
                    </div>
                    @endhasPerm
                    <div class="form-group" style="padding-left:15px;">
                        <a href="javascript:;" class="btn btn-success btn-mid b5submit_btn" data-target="form-config-site-site" data-title="确定提交配置信息">保存</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

@stop
@section('script')
    <script>
        $(function () {
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#clock_urban_user'
                    ,type: 'time'
                    ,format: 'HH:mm'
                    ,range: true
                });
            });
            @hasPerm('system:urbanscheduling:index')
            var options = {
                modalName: "分组排班",
                showToolbar: false,
                pagination: false,
                sidePagination:'client',
                url: "{{route('system.urbanscheduling.index')}}",
                columns: [
                    {field: 'id', title: 'ID', align: 'center',visible: false},
                    {field: 'name', title: '分组名称', align: 'left'},
                    {field: 'scheduling_text', title: '排班方式', align: 'left'},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:urbanscheduling:type")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="schedulingType(\'' + row.id + '\', \'' + row.name + '\')">排班方式</a> ');
                            @endhasPerm
                            @hasPerm("system:urbanscheduling:scheduling")
                            if (row.scheduling_type == 2) {
                                actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="scheduling(\'' + row.id + '\')"> 排班</a> ');
                            }
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
            @endhasPerm
        });
        function schedulingType(id, name) {
            $.modal.open('排班方式-'+name, '{{route('system.urbanscheduling.type')}}?id=' + id)
        }
        function scheduling(group_id) {
            $.modal.openTab("排班", '{{route('system.urbanscheduling.scheduling')}}?group_id='+group_id, true);
        }
    </script>
@append