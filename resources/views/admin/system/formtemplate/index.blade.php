@extends('admin.layout.full')
@include("widget.asset.select2")
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }

    </style>
@append
@section('content')
    <div class="container-div">
        <div class="row">
            <div class="btn-group-sm" id="toolbar" role="group" style="width: 100%">
                @hasPerm("system:{$app}:add")
                <a class="btn btn-warning" onclick="$.operate.addFull()"><i class="fa fa-plus"></i> 新增</a>
                @endhasPerm
                <div class="pull-right">
                    <form id="role-form">
                        <div class="select-list">
                            <ul>
                                <li>
                                    <select name="relevance">
                                        <option value="">关联人员类型</option>
                                        @foreach($relevance as $k=>$v)
                                            <option value="{{$k}}">{{$v}}</option>
                                        @endforeach
                                    </select>
                                </li>
                                <li><input type="text" name="like[title]" value="" placeholder="表单名称" autocomplete="off"></li>
                                <li>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "{{$type_text}}",
                sortName:'id',
                sortOrder: "desc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'center', sortable: true},
                    {field: 'title', title: '表单名称', align: 'left'},
                    {field: 'relevances', title: '关联人员类型', align: 'left',formatter: function (value, row, index) {
                            var relevance = [];
                            $.each(value,function (k,v) {
                                relevance.push(v.user_type_text);
                            });
                            return relevance.length ? relevance.join('、') : '-';
                        }},
                    {field: 'status',title: '状态',align: 'center',formatter: function (value, row, index) {
                            return $.view.statusTools(row,true);
                        }},
                    {field: 'create_time', title: '创建时间', align: 'center', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:{$app}:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.editFull(\'' + row.id + '\')">编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:{$app}:drop")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"> 删除</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

