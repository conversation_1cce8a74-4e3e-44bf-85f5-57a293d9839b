@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}
@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-xs-4 control-label">姓名：</label>
        <div class="col-xs-5" >
            <div class="form-control-static">{{$info['username']}}</div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-4 control-label">手机号：</label>
        <div class="col-xs-5" >
            <div class="form-control-static">{{$info['mobile']}}</div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-4 control-label">所属社区：</label>
        <div class="col-xs-5" >
            <div class="form-control-static">{{$info['grid']['community']['name'] ?? ''}}</div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-4 control-label">清单制账号：</label>
        <div class="col-xs-5" >
            <input type="text" name="qdz_account" value="{{$relevance['qdz_account'] ?? ''}}" class="form-control" placeholder="请输入"  autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-4 control-label">清单制密码：</label>
        <div class="col-xs-5" >
            <input type="text" name="qdz_pwd" value="{{$relevance['qdz_pwd'] ?? ''}}" class="form-control"  placeholder="请输入" autocomplete="off"/>
        </div>
    </div>

</form>
@stop

@section('script')
    <script src="{{asset('static/admin/js/sm4.js')}}"></script>
    <script>
        $("#form-add").validate();
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
