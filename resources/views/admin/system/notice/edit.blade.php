@extends('admin.layout.form')
@include('widget.asset.upload')
@include('widget.asset.summernote')
@include('widget.asset.mypicker')
@include('widget.asset.select2')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">信息标题：</label>
            <div class="col-sm-8">
                <input type="text" name="title" value="{{$info['title']}}" class="form-control" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">热门：</label>
            <div class="col-sm-1">
                <label class="radio-box">
                    <input type="radio" name="hot_tag" value="1" @if($info["hot_tag"] == "1") checked @endif /> 是
                </label>
                <label class="radio-box">
                    <input type="radio" name="hot_tag" value="0" @if($info["hot_tag"] == "0") checked @endif /> 否
                </label>
            </div>
            <label class="col-sm-2 control-label is-required">状态：</label>
            <div class="col-sm-2">
                <label class="radio-box">
                    <input type="radio" name="status" value="1" @if($info["status"] == "1") checked @endif /> 显示
                </label>
                <label class="radio-box">
                    <input type="radio" name="status" value="0" @if($info["status"] == "0") checked @endif /> 隐藏
                </label>
            </div>
            <label class="col-sm-2 control-label">排序值：</label>
            <div class="col-sm-2">
                <input type="text" name="listsort" value="{{$info['listsort']}}" class="form-control" autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">发布时间：</label>
            <div class="col-sm-3">
                <input type="text" name="publish_date" id="publish_date" value="{{$info['publish_date']}}" class="form-control" required autocomplete="off" readonly/>
            </div>
            <label class="col-sm-2 control-label is-required">截止时间：</label>
            <div class="col-sm-3">
                <input type="text" name="end_date" id="end_date" value="{{$info['end_date']}}" class="form-control" required autocomplete="off" readonly/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">经营类别：</label>
            <div class="col-sm-8">
                <select class="form-control select2" name="industry_type[]" multiple required>
                    <option value="">请选择</option>
                    @foreach($industry_list as $item)
                        <option value="{{$item['id']}}" @if(in_array($item['id'],$info["industry_type"])) selected @endif >{{$item['name']}}</option>
                    @endforeach
                </select>
            </div>

        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">政策附件：</label>
            <div class="col-sm-8">
                <input type="hidden" name="file" value="" id="file" required>
                <x-upload type="file" name="file_upload" :extend="['cat'=>'file','link'=>1,'exts'=>'txt|rar|doc|png|pdf|docx|xlsx|ppt|pptx', 'tips'=>'格式为txt|rar|doc|png|pdf|docx|xlsx|ppt|pptx；大小不能超过100M','data'=>$info['file']]"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">政策内容：</label>
            <div class="col-sm-8">
                <textarea class="summernote_content hide" id="content" name="content">{{$info['content']}}</textarea>
                <div class="summernote" data-place="请输入政策内容" id="contentEditor"></div>
            </div>
        </div>

    </form>
@stop

@section('script')
    <script>
        $(function () {
            $("#publish_date").click(function () {
                WdatePicker({dateFmt: 'yyyy-MM-dd'})
            });
            $("#end_date").click(function () {
                WdatePicker({minDate: '#F{$dp.$D(\'publish_date\')}', dateFmt: 'yyyy-MM-dd'})
            });
            if($("input[name='file_upload[]']").length>0){
                $("input[name='file_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval){
                        file = imgval
                    }
                })
            }
            $("#file").val(file);
        });
        function submitHandler() {
            if ($.validate.form()) {
                if($("#contentEditor").summernote('isEmpty')){
                    $.modal.msgError('请填写内容')
                    return false;
                }
                var sHTML = $('#contentEditor').summernote('code');
                $("#content").val(sHTML);
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop

