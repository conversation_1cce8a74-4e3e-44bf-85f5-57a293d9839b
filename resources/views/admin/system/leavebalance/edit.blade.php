@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}
@section('css_common')
    <style>
        .col-xs-2 {
            min-width: 100px;
            padding-top: 7px;
            padding-right:0;
            text-align: right;
        }
    </style>
@append
@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-xs-2 control-label">{{$user_type_text}}：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <div class="form-control-static">{{$info['username']}}</div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label is-required">年假：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <div class="input-group">
                <input type="number" name="nj_balance" min="0" step="1" value="{{$info['nj_balance'] ?? '0'}}" placeholder="请输入" class="form-control" autocomplete="off">
                <div class="input-group-addon">天</div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label is-required">调休：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <div class="input-group">
                <input type="number" name="tx_balance" min="0" step="1" value="{{$info['tx_balance'] ?? '0'}}" placeholder="请输入" class="form-control" autocomplete="off">
                <div class="input-group-addon">小时</div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label is-required">陪产假：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <div class="input-group">
                <input type="number" name="pcj_balance" min="0" step="1" value="{{$info['pcj_balance'] ?? '0'}}" placeholder="请输入" class="form-control" autocomplete="off">
                <div class="input-group-addon">天</div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label is-required">育儿假：</label>
        <div class="col-xs-5" style="padding-left: 0;">
            <div class="input-group">
                <input type="number" name="yej_balance" min="0" step="1" value="{{$info['yej_balance'] ?? '0'}}" placeholder="请输入" class="form-control" autocomplete="off">
                <div class="input-group-addon">天</div>
            </div>
        </div>
    </div>

</form>
@stop

@section('script')
    <script>

        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
