@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }
        tr td .badge-primary {
            font-size: 14px;
            margin-right: 5px;
            min-width: 50px;
        }
    </style>
@append
@section('content')
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("system:{$app}:export")
        <a class="btn btn-warning" onclick="exportExcel()">导出</a>
        @endhasPerm
        @hasPerm("system:{$app}:import")
        <a class="btn btn-warning" onclick="importExcel()">导入</a>
        @endhasPerm
        @hasPerm("system:{$app}:clear")
        <a class="btn btn-warning" onclick="clearAll()">清空余额</a>
        @endhasPerm
        <div class="pull-right">
            <form id="role-form">
                <div class="select-list">
                    <ul>
                        @if($user_type == \App\Models\System\Users::TYPE_WGY)
                        <li>
                            <select name="cid" id="cid" class="select2">
                                <option value="">所属社区</option>
                                @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                                    <option value="{{$id}}">{{$name}}</option>
                                @endforeach
                            </select>
                        </li>
                        @elseif ($user_type == \App\Models\System\Users::TYPE_CG)
                            <li>
                                <select name="where[group_id]" class="select2">
                                    <option value="">所属分组</option>
                                    @foreach($urbanGroup as $item)
                                        <option value="{{$item['id']}}">{{$item['name']}}</option>
                                    @endforeach
                                </select>
                            </li>
                            <li>
                                <select name="where[identity]" class="select2">
                                    <option value="">身份</option>
                                    @foreach($urbanIdentity as $key=>$item)
                                        <option value="{{$key}}">{{$item}}</option>
                                    @endforeach
                                </select>
                            </li>
                        @endif
                        <li><input type="text" name="keywords" value="" placeholder="姓名电话" autocomplete="off"></li>
                        <li>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "{{$user_type_text}}假期余额",
                sortName:'id',
                sortOrder: "desc",
                showToolbar: false,
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'username',
                        title: '姓名',
                    },
                    {
                        field: 'mobile',
                        title: '手机号',
                    },
                    @if($user_type == \App\Models\System\Users::TYPE_WGY)
                    {
                        field: 'grid.community.name',
                        title: '所属社区',
                    },
                    {
                        field: 'grid.name',
                        title: '所属网格',
                    },
                    @elseif ($user_type == \App\Models\System\Users::TYPE_CG)
                    {
                        field: 'urban_group.name',
                        title: '所属分组',
                    },
                    {
                        field: 'identity_text',
                        title: '身份',
                    },
                    @endif
                    {
                        field: '',
                        title: '假期余额',
                        formatter: function (value, row, index) {
                            return '年假'+row.nj_balance+'天、调休'+row.tx_balance+'小时、陪产假'+row.pcj_balance+'天、育儿假'+row.yej_balance+'天'
                        }
                    },
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:{$app}:edit")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" data-width="500" data-height="400" onclick="$.operate.edit(\'' + row.id + '\', this)">编辑</a> ');
                            @endhasPerm

                            @hasPerm("system:{$app}:clear")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="clearBalance(\'' + row.id + '\',\'' + row.username + '\')">清空余额</a> ');
                            @endhasPerm
                            @hasPerm("system:{$app}:log")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="logs(\'' + row.id + '\',\'' + row.username + '\')">余额明细</a> ');
                            @endhasPerm
                                return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function clearBalance(id, username) {
            $.modal.confirm("确定清空【" + username + "】假期余额吗？", function() {
                $.operate.submit('{{route("system.{$app}.clear")}}?type=one', "post", "json", { "id": id });
            });
        }
        function logs(id, username) {
            var options = {
                title: "【" + username + "】假期余额",
                full: true,
                url: '{{route("system.{$app}.log")}}?id='+id,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            };
            $.modal.openOptions(options);
        }
        function clearAll() {
            $.modal.confirm("确定清空所有{{$user_type_text}}假期余额吗？", function() {
                $.operate.submit('{{route("system.{$app}.clear")}}?type=all', "post", "json");
            });
        }
        function exportExcel() {
            $.modal.loading('正在处理中，请稍后...');
            var dataParam = new FormData(document.querySelector('#role-form'));
            var url = '{{route("system.{$app}.export")}}';
            var xhr = new XMLHttpRequest();
            xhr.open('POST', url, true);    // 也可以使用POST方式，根据接口
            xhr.responseType = "blob";    // 返回类型blob
            xhr.setRequestHeader("X-CSRF-TOKEN", $('meta[name="csrf-token"]').attr('content'));
            // xhr.setRequestHeader('content-type', 'application/json');
            xhr.onload = function () {
                // 请求完成
                if (this.status === 200) {
                    // 返回200
                    var response = this.response;
                    let contentType = xhr.getResponseHeader('Content-Type');
                    contentType = contentType != '' ? contentType : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    let blob = new Blob([response], {type: contentType});
                    const reader = new FileReader();
                    if (contentType === 'application/json' || contentType === 'application/javascript') {
                        reader.readAsText(blob, 'utf-8');
                        reader.onload = (e) => {
                            const resp = JSON.parse(reader.result);
                            if (resp.hasOwnProperty('code') && resp.code != web_status.SUCCESS) {
                                $.modal.alertError(resp.msg);
                            } else {
                                $.modal.alertError('导出失败');
                            }
                        };
                    } else {
                        const disposition = xhr.getResponseHeader('Content-Disposition');
                        let fileName;
                        let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
                        let matches = filenameRegex.exec(disposition)
                        if (matches != null && matches[1]) {
                            fileName = matches[1].replace(/['"]/g, '')
                        }
                        // 通过 URLEncoder.encode(pFileName, StandardCharsets.UTF_8.name()) 加密编码的, 使用decodeURI(fileName) 解密
                        fileName = decodeURI(fileName)
                        var urlCreator = window.URL || window.webkitURL;
                        var url = urlCreator.createObjectURL(blob); //这个函数的返回值是一个字符串，指向一块内存的地址。
                        //以下代码保存我的excel导出文件
                        var link = document.createElement('a'); //创建事件对象
                        link.setAttribute('href', url);
                        link.setAttribute("download", fileName);
                        var event = document.createEvent("MouseEvents"); //初始化事件对象
                        event.initMouseEvent("click", true, true, document.defaultView, 0, 0, 0, 0, 0, false, false, false, false, 0, null); //触发事件
                        link.dispatchEvent(event);
                    }
                } else {
                    $.modal.alertError('导出失败');
                }
                $.modal.closeLoading();
            };
            // 发送ajax请求
            xhr.send(dataParam);
        }
        function importExcel() {
            $.modal.layerinit(function (layer) {
                layer.open({
                    type: 1,
                    area: ['400px', '260px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: '导入假期余额',
                    content: $('#importTpl').html(),
                    btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    btn1: function (index, layero) {
                        var file = layero.find('#file').val();
                        if (file == '' && !$.common.endWith(file, '.xlsx', 'i')) {
                            $.modal.msgWarning("请选择后缀为 xlsx”的文件。");
                            return false;
                        }
                        var index = layer.load(2, {shade: false});
                        $.modal.disable();
                        var formData = new FormData(layero.find('form')[0]);
                        $.ajax({
                            url: '{{route("system.{$app}.import")}}',
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            type: 'POST',
                            success: function (result) {
                                if (result.code == web_status.SUCCESS) {
                                    $.modal.closeAll();
                                    if (result.data.errorNum > 0) {
                                        $.modal.layerinit(function (layer) {
                                            layer.alert(result.msg, {
                                                icon: $.modal.icon('warning'),
                                                title: "系统提示",
                                                btn: ['下载', '取消'],
                                                btnclass: ['btn btn-primary'],
                                            },function (index) {
                                                window.open(result.data.error_url)
                                            });
                                        });
                                    } else {
                                        $.modal.alertSuccess(result.msg);
                                        $.table.refresh();
                                    }
                                } else if (result.code == web_status.WARNING) {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertWarning(result.msg)
                                } else {
                                    layer.close(index);
                                    $.modal.enable();
                                    $.modal.alertError(result.msg);
                                }
                            },
                            complete: function () {
                                layero.find("#file").val("")
                            }
                        });
                    }
                });
            });
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10 ml5">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
            </div>
            <div class="col-xs-offset-1 mt10" style="color: red;">
                <p>1、点击下载模板：
                    <a download href="{{asset('template/假期余额.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                <p>2、数据将从第三行开始读取，请确保数据格式正确。</p>
                <p>3、请确保导入的姓名、电话与系统中一致。</p>
            </div>
        </form>
    </script>
@stop

