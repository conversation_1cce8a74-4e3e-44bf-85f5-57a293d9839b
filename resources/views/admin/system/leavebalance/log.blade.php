@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <input type="hidden" name="id" value="{{$user['id']}}">
            <div class="select-list">
                <ul>
                    <li>
                        <select name="where[leave_type]" class="select2">
                            <option value="">余额类型</option>
                            <option value="1">年假</option>
                            <option value="5">调休</option>
                            <option value="6">陪产假</option>
                            <option value="10">育儿假</option>
                        </select>
                    </li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        <a class="btn btn-warning btn-sm" id="table-export" data-name="{{$user['username'] ?? ''}}-假期余额明细" data-type="excel"><i class="fa fa-download"></i>
            导出
        </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/bootstrap-table-export.js')}}"></script>
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/tableExport.js')}}"></script>
    <script>
        $(function () {
            var options = {
                modalName: "[{{$user['username'] ?? ''}}]-假期余额明细",
                sortName:'create_time',
                sortOrder: "desc",
                showExport: true,
                exportTypes: ['csv', 'excel'],
                url:'{{route("system.$app.log")}}',
                columns: [
                    {checkbox: true},
                    {field: 'create_time', title: '操作时间', align: 'left', sortable: true},
                    {field: 'leave_type_text', title: '余额类型', align: 'left'},
                    {field: 'before', title: '变更前余额', align: 'left', formatter: function (value, row, index) {
                            if (row.leave_type == 5) {
                                return value + '小时';
                            }
                            return value + '天';
                        }},
                    {field: 'change', title: '变更余额', align: 'left', formatter: function (value, row, index) {
                            if (value >= 0) {
                                value = '+'+ value;
                            }
                            if (row.leave_type == 5) {
                                return value + '小时';
                            }
                            return value + '天';
                        }},
                    {field: 'after', title: '变更后余额', align: 'left', formatter: function (value, row, index) {
                            if (row.leave_type == 5) {
                                return value + '小时';
                            }
                            return value + '天';
                        }},
                    {field: 'note', title: '备注', align: 'left'},
                    {
                        title: '关联审批',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];

                            @hasPerm("street:kaoqinjiaban:detail")
                            if (row.apply_id && row.type == 3) {
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="jiaban(\'' + row.apply_id + '\')"> 加班</a> ');
                            }
                            @endhasPerm
                            @hasPerm("street:kaoqinleave:detail")
                            if (row.apply_id && row.type == 1) {
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="qinjia(\'' + row.apply_id + '\')"> 请假</a>');
                            }
                            @endhasPerm

                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function tableSearchReset() {
            layui.formSelects.value('select3', []);
        }
        function jiaban(id) {
            var options = {
                title: "审批详情",
                full: true,
                url: '{{route("street.kaoqinjiaban.detail")}}?id='+id,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            };
            $.modal.openOptions(options);
        }
        function qinjia(id) {
            var options = {
                title: "审批详情",
                full: true,
                url: '{{route("street.kaoqinleave.detail")}}?id='+id,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            };
            $.modal.openOptions(options);
        }
        function exportExcel() {
            var that = $.fn.bootstrapTable.Constructor;
            console.log( $('#myTable').bootstrapTable('getThis'));
            console.log($.fn.bootstrapTable.Constructor.this);
            doExport = function () {
                that.$el.tableExport({
                    type: 'excel', // 导出格式，可以是 'csv' 或 'excel'
                    fileName: '网格员考勤记录', // 导出文件的文件名
                    worksheetName: 'Worksheet', // Excel中的工作表名称
                    exportDataType:"all",
                    ignoreColumn:[0]
                });
            };
            that.$el.one(that.options.sidePagination === 'server' ? 'post-body.bs.table' : 'page-change.bs.table', function () {
                doExport();
                that.togglePagination();
            });
            that.togglePagination();
        }
    </script>
@stop

