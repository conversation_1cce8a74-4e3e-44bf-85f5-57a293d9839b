@extends('admin.layout.form')
@include('widget.asset.select2')
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">所属科室：</label>
            <div class="col-sm-8">
                <select name="cid" id="cid" class="select2" required>
                    <option value="">请选择</option>
                    @foreach($structs as $id=>$item)
                        <option value="{{$id}}">{{$item['name']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">管理员：</label>
            <div class="col-sm-8">
                <select name="user_id" id="user_id" class="select2" required>
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">清单制账号：</label>
            <div class="col-sm-8">
                <input type="text" name="qdz_account" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">清单制密码：</label>
            <div class="col-sm-8">
                <input type="password" name="qdz_pwd" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {
            $('#cid').on("change", function() {
                let communityId = $(this).val();
                let user = $('#user_id');
                user.select2('destroy').empty();
                user.select2({data:[{id:"",text:"请选择"}],width:200});
                if (communityId) {
                    $.ajax({
                        url: '{{route('admin.getadminbysid')}}',
                        type: 'get',
                        dataType: 'json',
                        data: {
                            sid: communityId
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                user.select2({data:res.data,width:200});
                            }
                        }
                    });
                }
            });
        });
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
