@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <div class="mt10" style="background-color: #fff;padding: 10px">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item active" role="presentation">
                <a class="nav-link" id="home-tab" data-toggle="tab" href="#resident" role="tab" aria-controls="detail" aria-selected="true">居民字段</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="log-tab" data-toggle="tab" href="#shop" role="tab" aria-controls="log" aria-selected="false">商铺字段</a>
            </li>
        </ul>
        <div class="bs-example bs-example-tabs b5navtab" data-example-id="togglable-tabs">
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane active" id="all" role="tabpanel" aria-labelledby="detail-tab">
                    <div class="btn-group-sm" id="toolbar" role="group">
                        @hasPerm("system:selffield:edit")
                        <a class="btn btn-warning" data-type="resident" onclick="$.operate.setFieldFull('',this,'{{route('system.selffield.edit')}}')"><i class="fa fa-cog"></i> 设置字段</a>
                        @endhasPerm
                    </div>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var param = '';
            $('a[data-toggle="tab"]').on('click', function (e) {
                e.preventDefault(); // 阻止默认的锚点跳转行为
                var tab = $(this).attr('href'); // 获取被点击的标签的 href
                tab = tab.replace(/^#+/, '');
                // 根据不同的标签设置不同的 iframe src
                param = tab;
                // 刷新表格
                $.table.search();
                // 激活 Bootstrap 标签
                $(this).tab('show');
                $("#toolbar a").data("type",tab)
            });

            var options = {
                modalName: "自定义字段",
                sortName:'id',
                sortOrder: "asc",
                pagination:false,
                queryParams: function (params) {
                    params['where[type]'] = param == 'shop' ? 2 : 1;
                    var formParams = $('#search-form').serializeArray();
                    $.each(formParams, function(i, field) {
                        params[field.name] = field.value;
                    });
                    return params;
                },
                columns: [
                    {checkbox: true},
                    {field: 'id', title: '编号', align: 'left', sortable: true,visible: false},
                    {field: 'field_name', title: '字段名称', align: 'left'},
                    {field: 'is_nust', title: '是否必填', align: 'center',formatter: function(value, row, index){
                            return value ? "是" : "否";
                        }},
                    {field: 'field_type_text', title: '字段类型', align: 'left'},
                    {field: 'default_value', title: '默认值', align: 'left',visible: false},
                    {field: 'extend_str', title: '扩展信息', align: 'left'},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("system:selffield:drop")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"></i> 删除</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

