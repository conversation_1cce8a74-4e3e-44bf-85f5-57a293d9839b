@extends('admin.layout.form')
{{--@include('widget.asset.beautifyhtml')--}}
@include('widget.asset.jquery-ui')
@include('widget.asset.editable')
@section('content')
    <style>
        .wrapper-content{background-color: #F1F1F1;height: 100%}
        .droppable-active{background-color:#ffe!important}
        .tools a{cursor:pointer;font-size:80%}
        .form-body .col-md-6,.form-body .col-md-12{
            padding-top: 5px;
            min-height:50px;
            background-color: #c1f3dc;
        }
        .draggable{cursor:move}
        .editable {
            border-bottom: dashed 1px #0088cc;

        }
        .remov-ibox {
            margin-left: 20px;
        }
        .iradio-blue.disabled {
            background-position: -120px 0;
            cursor: default;
        }
        .icheckbox-blue.disabled {
            background-position: -20px 0;
            cursor: default;
        }
        .remove-label-box {
            margin-left: 10px;
        }
        .add-radio-box {
            font-size: 18px;
            cursor: pointer;
        }
        .add-checkbox-box {
            font-size: 18px;
            cursor: pointer;
        }
    </style>
    <div class="row" style="height: 100%">
        <div class="col-sm-5">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>元素</h5>
                </div>
                <div class="ibox-content">
                    <div class="alert alert-info">
                        拖拽左侧的表单元素到右侧区域，即可生成相应的表单！
                    </div>
                    <form role="form" class="form-horizontal m-t">
                        <div class="form-group draggable" data-type="1">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">文本框</span>：
                            </label>
                            <div class="col-sm-8">
                                <input type="text" name="" class="form-control"  placeholder="请输入文本">
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="2">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">多行文本框</span>：
                            </label>
                            <div class="col-sm-8">
                                <textarea type="text" name="" class="form-control" placeholder="请输入文本"></textarea>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="3">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">单选框</span>：
                            </label>
                            <div class="col-sm-8">
                                <label class="radio-box" style="line-height: normal;"><input type="radio" disabled><span class="editable">是</span></label>
                                <label class="radio-box" style="line-height: normal;"><input type="radio" disabled><span class="editable">否</span></label>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="4">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">复选框</span>：
                            </label>
                            <div class="col-sm-8">
                                <label class="check-box" style="line-height: normal;"><input type="checkbox" disabled><span class="editable">选项</span></label>
                                <label class="check-box" style="line-height: normal;"><input type="checkbox" disabled><span class="editable">选项</span></label>
                                <label class="check-box" style="line-height: normal;"><input type="checkbox" disabled><span class="editable">选项</span></label>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="7">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">日期选择</span>：
                            </label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="text" class="form-control time-input" placeholder="日期选择 2023-06-21">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="8">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">日期范围选择</span>：
                            </label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="text" class="form-control date-range-input" placeholder="日期范围选择 2023-06-21~2023-06-25">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="9">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">时间选择</span>：
                            </label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="text" class="form-control datetime-input" placeholder="时间选择 2023-06-21 22:10:00">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="10">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">时间范围选择</span>：
                            </label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="text" class="form-control datetime-range-input" placeholder="时间范围选择 2023-06-21 20:11:59~2023-06-25 23:20:22">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="11">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">选择定位</span>：
                            </label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="定位选择">
                                    <span class="input-group-addon"><i class="fa fa-location-arrow"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="12">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">图片上传</span>：
                            </label>
                            <div class="col-sm-8">
                                <div class="b5uploadmainbox b5uploadfilebox" data-type="image">
                                    <button type="button" class="btn-b5upload btn btn-primary btn-sm" id="image_upload" data-exts="3" data-multi="3" data-cat="demo" data-inputname="">
                                        <i class="fa fa-upload"></i> 上传图片
                                    </button>
                                    <div class="uploadimg_link" style="width: 150px">
                                        <input type="text" min="1" placeholder="最多上传数量" style="width: 150px" class="form-control image_num" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group draggable" data-type="13">
                            <label class="col-sm-3 is-required">
                                <input type="checkbox" class="input-required" title="勾选代表该项必填">
                                <span class="editable">文件上传</span>：
                            </label>
                            <div class="col-sm-8">
                                <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                                    <button type="button" class="btn-b5upload btn btn-primary btn-sm" id="files_upload" data-exts="3" data-multi="3" data-cat="demo" data-inputname="">
                                        <i class="fa fa-upload"></i> 上传文件
                                    </button>
                                    <div class="uploadimg_link" style="width: 150px">
                                        <input type="text" min="1" placeholder="最多上传数量" style="width: 150px" class="form-control file_num" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                    </form>
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
        <div class="col-sm-7" id="group-list" style="height: 100%">
            <div class="ibox float-e-margins" id="base" data-index="0">
                <div class="ibox-title">
                    <h5>表单标题</h5>
                </div>
                <div class="ibox-content" id="ibox-content">
                        <div class="ibox float-e-margins ibox-up-down" style="margin-bottom: 5px;">
                            <div class="ibox-content">
                                <div class="row form-body form-horizontal m-t" style="margin-top: 0px;">
                                    <div class="col-md-12 droppable sortable group-input-box ui-droppable ui-sortable">
                                        <x-field-template-item :items="$info"></x-field-template-item>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')

    <script type="text/javascript">
        let index = {{$max_index}};
        $(document).ready(function() {
            init();
            $(document).on("click", ".remove-link", function(ev) {
                $(this).parent().parent().remove()
            });
            $(document).on("click", ".remov-ibox", function(ev) {
                $(this).parents('.ibox-up-down').remove()
            });
            $(document).on("click", ".remove-label-box", function(ev) {
                $(this).parent().remove()
            });
            $(document).on("click", ".up-down", function(ev) {
                let i = $(this).find('i');
                if (i.hasClass('fa-angle-up')) {
                    i.removeClass('fa-angle-up')
                    i.addClass('fa-angle-down')
                    $(this).parents('.ibox-up-down').find('.ibox-content').show();
                } else {
                    i.removeClass('fa-angle-down')
                    i.addClass('fa-angle-up')
                    $(this).parents('.ibox-up-down').find('.ibox-content').hide();
                }
            });
            $(document).on("click", ".add-radio-box", function(ev) {
                index++;
                let html = '<label class="radio-box" style="line-height: normal;" data-index="'+index+'">'+
                    '     <div class="iradio-blue disabled">'+
                    '         <input type="radio" disabled="" style="position: absolute; opacity: 0;">'+
                    '             <ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins>'+
                    '         </div>'+
                    '     <span class="editable editable-click">选项</span>'+
                    '     <span class="remove-label-box" title="删除该项"> X</span>'+
                    ' </label>'
                $(this).before(html);
                editableInit();
            });
            $(document).on("click", ".add-checkbox-box", function(ev) {
                index++;
                let html = '<label class="check-box" style="line-height: normal;" data-index="'+index+'">'+
                    '     <div class="icheckbox-blue disabled">'+
                    '         <input type="checkbox" disabled="" style="position: absolute; opacity: 0;">'+
                    '             <ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins>'+
                    '         </div>'+
                    '     <span class="editable editable-click">选项</span>'+
                    '     <span class="remove-label-box" title="删除该项"> X</span>'+
                    ' </label>';
                $(this).before(html);
                editableInit();
            });
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '.date-range-input'
                    ,type:'date'
                    ,range:'~'
                    ,btns:['now','confirm']
                });
                laydate.render({
                    elem: '.datetime-input'
                    ,type:'datetime'
                });
                laydate.render({
                    elem: '.datetime-range-input'
                    ,type:'datetime'
                    ,range:'~'
                });
            });
        });
        var setup_draggable = function() {
            $(".draggable").draggable({
                appendTo: "body",
                helper: "clone"
            });
            $(".droppable").droppable({
                accept: ".draggable",
                helper: "clone",
                hoverClass: "droppable-active",
                drop: function(event, ui) {
                    var $orig = $(ui.draggable);
                    if (!$(ui.draggable).hasClass("dropped")) {
                        index++;
                        var $el = $orig.clone().addClass("dropped").css({
                            "position": "static",
                            "left": null,
                            "right": null
                        }).attr({'data-index':index}).appendTo(this);
                        if ($el.find("label").hasClass("radio-box")) {
                            $.each($el.find(".radio-box"), function (i, item) {
                                index++;
                                $(item).attr({'data-index':index});
                                $('<span class="remove-label-box" title="删除该项"> X</span>').appendTo(item)
                            })
                            $('<span class="add-radio-box" title="增加选项"> +</span>').appendTo($el.find('.col-sm-8'))
                        } else if ($el.find("label").hasClass("check-box")) {
                            $.each($el.find(".check-box"), function (i, item) {
                                index++;
                                $(item).attr({'data-index':index});
                                $('<span class="remove-label-box" title="删除该项"> X</span>').appendTo(item)
                            })
                            $('<span class="add-checkbox-box" title="增加选项"> +</span>').appendTo($el.find('.col-sm-8'))
                        }
                        $('<label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>').appendTo($el)
                    } else {
                        if ($(this)[0] != $orig.parent()[0]) {
                            var $el = $orig.clone().css({
                                "position": "static",
                                "left": null,
                                "right": null
                            }).appendTo(this);
                            $orig.remove()
                        }
                    }
                    editableInit();
                }
            }).sortable()
        };

        function editableInit()
        {
            $('.editable').editable({
                emptytext: "空",
                showbuttons:true,
            });
            $('.editable1').editable({
                emptytext: "空",
                showbuttons:true,
                mode: "inline",
            });
        }
        function init() {
            setup_draggable();
            editableInit();
        }
        function submitHandler() {
            $.modal.disable();
            $.modal.loading("正在处理中，请稍后...");
            input = $('#group-list .ibox').find('.group-input-box')
            let extend = getInputList(input);
            $.operate.save(oesUrl, {data:extend,field_type:"{{$input['type'] ?? 'resident'}}"});
        }
        function getInputList(input) {
            let data = [];
            $.each(input.find('.form-group'), function (i, obj) {
                let item = $(obj);
                let item_index = item.data('index');
                let item_type = item.data('type');
                let item_title = item.find('span').html();
                let required = item.find('.input-required').is(':checked');
                let extend = [];
                if (item_type == '13') {
                    extend = {file_num:item.find('.file_num').val()};
                } else if (item_type == '12') {
                    extend = {file_num:item.find('.image_num').val()};
                } else if (item_type == '3') {
                    $.each(item.find('.radio-box'), function (j, label) {
                        let obj = $(label);
                        let index = obj.data('index');
                        let title = obj.find('span.editable').html();
                        extend.push({
                            index:index,
                            title:title,
                        });
                    })
                } else if (item_type == '4') {
                    $.each(item.find('.check-box'), function (j, label) {
                        let obj = $(label);
                        let index = obj.data('index');
                        let title = obj.find('span.editable').html();
                        extend.push({
                            index:index,
                            title:title,
                        });
                    })
                }
                data.push({
                    index:item_index,
                    type:item_type,
                    title:item_title,
                    required:required ? 1 : 0,
                    extend:extend,
                });
            });
            return data;
        }
    </script>
@stop
