@extends('admin.layout.form')
@include('widget.asset.select2')
@include('widget.asset.ztree')
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">所属社区：</label>
            <div class="col-sm-8">
                <select name="cid" id="cid" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                        <option value="{{$id}}">{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">网格名称：</label>
            <div class="col-sm-8">
                <div style="display: flex">
                    <div class="form-control-static" style="display: inline-block">
                        第
                    </div>
                    <input type="number" min="1" step="1" name="grid_num" style="width: 100px;display: inline-block;margin: 0 15px;" value="" class="form-control" required autocomplete="off"/>
                    <div class="form-control-static" style="display: inline-block">
                        网格
                    </div>

                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请输入正整数</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">包含小区：</label>
            <div class="col-sm-8">
                <label class="check-box">
                    <input type="checkbox" value="1" class="treeOpClass">展开/折叠
                </label>
                <label class="check-box">
                    <input type="checkbox" value="2" class="treeOpClass">全选/全不选
                </label>
                <input type="hidden" name="treeId" id="treeId" value="">
                <div id="menuTrees" class="ztree">请先选择所属社区</div>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {

            $('input.treeOpClass').on('ifChanged', function(obj){
                var type = $(this).val();
                var checked = obj.currentTarget.checked;
                if (type == 1) {
                    if (checked) {
                        $._tree.expandAll(true);
                    } else {
                        $._tree.expandAll(false);
                    }
                } else if (type == "2") {
                    if (checked) {
                        $._tree.checkAllNodes(true);
                        $.tree.zOnCheck();
                    } else {
                        $._tree.checkAllNodes(false);
                        $.tree.zOnCheck();
                    }
                }
            })
            $('#cid').change(function (){
                var cid = $(this).val();
                $('#treeId').val('');
                var options = {
                    id: "menuTrees",
                    url: "{{ route('admin.getgridselectunit') }}?cid="+cid,
                    ismult:true,
                    childparent:false,
                    expandLevel: 1,
                    callBack:function (tree) {
                        tree.setting.check.chkboxType = { "Y": "ps", "N": "ps" };
                        if (tree.getNodes().length === 0) {
                            $('#menuTrees').html('暂无可选小区');
                        }
                    }
                };
                $.tree.init(options);
            });
        })
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
