@extends('admin.layout.full')

@include('widget.asset.select2')
@section('css_common')
    <style>
        .bootstrap-table .select-list li {
            display: inline-block;
            margin: 0 15px 5px 0;
        }
        .bootstrap-table .bs-bars {
            width: 100%;
        }
        .bootstrap-table .select-list li:last-child {
            margin-right: 0;
        }

    </style>
@append
@section('content')
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="btn-group-sm" id="toolbar" role="group" style="width: 100%">
                @hasPerm("system:gridgrid:add")
                <a class="btn btn-warning" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
                @endhasPerm
                @hasPerm("system:{$app}:import")
                <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.importExcel()">
                    <i class="fa fa-upload"></i> 导入
                </a>
                @endhasPerm
                @hasPerm("system:{$app}:export")
                <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.exportExcel()">
                    <i class="fa fa-download"></i> 导出
                </a>
                @endhasPerm
                <div class="pull-right">
                    <form id="role-form">
                        <div class="select-list">
                            <ul>
                                <li>
                                    <select name="where[cid]" class="select2">
                                        <option value="">所属社区</option>
                                        @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                                            <option value="{{$id}}">{{$name}}</option>
                                        @endforeach
                                    </select>
                                </li>
                                <li>
                                    <select name="is_region" class="select2">
                                        <option value="">是否设置区域</option>
                                        <option value="1">是</option>
                                        <option value="0">否</option>
                                    </select>
                                </li>
                                <li><input type="text" name="like[name]" value="" placeholder="网格名称" autocomplete="off"></li>
                                <li>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
    <script>
        $(function () {
            getUserList();
        });

        function getUserList() {
            var options = {
                modalName: "网格",
                sortName:'id',
                sortOrder:'desc',
                showToolbar: false,
                columns: [
                    {field: 'id', title: '网格ID', align: 'left', sortable: true,visible: false},
                    {
                        field: 'name',
                        align: 'left',
                        title: '网格名称',
                    },
                    {
                        field: 'community.name',
                        align: 'left',
                        title: '所属社区'
                    },
                    {
                        field: 'res_names',
                        align: 'left',
                        title: '绑定小区'
                    },
                    {
                        field: 'is_region',
                        title: '是否设置区域',
                        formatter: function(value, row, index) {
                            return $.view.statusShow(row,false,['否','是'], 'is_region');
                        }
                    },

                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true,visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        export:false,
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("system:gridgrid:region")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="region(\'' + row.id + '\', \'' + row.name + '\')"> 区域</a> ');
                            @endhasPerm
                            @hasPerm("system:gridgrid:edit")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\', \'' + row.name + '\')"> 编辑</a> ');
                            @endhasPerm
                            @hasPerm("system:gridgrid:drop")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\', \'' + row.name + '\')"> 删除</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        }

        function region(id, title) {
            $.modal.openFull("地图区域-"+title, '{{route('system.gridgrid.region')}}?id='+id);
        }
    </script>
    <script id="importTpl" type="text/template">
        <form enctype="multipart/form-data" class="mt20 mb10">
            <div class="col-xs-offset-1">
                <input type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <div class="pull-left mt10" style="color: red">
                    <p>1、点击下载模板：
                        <a download href="{{asset('template/网格.xlsx')}}" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
                    <p>2、仅允许导入“xlsx”格式文件！</p>
                </div>
            </div>
        </form>
    </script>
@stop

