@extends('admin.layout.layout')
@include("widget.asset.select2")
@section('content')
<div class="col-sm-12 search-collapse">
    <form id="role-form">
        <div class="select-list">
            <ul>
                <li class="select-time">
                    <input type="text" name="start_date" id="startTime" value="{{$params['start_date']}}" placeholder="开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="end_date" id="endTime" value="{{$params['end_date']}}" placeholder="结束时间" readonly>
                </li>
                <li>
                    <select name="group_id" class="select2">
                        <option value="">所属分组</option>
                        @foreach($groups as $item)
                            <option value="{{$item['id']}}">{{$item['name']}}</option>
                        @endforeach
                    </select>
                </li>
                <li>
                    <select name="position_id" class="select2" id="">
                        <option value="">请选择身份</option>
                        @foreach($positions as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                </li>
                <li><input type="text" name="group_user" placeholder="姓名或手机号" autocomplete="off" ></li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">

</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/bootstrap-table-export.js')}}"></script>
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/tableExport.js')}}"></script>
    <script>
        $(function () {
            var options = {
                modalName: "城管轨迹",
                sortName:'id',
                sortOrder: "desc",
                showExport: true,
                exportTypes: ['csv', 'excel'],
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'center', sortable: true},
                    {field: 'date_range', title: '统计时间', align: 'center'},
                    {field: 'username', title: '姓名', align: 'center'},
                    {field: 'mobile', title: '手机号', align: 'center'},
                    {field: 'group_name', title: '分组', align: 'center'},
                    {field: 'identity_text', title: '身份', align: 'center'},
                    {field: 'step_num', title: '步数', align: 'center', sortable: true},
                    {field: 'stay_num', title: '停留次数<a href="javascript:;" class="tooltip-show" data-toggle="tooltip" title="停留60分钟及以上的次数"><i class="fa fa-info-circle"></i></a>', align: 'center', sortable: true},
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("urban:trajectory:viewguiji")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="viewguiji(\'' + row.id + '\',\'' + row.date_range + '\')"> 轨迹</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function viewguiji(id, date_range) {
            var url = '{{route('urban.trajectory.viewguiji')}}';
            $.modal.layerinit(function (layer) {
                var index = layer.open({
                    type: 2,
                    fix: false,
                    maxmin: true,
                    shade: 0.3,
                    title: '轨迹',
                    content: url+'?id='+id+'&date_range='+date_range,
                    btn: ['关闭'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    cancel: function (index) {
                        return true;
                    }
                });
                layer.full(index);
            });
        }
    </script>
@stop

