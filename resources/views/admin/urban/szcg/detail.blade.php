
@extends('admin.layout.form')
@include('widget.asset.viewer')
@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }
        .multiple-title{
            cursor: pointer;
            padding: 10px;
        }
        .multiple-title.active{
            color: #1d6eff;
            background-color: #f0f2f5;
            border-radius: 5px;
        }

        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            background-color: #f7f8fa;
            padding: 20px 5px;
        }
        .detail-box .arrange {
            padding-left: 10px;
            padding-right: 10px;
        }
        .detail-box .arrange:first-child{
            padding-left: 0;
        }
        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding: 20px;
        }

        .btn-xs{
            font-size: 12px;
        }
        .xgry{
            display: none;
        }
        .flip-clock-label {
            display: none;
        }
        .header-title {
            font-style: normal;
            font-weight: 500;
            font-size: 22px;
            line-height: 40px;
            padding-left: 10px;
            color: #000000;
        }
        .mbig-btn {
            background-color: #fff;
            border: 1px solid #2256FF;
            color: #2256FF !important;
            padding: 8px 5px;
            border-radius: 7px;
            margin: 0 5px;
            display: inline-block;
            min-width: 96px;
            text-align: center;
        }
        .mbig-btn:hover{
            background-color: #2256FF;
            border-color: #2256FF;
            color: #FFFFFF !important;
        }
        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
        }
        .butuchu {
            color: #000000;
            font-size: 14px;

        }
        .butuchu1 {
            color: #999999;
            font-size: 14px;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 20px;
            height: 20px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .history-edit {
            color: #1d6eff;
            background-color: #f0f2f5;
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
            text-align: center;
            line-height: 24px;
        }
        .table.table-tr-b>tbody>tr>td {
            border: none;
            border-bottom: 1px solid #ecedf1 !important;
        }
        .table.table-tr-b>thead>tr>th {
            border: none;
            background-color: #f7f8fa;
            color: black;
            height: 45px;
        }
        #countdown {
            background-color: #2357ff;
            color: #fff;
            padding: 6px 10px 6px 20px;
            border-radius: 7px;
            letter-spacing: 10px;
            text-align: center;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color: #f1f8fe;
            margin: 0px 1px -8px 0px;
            padding: 15px;
            border-radius: 4px;
        }
        .tooltip-inner {
            padding: 4px 7px;
            margin: 2px;
            line-height: 14px;
        }
        .form-control-static {
            word-break: break-all;
        }
        .b5uploadimg_cell img {
            cursor: pointer;
        }
    </style>
@append
@section('content')
{{--<div class="row">--}}
{{--    <span class="header-title">事件详情</span>--}}
{{--    <span  class="pull-right" style="float: right;padding-right: 30px;padding-top: 10px">--}}
{{--            <a href="javascript:void(0)" class="mbig-btn" id="show-video-map"><i class="fa fa-file-movie-o"></i>视频和地图</a>--}}
{{--            @if($info['buttons']['assign'] && hasPerm('info:inforeport:assign'))--}}
{{--                <a href="javascript:void(0)" id="btn-assign" class="mbig-btn"><i class="fa fa-user-plus"></i> 增派</a>--}}
{{--            @endif--}}
{{--        </span>--}}
{{--</div>--}}
<div class="row detail-box">
    <div class="col-sm-6 arrange" id="base-info">
        <div class="item">
            <form action="" class="form-horizontal" style="padding-bottom: 10px;">
                <div>
                    <div class="row">
                        <div class="item-head-title">基本信息</div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">任务号：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['task_num']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">问题来源：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['event_src_name']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">问题类型：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['event_type_name']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">案卷类型：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['rec_type_name']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">大类名称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['main_type_name']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">小类名称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['sub_type_name']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">问题描述：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static">{{$info['event_desc']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">采集员：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['report_patrol_name']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">所属区域：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['district_name']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">所属街道：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['street_name']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">所属社区：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['community_name']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">地址描述：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static">{{$info['address']}}</div>
                        </div>
                    </div>
                    @foreach($mediaList as $key=>$media)
                        <div class="form-group">
                            <label class="col-sm-2 control-label">{{$media['usage']}}：</label>
                            <div class="col-sm-10">
                                <div class="b5uploadimgbox" style="display: inline-flex">
                                    <div class="b5uploadlistbox" id="images{{$key}}">
                                        @foreach($media['list'] as $item)
                                            @if($item['type'] == 'IMAGE')
                                                <div class="b5upload_li">
                                                    <div class="b5uploadimg_con">
                                                        <div class="b5uploadimg_cell">
                                                            <img src="{{$item['url']}}" loading="lazy" alt="">
                                                        </div>
                                                    </div>
                                                </div>
                                            @else
                                                <div class="b5upload_li">
                                                    <div class="b5uploadimg_con">
                                                        <div class="b5uploadimg_cell" style="flex-direction: column;">
                                                            <div >
                                                                <a href="{{$item['url']}}" target="_blank">
                                                                    <i class="fa @if($item['type'] == 'VIDEO')fa-file-video-o @else fa-file @endif " style="font-size: 6em;" aria-hidden="true"></i>
                                                                </a>
                                                            </div>

                                                            <div class="help-block m-b-none" style="text-align: center;">
                                                                <a href="{{$item['url']}}" class="tooltip-show" data-toggle="tooltip" data-target="copy" target="_blank" title="{{$item['name']}}">{{$item['sort_name']}}</a>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                    @if (isset($info['szcg']['status']) && in_array($info['szcg']['status'], [3,4]) && !empty($info['szcg']['ucenter_image']))
                    <div class="row">
                        <div class="item-head-title">处置</div>
                    </div>
                        @if (in_array($info['szcg']['status'], [4]))
                        <div class="form-group">
                            <label class="col-sm-2 control-label">处置后：</label>
                            <div class="col-sm-10">
                                @if (!empty($info['szcg']['ucenter_image']))
                                    <div class="b5uploadmainbox b5uploadimgbox">
                                        <div class="b5uploadlistbox imgs_upload_imglist" id="ucenter_image">
                                            @foreach($info['szcg']['ucenter_image'] as $img)
                                                <div class="b5upload_li">
                                                    <div class="b5uploadimg_con">
                                                        <div class="b5uploadimg_cell">
                                                            <img src="{{$img}}" alt="">
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        @endif
                    @endif
                    <div id="btns" style="margin-top: 20px;">
                        @if(!empty($info['buttons']['audit']) && hasPerm('urban:szcg:audit'))
                            <a href="javascript:void(0)" onclick="audit()" class="mbig-btn">审核</a>
                        @endif
                        @if(!empty($info['buttons']['handle']) && hasPerm('urban:szcg:handle'))
                            <a href="javascript:void(0)" onclick="handle()" class="mbig-btn">处理</a>
                        @endif
                        @if(!empty($info['buttons']['delay']) && hasPerm('urban:szcg:delay'))
                            <a href="javascript:void(0)" onclick="delay()" class="mbig-btn">延期</a>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-6 arrange" id="flow">
        <div class="item">
            <div style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-bottom: 10px;">
                <div style="display: flex">
                    <strong class="item-head-title">流程</strong>
                </div>
            </div>
            <div class="flow-tree">
                <ul class="layui-timeline">
                    @foreach($flowList ?? [] as $item)
                        <li class="layui-timeline-item">
                            <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                            <div class="layui-timeline-content layui-text">
                                <div class="flow-item">
                                    <p class="butuchu">{{$item['create_time']}}</p>
                                    <div style="display: inline-block;">
                                        <div class="butuchu">{{$item['struct_name']}}  {{$item['username']}}</div>
                                    </div>
                                    <div style="display: inline-block;position: relative">
                                        <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                            <div class="tooltip-inner" style=" background-color:#0076f6;">{{$item['action_name']}}</div>
                                        </div>
                                    </div>
                                    @if ($item['detail'] != '')
                                        <div>
                                            <div class="butuchu">{{$item['detail']}}</div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
</div>
@stop
@section('script')
    <script>

        $(function () {
            $.each($(".b5uploadimgbox .b5uploadlistbox"),function (){
                new Viewer(this,{title:false});
            })
        });
        function refresh() {
            window.location.reload();
        }
        function audit() {
            $.modal.open("审核", '{{route('urban.szcg.audit')}}?id={{$info['rec_id']??''}}');
        }
        function handle() {
            $.modal.open("处理", '{{route('urban.szcg.handle')}}?id={{$info['rec_id']??''}}');
        }
        function delay() {
            $.modal.open("延期", '{{route('urban.szcg.delay')}}?id={{$info['rec_id']??''}}');
        }



    </script>
@stop
