@extends('admin.layout.form')
@include('widget.asset.select2')
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('content')
    <script>
        var annexIndex = 0;
    </script>
<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['rec_id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">延期原因：</label>
        <div class="col-sm-9">
            <select class="select2" name="delay_reason" data-width="100%" required>
                <option value="">请选择</option>
                @foreach($delayReason as $k=>$v)
                    <option value="{{$k}}">{{$v}}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">延期方式：</label>
        <div class="col-sm-9">
            <label class="radio-box">
                <input type="radio" name="delay_type" value="1" checked>选择具体时间
            </label>
            <label class="radio-box">
                <input type="radio" name="delay_type" value="2">填写延期时长
            </label>
        </div>
    </div>
    <div class="form-group" id="delay_type_select">
        <label class="col-sm-3 control-label is-required">处置完成日期：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control time-input" placeholder="请选择时间" readonly data-type="datetime" data-format="yyyy-MM-dd HH:mm:ss" name="delay_time" required="" autocomplete="off" aria-required="true">
        </div>
    </div>
    <div class="form-group" style="display: none" id="delay_type_input">
        <label class="col-sm-3 control-label is-required">延期时长：</label>
        <div class="col-sm-9">
            <div style="display: flex;line-height: 32px;">
                <input type="number" min="0" step="1" class="form-control" style="display: inline-block;width: 80px" name="delay_day">天
                <input type="number" min="0" step="1" max="59" class="form-control" style="display: inline-block;width: 80px" name="delay_hour">小时
                <input type="number" min="0" step="1" max="59" class="form-control" style="display: inline-block;width: 80px" name="delay_minute">分
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">备注：</label>
        <div class="col-sm-9">
            <textarea rows="3" placeholder="请输入"  name="note" required class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">附件：</label>
        <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
            <div class="col-sm-9">
                <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                    <button type="button" class="btn-b5upload btn btn-warning btn-sm" id="annex" data-exts=""  data-multi="9"  data-cat="work" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 最多上传9个文件</span>
                    <div class="b5uploadlistbox annex_filelist" id="annex_filelist"></div>
                </div>
            </div>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        b5uploadfileinit('annex',uploadAfter);
        dragula([annex_filelist]);
        $("#form-add").validate();

        $(function () {
            $('input[name=delay_type]').on('ifChecked', function () {
                if ($(this).val() == 2) {
                    $('#delay_type_input').show();
                    $('#delay_type_select').hide();
                } else {
                    $('#delay_type_input').hide();
                    $('#delay_type_select').show();
                }
            })
        });
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('urban.szcg.delay')}}', $('#form-add').serialize());
            }
        }

        function uploadAfter(id, data) {
            annexIndex++;
            let ext = data.ext;
            let url = data.url;
            let filename = data.originName;
            if(!filename){
                filename = getFileName(url);
            }
            var classname = getExtClass(url);
            var html='<div class="b5upload_li">' +
                '           <input type="hidden" name="annex['+annexIndex+'][url]" value="'+url+'">' +
                '           <input type="hidden" name="annex['+annexIndex+'][originName]" value="'+filename+'">' +
                '           <input type="hidden" name="annex['+annexIndex+'][ext]" value="'+ext+'">' +
                '           <div class="b5upload_filetype '+classname+'"></div>' +
                '           <div class="b5upload_filename">'+filename+
                '        </div>' +
                '               <div class="b5upload_fileop">' +
                '                   <a href="javascript:;" onclick="b5uploadImgRemove(this)"><i class="fa fa-trash-o"></i>删除</a>' +
                '                  <a href="'+url+'" target="_blank"><i class="fa fa-hand-o-right"></i>查看</a>' +
                '               </div>' +
                '      </div>';
            b5uploadhtmlshow(id,html);
        }
    </script>
@stop
