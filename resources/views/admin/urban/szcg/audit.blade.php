@extends('admin.layout.form')
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['rec_id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">处置照片：</label>
        <div class="col-sm-8">
            <input type="hidden" name="ucenter_image" value="" id="imgs" required>
            <x-upload type="img" name="imgs_upload"
                      :extend="['cat'=>'demo',
                      'link'=>0,'multi'=>9,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M；最多上传9张；可拖动排序','data'=>$info['szcg']['urban_image']]"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">备注：</label>
        <div class="col-sm-9">
            <textarea rows="3" placeholder="请输入"  name="note" required class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-add").validate();

        function submitHandler() {
            var imgs = [];
            if($("input[name='imgs_upload[]']").length>0){
                $("input[name='imgs_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval){
                        imgs.push(imgval)
                    }
                })
            }
            $("#imgs").val(imgs.join(','));
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('urban.szcg.audit')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
