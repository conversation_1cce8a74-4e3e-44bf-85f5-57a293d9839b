@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <div class="col-sm-12 search-collapse">
        <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 10px;">
            <li role="presentation" class="active">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(0)">全部 <span id="status0">0</span></a>
            </li>

            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(1)">待处理 <span id="status1">0</span></a>
            </li>

            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(2)">处置中 <span id="status2">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(3)">待审核 <span id="status3">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(4)">已处置 <span id="status4">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(5)">已回退 <span id="status5">0</span></a>
            </li>
        </ul>
        <form id="role-form">
            <input type="hidden" name="status" value="">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="like[event_desc]" placeholder="搜索"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        {{--@hasPerm("urban:export")
                        <a class="btn btn-warning btn-sm" onclick="$.table.exportExcel()"><i class="fa fa-download"></i>
                            导出</a>
                        @endhasPerm--}}
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table" data-auto-refresh="true" data-auto-refresh-silent="true" data-auto-refresh-status="true" data-auto-refresh-interval="10"></table>
    </div>
@stop

@section('script')
    <script src="{{asset('static/plugins/bootstrap-table/extensions/auto-refresh/bootstrap-table-auto-refresh.js')}}"></script>
    <script>
        $(function () {
            var options = {
                modalName: "事件",
                sortName: 'rec_id',
                sortOrder: "desc",
                detailUrl:'{{route('urban.szcg.detail')}}?id=%id%',
                columns: [
                    {field: 'task_num', title: '任务号', align: 'left'},
                    {field: 'end_time', title: '当前阶段剩余时间', align: 'left'},
                    {field: 'dispose_deadline', title: '当前阶段截止时间', align: 'left', sortable: true},
                    {field: 'create_time', title: '上报时间', align: 'left'},
                    {field: 'event_src_name', title: '问题来源', align: 'left'},
                    {field: 'event_desc', title: '问题描述', align: 'left', formatter: function (value, row, index) {
                            return $.table.tooltip(value, 40);
                        }},
                    {field: 'status_text', title: '状态', align: 'left'},
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("urban:szcg:detail")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="operateview(\'' + row.rec_id + '\')"> 详情</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ],
                responseHandler:function (res) {
                    if (res.code === 0) {
                        $.each(res.extend, function (i, item) {
                            $('#'+i).html(item);
                        });
                    }
                }
            };
            $.table.init(options);
        });
        function switch_status(status) {
            $('input[name=status]').val(status);
            $.table.search();
        }
        function operateview(id) {
            $.modal.openOptions({
                title: '详情',
                url: '{{route('urban.szcg.detail')}}?id='+id,
                btn:['关闭'],
                full: true,
                yes:function(index, layero){
                    layer.close(index);
                }
            });
        }
    </script>
@stop

