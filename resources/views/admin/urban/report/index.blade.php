@php use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <style>
        .fixed-table-toolbar{display: none}
    </style>
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="create_time" id="create_time" value="" placeholder="上报时间"></li>
                    @if(C_ID == 0)
                        <li>
                            <select name="group_id" class="select2">
                                <option value="">所属分组</option>
                                @foreach($urbanGroup as $item)
                                    <option value="{{$item['id']}}">{{$item['name']}}</option>
                                @endforeach
                            </select>
                        <li>
                        <li>
                            <select name="where[is_synchronization]" class="select2">
                                <option value="">是否同步</option>
                                    <option value="0" >未同步</option>
                                    <option value="1" >已同步</option>
                            </select>
                        <li>
                        <li>
                            <select name="where[is_instructions]" class="select2">
                                <option value="">是否有批示</option>
                                    <option value="0" >无批示</option>
                                    <option value="1" >有批示</option>
                            </select>
                        <li>
                    @endif
                    <li><input type="text" name="user_name" placeholder="搜索"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        {{--@hasPerm("urban:export")
                        <a class="btn btn-warning btn-sm" onclick="$.table.exportExcel()"><i class="fa fa-download"></i>
                            导出</a>
                        @endhasPerm--}}
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#create_time'
                ,type: 'month'
                ,max:'{{date('Y-m')}}'
            });
        });
        $(function () {
            var options = {
                modalName: "上报记录",
                sortName: 'id',
                sortOrder: "desc",
                detailUrl:'{{route('urban.report.detail')}}?id=%id%',
                columns: [
                    {field: 'userinfo.urban_group.name', title: '分组', align: 'left'},
                    {field: 'userinfo.username', title: '姓名', align: 'left'},
                    {field: 'create_time', title: '上报时间', align: 'center', sortable: true},
                    {
                        field: 'title', title: '上报内容', align: 'left', formatter: function (value, row, index) {
                            let htmls = '';
                            row.reportvalue.forEach((element, index) => {
                                if (index < 2) {
                                    if(element.item_type == 'radio' || element.item_type == 'checkbox'){
                                        htmls += "<p>" + element.template_item_title + ': ' + element.item_text + "</p>";
                                    }else{
                                        htmls += "<p>" + element.template_item_title + ': ' + element.item_value + "</p>";
                                    }
                                }
                            });
                            return htmls;
                        }
                    },
                    {
                        field: 'is_synchronization', title: '是否同步', align: 'left', formatter: function (value, row, index) {
                            if (value == 0) {
                                return "<p>未同步</p>";
                            }
                            if (value == 1) {
                                return "<p>已同步</p>";
                            }
                        }
                    },
                    {
                        field: 'is_instructions', title: '是否有批示', align: 'left', formatter: function (value, row, index) {
                            if (value == 0) {
                                return "<p>无批示</p>";
                            }
                            if (value == 1) {
                                return "<p>有批示</p>";
                            }
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("urban:report:detail")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="operateview(\'' + row.id + '\')"> 详情</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });

        function operateview(id) {
            $.operate.detail(id, "1200");
        }
    </script>
@stop

