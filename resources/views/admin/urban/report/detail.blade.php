@extends('admin.layout.form')
@include("widget.asset.select2")
@section('css_common')
    <style>

        .form-horizontal h4{
            /*line-height: 40px;*/
            position: relative;
            padding-left: 15px; /* 确保文字与竖线有间距 */
            margin-bottom: 10px;
            font-size: 15px;
            font-weight: normal;
        }
        h4::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px; /* 竖线宽度 */
            background-color: #0076F6; /* 竖线颜色 */
        }

        .liuchengtu{
            margin: 20px 10px;
            display: flex;
            align-items: center;
        }
        .liuchengtu .block {
            padding: 10px;
            border-radius: 5px;
            background-color: #F2F2F2;
        }
        .glyphicon{
            font-size: 20px;
        }
        .approval {
            width: 250px;
        }
        .approval:hover .approval-header-icon{
            display: block;
        }
        .approval-header-icon {
            display: none;
        }
        .approval-header-icon a{
            color: black;
        }
        .approval-body {
            display: flex;
            flex-wrap: wrap;
            padding-top: 5px;
        }
        .approval-body-item {
            display: inline-block;
            padding: 5px;
            margin: 5px;
            background-color: #FFFFFF;
            border-radius: 10px;
        }
    </style>
@append
@section('content')
    <style>
        .bg-thead{background-color: aliceblue;}
    </style>
    <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">上报信息</h3>
    <form class="form-horizontal m">
        {{--@foreach($info['reportvalue'] as $item)
            <div class="form-group">
                <label class="col-sm-2 control-label">{{$item['reportvalueformtemplateitem']['item_title']}}：</label>
                <div class="col-sm-10" style="padding-top: 8px;">
                    {{$item['item_value']}}
                </div>
            </div>
        @endforeach--}}
        @foreach($info['reportvalue'] as $item)
            <div class="form-group">
                <label class="col-sm-2 control-label">{{$item['item_title']}}：</label>
                    @if($item['item_type'] == 'file')
                    @foreach($item['item_value'] as $img)
                            <a href="{{$img['url']}}" target="_blank"> <img src="{{$img['url']}}" height="60px"></a>
                        @endforeach
                    @elseif($item['item_type'] == 'radio' || $item['item_type'] == 'checkbox')
                        @foreach($item['extend'] as $v)
                            @if($item['item_value'] == $v['index'])
                                <div class="col-sm-10" style="padding-top: 8px;">
                                    {{$v['title']}}
                                </div>
                            @endif
                        @endforeach
                    @else
                        @if($item['item_type'] == 'radio' || $item['item_type'] == 'checkbox')
                        <div class="col-sm-10" style="padding-top: 8px;">
                            {{$item['item_text']}}
                        </div>
                        @else
                        <div class="col-sm-10" style="padding-top: 8px;">
                            {{$item['item_value']}}
                        </div>
                        @endif
                    @endif
            </div>
        @endforeach
    </form>
    <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">批示</h3>
        
    <div class="liuchengtu">
            <!-- <div class="block">批示</div>
            <div class="glyphicon glyphicon-arrow-right mr20 ml20"></div> -->
            @foreach($info['reportinstructions'] as $itemSec)
                <div class="block approval">
                    <div class="approval-header">
                        <div class="approval-header-title pull-left">{{$itemSec['create_time']}}</div>
                        <div class="approval-header-icon pull-right">
                            @hasPerm("system:{$app}:edit")
                            <a href="javascript:;" onclick="editApproval({{$info['id']}}, {{$itemSec['id']}})">查看</a>
                            @endhasPerm
                            {{--@hasPerm("system:{$app}:drop")
                            <a href="javascript:;" onclick="removeApproval({{$info['id']}})"><i class="fa fa-remove"></i></a>
                            @endhasPerm--}}
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <div class="approval-body">
                        <div class="approval-body-item">{{$itemSec['instructionsadmin']['struct']['name']}}</div>
                        <div class="approval-body-item">{{$itemSec['instructionsadmin']['username']}}</div>
                    </div>
                    <div class="approval-body">
                        <div class="approval-body-item">{{$itemSec['opinion']}}</div>
                    </div>
                </div>
                @if($info['admin_id'] != $itemSec['instructionsadmin']['id'])
                    <div class="glyphicon glyphicon-arrow-right mr20 ml20"></div>
                @endif
            @endforeach
            @hasPerm("system:{$app}:instructions")
                @if($info['but_view'] == true)
                <a class="btn btn-xs" onclick="addApproval({{$info['id']}});">添加批示</a>
                @endif
            @endhasPerm
        </div>
@stop
@section('script')
    <script>
        function addApproval(report_id) {
            var title = '添加批示';
            var url = '{{route("grid.{$app}.instructions")}}?report_id='+report_id;
            $.modal.open(title, url,600,500);
        }
        function editApproval(report_id, id) {
            var title = '查看批示';
            var url = '{{route("grid.{$app}.instructionsedit")}}?report_id='+report_id+'&id='+id;
            $.modal.open(title, url,600,500);
        }
    </script>
@stop