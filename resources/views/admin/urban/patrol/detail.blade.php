@extends('admin.layout.form')
@include("widget.asset.select2")
@section('css_common')
    <style>

        .form-horizontal h4{
            /*line-height: 40px;*/
            position: relative;
            padding-left: 15px; /* 确保文字与竖线有间距 */
            margin-bottom: 10px;
            font-size: 15px;
            font-weight: normal;
        }
        h4::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px; /* 竖线宽度 */
            background-color: #0076F6; /* 竖线颜色 */
        }

        .liuchengtu{
            margin: 20px 10px;
            display: flex;
            align-items: center;
        }
        .liuchengtu .block {
            padding: 10px;
            border-radius: 5px;
            background-color: #F2F2F2;
        }
        .glyphicon{
            font-size: 20px;
        }
        .approval {
            width: 250px;
        }
        .approval:hover .approval-header-icon{
            display: block;
        }
        .approval-header-icon {
            display: none;
        }
        .approval-header-icon a{
            color: black;
        }
        .approval-body {
            display: flex;
            flex-wrap: wrap;
            padding-top: 5px;
        }
        .approval-body-item {
            display: inline-block;
            padding: 5px;
            margin: 5px;
            background-color: #FFFFFF;
            border-radius: 10px;
        }
    </style>
@append
@section('content')
    <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">巡查信息</h3>
    <form class="form-horizontal m">
        @foreach($info['reportvalue'] as $item)
            <div class="form-group">
                <label class="col-sm-2 control-label">{{$item['template_item_title']}}：</label>
                    @if($item['item_type'] == 'file')
                        <div class="col-sm-10">
                            <div class="b5uploadfilebox" style="display: inline-flex">
                                <div class="b5uploadlistbox" >
                                    @foreach((array)json_decode($item['item_value'], true) as $file)
                                        <div class="b5upload_li">
                                            <div class="b5upload_filetype "></div>
                                            <div class="b5upload_filename">
                                                <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="col-sm-10">
                            <div class="form-control-static">{{$item['item_text']}}</div>
                        </div>
                    @endif
            </div>
        @endforeach
    </form>
@stop
