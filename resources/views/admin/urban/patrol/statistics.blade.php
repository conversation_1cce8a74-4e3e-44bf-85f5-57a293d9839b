@extends('admin.layout.layout')

@include('widget.asset.treetable')
@include('widget.asset.select2')
@section('content')
    
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="month" id="month" value="{{date('Y-m')}}" readonly placeholder="巡查月份"></li>
                    <li>
                        <select name="is_completed" class="select2">
                            <option value="">完成状态</option>
                                <option value="1">未完成</option>
                                <option value="2">已完成</option>
                        </select>
                    <li>
                    <li><input type="text" name="keywords" placeholder="搜索" autocomplete="off"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#month'
                ,type: 'month'
                ,max:'{{date('Y-m')}}'
                ,btns:['now','confirm']
            });
        });
        var datas = '';
        $(function() {
            var options = {
                modalName: "巡查记录",
                detailView: true,
                pagination:false,
                url: "{{ route('urban.patrol.statistics') }}",
                columns: [
                    {field: 'title',title: '巡查类型'},
                    {field: 'target',title: '当月目标巡查次数',sortable: true},
                    {field: 'report_total', title: '累计巡查次数',sortable: true},
                    {field: 'report_month',title: '当月巡查次数',sortable: true},
                    {field: 'is_completed',title: '当月完成状态', formatter: function (value, row, index) {
                        if(value === 1){
                            return '未完成';
                        }else if (value === 2){
                            return '已完成';
                        } else {
                            return '-';
                        }
                        }},
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("urban:patrol:index")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="operateview(\'' + row.month + '\',\'' + row.id + '\')"> 详细记录</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ],
                onExpandRow : function(index, row, $detail) {
                    var childTable = $detail.html('<table style="table-layout:fixed;font-size: 12px"></table>').find('table');
                    $(childTable).bootstrapTable({
                        url: '{{route('urban.patrol.ajaxgrouplist')}}',
                        method: 'post',
                        sidePagination: "server",
                        contentType: "application/x-www-form-urlencoded",

                        queryParams: function(params) {
                            return {
                                id: row.id,
                                month: row.month,
                                orderByColumn: params.sort,
                                isAsc: params.order
                            };
                        },
                        columns: [
                            {field:'name',title: '分组',align: "left"},
                            {field: 'target',title: '当月目标巡查次数',sortable: true},
                            {field: 'report_total', title: '累计巡查次数',sortable: true},
                            {field: 'report_month',title: '当月巡查次数',sortable: true},
                            {field: 'is_completed',title: '当月完成状态', formatter: function (value, row, index) {
                                    if(value === 1){
                                        return '未完成';
                                    }else if (value === 2){
                                        return '已完成';
                                    } else {
                                        return '-';
                                    }
                                }},
                            {
                                title: '操作',
                                align: 'left',
                                formatter: function(value, row1, index) {
                                    var actions = [];
                                    @hasPerm("grid:patrol:index")
                                    actions.push('<a class="btn btn-xs" href="javascript:;" onclick="operateview(\'' + row.month + '\',\'' + row.id + '\',\'' + row1.id + '\')">详细记录</a> ');
                                    @endhasPerm
                                        return actions.join('');
                                }
                            }],
                        responseHandler: function(res) {
                            if (res.code == web_status.SUCCESS) {
                                return { rows: res.data, total: res.total, extend: res.hasOwnProperty('extend')?res.extend:{}};
                            } else {
                                $.modal.alertWarning(res.msg);
                                return { rows: [], total: 0 };
                            }
                        }
                    });
                },
            };
            $.table.init(options);
        });

        function operateview(month, tid, gid) {
            $.modal.openOptions({
                title:'巡查记录列表',
                url:'{{route('urban.patrol.index')}}?tid='+tid+'&month='+month+'&gid='+gid,
                full:true,
                btn:['关闭'],
                yes:function(index, layero){
                    layer.close(index);
                }
            });
        }
    </script>
@stop
