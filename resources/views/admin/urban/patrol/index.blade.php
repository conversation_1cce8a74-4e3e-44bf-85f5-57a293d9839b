@php use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <style>
        .fixed-table-toolbar{display: none}
    </style>
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="month" id="month" value="{{$input['month'] ?? ''}}" placeholder="月份"></li>
                    <li>
                        <select name="where[group_id]" class="select2">
                            <option value="">所属分组</option>
                            @foreach($urbanGroup as $item)
                                <option value="{{$item['id']}}" @if(isset($input['gid']) && $input['gid'] == $item['id']) selected @endif>{{$item['name']}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li>
                        <select name="where[template_id]" class="select2">
                            <option value="">所属表单模板</option>
                            @foreach($formTemplate as $item)
                                <option value="{{$item['id']}}" @if(isset($input['tid']) && $input['tid'] == $item['id']) selected @endif>{{$item['title']}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li><input type="text" name="keywords" placeholder="搜索"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        {{--@hasPerm("grid:watchman:export")
                        <a class="btn btn-warning btn-sm" onclick="$.table.exportExcel()"><i class="fa fa-download"></i>
                            导出</a>
                        @endhasPerm--}}
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#month'
                ,type: 'month'
                ,max:'{{date('Y-m')}}'
            });
        });
        $(function () {
            var options = {
                modalName: "巡查记录列表",
                sortName: 'id',
                sortOrder: "desc",
                columns: [
                    {field: 'groupinfo.name', title: '分组', align: 'left'},
                    {field: 'userinfo.username', title: '姓名', align: 'left'},
                    {field: 'create_time', title: '巡查时间', align: 'left', sortable: true},
                    {field: 'title', title: '巡查信息', align: 'left', formatter: function (value, row, index) {
                            let htmls = '';
                            row.values.forEach((element, index) => {
                                htmls += "<p >"+element.item_title+': '+element.item_text+"</p>";
                            });
                            return htmls;
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("urban:patrol:detail")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.detail(\'' + row.id + '\')"> 详情</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

