<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{$system_name}}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="{{asset('static/plugins/layui/css/layui.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/login.css')}}" rel="stylesheet"/>
    <script>
        if (window !== top) top.location.replace(location.href);
    </script>
	<style>
		.er_code{
			display: none;
			margin-top: 10px;
		}
		.app_text{
			text-decoration: underline;
			color:#ffffff;
			
		}
		.hover_text{
			display:flex;
			flex-direction: column;
			align-items: center;
		}
	   .hover_text:hover .er_code{
			display: block;
			
		}
	</style>
</head>
<body>
<div class="login-wrapper layui-anim layui-anim-scale"  style="display: flex;flex-direction: column;align-items: center;">
    <form class="layui-form" id="login-form">
        <div class="login-title">{{$system_name}}</div>
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-username"></i>
            <input class="layui-input" id="username" name="username" maxlength="11" value=""  placeholder="请输入手机号" autocomplete="off" required/>
        </div>
        <div class="layui-form-item layui-input-icon-group login-captcha-group layui-input code_btn">
            <i class="layui-icon layui-icon-password"></i>
            <input class="layui-input" id="password" name="password" value="" style="padding-left: 33px;" placeholder="请输入密码" type="password" autocomplete="off" required/>
            <img id="pwdeye" style="width: 27px;cursor: pointer;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUdwTNHR0dra2tPT09HR0dHR0dHR0dPT09HR0dLS0tHR0dLS0tHR0dDQ0HhB6yAAAAANdFJOUwDnCzD0osoddluJRLXFpER1AAABoUlEQVRIx+1Uv0vDQBQ+U7TFdrAURIRAoVLQqS2uLjqpBFyqU0ALokuhi4KKoIuD4uIiHQQVQZdCB0HH/gMOwbTa0u9/8S65H4lNMjk45Bsu73v33bt3792FkBgxYvwp0rfR80dtOlzb7cgYxjcddaxHiV5h0bGOqFBpAz36WQRWw0V3wAr9JAzgLEyTAuwSM8pANx+s0Uwg51gJHdgLFh0CVkmaduCGKUMt1z6AYWNUk2R+mUgRwNpIWtoTddckpSuAmV8q7Zx5h4KOAzR3zPp2TD5QlwVscN4Cmkw1vFGaeRbdagIDWbAuWTBY8P0313VywZj9SExRaFr5DiEHcDBdfbms7rj2FiEZoO+0EOixnI91+GA9s+zrQNttcE2lIcETLLoNNmHxY415RVP8kDpNmIly/ERLGLxvZg07u3vVwid3lh1RWbSQqBmm51ZCZzG0ipipiz3Yzj1hFnydSAL3wp4EGmG3UF72dNhtnYCtiIHtQNGpcw4OE18hD6MfQoh38bIic96wPlFHkUyISFVV1jDgkVW8rJCPf7gx/jt+AKid4q/Um1olAAAAAElFTkSuQmCC">
        </div>
        <div class="layui-form-item layui-input-icon-group login-captcha-group layui-input code_btn">
            <i class="layui-icon layui-icon-auz"></i>
            <input  class="layui-input" id="captcha" name="captcha" value="" style="padding-left: 33px;" placeholder="请输入验证码" autocomplete="off" required/>
            <img onclick="changeCaptcha()" src="" class="login-captcha" alt="点击刷新验证码" id="captcha_img"/>
        </div>
{{--        <div class="layui-form-item">--}}
{{--            <input type="checkbox" id="remember" name="remember" value="1" title="记住密码" lay-skin="primary" >--}}
{{--        </div>--}}
        <div class="layui-form-item">
            <div type="button" class="layui-btn layui-btn-fluid" id="subBtn">登录</div>
        </div>
    </form>
	<div class="hover_text">
	  <div class="app_text" style="cursor: pointer;">APP下载</div>
	  <img class="er_code" src="data:image/png;base64,{{$qrcode}}"  style="width:120px;height:120px;">  
	</div>
</div>

<div class="login-copyright">copyright © 2025 all rights reserved.</div>
<script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
<script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script src="{{asset('static/plugins/crypto-js/crypto-js.js')}}"></script>
<script src="{{asset('static/plugins/crypto-js/aes.js')}}"></script>
<script>
    $(function (){
        changeCaptcha();
        $('#pwdeye').click(function (){
            var pwd = $('#password');
            var t = pwd.attr('type');
            if (t == 'password') {
                pwd.attr('type','text');
                $(this).attr('src', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADUAAAAuCAMAAAC/KaEaAAAAOVBMVEVHcEzNzc3Nzc3Nzc3Q0NDNzc3MzMzNzc3Nzc3V1dXNzc3Nzc3MzMz////r6+vh4eHx8fH+/v7S0tLiR/+pAAAADHRSTlMAYZKBI63xxt4NPlNOUVuQAAABOklEQVRIx+VVyZaEIAwUZNXI9v8fO6O20wQC+ui5dd30pbJUQjJN3wO7cqmNAmU0Z+IRRcwaEAxf7jirBAKa9TiLgQZMkyckdKDpApmCLtRMkDgySd6FEJxP+U9pS7GRcClsF0LO07ZDcnHL4HJRUHFIh7BhhAaN90iY9k5yRekdhjE67/0r1Zgnqa+i8t6miIp5+cglmYmiTjP/9+0rReAYywU1KpZGh5uIpsuWoXyV0JFy9IBzFFCxAlSaIpayxSS5yuR0hAr7DWZuHFPhzWAsUbOIulxR15iGg/0am43JKmIOzyQTMYecmvnT+ZbPPBL1/VTk8/dl7Idvubc3YntvVDvq4kW8o8R/7MN998INZnrP64E9vzeueVNU7xZZpuk7ZO9uJS8CKrk8O7KMS7MPp9Ly4V3+avwAZ4tEuL3lSDsAAAAASUVORK5CYII=');
            } else {
                pwd.attr('type','password');
                $(this).attr('src', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUdwTNHR0dra2tPT09HR0dHR0dHR0dPT09HR0dLS0tHR0dLS0tHR0dDQ0HhB6yAAAAANdFJOUwDnCzD0osoddluJRLXFpER1AAABoUlEQVRIx+1Uv0vDQBQ+U7TFdrAURIRAoVLQqS2uLjqpBFyqU0ALokuhi4KKoIuD4uIiHQQVQZdCB0HH/gMOwbTa0u9/8S65H4lNMjk45Bsu73v33bt3792FkBgxYvwp0rfR80dtOlzb7cgYxjcddaxHiV5h0bGOqFBpAz36WQRWw0V3wAr9JAzgLEyTAuwSM8pANx+s0Uwg51gJHdgLFh0CVkmaduCGKUMt1z6AYWNUk2R+mUgRwNpIWtoTddckpSuAmV8q7Zx5h4KOAzR3zPp2TD5QlwVscN4Cmkw1vFGaeRbdagIDWbAuWTBY8P0313VywZj9SExRaFr5DiEHcDBdfbms7rj2FiEZoO+0EOixnI91+GA9s+zrQNttcE2lIcETLLoNNmHxY415RVP8kDpNmIly/ERLGLxvZg07u3vVwid3lh1RWbSQqBmm51ZCZzG0ipipiz3Yzj1hFnydSAL3wp4EGmG3UF72dNhtnYCtiIHtQNGpcw4OE18hD6MfQoh38bIic96wPlFHkUyISFVV1jDgkVW8rJCPf7gx/jt+AKid4q/Um1olAAAAAElFTkSuQmCC');
            }
        });
    });
    layui.use(['layer','form'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        $('.login-wrapper').removeClass('layui-hide');
        $("#subBtn").click(function (){
            if(!$("#username").val()){
                layer.msg('请输入账号');
                return false;
            }
            if(!$("#password").val()){
                layer.msg('请输入密码');
                return false;
            }
            if(!$("#captcha").val()){
                layer.msg('请输入验证码');
                return false;
            }
            $("#subBtn").attr('disabled', true).text('登录中...');
            var loadIndex = layer.load(2, {shade: [0.2,'#000']});
            $.ajax({
                headers:{'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')},
                type: "POST",
                url: "{{route('admin.login')}}",
                data: {
                    "username":Encrypt($("#username").val()),
                    "password":Encrypt($("#password").val()),
                    "captcha":$("#captcha").val(),
                },
                dataType: "json",
                success: function (res) {
                    if (res.success) {
                        layer.msg('登录成功',{icon: 1,shade: [0.5, '#393D49'],time:1500},function () {
                            window.location.href = "{{route('admin.index')}}";
                        });
                    } else {
                        if (res.code === 503) {
                            layer.msg(res.msg,{icon: 1,shade: [0.5, '#393D49'],time:1500},function () {
                                window.location.href = "{{route('admin.login')}}";
                            });
                        } else {
                            changeCaptcha();
                            layer.msg(res.msg,{shade: [0.5, '#393D49'],time:1500},function () {
                                $("#subBtn").text('登录').removeAttr('disabled');
                            });
                        }
                    }
                },
                complete:function(){
                    layer.close(loadIndex);
                },
                error: function () {
                    layer.msg('网络请求失败',{shade: [0.5, '#393D49'],time:1500},function () {
                        $("#subBtn").text('登录').removeAttr('disabled');
                    });
                }
            });
        });
    });
    function changeCaptcha(){
        var url = urlcreate("{{ captcha_src('admin') }}",'_t='+Math.random());
        $("#captcha_img").attr('src',url)
    }
    function urlcreate(url,params){
        if (params) {
            if (url.indexOf('?') > 0) {
                url += '&' + params;
            } else {
                url += '?' + params;
            }
        }
        return url;
    }
    function Encrypt(word) {
        //字符串可以自定义
        var key = CryptoJS.enc.Utf8.parse("{{$aes_key}}");
        var srcs = CryptoJS.enc.Utf8.parse(word);
        var encrypted = CryptoJS.AES.encrypt(srcs, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7});
        return encrypted.toString();
    }
    $(document).keyup(function(event){
        if(event.keyCode ==13){
            $("#subBtn").trigger("click");
        }
    });
</script>

</body>
</html>
