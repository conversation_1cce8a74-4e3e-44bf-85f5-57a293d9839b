
@extends('admin.layout.form')

@include('widget.asset.ztree')

@section('content')
<style>
    body {height: auto;font-family: "Microsoft YaHei";background-color: #fff !important;}
    button {font-family: "SimSun", "Helvetica Neue", Helvetica, Arial;}
</style>
<input type="hidden" name='treeId' id='treeId' value='{{$id}}'>
<input type="hidden" name='treeName' id='treeName' value=''>
<div class="wrapper">
    <div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
        <label id="btnShow" title="显示搜索" style="display:none;">︾</label>
        <label id="btnHide" title="隐藏搜索">︽</label>
    </div>
    <div class="treeSearchInput" id="search">
        <label for="keyword">关键字：</label><input type="text" class="empty" id="keyword" maxlength="50" autocomplete="off" >
        <button class="btn" id="btn" onclick="$.tree.searchNode()"> 搜索</button>
    </div>
    <div class="treeExpandCollapse">
        <a href="javascript:;" onclick="$.tree.expand()">展开</a> /
        <a href="javascript:;" onclick="$.tree.collapse()">折叠</a>
    </div>
    <div id="tree" class="ztree treeselect"></div>
</div>
@stop

@section('script')
    <script>
        let ismult = "{{$ismult??''}}";
        $(function () {
            var options = {
                url: aUrl,
                expandLevel: 1,
                ismult:false,
                childparent:true,
                showParentLevel:0,
                check: {
                    enable: true,             // 置 zTree 的节点上是否显示 checkbox / radio
                    chkStyle:'radio',
                    autoCheckTrigger:false,
                    nocheckInherit: true,      // 设置子节点是否自动继承
                    chkDisabledInherit: true,  // 设置子节点是否自动继承
                    radioType:"all",
                },
                onClick: onClick,
                // onCheck: onCheck,
                // callBack:onCheck,
            };
            $.tree.init(options);
        });
        function onClick(event, ztreeId, treeNode){
            if (treeNode.isParent) {
                $._tree.cancelSelectedNode(treeNode);
                $("#treeId").val('');
                $("#treeName").val('');
                return false;
            } else {
                var parentName = [];
                showParentAllName(treeNode,parentName);
                parentName.push(treeNode.name)
                $("#treeId").val(treeNode.id);
                $("#treeName").val(parentName.join('/'));
            }
        }
        function showParentAllName(nodes,rearr) {
            var showParentLevel = $.tree._option.showParentLevel;
            if(showParentLevel !== false){
                if(nodes.level > showParentLevel){
                    var parentnode=nodes.getParentNode();
                    if(parentnode){
                        rearr.unshift(parentnode.name);
                        showParentAllName(parentnode,rearr)
                    }
                }
            }
        }
    </script>
@stop
