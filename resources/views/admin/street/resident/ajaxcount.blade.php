<div style="margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;flex-shrink: 0">
    <span  style="margin-bottom: 10px;margin-left: 15px;">总数 <strong>{{$result['count'] ?? 0}}</strong>人<span style="font-size: 12px;"></span></span>
    <div  style="display: flex;flex-direction: row;flex-shrink: 0;padding-right: 30px;">
        @if($result['boy_count'] > 0)
            <a class="nav_item @if($input['index'] == 1 && $input['type'] == 1) select-change @endif" data-index="1" data-type="1" >
                        <span  >
                            <img src="{{asset('static/admin/images/tag/boy.png')}}" width="18px">
                        </span>
                <span style="margin-left: 12px;" >
                            <p style="text-align: left">男</p>
                            <p style="text-align: left;font-weight: bold">{{$result['boy_count']}}</p>
                        </span>
            </a>
        @endif
        @if($result['girl_count'] > 0)
            <a class="nav_item @if($input['index'] == 2 && $input['type'] == 1) select-change @endif" data-index="2" data-type="1" >
                        <span >
                            <img src="{{asset('static/admin/images/tag/girl.png')}}" width="18px">
                        </span>
                <span style="margin-left: 12px;">
                            <p style="text-align: left">女</p>
                            <p style="text-align: left;font-weight: bold;">{{$result['girl_count']}}</p>
                        </span>
            </a>
        @endif
        @if($result['bm_count'] > 0)
            <a class="nav_item @if($input['index'] == 3 && $input['type'] == 1) select-change @endif" data-index="0" data-type="1" >
                        <span >
                            <img src="{{asset('static/admin/images/user.png')}}" width="18px">
                        </span>
                <span style="margin-left: 12px;">
                            <p style="text-align: left">保密</p>
                            <p style="text-align: left;font-weight: bold;">{{$result['bm_count']}}</p>
                        </span>
            </a>
        @endif
        @if($result['80_count'] > 0)
            <a class="nav_item @if($input['index'] == 3 && $input['type'] == 1) select-change @endif" data-index="3" data-type="1">
                        <span >
                            <img src="{{asset('static/admin/images/tag/gl.png')}}" width="18px">
                        </span>
                <span style="margin-left: 12px;">
                            <p style="text-align: left">高龄(≥80岁)</p>
                            <p style="text-align: left;font-weight: bold">{{$result['80_count']}}</p>
                        </span>
            </a>
        @endif

        @if($result['5_count'] > 0)
            <a class="nav_item @if($input['index'] == 4 && $input['type'] == 1) select-change @endif" data-index="4" data-type="1">
                        <span >
                            <img src="{{asset('static/admin/images/tag/boy.png')}}" width="18px">
                        </span>
                <span style="margin-left: 12px;">
                            <p style="text-align: left">幼儿(≤5岁)</p>
                            <p style="text-align: left;font-weight: bold">{{$result['5_count']}}</p>
                        </span>
            </a>
        @endif
        @if($result['67_count'] > 0)
            <a class="nav_item @if($input['index'] == 5 && $input['type'] == 1) select-change @endif" data-index="5" data-type="1">
                        <span >
                            <img src="{{asset('static/admin/images/tag/69.png')}}" width="18px">
                        </span>
                <span style="margin-left: 12px;">
                            <p style="text-align: left">60-79岁</p>
                            <p style="text-align: left;font-weight: bold">{{$result['67_count']}}</p>
                        </span>
            </a>
        @endif
        @if($result['landscape_count'] > 0)
            <a class="nav_item @if($input['index'] == 6 && $input['type'] == 1) select-change @endif" data-index="6" data-type="1">
                        <span >
                            <img src="{{asset('static/admin/images/tag/dy.png')}}" width="18px">
                        </span>
                <span style="margin-left: 12px;">
                            <p style="text-align: left">党员</p>
                            <p style="text-align: left;font-weight: bold">{{$result['landscape_count']}}</p>
                        </span>
            </a>
        @endif
        @if($result['relationship_count'] > 0)
            <a class="nav_item @if($input['index'] == 7 && $input['type'] == 1) select-change @endif" data-index="7" data-type="1">
                        <span  >
                            <img src="{{asset('static/admin/images/tag/zh.png')}}" width="18px">
                        </span>
                <span  style="margin-left: 12px;">
                            <p style="text-align: left">租户</p>
                            <p style="text-align: left;font-weight: bold">{{$result['relationship_count']}}</p>
                        </span>
            </a>
        @endif
    </div>
</div>
@foreach($result['tag'] as $groupitem)
    @if($groupitem['count'] > 0)
    <div style="margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;flex-shrink: 0">
        <span  style="margin-bottom: 10px;margin-left: 15px;">{{$groupitem['group_name']}} <strong>{{$groupitem['count'] ?? 0}}人</strong></span>
        <div  style="display: flex;flex-direction: row;flex-shrink: 0;padding-right: 30px;">
            @foreach($groupitem['tag'] as $tagItem)
                @if($tagItem['count'] > 0)
                <a class="nav_item @if($input['index'] == $tagItem['id'] && $input['type'] == 2) select-change @endif" data-index="{{$tagItem['id']}}" data-type="2" >
                        <span  >
                            <img src="{{asset('static/admin/images/tag/tag.png')}}" width="18px">
                        </span>
                    <span style="margin-left: 12px;" >
                            <p style="text-align: left">{{$tagItem['name']}}</p>
                            <p style="text-align: left;font-weight: bold">{{$tagItem['count']}}</p>
                        </span>
                </a>
                @endif
            @endforeach
        </div>
    </div>
    @endif
@endforeach
<script>
    $(".nav_item").click(function () {
        $(".nav_item").removeClass("select-change");
        $(this).addClass("select-change");
        var index = $(this).data("index");
        var type = $(this).data("type");
        $("#selectIndex").val(index);
        $("#selectType").val(type);
        $.table.search();
    })
</script>