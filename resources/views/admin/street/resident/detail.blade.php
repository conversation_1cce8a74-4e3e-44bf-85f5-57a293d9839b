@php use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.form')
@include("widget.asset.select2")

@section('content')
    <style>
        .bg-thead{background-color: aliceblue;}
        .fixed-table-toolbar{display: none}
    </style>
    <h3>{{$info['user_name']}}-居民详情</h3>
    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
        <ul class="layui-tab-title">
            <li class="layui-this">基本信息</li>
            <li>人员标签</li>
            @hasPerm("street:residentlog:index")
            <li>信息变更记录</li>
            @endhasPerm
        </ul>
        <div class="layui-tab-content" style="height: 480px;">
            <div class="layui-tab-item layui-show">
                <form class="form-horizontal m">
                <div class="form-group">
                    <label class="col-sm-2 control-label">所属社区：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static">{{$info['cname_text']}}</div>
                    </div>
                    <label class="col-sm-2 control-label">小区-楼栋单元楼层房号：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static">{{$info['house']}}</div>
                    </div>
                </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">姓名：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['user_name']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">性别：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['sex_name']}}</div>
                        </div>

                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">年龄：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['age']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">证件号：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{Functions::desensitizeIdCard($info['id_card'])}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">出生日期：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['birthday']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">信息完整度：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['integrity']}}%</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">联系电话：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{Functions::maskPhoneNumber($info['mobile'])}}</div>
                        </div>
                        <label class="col-sm-2 control-label">第二联系电话：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{Functions::maskPhoneNumber($info['mobile2'])}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">与户主关系：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['relationship_text']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">政治面貌：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['landscape_text']}}</div>
                        </div>
                    </div>
                    @if($info['extend'])
                        @foreach($info['extend'] as $item)
                            <div class="form-group">
                                @foreach($item as $kv=>$itemS)
                                    <label class="col-sm-2 control-label">{{$itemS['title']}}：</label>
                                    @if($itemS['type'] == 12)
                                        <div class="col-sm-4"  style="margin-top: 7px">
                                            @php $img = explode(",",$itemS['value']) @endphp
                                            @foreach($img as $im)
                                                <a href="{{$im}}" target="_blank"><img src="{{$im}}" height="60px"></a>
                                            @endforeach
                                        </div>
                                    @elseif($itemS['type'] == 13)
                                        <div class="col-sm-4"  style="margin-top: 7px">
                                            @php $files = explode(",",$itemS['value']) @endphp
                                            @foreach($files as $key=>$file)
                                                <a href="{{$file}}" target="_blank">附件{{$key+1}}</a>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="col-sm-4">
                                            @if(is_array($itemS['value']))
                                                <div class="form-control-static">{{implode(",",$itemS['value'])}}</div>
                                            @else
                                                <div class="form-control-static">{{$itemS['value']}}</div>
                                            @endif

                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        @endforeach
                    @endif
                </form>
            </div>
            <div class="layui-tab-item">
                @if(Functions::getResTagGroup($info['id']))
                @foreach(Functions::getResTagGroup($info['id']) as $groupT)
                <div style="padding-bottom: 20px">
                    <h4>{{$groupT['g_name']}}</h4>
                </div>
                <div style="padding-bottom: 40px">
                    @foreach($groupT['tag'] as $item)
                        @if($item['tag_id'] == 1)
                            <a class="btn btn-default" style="background: #FFE6E6;color: #FF9999;border: 1px solid #FF9999;font-weight: normal;">{{$item['tag_name']}}</a>
                        @elseif($item['tag_id'] == 2)
                            <a class="btn btn-default" style="background:#FFFFE0;color:#FFD699;border:1px solid #FFD699;font-weight:normal">{{$item['tag_name']}}</a>
                        @elseif($item['tag_id'] == 3)
                            <a class="btn btn-default" style="background:#E0E9FF;color:#99C6FF;border:1px solid #99C6FF;font-weight:normal">{{$item['tag_name']}}</a>
                        @else
                            <a class="btn btn-default">{{$item['tag_name']}}</a>
                        @endif

                    @endforeach
                </div>
                @endforeach
                @else
                    <div style="padding-bottom: 20px">
                        <h4>暂无标签分组</h4>
                    </div>
                @endif
            </div>
            <div class="layui-tab-item">
                <div class="col-sm-12 search-collapse">
                    <form id="role-form">
                        <div class="select-list">
                            <ul>
                                <li class="select-time">
                                    <label></label>
                                    <input type="text" name="between[op_datetime][start]" id="startTime" placeholder="开始时间" readonly>
                                    <span>-</span>
                                    <input type="text" name="between[op_datetime][end]" id="endTime" placeholder="结束时间" readonly>
                                </li>
                                <li><input type="text" name="like[field]" value="" placeholder="字段名"></li>
                                <li>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search('role-form','bootstrap-table')"><i class="fa fa-search"></i> 搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('role-form','bootstrap-table')"><i class="fa fa-refresh"></i> 重置</a>
                                    @hasPerm("street:residentlog:export")
                                    <a class="btn btn-warning single  btn-sm" onclick="$.table.exportExcel('role-form')"><i class="fa fa-download"></i> 导出</a>
                                    @endhasPerm
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        layui.use('element', function(){
            var $ = layui.jquery,
                element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
        });
        $(function () {
            var options = {
                modalName: "信息变更记录",
                sortName:'id',
                sortOrder: "desc",
                url:'{{route('street.residentlog.index')}}?resident_id={{$info['id']}}',
                columns: [
                    {field: 'id', title: 'ID',  sortable: true,visible:false},
                    {field:'op_user',title: '操作人',align: "left"},
                    {field:'op_datetime',title: '操作时间',align: "left"},
                    {field:'field',title: '修改字段',align: "left"},
                    {field:'before_value_text',title: '修改前',align: "left",formatter: function (value, row, index) {
                            if(row.is_file){
                                return $.table.imageView(row,'before_value_text','50px');
                            }
                            return value;
                        }},
                    {field:'after_value_text',title: '修改后',align: "left",formatter: function (value, row, index) {
                            if(row.is_file){
                                return $.table.imageView(row,'after_value_text','50px');
                            }
                            return value;
                        }},
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true, visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', visible: false}
                ],
            };
            $.table.init(options);
        });
    </script>
@stop
