@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.layout')
@include('widget.asset.viewer')
@include('widget.asset.select2')

@section('content')
    <style>
        .select-list li li{
            margin: 0px 15px -4px 0px;
        }
        .nav_item{
            margin-left: 12px;
            display: flex;
            flex-shrink: 0;
            flex-direction: row;
            background-color: #F1F8FE;
            /*border: 1px solid  #ddd;*/
            color: #444;
            border-radius: 6px;
            align-items: center;
            padding: 5px  10px;
            box-sizing: border-box;
        }
        .select-change {
            color: #2256ff;
            background-color: #DEE6FF;
            border: 1px solid #2256ff;
        }
    </style>
    <div id="scene-content" style="background-color: #FFFFFF;border-radius: 6px;margin-top: 15px;overflow-x: scroll;display: flex;flex-direction: row">

    </div>
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <input type="hidden" name="index" value="" id="selectIndex" />
            <input type="hidden" name="type" value="" id="selectType" />
            @if($house_id > 0)
                <input type="hidden" name="house_id" id="department" value="{{$house_id}}">
            @else
                <input type="hidden" name="house_id" id="department" value="">
            @endif
            <div class="select-list">
                <ul>
                    @if(C_ID == 0)
                        <li>
                            <select name="where[cid]" class="select2" id="select-cid">
                                <option value="">所属社区</option>
                                @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                                    <option value="{{$type}}">{{$name}}</option>
                                @endforeach
                            </select>
                        </li>
                    @else
                        <input type="hidden" name="where[cid]" value="{{C_ID}}"/>
                    @endif
                    <li>
                        <select name="where[res_id]" class="select2" id="select-qid" required>
                            <option value="">选择小区</option>
                            @foreach($quarters as $name)
                                <option value="{{$name['id']}}">{{$name['name']}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li id="department-li">

                    </li>
                    <li>
                        <select name="sex" class="select2">
                            <option value="">性别</option>
                            <option value="1">男</option>
                            <option value="2">女</option>
                            <option value="0">保密</option>
                        </select>
                    </li>
                    <li>
                        <select name="landscape" class="select2">
                            <option value="">政治面貌</option>
                            @foreach(Functions::getDictDate('resident_landscape') as $type=>$name)
                                <option value="{{$type}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li>
                        <select name="relationship" class="select2">
                            <option value="">与户主关系</option>
                            @foreach(Functions::getDictDate('resident_relationship') as $type=>$name)
                                <option value="{{$type}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </li>
                        <li class="layui-form-item">
                            <div class="layui-inline">
                                <div class="layui-input-inline">
                                    <input type="text" name="age[min]" placeholder="最小年龄" autocomplete="off"
                                           class="layui-input">
                                </div>
                                <div class="layui-form-mid">~</div>
                                <div class="layui-input-inline">
                                    <input type="text" name="age[max]" placeholder="最大年龄" autocomplete="off"
                                           class="layui-input">
                                </div>
                            </div>
                        </li>
                        <li>标签：
                            <select name="tag" id="select-tag" class="select2" data-width="500px" multiple>
                                @foreach(Functions::getPerpoleTag() as $type=>$name)
                                    <optgroup label="------{{$name['name']}}------" class="group-{{$name['id']}}">
                                        @foreach($name['lable'] as $lab)
                                            <option value="{{$lab['id']}}">{{$lab['name']}}</option>
                                        @endforeach
                                    </optgroup>
                                @endforeach
                            </select>
                        </li>


                        <li><input type="text" name="orkeyword[user_name|mobile|id_card]" value=""
                                   placeholder="姓名/电话/证件号"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="searchlist()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("street:resident:add")
        <a class="btn btn-warning btn-xs" onclick="$.operate.addFull()"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("street:resident:import")
        <x-import-template-item
                :extend="['url'=>route('street.resident.import'),'id'=>'resident-upload','title'=>'批量导入居民','temp_url' => asset('template/居民.xlsx'), 'tips'=>'严格按照要求导入']"/>
        @endhasPerm
        @hasPerm("street:resident:export")
        <a class="btn btn-warning btn-xs" onclick="exportCsv()"><i class="fa fa-download"></i> 导出</a>
        @endhasPerm
        @hasPerm("street:resident:dropall")
        <a class="btn btn-warning multiple disabled" onclick="$.operate.removeAll(this)"><i class="fa fa-trash"></i>
            批量删除</a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        refreshhtml();
        $(function () {
            var options = {
                modalName: "居民",
                sortName: 'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', sortable: true, visible: false},
                    {field: 'cname_text', title: '社区', align: "left"},
                    {field: 'res_name', title: '小区', align: 'left'},
                    {field: 'house_name', title: '房屋', align: 'left'},
                    {field: 'user_name', title: '姓名', align: 'left'},
                    {field: 'sex_name', title: '性别', align: 'left'},
                    {field: 'age', title: '年龄', align: 'left'},
                    {field: 'mobile_text', title: '联系电话', align: 'left'},
                    {field: 'id_card_text', title: '证件号', align: 'left'},
                    {field: 'relationship_text', title: '与户主关系', align: 'left'},
                    {field: 'landscape_text', title: '政治面貌', align: 'left'},
                    {field: 'tags_text', title: '人员标签', align: 'left'},
                    {
                        field: 'integrity',
                        title: '信息完整度',
                        align: 'left',
                        sortable: true,
                        formatter: function (value, row, index) {
                            return value + "%";
                        }
                    },
                    {field: 'create_time', title: '创建时间', align: 'left', visible: false, sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function (value, row, index) {
                            var actions = [];
                            @hasPerm("street:resident:detail")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="detail(\'' + row.id + '\')">详情</a> ');
                            @endhasPerm
                            @hasPerm("street:resident:edit")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.editFull(\'' + row.id + '\')">编辑</a> ');
                            @endhasPerm
                            @hasPerm("street:resident:drop")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                            @endhasPerm

                            return actions.join('');
                        }
                    }
                ],
            };
            $.table.init(options);
        });
        @hasPerm("street:resident:detail")
        function detail(id) {
            $.modal.openTab("居民详情", '{{route('street.resident.detail')}}?id=' + id, true);
        }
        @endhasPerm
        // 当选中选项时触发
        $('#select-cid').on('select2:select', function (e) {
            var id = e.params.data.id;
            $("#department-li").html("");
            $.ajax({
                url: '{{route('street.resident.ajaxres')}}',
                type: "post",
                data: {"c_id": id},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $('#department_text').html("楼栋-单元");
                        $('#department').val("");
                        var html = '<option value="">选择小区</option>';
                        $.each(result.data, function (index, item) {
                            html += '<option value="' + item.id + '">' + item.name + '</option>';
                        })
                        $("#select-qid").html(html)
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else {
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                }
            });
        });
        $('#select-qid').on('select2:select', function (e) {
            var id = e.params.data.id;
            $.ajax({
                url: '{{route('street.resident.ajaxqes')}}',
                type: "post",
                data: {"id": id},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        //设置html
                        $("#department-li").html(
                            '<span class="select2 select2-container select2-container--bootstrap select2-container--below" id="department_dropdown" dir="ltr">' +
                            '<span class="selection">' +
                            '<span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-labelledby="select2-whereevent_status-jc-container">' +
                            '<span class="select2-selection__rendered" id="department_text" role="textbox" aria-readonly="true" title="楼栋-单元">楼栋-单元</span>' +
                            '<span class="select2-selection__arrow" role="presentation">' +
                            '<b role="presentation"></b>' +
                            '</span>' +
                            '</span>' +
                            '</span>' +
                            '<span class="dropdown-wrapper" aria-hidden="true"></span>' +
                            '</span>');
                        $('#department_text').html("楼栋-单元");
                        $('#department').val("");
                        layui.use(['dropdown'], function () {
                            var dropdown = layui.dropdown
                            var inst = dropdown.render();
                            inst.reload({
                                elem: '#department_dropdown'
                                , data: []
                            });
                            inst.reload({
                                elem: '#department_dropdown'
                                , clickParent: false
                                , data: result.data
                                , click: function (obj) {
                                    $('#department_text').html(obj.titles);
                                    $('#department').val(obj.ids);
                                }
                            });
                        });
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else {
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                }
            });
        });
        function tableSearchReset(){
            $("#select-tag").val("").trigger('change');
        }
        function reset(){
            $(".nav_item").removeClass("select-change");
            $("#selectIndex").val("0");
            $("#selectType").val("0");
            $('#department_text').html("楼栋-单元");
            $('#department').val("");
            $("#department-li").html('');
            $.form.reset();
            refreshhtml();
        }
        function searchlist(){
            $.table.search()
            refreshhtml();
        }
        function exportCsv(){
            window.location.href='{{route('street.resident.export')}}?'+$("#role-form").serialize();
        }
        function refreshhtml() {
            $('#scene-content').load('{{route('street.resident.ajaxcount')}}?'+$("#role-form").serialize());
        }
    </script>
@stop

