@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.form')
@include("widget.asset.select2")
@include("widget.asset.upload")
@include("widget.asset.dragula")
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            @if(C_ID == 0)
                <label class="col-sm-2 control-label is-required">所属社区：</label>
                <div class="col-sm-4">
                    <select name="cid" class="select2" id="select-cid" required>
                        <option value="">选择社区</option>
                        @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                            <option value="{{$type}}">{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            @endif
            <label class="col-sm-2 control-label is-required">所属小区：</label>
            <div class="col-sm-4">
                <select name="res_id" class="select2" id="select-qid" required>
                    <option value="">选择小区</option>
                    @foreach($quarters as $name)
                        <option value="{{$name['id']}}">{{$name['name']}}</option>
                    @endforeach
                </select>
            </div>

        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">选择房屋：</label>
            <div class="col-sm-4" id="house-list">
                <input type="hidden" name="house_id" id="department" required value="">
                <span class="select2 select2-container select2-container--bootstrap select2-container--below"
                      id="department_dropdown" dir="ltr">
                    <span class="selection">
                        <span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true"
                              aria-expanded="false" tabindex="0"
                              aria-labelledby="select2-whereevent_status-jc-container">
                            <span class="select2-selection__rendered" id="department_text" role="textbox"
                                  aria-readonly="true" title="楼栋-单元-楼层-房号">楼栋-单元-楼层-房号</span>
                            <span class="select2-selection__arrow" role="presentation">
                                <b role="presentation"></b>
                            </span>
                        </span>
                    </span>
                <span class="dropdown-wrapper" aria-hidden="true"></span>
            </span>
            </div>
            <label class="col-sm-2 control-label is-required">姓名：</label>
            <div class="col-sm-4">
                <input type="text" name="user_name" value="" class="form-control" placeholder="请输入姓名" required
                       autocomplete="off"/>
            </div>
        </div>
        <div  class="form-group">
            <label class="col-sm-2 control-label is-required">证件号：</label>
            <div class="col-sm-4">
                <input type="text" name="id_card" id="id_card" maxlength="18" value="" class="form-control" placeholder="请输入证件号"
                       required autocomplete="off"/>
            </div>
            <label class="col-sm-2 control-label is-required">性别：</label>
            <div class="col-sm-4" style="line-height: 30px;">
                <label class="radio-box">
                    <input type="radio" name="sex" value="1" checked="false"/> 男
                </label>
                <label class="radio-box">
                    <input type="radio" name="sex" value="2" checked="false"/> 女
                </label>
                <label class="radio-box">
                    <input type="radio" name="sex" value="0" checked="true"/> 保密
                </label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">联系电话：</label>
            <div class="col-sm-4">
                <input type="text" name="mobile" value="" maxlength="13" class="form-control" placeholder="请输入联系电话" required
                       autocomplete="off"/>
            </div>
            <label class="col-sm-2 control-label">第二联系电话：</label>
            <div class="col-sm-4">
                <input type="text" name="mobile2" value="" maxlength="13" class="form-control" placeholder="请输入第二联系电话"
                       autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">出生日期：</label>
            <div class="col-sm-4">
                <input type="text" name="birthday" value="" id="birthday" class="form-control" placeholder="请选择出生日期"
                       required autocomplete="off"/>
            </div>
            <label class="col-sm-2 control-label is-required">政治面貌：</label>
            <div class="col-sm-4">
                <select name="landscape" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(Functions::getDictDate("resident_landscape") as $type=>$name)
                        <option value="{{$type}}">{{$name}}</option>
                    @endforeach
                </select>
            </div>

        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">与户主关系：</label>
            <div class="col-sm-4">
                <select name="relationship" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(Functions::getDictDate("resident_relationship") as $type=>$name)
                        <option value="{{$type}}">{{$name}}</option>
                    @endforeach
                </select>
            </div>
            <label class="col-sm-2 control-label">设置人员标签：</label>
            <div class="col-sm-4">
                <select name="tag[]" class="select2" data-width="auto" multiple>
                    <option value="">请选择标签</option>
                    @foreach(Functions::getPerpoleTag() as $type=>$name)
                        <optgroup label="------{{$name['name']}}------" class="group-{{$name['id']}}">
                            @foreach($name['lable'] as $lab)
                                <option value="{{$lab['id']}}">{{$lab['name']}}</option>
                            @endforeach
                        </optgroup>
                    @endforeach
                </select>
            </div>
        </div>
        @if($selffield)
            @foreach($selffield as $groupItem)
                <div class="form-group">
                    @foreach($groupItem as $item)
                        <input type="hidden" name="extend[{{$item['field_name']}}][type]"
                               value="{{$item['field_type']}}">
                        <input type="hidden" name="extend[{{$item['field_name']}}][title]"
                               value="{{$item['field_name']}}">
                        <label class="col-sm-2 control-label @if($item['is_nust']) is-required @endif">{{$item['field_name']}}
                            ：</label>
                        <div class="col-sm-4">
                            @if($item['field_type'] == 1)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       @if($item['is_nust']) required @endif class="form-control"
                                       placeholder="请输入{{$item['field_name']}}" autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 2)
                                <textarea type="text" name="extend[{{$item['field_name']}}][value]"
                                          @if($item['is_nust']) required @endif class="form-control"
                                          placeholder="请输入{{$item['field_name']}}"></textarea>
                            @endif
                            @if($item['field_type'] == 3)
                                @foreach($item['extend'] as $ex)
                                    <label class="radio-box" style="line-height: 30px;">
                                        <input type="radio" name="extend[{{$item['field_name']}}][value]"
                                               value="{{$ex['title']}}"
                                               @if($item['is_nust']) required @endif/> {{$ex['title']}}
                                    </label>
                                @endforeach
                            @endif
                            @if($item['field_type'] == 4)
                                @foreach($item['extend'] as $ex)
                                    <label class="radio-box" style="line-height: 30px;">
                                        <input type="checkbox" name="extend[{{$item['field_name']}}][value][]"
                                               value="{{$ex['title']}}" class="input-required"
                                               @if($item['is_nust']) required @endif > {{$ex['title']}}
                                    </label>
                                @endforeach
                            @endif
                            @if($item['field_type'] == 5)
                                <select name="extend[{{$item['field_name']}}][value]" class="select2"
                                        style="width: 200px" req@if($item['is_nust']) required @endifuired>
                                    <option value="">选择{{$item['field_name']}}</option>
                                    @foreach($item['extend'] as $name)
                                        <option value="{{$name['title']}}">{{$name['title']}}</option>
                                    @endforeach
                                </select>
                            @endif
                            @if($item['field_type'] == 6)
                                <select name="extend[{{$item['field_name']}}][value][]" class="select2" multiple="true"
                                        data-width="auto" @if($item['is_nust']) required @endif>
                                    <option value="">请选择{{$item['field_name']}}</option>
                                    @foreach($item['extend'] as $name)
                                        <option value="{{$name['title']}}">{{$name['title']}}</option>
                                    @endforeach
                                </select>
                            @endif
                            @if($item['field_type'] == 7)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control filter-date" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif />
                            @endif
                            @if($item['field_type'] == 8)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control date-range" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 9)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control filter-time" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 10)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control time-range" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 12)
                                <input type="hidden" name="extend[{{$item['field_name']}}][value]" value=""
                                       id="imgs{{$item['max_index']}}" @if($item['is_nust']) required @endif>
                                <x-upload type="img" name="imgs{{$item['max_index']}}_upload"
                                          :extend="['cat'=>'demo','link'=>1,'multi'=>10,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M；可拖动排序']"/>
                            @endif
                            @if($item['field_type'] == 13)
                                <input type="hidden" name="extend[{{$item['field_name']}}][value]" value=""
                                       id="files{{$item['max_index']}}" @if($item['is_nust']) required @endif>
                                <x-upload type="file" name="files{{$item['max_index']}}_upload"
                                          :extend="['cat'=>'demo','link'=>1,'multi'=>10,'exts'=>'txt|rar|doc|png', 'tips'=>'格式为txt|rar|doc|pdf|xls|docx|xls|ppt|pptx,大小不能超过100M']"/>
                            @endif
                            @if($item['field_type'] == 11)
                                <div class="input-group">
                                    <input type="text" name="extend[{{$item['field_name']}}][value]" value="" readonly
                                           class="form-control lnglat" @if($item['is_nust']) required
                                           @endif autocomplete="off" placeholder="请选择"/>
                                    <div class="input-group-addon"><i class="fa fa-map-marker"></i></div>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endforeach
        @endif
    </form>
@stop

@section('script')
    <script>
        var laydate;
        $(function () {
            layui.use('laydate', function () {
                laydate = layui.laydate;
                laydate.render({
                    elem: '#birthday',
                    type: 'date',
                    trigger: 'click',
                    max: '{{date("Y-m-d")}}'
                });
                $('.filter-date').each(function () {
                    laydate.render({
                        elem: this,
                        type: 'date',
                        trigger: 'click'
                    });
                });
                $('.date-range').each(function () {
                    laydate.render({
                        elem: this,
                        type: 'date',
                        range: true,
                        trigger: 'click'
                    });
                });
                $('.filter-time').each(function () {
                    laydate.render({
                        elem: this,
                        type: 'datetime',
                        trigger: 'click'
                    });
                });
                $('.time-range').each(function () {
                    laydate.render({
                        elem: this,
                        type: 'datetime',
                        trigger: 'click',
                        range: true,
                    });
                });
            })
        });
        $('.lnglat').click(function () {
            let lnglat = $(this).val();
            var obj = $(this)
            $.modal.openOptions({
                title: '经纬度选择',
                url: '{{route('admin.lnglat')}}?lnglat=' + lnglat,
                callBack: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']]; //得到iframe页的窗口对象，执行iframe页的方法：
                    obj.val(iframeWin.getLngLat())
                    layer.close(index);
                }
            });
        });

        function submitHandler() {
            if ($.validate.form()) {
                var imgs = [];
                var files = [];
                @foreach($imgindex as $index)
                if ($("input[name='imgs{{$index}}_upload[]']").length > 0) {
                    $("input[name='imgs{{$index}}_upload[]']").each(function () {
                        var imgval = $(this).val();
                        if (imgval) {
                            imgs.push(imgval)
                        }
                    })
                }
                $("#imgs{{$index}}").val(imgs.join(','));
                @endforeach
                        @foreach($fileindex as $index)
                if ($("input[name='files{{$index}}_upload[]']").length > 0) {
                    $("input[name='files{{$index}}_upload[]']").each(function () {
                        var imgval = $(this).val();
                        if (imgval) {
                            files.push(imgval)
                        }
                    })
                }
                $("#files{{$index}}").val(files.join(','));
                @endforeach
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }

        $("#id_card").change(function () {
            $.ajax({
                url: '{{route('street.resident.ajaxverifyidcard')}}',
                type: "post",
                data: {"value": $(this).val()},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        if (result.data.state == 0) {
                            $.modal.alertWarning("身份证号码错误~")
                        } else {
                            $("#birthday").val(result.data.birthday);
                            $('input[name="sex"][value="' + result.data.gender + '"]').iCheck('check');
                        }
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                }
            });
        })
        // 当选中选项时触发
        $('#select-cid').on('select2:select', function (e) {
            var id = e.params.data.id;
            $.ajax({
                url: '{{route('street.resident.ajaxres')}}',
                type: "post",
                data: {"c_id": id},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#house-list").html("");
                        var html = '<option value="">选择小区</option>';
                        $.each(result.data, function (index, item) {
                            html += '<option value="' + item.id + '">' + item.name + '</option>';
                        })
                        $("#select-qid").html(html)
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else {
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                }
            });
        });
        $('#select-qid').on('select2:select', function (e) {
            var id = e.params.data.id;
            $.ajax({
                url: '{{route('street.resident.ajaxqes')}}',
                type: "post",
                data: {"id": id,"type":1},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#house-list").html('<input type="hidden" name="house_id" id="department" required value="">' +
                            '<span class="select2 select2-container select2-container--bootstrap select2-container--below" id="department_dropdown" dir="ltr">' +
                            '<span class="selection">' +
                            '<span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-labelledby="select2-whereevent_status-jc-container">' +
                            '<span class="select2-selection__rendered" id="department_text" role="textbox" aria-readonly="true" title="楼栋-单元-楼层-房号">楼栋-单元-楼层-房号</span>' +
                            '<span class="select2-selection__arrow" role="presentation">' +
                            '<b role="presentation"></b>' +
                            '</span>' +
                            '</span>' +
                            '</span>' +
                            '<span class="dropdown-wrapper" aria-hidden="true"></span>' +
                            '</span>');
                        $('#department_text').html("楼栋-单元-楼层-房号");
                        $('#department').val("");
                        layui.use(['dropdown'], function () {
                            var dropdown = layui.dropdown
                            dropdown.render({
                                elem: '#department_dropdown'
                                , clickParent: false
                                , data: result.data
                                , click: function (obj) {
                                    $('#department_text').html(obj.titles);
                                    $('#department').val(obj.id);
                                }
                            });
                        });
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                        $.modal.closeLoading();
                        $.modal.enable();
                    } else {
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                }
            });
        });
    </script>
@stop
