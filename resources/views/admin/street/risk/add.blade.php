@extends('admin.layout.form')
@include("widget.asset.select2")
@include("widget.asset.upload")
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">场所类型：</label>
            <div class="col-sm-3">
                <div class="input-group">
                    <input type="hidden" id="treeId" name="risk_type" value="">
                    <input type="text" id="treeName" value="" class="form-control" required placeholder="请选择" readonly autocomplete="off"/>
                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                </div>
            </div>
            <label class="col-sm-2 control-label is-required">场所名称：</label>
            <div class="col-sm-3">
                <input type="text" name="name" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
            </div>
        </div>
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <ul class="layui-tab-title">
                <li class="layui-this is-required control-label">基础信息</li>
                <li>工商信息</li>
            </ul>
            <div class="layui-tab-content" style="height: 480px;">
                <div class="layui-tab-item layui-show">
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">行政区划：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="cid" required data-width="100%">
                                <option value="">请选择</option>
                                @foreach($communities as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                        <label class="col-sm-2 control-label">详细地址：</label>
                        <div class="col-sm-2">
                            <div class="input-group">
                                <input type="text" name="address" value="" class="form-control"  autocomplete="off" placeholder="请输入" />
                                <div class="input-group-addon"><i class="fa fa-map-marker"></i></div>
                            </div>
                        </div>
                        <label class="col-sm-2 control-label">经纬度：</label>
                        <div class="col-sm-2">
                            <div class="input-group">
                                <input type="text" name="lnglat" id="lnglat" value="" readonly class="form-control" autocomplete="off" placeholder="请选择" />
                                <div class="input-group-addon"><i class="fa fa-map-marker"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">负责人：</label>
                        <div class="col-sm-2">
                            <input type="text" name="responsible" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
                        </div>
                        <label class="col-sm-2 control-label is-required">负责人电话：</label>
                        <div class="col-sm-2">
                            <input type="text" name="responsible_phone" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
                        </div>
                        <label class="col-sm-2 control-label is-required">安全负责人：</label>
                        <div class="col-sm-2">
                            <input type="text" name="safe_responsible" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">安全负责人电话：</label>
                        <div class="col-sm-2">
                            <input type="text" name="safe_responsible_phone" value="" class="form-control" placeholder="请输入" required autocomplete="off"/>
                        </div>
                        <label class="col-sm-2 control-label is-required">行业分类：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="category_code" id="category_code" required data-width="100%">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <label class="col-sm-2 control-label">简介：</label>
                        <div class="col-sm-2">
                            <input type="text" name="brief" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">现场图片：</label>
                        <div class="col-sm-2">
                            <input type="hidden" name="scene_image" value="" id="scene_image">
                            <x-upload type="img" name="scene_image_upload" :extend="['cat'=>'risk','tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M']"/>
                        </div>
                        <label class="col-sm-2 control-label">资质证照：</label>
                        <div class="col-sm-2">
                            <input type="hidden" name="qualification_image" value="" id="qualification_image">
                            <x-upload type="img" name="qualification_image_upload" :extend="['cat'=>'risk','tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M']"/>
                        </div>
                        <label class="col-sm-2 control-label">风险场所网格号：</label>
                        <div class="col-sm-2">
                            <input type="text" name="grid_num" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">机构类型：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="organization_type" required data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_organization_type as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                        <label class="col-sm-2 control-label is-required">产权权属：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="property_ownership" required data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_property_ownership as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                        <label class="col-sm-2 control-label is-required">企业级次：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="enterprise_level" required data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_enterprise_level as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">企业规模：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="enterprise_size" required data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_enterprise_size as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                        <label class="col-sm-2 control-label is-required">能源类型：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="energy_type" required data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_energy_type as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                        <label class="col-sm-2 control-label">管理评估等级：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="manage_level"  data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_manage_level as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">上传证照：</label>
                        <div class="col-sm-2">
                            <input type="hidden" name="image" value="" id="image">
                            <x-upload type="img" name="image_upload" :extend="['cat'=>'risk','tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M']"/>
                        </div>
                        <label class="col-sm-2 control-label">工商注册名称：</label>
                        <div class="col-sm-2">
                            <input type="text" name="company_name" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                        </div>
                        <label class="col-sm-2 control-label">统一社会信用代码：</label>
                        <div class="col-sm-2">
                            <input type="text" name="credit_code" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">法定代表人：</label>
                        <div class="col-sm-2">
                            <input type="text" name="legal" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                        </div>
                        <label class="col-sm-2 control-label">成立时间：</label>
                        <div class="col-sm-2">
                            <input type="text" name="reg_time" value="" class="form-control time-input" placeholder="请新增" readonly autocomplete="off"/>
                        </div>
                        <label class="col-sm-2 control-label">注册地址：</label>
                        <div class="col-sm-2">
                            <input type="text" name="reg_address" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">经营地址：</label>
                        <div class="col-sm-2">
                            <input type="text" name="bus_address" value="" class="form-control" placeholder="请输入" autocomplete="off"/>
                        </div>
                        <label class="col-sm-2 control-label">单位类型：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="unit_type" data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_unit_type as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                        <label class="col-sm-2 control-label">经营状态：</label>
                        <div class="col-sm-2">
                            <select class="select2" name="bus_status" data-width="100%">
                                <option value="">请选择</option>
                                @foreach($risk_bus_status as $k=>$v)
                                    <option value="{{$k}}">{{$v}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        $(function () {
            $('#lnglat').click(function () {
                let lnglat = $(this).val();
                $.modal.openOptions({
                    title: '经纬度选择',
                    url: '{{route('admin.lnglat')}}?lnglat='+lnglat,
                    callBack: function (index, layero) {
                        var iframeWin = window[layero.find('iframe')[0]['name']]; //得到iframe页的窗口对象，执行iframe页的方法：
                        $('#lnglat').val(iframeWin.getLngLat())
                        layer.close(index);
                    }
                });
            });
            $("#treeName").click(function () {
                var treeId=$("#treeId").val();
                var url = urlcreate("{{route('street.risktype.tree')}}","id="+treeId+"&parent=0");
                $.modal.openOptions({
                    title: '场所类型选择',
                    width: "380",
                    url: url,
                    callBack: function (index, layero){
                        var body = layer.getChildFrame('body', index);
                        var structId = body.find('#treeId').val();
                        $("#treeId").val(structId);
                        $("#treeName").val(body.find('#treeName').val());
                        layer.close(index);
                        //获取行业分类
                        var category_code = $('#category_code')
                        category_code.select2('destroy').empty();
                        category_code.select2({data:[{id:"",text:"请选择"}]});
                        if(structId) {
                            $.ajax({
                                url: "{{route('street.risktype.ajaxsitetemplate')}}",
                                type: "post",
                                dataType: "json",
                                data: {code: structId},
                                success: function (res) {
                                    if (res.code == 0) {
                                        category_code.select2({data:res.data.data});
                                        if (res.data.info.isEnterprise == 1) {
                                            $('.layui-tab-title li').first().next().show();
                                        } else {
                                            $('.layui-tab-title li').first().click();
                                            $('.layui-tab-title li').first().next().hide();
                                        }
                                    }
                                }
                            });
                        }
                    }
                });
            });
        });
        function submitHandler() {
            var image = '';
            var scene_image = '';
            var qualification_image = '';
            if($("input[name='image_upload[]']").length>0){
                $("input[name='image_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval){
                        image = imgval;
                    }
                })
            }
            $("#image").val(image);
            if($("input[name='scene_image_upload[]']").length>0){
                $("input[name='scene_image_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval){
                        scene_image = imgval;
                    }
                })
            }
            $("#scene_image").val(scene_image);
            if($("input[name='qualification_image_upload[]']").length>0){
                $("input[name='qualification_image_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if(imgval){
                        qualification_image = imgval;
                    }
                })
            }
            $("#qualification_image").val(qualification_image);


            if ($.validate.form("",{ignore:""})) {
                $.operate.saveTab(oasUrl, $('#form-add').serialize());
            } else {
                $.modal.msgWarning('请填写必填项');
            }
        }

    </script>
@stop
