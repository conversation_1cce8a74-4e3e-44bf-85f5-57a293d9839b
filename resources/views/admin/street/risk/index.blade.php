@extends('admin.layout.full')
@include('widget.asset.select2')
@include('widget.asset.jquery-layout')
@include('widget.asset.ztree')
@section('content')
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa icon-grid"></i> 所属行业
                </div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" title="刷新组织" onclick="getStructList()"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div id="tree" class="ztree"></div>
            </div>
        </div>
    </div>
    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <div class="col-sm-12 search-collapse">
                    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 10px;">
                        <li role="presentation" class="active">
                            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_level('')">所有场所 <span id="level_all">0</span></a>
                        </li>
                        @foreach($riskLevels as $level=>$title)
                        <li role="presentation" class="">
                            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_level('{{$level}}')">{{$title}} <span id="level_{{$level}}">0</span></a>
                        </li>
                        @endforeach
                    </ul>
                    <form id="role-form">
                        <input type="hidden" name="structId" id="structId" value="">
                        <input type="hidden" id="risk_level" name="risk_level" value="">
                        <div class="select-list">
                            <ul>
                                <li><input type="text" name="like[name]" value="" placeholder="风险场所名称" autocomplete="off"></li>
                                <li>
                                    <select name="where[cid]" class="select2">
                                        <option value="">所属社区</option>
                                        @foreach($community as $id=>$name)
                                            <option value="{{$id}}">{{$name}}</option>
                                        @endforeach
                                    </select>
                                </li>
                                <li>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="btn-group-sm" id="toolbar" role="group">
                    @hasPerm("street:risk:add")
                    <a class="btn btn-warning btn-xs" onclick="$.operate.addFull()"><i class="fa fa-plus"></i> 新增</a>
                    @endhasPerm
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        var struct_treeUrl="{{route('street.risk.tree')}}";
        $(function () {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 200,togglerContent_open:"<i class='fa fa-caret-left'></i>",togglerContent_closed:"<i class='fa fa-caret-right'></i>" });
            getCategoryList();
            getList();

            $('#btnExpand').click(function() {
                $._tree.expandAll(true);
                $(this).hide();
                $('#btnCollapse').show();
            });

            $('#btnCollapse').click(function() {
                $._tree.expandAll(false);
                $(this).hide();
                $('#btnExpand').show();
            });

        });
        function getCategoryList() {
            var options = {
                url: struct_treeUrl,
                expandLevel: 2,
                onClick : zOnClick
            };
            $.tree.init(options);
            $("#structId").val('');
            $.table.search();
            function zOnClick(event, treeId, treeNode) {
                $("#structId").val(treeNode.id);
                $.table.search();
            }
        }
        function getList() {
            var options = {
                modalName: "风险场所",
                sortName:'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {field: 'name', title: '风险场所名称', formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }},
                    {field: 'community.name', title: '所属社区', align: 'left'},
                    {field: 'risk_type_text', title: '场所类型', align: 'left', formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }},
                    {field: 'category.name', title: '所属行业', align: 'left'},
                    {field: 'risk_level_text', title: '风险等级', align: 'left'},
                    {field: 'responsible', title: '负责人', align: 'left'},
                    {field: 'safe_responsible', title: '安全负责人', align: 'left'},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                            @hasPerm("street:risk:detail")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="detail(\'' + row.id + '\')"> 详情</a> ');
                            @endhasPerm
                            @hasPerm("street:risk:edit")
                            actions.push('<a class="btn btn-xs" href="javascript:;"  onclick="$.operate.editFull(\'' + row.id + '\', this)">编辑</a> ');
                            @endhasPerm
                            @hasPerm("street:risk:drop")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                            @endhasPerm
                                return actions.join('');
                        }
                    }
                ],
                responseHandler:function (res) {
                    if (res.code === 0) {
                        $.each(res.extend, function (i, item) {
                            $('#'+i).html(item);
                        });
                    }
                }
            };
            $.table.init(options);
        }
        function detail(id) {
            $.modal.layerinit(function (layer) {
                var index = layer.open({
                    type: 2,
                    fix: false,
                    maxmin: true,
                    shade: 0.3,
                    title: '风险场所详情',
                    content: '{{route('street.risk.detail')}}?id='+id,
                    btn: ['关闭'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    cancel: function (index) {
                        return true;
                    }
                });
                layer.full(index);
            });
        }
        function switch_level(level) {
            $('#risk_level').val(level);
            $.table.search();
        }
    </script>
@stop

