@extends('admin.layout.layout')

@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>日期： </label>
                        <input type="text" class="time-input" name="where[kaoqin.clock_date]" id="clock_date" placeholder="日期" value="{{date('Y-m-d')}}" readonly>
                    </li>
                    <li>
                        <label>姓名： </label>
                        <input type="text"  name="like[users.username]"  placeholder="" value="" autocomplete="off">
                    </li>
                    <li>
                        <select name="in[kaoqin.status]" xm-select="select3">
                            <option value="">所有状态</option>
                            <option value="1">正常</option>
                            <option value="2">迟到</option>
                            <option value="3">早退</option>
                            <option value="4">上班缺卡</option>
                            <option value="5">下班缺卡</option>
                            <option value="6">加班</option>
                            <option value="7">外出</option>
                            <option value="8">请假</option>
                        </select>
                    </li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        <a class="btn btn-warning btn-sm" id="table-export" data-name="综治考勤记录" data-type="excel"><i class="fa fa-download"></i>
            导出
        </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/bootstrap-table-export.js')}}"></script>
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/tableExport.js')}}"></script>
    <script>
        $(function () {
            var options = {
                modalName: "考勤记录",
                sortName:'id',
                sortOrder: "desc",
                showExport: true,
                exportTypes: ['csv', 'excel'],
                url:'{{route('street.kaoqin.zz')}}',
                queryParams: function (params) {
                    // params['where[users.user_type]'] = 4;
                    var formParams = $('#role-form').serializeArray();
                    $.each(formParams, function(i, field) {
                        params[field.name] = field.value;
                    });
                    params.pageSize = params.limit;
                    params.pageNum = params.offset / params.limit + 1;
                    return params;
                },
                columns: [
                    {checkbox: true},
                    // {field: 'id', title: '序号', align: 'left',formatter:function(value,row,index){return ++index; }},
                    {field: 'username', title: '姓名', align: 'center'},
                    {field: 'clock_in_time', title: '上班打卡', align: 'center'},
                    {field: 'clock_out_time', title: '下班打卡', align: 'center'},
                    {field: 'status', title: '状态', align: 'center'},

                    {
                        title: '关联审批',
                        align: 'left',
                        formatter: function(value, row, index) {
                            //console.log(row)
                            var actions = [];
                            if(row['jiaban']['has']){
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.modal.open(`审批详情`,`{{route('street.kaoqinjiaban.detail')}}?id='+row['jiaban']['info']['id']+'`,1200,600)"> 加班</a> ');
                            }
                            if(row['leave']['has']){
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.modal.open(`审批详情`,`{{route('street.kaoqinleave.detail')}}?id='+row['leave']['info']['id']+'`,1200,600)"> 请假</a> ');
                            }
                            if(row['out']['has']){
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.modal.open(`审批详情`,`{{route('street.kaoqinout.detail')}}?id='+row['out']['info']['id']+'`,1200,600)"> 外出</a> ');
                            }
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
        function tableSearchReset() {
            layui.formSelects.value('select3', []);
        }
    </script>
@stop

