@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}

@section('content')
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡时间：</label>
        <div class="col-sm-8">
            <input type="text" name="clock_date" value="" class="form-control time-input" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡月份：</label>
        <div class="col-sm-8">
            <input type="text" name="clock_mouth" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">所属社区：</label>
        <div class="col-sm-8">
            <input type="number" name="cid" value="" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">所属网格：</label>
        <div class="col-sm-8">
            <input type="number" name="grid_id" value="" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">网格员ID：</label>
        <div class="col-sm-8">
            <input type="number" name="grid_user_id" value="" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">姓名：</label>
        <div class="col-sm-8">
            <input type="text" name="user_name" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">手机号码：</label>
        <div class="col-sm-8">
            <input type="text" name="mobile" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">1 上班 2 下班：</label>
        <div class="col-sm-8">
            <input type="text" name="type" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡时间：</label>
        <div class="col-sm-8">
            <input type="text" name="clock_time" value="" class="form-control time-input" required autocomplete="off" data-type="datetime"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">1 正常 2 迟到  3 早退 4 上班缺卡  5 下班缺卡 ：</label>
        <div class="col-sm-8">
            <label class="radio-box">
                <input type="radio" name="status" value="0"/> 隐藏
            </label>
            <label class="radio-box">
                <input type="radio" name="status" value="1" checked/> 显示
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡经纬度：</label>
        <div class="col-sm-8">
            <input type="text" name="location" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">异常时间【秒】 早退  迟到 【缺卡不负责】：</label>
        <div class="col-sm-8">
            <input type="number" name="anomaly_time" value="" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">是否为外勤：</label>
        <div class="col-sm-8">
            <input type="text" name="is_waiqin" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">外勤地址：</label>
        <div class="col-sm-8">
            <input type="text" name="waiqin_address" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">外勤理由：</label>
        <div class="col-sm-8">
            <input type="text" name="waiqin_info" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">手动修改状态人：</label>
        <div class="col-sm-8">
            <input type="number" name="edit_admin_id" value="" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">手动修改原因：</label>
        <div class="col-sm-8">
            <input type="text" name="edit_info" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
