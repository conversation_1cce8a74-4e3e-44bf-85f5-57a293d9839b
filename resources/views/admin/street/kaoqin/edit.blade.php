@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}


@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡时间：</label>
        <div class="col-sm-8">
            <input type="text" name="clock_date" value="{{$info['clock_date']}}" class="form-control time-input" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡月份：</label>
        <div class="col-sm-8">
            <input type="text" name="clock_mouth" value="{{$info['clock_mouth']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">所属社区：</label>
        <div class="col-sm-8">
            <input type="number" name="cid" value="{{$info['cid']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">所属网格：</label>
        <div class="col-sm-8">
            <input type="number" name="grid_id" value="{{$info['grid_id']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">网格员ID：</label>
        <div class="col-sm-8">
            <input type="number" name="grid_user_id" value="{{$info['grid_user_id']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">姓名：</label>
        <div class="col-sm-8">
            <input type="text" name="user_name" value="{{$info['user_name']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">手机号码：</label>
        <div class="col-sm-8">
            <input type="text" name="mobile" value="{{$info['mobile']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">1 上班 2 下班：</label>
        <div class="col-sm-8">
            <input type="text" name="type" value="{{$info['type']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡时间：</label>
        <div class="col-sm-8">
            <input type="text" name="clock_time" value="{{$info['clock_time']}}" class="form-control time-input" required autocomplete="off" data-type="datetime"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">1 正常 2 迟到  3 早退 4 上班缺卡  5 下班缺卡 ：</label>
        <div class="col-sm-8">
            <label class="radio-box">
                <input type="radio" name="status" value="1" @if($info["status"] == "0") checked @endif/> 隐藏
            </label>
            <label class="radio-box">
                <input type="radio" name="status" value="1" @if($info["status"] == "1") checked @endif/> 显示
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">打卡经纬度：</label>
        <div class="col-sm-8">
            <input type="text" name="location" value="{{$info['location']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">异常时间【秒】 早退  迟到 【缺卡不负责】：</label>
        <div class="col-sm-8">
            <input type="number" name="anomaly_time" value="{{$info['anomaly_time']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">是否为外勤：</label>
        <div class="col-sm-8">
            <input type="text" name="is_waiqin" value="{{$info['is_waiqin']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">外勤地址：</label>
        <div class="col-sm-8">
            <input type="text" name="waiqin_address" value="{{$info['waiqin_address']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">外勤理由：</label>
        <div class="col-sm-8">
            <input type="text" name="waiqin_info" value="{{$info['waiqin_info']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">手动修改状态人：</label>
        <div class="col-sm-8">
            <input type="number" name="edit_admin_id" value="{{$info['edit_admin_id']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">手动修改原因：</label>
        <div class="col-sm-8">
            <input type="text" name="edit_info" value="{{$info['edit_info']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-edit").validate();

        {{--$("#XX").val(["{{$info['xx']}}"]).trigger("change");--}}

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
