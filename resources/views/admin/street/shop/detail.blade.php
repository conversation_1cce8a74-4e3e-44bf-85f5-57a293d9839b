@extends('admin.layout.form')
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp

<style>
    .layui-tab-title li a{
        color: #2F4650;
    }
    .layui-this a{
        color: #2195FF !important;
    }
    .fixed-table-body{
        height: auto !important;
    }
    .fixed-table-toolbar{display: none}
</style>
@section('content')
    <div class="mt10" style="background-color: #fff;">
        <div class="layui-tab layui-tab-brief" data-example-id="togglable-tabs">
            <ul class="layui-tab-title" id="myTab" role="tablist">
                <li class="layui-this" role="presentation">
                    <a class="nav-link" id="home-tab" data-toggle="tab" href="#detail" role="tab" aria-controls="detail"
                       aria-selected="true">商铺信息</a>
                </li>
                <li class="" role="presentation">
                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#log" role="tab" aria-controls="log"
                       aria-selected="false">信息变更记录</a>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane active" id="detail" role="tabpanel" aria-labelledby="detail-tab">
                    <div class="form-horizontal m">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">商铺类型：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['shop_type_text']}}</div>
                            </div>
                            @if(C_ID == 0)
                                <label class="col-sm-2 control-label resids__ ">所属社区：</label>
                                <div class="col-sm-4 resids__">
                                    @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                                        @if($type==$info['cid'])
                                            <div class="form-control-static">{{$name}}</div>
                                        @endif
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label resids"
                                   @if($info['shop_type']==2)style="display: none;"@endif>所属小区：</label>
                            <div class="col-sm-4 resids" @if($info['shop_type']==2)style="display: none;"@endif>
                                @foreach(Functions::getQuarters($info['cid']) as $key=>$name)
                                    @if($key==$info['quarter_id'])
                                        <div class="form-control-static">{{$name}}</div>
                                    @endif
                                @endforeach
                            </div>
                            <label class="col-sm-2 control-label mainids"
                                   @if($info['shop_type']==1)style="display: none;"@endif>选择主街：</label>
                            <div class="col-sm-4 mainids" @if($info['shop_type']==1)style="display: none;"@endif>
                                <select name="shop_main_street" class="select2" required>
                                    <option value="">请选择主街</option>
                                    @foreach($shop_main_street as $key=>$value)
                                        <option value="{{$value['value']}}" @selected($value['value']==$info['shop_main_street'])>{{$value['name']}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商铺名称：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['name']}}</div>
                            </div>
                            <label class="col-sm-2 control-label ">商铺地址：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['address']}}</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label ">所属行业：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['industry_pid_text']}}</div>
                            </div>
                            <label class="col-sm-2 control-label ">经营类别：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['industry_id_text']}}</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label ">负责人：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['link_user']}}</div>
                            </div>
                            <label class="col-sm-2 control-label ">联系方式：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['link_phone']}}</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label ">楼层：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['floor']}}</div>
                            </div>
                            <label class="col-sm-2 control-label ">面积（㎡）：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['area']}}</div>
                            </div>
                        </div>

                        <div class="form-group">

                            <label class="col-sm-2 control-label ">租金单价（元/㎡/月）：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['rent_price']}}</div>
                            </div>
                            <label class="col-sm-2 control-label ">从业人数：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static">{{$info['practice_num']}}</div>
                            </div>

                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">能源方式：</label>
                            <div class="col-sm-4" style="line-height: 30px;">
                                <div class="form-control-static">{{$info['shop_energy_type_text']}}</div>
                            </div>
                            <label class="col-sm-2 control-label ">状态：</label>
                            <div class="col-sm-4" style="line-height: 30px;">
                                <input type="text" value="{{$info['shop_status_text']}}" disabled>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">门头照片：</label>
                            <div class="col-sm-4">
                                <div class="b5uploadimgbox">
                                    <div class="b5uploadlistbox">
                                        @if ($info['shop_pic'] != '')
                                            @foreach(explode(',', $info['shop_pic']) as $image)
                                                <div class="b5upload_li">
                                                    <div class="b5uploadimg_con">
                                                        <div class="b5uploadimg_cell">
                                                            <img src="{{$image}}" alt="">
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <label class="col-sm-2 control-label ">营业执照：</label>
                            <div class="col-sm-4">
                                <div class="b5uploadimgbox">
                                    <div class="b5uploadlistbox">
                                        @if ($info['license_img'] != '')
                                            @foreach(explode(',', $info['license_img']) as $image)
                                                <div class="b5upload_li">
                                                    <div class="b5uploadimg_con">
                                                        <div class="b5uploadimg_cell">
                                                            <img src="{{$image}}" alt="">
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($selffield)
                            @foreach($selffield as $groupItem)
                                <div class="form-group">
                                    @foreach($groupItem as $item)
                                        <input type="hidden" name="extend[{{$item['field_name']}}][type]"
                                               value="{{$item['field_type']}}">
                                        <input type="hidden" name="extend[{{$item['field_name']}}][title]"
                                               value="{{$item['field_name']}}">
                                        <label class="col-sm-2 control-label @if($item['is_nust']) @endif">{{$item['field_name']}}
                                            ：</label>
                                        <div class="col-sm-4">
                                            @if($item['field_type'] == 1)
                                                <input type="text" name="extend[{{$item['field_name']}}][value]"
                                                       value="" @if($item['is_nust']) disabled
                                                       @endif class="form-control"
                                                       placeholder="请输入{{$item['field_name']}}" autocomplete="off"
                                                       disabled/>
                                            @endif
                                            @if($item['field_type'] == 2)
                                                <textarea type="text" name="extend[{{$item['field_name']}}][value]"
                                                          @if($item['is_nust']) disabled @endif class="form-control"
                                                          placeholder="请输入{{$item['field_name']}}"
                                                          disabled>{{$item['default_value']}}</textarea>
                                            @endif
                                            @if($item['field_type'] == 3)
                                                @foreach($item['extend'] as $ex)
                                                    <label class="radio-box" style="line-height: 30px;">
                                                        <input type="radio"
                                                               name="extend[{{$item['field_name']}}][value]"
                                                               value="{{$ex['title']}}" @if($item['is_nust']) disabled
                                                               @endif disabled/> {{$ex['title']}}
                                                    </label>
                                                @endforeach
                                            @endif
                                            @if($item['field_type'] == 4)
                                                @foreach($item['extend'] as $ex)
                                                    <label class="radio-box" style="line-height: 30px;">
                                                        <input type="checkbox"
                                                               name="extend[{{$item['field_name']}}][value][]"
                                                               value="{{$ex['title']}}" class="input-required"
                                                               @if($item['is_nust']) disabled
                                                               @endif disabled> {{$ex['title']}}
                                                    </label>
                                                @endforeach
                                            @endif
                                            @if($item['field_type'] == 5)
                                                <select name="extend[{{$item['field_name']}}][value]" class="select2"
                                                        style="width: 200px" @if($item['is_nust']) required
                                                        @endif disabled>
                                                    <option value="">选择{{$item['field_name']}}</option>
                                                    @foreach($item['extend'] as $name)
                                                        <option value="{{$name['title']}}">{{$name['title']}}</option>
                                                    @endforeach
                                                </select>
                                            @endif
                                            @if($item['field_type'] == 6)
                                                <select name="extend[{{$item['field_name']}}][value][]" class="select2"
                                                        multiple="true" data-width="auto" @if($item['is_nust']) required
                                                        @endif disabled>
                                                    <option value="">请选择{{$item['field_name']}}</option>
                                                    @foreach($item['extend'] as $name)
                                                        <option value="{{$name['title']}}">{{$name['title']}}</option>
                                                    @endforeach
                                                </select>
                                            @endif
                                            @if($item['field_type'] == 7)
                                                <input type="text" name="extend[{{$item['field_name']}}][value]"
                                                       value="" class="form-control filter-date"
                                                       placeholder="请选择{{$item['field_name']}}"
                                                       @if($item['is_nust']) disabled @endif disabled/>
                                            @endif
                                            @if($item['field_type'] == 8)
                                                <input type="text" name="extend[{{$item['field_name']}}][value]"
                                                       value="" class="form-control date-range"
                                                       placeholder="请选择{{$item['field_name']}}"
                                                       @if($item['is_nust']) disabled @endif autocomplete="off"
                                                       disabled/>
                                            @endif
                                            @if($item['field_type'] == 9)
                                                <input type="text" name="extend[{{$item['field_name']}}][value]"
                                                       value="" class="form-control filter-time"
                                                       placeholder="请选择{{$item['field_name']}}"
                                                       @if($item['is_nust']) disabled @endif autocomplete="off"
                                                       disabled/>
                                            @endif
                                            @if($item['field_type'] == 10)
                                                <input type="text" name="extend[{{$item['field_name']}}][value]"
                                                       value="" class="form-control time-range"
                                                       placeholder="请选择{{$item['field_name']}}"
                                                       @if($item['is_nust']) disabled @endif autocomplete="off"
                                                       disabled/>
                                            @endif
                                            @if($item['field_type'] == 12)
                                                <input type="hidden" name="extend[{{$item['field_name']}}][value]"
                                                       value="" id="imgs{{$item['max_index']}}"
                                                       @if($item['is_nust']) required @endif>
                                                <x-upload type="img" name="imgs{{$item['max_index']}}_upload"
                                                          :extend="['cat'=>'demo','link'=>1,'multi'=>10,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M；可拖动排序']"/>
                                            @endif
                                            @if($item['field_type'] == 13)
                                                <input type="hidden" name="extend[{{$item['field_name']}}][value]"
                                                       value="" id="files{{$item['max_index']}}"
                                                       @if($item['is_nust']) required @endif>
                                                <x-upload type="file" name="files{{$item['max_index']}}_upload"
                                                          :extend="['cat'=>'demo','link'=>1,'multi'=>10,'exts'=>'txt|rar|doc|png', 'tips'=>'格式为txt|rar|doc|pdf|xls|docx|xls|ppt|pptx,大小不能超过100M']"/>
                                            @endif
                                            @if($item['field_type'] == 11)
                                                <div class="input-group">
                                                    <input type="text" name="extend[{{$item['field_name']}}][value]"
                                                           value="" readonly class="form-control lnglat"
                                                           @if($item['is_nust']) required @endif autocomplete="off"
                                                           placeholder="请选择" disabled/>
                                                    <div class="input-group-addon"><i class="fa fa-map-marker"></i>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            @endforeach
                        @endif

                    </div>
                </div>
                <div class="tab-pane" id="log" role="tabpanel" aria-labelledby="log-tab">
                    <div class="col-sm-12 search-collapse">
                        <form id="search-form">
                            <div class="select-list">
                                <ul>
                                    <li class="select-time">
                                        <label>修改时间： </label>
                                        <input type="text" name="between[create_time][start]" id="startTime"
                                               placeholder="开始时间" readonly>
                                        <span>-</span>
                                        <input type="text" name="between[create_time][end]" id="endTime"
                                               placeholder="结束时间" readonly>
                                    </li>
                                    <li>
                                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                                    class="fa fa-search"></i> 搜索</a>
                                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                                    class="fa fa-refresh"></i> 重置</a>
                                    </li>
                                </ul>
                            </div>
                        </form>
                    </div>

                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            $(".nav-tabs li:first").addClass('active')
            $(".tab-content .tab-pane:first").addClass('active')

            var options = {
                url: 'shopchangelogs/index',
                queryParams: function (params) {
                    params['where[shop_id]'] = {{$info['id']}};
                    var formParams = $('#search-form').serializeArray();
                    $.each(formParams, function (i, field) {
                        params[field.name] = field.value;
                    });
                    params['pageNum'] = params.offset / params.limit + 1;
                    params['pageSize'] = params.limit;
                    params['orderByColumn'] = params.sort;
                    params['isAsc'] = params.order;
                    return params;
                },
                modalName: "商铺信息变更记录",
                sortName: 'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true},
                    // {field: 'user_id', title: '操作人', align: 'center'},
                    {field: 'username', title: '操作人', align: 'center'},
                    {field: 'user_type_text', title: '操作人类型', align: 'center'},
                    {field: 'create_time', title: '操作时间', align: 'left', sortable: true},
                    {field: 'field_name', title: '字段名', align: 'center'},
                    {field: 'old_value', title: '修改前', align: 'center',formatter: function (value, row, index) {
                            if(row.is_file){
                                return $.table.imageView(row,'old_value','50px');
                            }
                            return value;
                        }},
                    {field: 'new_value', title: '修改后', align: 'center',formatter: function (value, row, index) {
                            if(row.is_file){
                                return $.table.imageView(row,'new_value','50px');
                            }
                            return value;
                        }},
                ]
            };
            $.table.init(options);
        })
    </script>
@stop
