@extends('admin.layout.form')
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp

@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">商铺类型：</label>
            <div class="col-sm-4" style="line-height: 30px;">
                @foreach($dicList['shop_type'] as $key=>$value)
                    <label class="form-check-label">
                        <input type="radio" name="shop_type"
                               value="{{$value['value']}}" @checked($key==0)/> {{$value['name']}}
                    </label>
                @endforeach
            </div>
            @if(C_ID == 0)
                <label class="col-sm-2 control-label resids__ is-required">选择社区：</label>
                <div class="col-sm-4 resids__">
                    <select name="cid" class="select2" id="select-cid" required>
                        <option value="0">所属社区</option>
                        @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                            <option value="{{$type}}">{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            @endif
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label resids">选择小区：</label>
            <div class="col-sm-4 resids">
                <select name="quarter_id" class="select2" id="select-qid" required>
                    <option value="0">选择小区</option>
                    @foreach($quarters as $name)
                        <option value="{{$name['id']}}">{{$name['name']}}</option>
                    @endforeach
                </select>
            </div>

            <label class="col-sm-2 control-label mainids" style="display: none;">选择主街：</label>
            <div class="col-sm-4 mainids" style="display: none;">
                <select name="shop_main_street" class="select2" required>
                    <option value="">请选择主街</option>
                    @foreach($shop_main_street as $key=>$value)
                        <option value="{{$value['value']}}">{{$value['name']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">商铺名称：</label>
            <div class="col-sm-4">
                <input type="text" name="name" value="" class="form-control" required autocomplete="off"
                       placeholder="请输入商铺名称"/>
            </div>
            <label class="col-sm-2 control-label is-required">商铺地址：</label>
            <div class="col-sm-4">
                <input type="text" name="address" value="" class="form-control" required autocomplete="off"
                       placeholder="请输入商铺地址"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">所属行业：</label>
            <div class="col-sm-4">
                <select name="industry_pid" class="select2" id="select-pid" required>
                    <option value="">请选择所属行业</option>
                    @foreach(Functions::getIndustrys(0) as $key=>$value)
                        <option value="{{$key}}">{{$value}}</option>
                    @endforeach
                </select>
            </div>

            <label class="col-sm-2 control-label is-required">经营类别：</label>
            <div class="col-sm-4">
                <select name="industry_id" class="select2" id="select-id" required>
                    <option value="">请选择经营类别</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">负责人：</label>
            <div class="col-sm-4">
                <input type="text" name="link_user" value="" class="form-control" required autocomplete="off"
                       placeholder="请输入负责人"/>
            </div>
            <label class="col-sm-2 control-label is-required">联系方式：</label>
            <div class="col-sm-4">
                <input type="text" name="link_phone" value="" class="form-control" required autocomplete="off"
                       placeholder="请输入联系方式"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">楼层：</label>
            <div class="col-sm-4">
                <input type="text" name="floor" value="" class="form-control" required autocomplete="off"
                       placeholder="请输入楼层"/>
            </div>
            <label class="col-sm-2 control-label">面积（㎡）：</label>
            <div class="col-sm-4">
                <input type="text" name="area" value="" class="form-control" autocomplete="off"
                       placeholder="请输入面积"/>
            </div>

        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">租金单位（元/㎡/月）：</label>
            <div class="col-sm-4">
                <input type="number" name="rent_price" value="" class="form-control"  autocomplete="off"
                       placeholder="请输入租金"/>
            </div>
            <label class="col-sm-2 control-label">从业人数：</label>
            <div class="col-sm-4">
                <input type="number" name="practice_num" value="" class="form-control" autocomplete="off"
                       placeholder="请输入从业人数"/>
            </div>

        </div>
        <div>
            <label class="col-sm-2 control-label">能源方式：</label>
            <div class="col-sm-4" style="line-height: 30px;">
                <input type="hidden" name="shop_energy_type" id="shop_energy_type" value="">
                @foreach($dicList['shop_energy_type'] as $key=>$value)
                    <label class="check-box">
                        <input type="checkbox" name="energy_type" value="{{$value['value']}}"/> {{$value['name']}}
                    </label>
                @endforeach
            </div>
            <label class="col-sm-2 control-label is-required">状态：</label>
            <div class="col-sm-4" style="line-height: 30px;">
                @foreach($dicList['shop_status'] as $key=>$value)
                    <label class="radio-box">
                        <input type="radio" name="shop_status"
                               value="{{$value['value']}}" @checked($key=='shop_status_ok')/> {{$value['name']}}
                    </label>
                @endforeach
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">门头照片：</label>
            <div class="col-sm-4">
                <input type="hidden" name="shop_pic" value="" id="shop_pic" >
                <x-upload type="img" name="shop_pic_upload"
                          :extend="['cat'=>'demo','link'=>1,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M']"/>
            </div>
            <label class="col-sm-2 control-label">营业执照：</label>
            <div class="col-sm-4">
                <input type="hidden" name="license_img" value="" id="license_img" >
                <x-upload type="img" name="license_img_upload"
                          :extend="['cat'=>'demo','link'=>1,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M']"/>
            </div>
        </div>
        @if($selffield)
            @foreach($selffield as $groupItem)
                <div class="form-group">
                    @foreach($groupItem as $item)
                        <input type="hidden" name="extend[{{$item['field_name']}}][type]"
                               value="{{$item['field_type']}}">
                        <input type="hidden" name="extend[{{$item['field_name']}}][title]"
                               value="{{$item['field_name']}}">
                        <label class="col-sm-2 control-label @if($item['is_nust']) is-required @endif">{{$item['field_name']}}
                            ：</label>
                        <div class="col-sm-4">
                            @if($item['field_type'] == 1)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       @if($item['is_nust']) required @endif class="form-control"
                                       placeholder="请输入{{$item['field_name']}}" autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 2)
                                <textarea type="text" name="extend[{{$item['field_name']}}][value]"
                                          @if($item['is_nust']) required @endif class="form-control"
                                          placeholder="请输入{{$item['field_name']}}"></textarea>
                            @endif
                            @if($item['field_type'] == 3)
                                @foreach($item['extend'] as $ex)
                                    <label class="radio-box" style="line-height: 30px;">
                                        <input type="radio" name="extend[{{$item['field_name']}}][value]"
                                               value="{{$ex['title']}}"
                                               @if($item['is_nust']) required @endif/> {{$ex['title']}}
                                    </label>
                                @endforeach
                            @endif
                            @if($item['field_type'] == 4)
                                @foreach($item['extend'] as $ex)
                                    <label class="radio-box" style="line-height: 30px;">
                                        <input type="checkbox" name="extend[{{$item['field_name']}}][value][]"
                                               value="{{$ex['title']}}" class="input-required"
                                               @if($item['is_nust']) required @endif > {{$ex['title']}}
                                    </label>
                                @endforeach
                            @endif
                            @if($item['field_type'] == 5)
                                <select name="extend[{{$item['field_name']}}][value]" class="select2"
                                        style="width: 200px" req@if($item['is_nust']) required @endifuired>
                                    <option value="">选择{{$item['field_name']}}</option>
                                    @foreach($item['extend'] as $name)
                                        <option value="{{$name['title']}}">{{$name['title']}}</option>
                                    @endforeach
                                </select>
                            @endif
                            @if($item['field_type'] == 6)
                                <select name="extend[{{$item['field_name']}}][value][]" class="select2" multiple="true"
                                        data-width="auto" @if($item['is_nust']) required @endif>
                                    <option value="">请选择{{$item['field_name']}}</option>
                                    @foreach($item['extend'] as $name)
                                        <option value="{{$name['title']}}">{{$name['title']}}</option>
                                    @endforeach
                                </select>
                            @endif
                            @if($item['field_type'] == 7)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control filter-date" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif />
                            @endif
                            @if($item['field_type'] == 8)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control date-range" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 9)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control filter-time" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 10)
                                <input type="text" name="extend[{{$item['field_name']}}][value]" value=""
                                       class="form-control time-range" placeholder="请选择{{$item['field_name']}}"
                                       @if($item['is_nust']) required @endif autocomplete="off"/>
                            @endif
                            @if($item['field_type'] == 12)
                                <input type="hidden" name="extend[{{$item['field_name']}}][value]" value=""
                                       id="imgs{{$item['max_index']}}" @if($item['is_nust']) required @endif>
                                <x-upload type="img" name="imgs{{$item['max_index']}}_upload"
                                          :extend="['cat'=>'demo','link'=>1,'multi'=>10,'tips'=>'格式为jpg,jpeg,png,gif；大小不能超过10M；可拖动排序']"/>
                            @endif
                            @if($item['field_type'] == 13)
                                <input type="hidden" name="extend[{{$item['field_name']}}][value]" value=""
                                       id="files{{$item['max_index']}}" @if($item['is_nust']) required @endif>
                                <x-upload type="file" name="files{{$item['max_index']}}_upload"
                                          :extend="['cat'=>'demo','link'=>1,'multi'=>10,'exts'=>'txt|rar|doc|png', 'tips'=>'格式为txt|rar|doc|pdf|xls|docx|xls|ppt|pptx,大小不能超过100M']"/>
                            @endif
                            @if($item['field_type'] == 11)
                                <div class="input-group">
                                    <input type="text" name="extend[{{$item['field_name']}}][value]" value="" readonly
                                           class="form-control lnglat" @if($item['is_nust']) required
                                           @endif autocomplete="off" placeholder="请选择"/>
                                    <div class="input-group-addon"><i class="fa fa-map-marker"></i></div>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endforeach
        @endif
    </form>
@stop

@section('script')
    <script>
        $(document).ready(function () {
            $("input[name='shop_type']").change(function () {
                var checkValue = $(this).val();
                if (checkValue == 1) {
                    $(".resids").show();
                    $('#select-cid').trigger("change");
                    $(".mainids").hide();
                }
                if (checkValue == 2) {
                    $(".resids").hide();
                    $(".mainids").show();
                }
            });

            // 当选中选项时触发
            $('#select-cid').on('change', function (e) {
                if ($('input[name="shop_type"]:checked').val() == 2) {
                    // 主街无联动关系
                    return false;
                }
                var id = $(this).val();
                //$("#department-li").html("");
                $.ajax({
                    url: '{{route('street.resident.ajaxres')}}',
                    type: "post",
                    data: {"c_id": id},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            var html = '<option value="">选择小区</option>';
                            $.each(result.data, function (index, item) {
                                html += '<option value="' + item.id + '">' + item.name + '</option>';
                            })
                            $("#select-qid").html(html)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else {
                            $.modal.alertError(result.msg);
                            $.modal.closeLoading();
                            $.modal.enable();
                        }
                    }
                });
            });

            // 当选中选项时触发
            $('#select-pid').on('select2:select', function (e) {
                var id = e.params.data.id;
                //$("#department-li").html("");
                console.log(e)
                $.ajax({
                    url: '{{route('system.industry.ajax')}}',
                    type: "post",
                    data: {"pid": id},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            var html = '<option value="">请选择经营类别</option>';
                            $.each(result.data, function (index, item) {
                                html += '<option value="' + index + '">' + item + '</option>';
                            })
                            $("#select-id").html(html)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else {
                            $.modal.alertError(result.msg);
                            $.modal.closeLoading();
                            $.modal.enable();
                        }
                    }
                });
            });
        });


        function submitHandler() {
            var license_img = '';
            var shop_pic = [];
            var shop_energy_type = [];

            if ($("input[name='license_img_upload[]']").length > 0) {
                $("input[name='license_img_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if (imgval) {
                        license_img = imgval;
                    }
                })
            }
            $("#license_img").val(license_img);

            if ($("input[name='shop_pic_upload[]']").length > 0) {
                $("input[name='shop_pic_upload[]']").each(function () {
                    var imgval = $(this).val();
                    if (imgval) {
                        shop_pic.push(imgval)
                    }
                })
            }
            $("#shop_pic").val(shop_pic.join(','));

            $('input[name="energy_type"]:checked').each(function () {
                shop_energy_type.push($(this).val());
            });
            $("#shop_energy_type").val(shop_energy_type.join(','));

            if ($.validate.form()) {


                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
