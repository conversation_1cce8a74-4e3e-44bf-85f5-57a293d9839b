@extends('admin.layout.layout')
@include('widget.asset.viewer')
@include('widget.asset.select2')
@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp

<style>
    .layui-tab-title li a {
        color: #2F4650;
    }

    .layui-this a {
        color: #2195FF !important;
    }

    .fixed-table-body {
        height: auto !important;
    }
</style>
@section('content')
    <div class="mt10 layui-tab layui-tab-brief" style="background-color: #fff;">
        <ul class="layui-tab-title" id="myTab" role="tablist">
            <li class="layui-this" role="presentation">
                <a class="nav-link" id="home-tab" data-toggle="tab" href="#all" role="tab" aria-controls="detail"
                   aria-selected="true">所有商铺 {{$dicList['all']}}</a>
            </li>
            @foreach($dicList['shop_type'] as $key=>$value)
                <li class="" role="presentation">
                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#{{$value['value']}}" role="tab"
                       aria-controls="log" aria-selected="false">{{$value['name']}} {{$value['shop_count']}}</a>
                </li>
            @endforeach
        </ul>
        <div class="bs-example bs-example-tabs b5navtab" data-example-id="togglable-tabs">
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane active" id="all" role="tabpanel" aria-labelledby="detail-tab">
                    <div class="col-sm-12 search-collapse">
                        <form id="role-form">
                            <div class="select-list">
                                <ul>

                                    <li>
                                        <select name="where[shop_type]" class="select2" id="select_type">
                                            <option value="">所属小区/主街</option>
                                            @foreach($dicList['shop_type'] as $key=>$value)
                                                <option value="{{$value['value']}}">{{$value['name']}}</option>
                                            @endforeach
                                        </select>
                                    <li>
                                    <li class="mainids" style="display: none;">
                                        <select name="where[shop_main_street]" class="select2">
                                            <option value="">主街</option>
                                            @foreach($dicList['shop_main_street'] as $key=>$value)
                                                <option value="{{$value['value']}}">{{$value['name']}}</option>
                                            @endforeach
                                        </select>
                                    <li>

                                    <li class="resids" style="display: none;">
                                        <select name="where[cid]" class="select2" id="select-cid">
                                            <option value="">所属社区</option>
                                            @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                                                <option value="{{$type}}">{{$name}}</option>
                                            @endforeach
                                        </select>
                                    <li>
                                    <li class="resids" style="display: none;">
                                        <select name="where[quarter_id]" class="select2" id="select-qid">
                                            <option value="">选择小区</option>
                                        </select>
                                    <li>

                                    <li>
                                        <select name="where[industry_pid]" class="select2" id="select-pid">
                                            <option value="">所属行业</option>
                                            @foreach(Functions::getIndustrys(0) as $key=>$value)
                                                <option value="{{$key}}">{{$value}}</option>
                                            @endforeach
                                        </select>
                                    <li>
                                    <li>
                                        <select name="where[industry_id]" class="select2" id="select-id">
                                            <option value="">经营类别</option>
                                        </select>
                                    <li>

                                    <li>
                                        <select name="findinset[shop_energy_type]" class="select2">
                                            <option value="">能源方式</option>
                                            @foreach($dicList['shop_energy_type'] as $key=>$value)
                                                <option value="{{$value['value']}}">{{$value['name']}}</option>
                                            @endforeach
                                        </select>
                                    <li>
                                    <li>
                                        <select name="where[shop_status]" class="select2">
                                            <option value="">状态</option>
                                            @foreach($dicList['shop_status'] as $key=>$value)
                                                <option value="{{$value['value']}}">{{$value['name']}}</option>
                                            @endforeach
                                        </select>
                                    <li>
                                    <li><input type="text" name="like[name]" placeholder="商铺名称"></li>
                                    <li>
                                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                                    class="fa fa-search"></i> 搜索</a>
                                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                                    class="fa fa-refresh"></i> 重置</a>
                                    </li>
                                </ul>
                            </div>
                        </form>
                    </div>
                    <div class="btn-group-sm" id="toolbar" role="group">
                        @hasPerm("street:shop:add")
                        <a class="btn btn-warning" onclick="$.operate.addFull()"><i class="fa fa-plus"></i> 新增</a>
                        @endhasPerm

                        @hasPerm("street:shop:import")
                        <x-import-template-item
                                :extend="['url'=>route('street.shop.import'),'id'=>'resident-upload','title'=>'批量导入商铺','temp_url' => asset('template/商铺.xlsx'), 'tips'=>'严格按照要求导入']"/>
                        @endhasPerm
                        @hasPerm("street:shop:export")
                        <a class="btn btn-warning btn-xs" onclick="$.table.exportExcel()"><i class="fa fa-download"></i>
                            导出</a>
                        @endhasPerm
                        @hasPerm("street:shop:dropall")
                        <a class="btn btn-warning multiple disabled" onclick="$.operate.removeAll(this)"><i
                                    class="fa fa-trash"></i>
                            批量删除</a>
                        @endhasPerm
                    </div>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var param = '';
            $('a[data-toggle="tab"]').on('click', function (e) {
                e.preventDefault(); // 阻止默认的锚点跳转行为
                var tab = $(this).attr('href'); // 获取被点击的标签的 href
                tab = tab.replace(/^#+/, '');
                // 根据不同的标签设置不同的 iframe src
                if (tab === 'all') {
                    param = '';
                } else {
                    param = tab;
                }
                // 刷新表格
                $.table.search();
                // 激活 Bootstrap 标签
                $(this).tab('show');
            });

            var options = {
                modalName: "商铺",
                sortName: 'listsort',
                sortOrder: "asc",
                queryParams: function (params) {
                    var formParams = $('#role-form').serializeArray();
                    $.each(formParams, function (i, field) {
                        params[field.name] = field.value;
                    });
                    if (param !== '') {
                        params['where[shop_type]'] = param;
                    }
                    console.log('params', params);
                    return params;
                },
                columns: [
                    {checkbox: true},
                    {field: 'id', title: '编号', align: 'left', sortable: true},
                    {field: 'shop_type_text', title: '商铺类型', align: 'left'},
                    {field: 'name', title: '商铺名称', align: 'left'},
                    {field: 'address', title: '地址', align: 'left'},
                    {field: 'industry_pid_text', title: '所属行业', align: 'left'},
                    {field: 'industry_id_text', title: '经营类别', align: 'left'},
                    {field: 'link_user', title: '负责人', align: 'left'},
                    {field: 'link_phone', title: '联系电话', align: 'left'},
                    {field: 'shop_energy_type_text', title: '能源方式', align: 'left'},
                    {field: 'shop_status_text', title: '状态', align: 'left'},
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true, visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("street:shop:detail")
                            // actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.detail(\'' + row.id + '\')"> 详情</a> ');
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="detail(\'' + row.id + '\')"> 详情</a> ');
                        @endhasPerm
                        @hasPerm("street:shop:edit")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.editFull(\'' + row.id + '\')"> 编辑</a> ');
                        @endhasPerm
                        @hasPerm("street:shop:drop")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')"></i> 删除</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
            // 当所属行业选中选项时触发
            $('#select-pid').on('select2:select', function (e) {
                var id = e.params.data.id;
                //$("#department-li").html("");
                console.log(e)
                $.ajax({
                    url: '{{route('system.industry.ajax')}}',
                    type: "post",
                    data: {"pid": id},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            var html = '<option value="">请选择经营类别</option>';
                            $.each(result.data, function (index, item) {
                                html += '<option value="' + id + '">' + item + '</option>';
                            })
                            $("#select-id").html(html)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else {
                            $.modal.alertError(result.msg);
                            $.modal.closeLoading();
                            $.modal.enable();
                        }
                    }
                });
            });
            // 当所属社区选中选项时触发
            $('#select-cid').on('select2:select', function (e) {
                if ($('input[name="shop_type"]:checked').val() == 2) {
                    // 主街无联动关系
                    return false;
                }
                var id = e.params.data.id;
                //$("#department-li").html("");
                $.ajax({
                    url: '{{route('street.resident.ajaxres')}}',
                    type: "post",
                    data: {"c_id": id},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            var html = '<option value="">选择小区</option>';
                            $.each(result.data, function (index, item) {
                                html += '<option value="' + item.id + '">' + item.name + '</option>';
                            })
                            $("#select-qid").html(html)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                            $.modal.closeLoading();
                            $.modal.enable();
                        } else {
                            $.modal.alertError(result.msg);
                            $.modal.closeLoading();
                            $.modal.enable();
                        }
                    }
                });
            });
            $('#select_type').on('select2:select', function (e) {
                var checkValue = $(this).val();
                console.log(checkValue);
                if (checkValue == 1) {
                    $(".resids").show();
                    $(".mainids").hide();
                } else if (checkValue == 2) {
                    $(".resids").hide();
                    $(".mainids").show();
                } else {
                    $(".resids").hide();
                    $(".mainids").hide();
                }
            });
        });


        function detail(id) {
            $.modal.openTab("商铺详情", '{{route('street.shop.detail')}}?id=' + id, true);
        }
    </script>
@stop

