@extends('admin.layout.layout')

@section('content')
    <div class="mt10" style="background-color: #fff;padding: 10px">
        <div class="bs-example bs-example-tabs b5navtab" data-example-id="togglable-tabs">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item active" role="presentation">
                    <a class="nav-link" id="home-tab" data-toggle="tab" href="#jiaban" role="tab" aria-controls="detail" aria-selected="true">加班</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#waichu" role="tab" aria-controls="log" aria-selected="false">外出</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="log-tab" data-toggle="tab" href="#qingjia" role="tab" aria-controls="log" aria-selected="false">请假</a>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane active" id="jiaban" role="tabpanel" aria-labelledby="detail-tab">
                    <iframe class="RuoYi_iframe" id="myIframe" src="{{route('street.kaoqinjiaban.index')}}" frameborder="0" width="100%" height="750px"></iframe>
                </div>
            </div>
        </div>
    </div>


@stop

@section('script')
    <script>
        $(function () {
            $('a[data-toggle="tab"]').on('click', function (e) {
                e.preventDefault(); // 阻止默认的锚点跳转行为
                var tab = $(this).attr('href'); // 获取被点击的标签的 href
                var iframe = $('#myIframe'); // 获取 iframe 元素

                // 根据不同的标签设置不同的 iframe src
                if (tab === '#jiaban') {
                    iframe.attr('src', '{{route('street.kaoqinjiaban.index')}}');
                } else if (tab === '#waichu') {
                    iframe.attr('src', '{{route('street.kaoqinout.index')}}');
                } else if (tab === '#qingjia') {
                    iframe.attr('src', '{{route('street.kaoqinleave.index')}}');
                }
                // 激活 Bootstrap 标签
                $(this).tab('show');
            });

            // 初始加载时可能也需要设置 iframe 的 src（可选）
            var initialTab = $('.nav-link.active').attr('href');
            if (initialTab) {
              var iframe = $('#myIframe');
              if (initialTab === '#jiaban') {
                iframe.attr('src', '{{route('street.kaoqinjiaban.index')}}');
              } else if (initialTab === '#waichu') {
                iframe.attr('src', '{{route('street.kaoqinout.index')}}');
              } else if (initialTab === '#qingjia') {
                  iframe.attr('src', '{{route('street.kaoqinleave.index')}}');
              }
            }


        });

    </script>
@stop

