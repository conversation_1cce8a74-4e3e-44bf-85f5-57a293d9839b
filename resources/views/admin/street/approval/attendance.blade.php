@extends('admin.layout.layout')

@section('content')
    <style>
        .separator {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 50%;
            width: 1px;
            background-color: #ddd;
            transform: translateX(-50%);
        }
        .left-content, .right-content {
            height: 100vh; /* 使内容区域填满整个高度 */
            overflow: auto; /* 如果内容溢出，允许滚动 */
            padding: 20px;
        }
        .p-3-l p{
            line-height: 30px;
        }
        .p-3-r p{
            line-height: 5px;
        }
        h4{
            /*line-height: 40px;*/
            position: relative;
            padding-left: 15px; /* 确保文字与竖线有间距 */
            margin-bottom: 10px;
        }
        h4::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px; /* 竖线宽度 */
            background-color: #0076F6; /* 竖线颜色 */
        }
    </style>

    <div class="container-fluid">
        <div class="row position-relative">
            <div class="col-md-6 left-content">
                <div class="p-3-l">
                    <!-- 左侧内容 -->
                    <h4>申请信息</h4>
                    <p>申请类型：请假-调休</p>
                    <p>开始时间：2024-06-17 07:15:11</p>
                    <p>结束时间：2024-06-17 07:15:11</p>
                    <p>事由：XXXXX</p>
                    <p>附件：文件名称.mp4</p>
                </div>
            </div>
            <div class="separator"></div>
            <div class="col-md-6 right-content">
                <div class="p-3-r">
                    <!-- 右侧内容 -->
                    <h4>审批流程</h4>
                    <div id="vertical-timeline" class="vertical-container light-timeline">
                        <div class="vertical-timeline-block">
                            <div class="vertical-timeline-icon blue-bg">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <div class="vertical-timeline-content">
                                <p>2024-12-04 10:05:21</p>
                                <p>XX科室 张三 <span class="badge badge-success">发起</span></p>
                            </div>
                        </div>

                        <div class="vertical-timeline-block">
                            <div class="vertical-timeline-icon blue-bg">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <div class="vertical-timeline-content">
                                <p>2024-12-04 10:05:21</p>
                                <p>城运中心 李四 <span class="badge badge-primary">通过</span></p>
                                <p>通过，此处是审核意见</p>
                            </div>
                        </div>

                        <div class="vertical-timeline-block">
                            <div class="vertical-timeline-icon blue-bg">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <div class="vertical-timeline-content">
                                <p>2024-12-04 10:05:21</p>
                                <p>XX社区 张三 <span class="badge badge-primary">通过</span></p>
                                <p>通过，此处是审核意见</p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

@stop

@section('script')
    <script>
        $(function () {
        });

    </script>
@stop

