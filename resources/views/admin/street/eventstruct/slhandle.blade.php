@extends('admin.layout.form')

@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-2 control-label is-required">处理方式：</label>
        <div class="col-sm-9">
            <label class="radio-box">
                <input type="radio" name="type" required value="1" checked> 下派
            </label>
            @if ($slhandle == 2)
                <label class="radio-box">
                    <input type="radio" name="type" required value="2"> 上报
                </label>
            @endif
        </div>
    </div>
    <div id="assign">
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">处置人员：</label>
            <div class="col-sm-9">
                <select name="user_ids" xm-select="user_ids" xm-select-search>
                    <option value="">请选择</option>
                    @foreach($disposalUsers as $item)
                        <optgroup label="{{$item['type_name']}}">
                            @foreach($item['users'] as $user)
                                <option value="{{$user['id']}}">{{$user['username']}}（{{$user['mobile']}}）</option>
                            @endforeach
                        </optgroup>
                    @endforeach
                </select>
            </div>
        </div>
        @if ($slhandle == 2)
        <div class="form-group">
            <label class="col-sm-2 control-label">协同科室：</label>
            <div class="col-sm-9">
                <select name="cooperate_struct_ids" xm-select="cooperate_struct_ids" xm-select-search>
                    <option value="">请选择</option>
                    @foreach($structs as $id=>$name)
                        <option value="{{$id}}" @if(in_array($id, $selectStructIds)) selected  disabled @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        @endif
    </div>

    @if ($slhandle == 2)
    <div class="form-group" style="display: none" id="report">
        <label class="col-sm-2 control-label is-required">上报人员：</label>
        <div class="col-sm-9">
            <select name="leader_ids" xm-select="leader_ids" xm-select-search>
                <option value="">请选择</option>
                @foreach($leaders['fg'] as $leader)
                    <option value="{{$leader['id']}}" selected disabled>{{$leader['username']}} 分管领导</option>
                @endforeach
                @foreach($leaders['main'] as $leader)
                    <option value="{{$leader['id']}}">{{$leader['username']}} 主要领导</option>
                @endforeach
            </select>
        </div>
    </div>
    @endif
    <div class="form-group">
        <label class="col-sm-2 control-label">处理意见：</label>
        <div class="col-sm-9">
            <textarea rows="3" placeholder="请输入"  name="note"  class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            $('input[name=type]').on('ifChecked', function () {
                if ($(this).val() == 1) {
                    $('#report').hide();
                    $('#assign').show();
                } else {
                    $('#assign').hide();
                    $('#report').show();
                }
            })
        });
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.eventstruct.slhandle')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
