@extends('admin.layout.form')

@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-2 control-label is-required">审核结果：</label>
        <div class="col-sm-9">
            <label class="radio-box">
                <input type="radio" name="audit_status" required value="1" checked> 通过
            </label>
            <label class="radio-box">
                <input type="radio" name="audit_status" required value="2"> 驳回
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">审核意见：</label>
        <div class="col-sm-9">
            <textarea rows="3" placeholder="请输入"  name="audit_note"  class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.eventstruct.disposeaudit')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
