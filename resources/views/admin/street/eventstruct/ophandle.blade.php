@extends('admin.layout.form')

@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-2 control-label is-required">处理方式：</label>
        <div class="col-sm-9">
            <label class="radio-box">
                <input type="radio" name="type" required value="1" checked> 下派
            </label>
            @if ($ophandle == 1)
                <label class="radio-box">
                    <input type="radio" name="type" required value="2"> 转派
                </label>
            @else
                <label class="radio-box">
                    <input type="radio" name="type" required value="3"> 上报
                </label>
            @endif
        </div>
    </div>
    <div class="form-group" id="assign">
        <label class="col-sm-2 control-label is-required">处置人员：</label>
        <div class="col-sm-9">
            <select name="user_ids" xm-select="user_ids" xm-select-search>
                <option value="">请选择</option>
                @foreach($disposalUsers as $item)
                    <optgroup label="{{$item['type_name']}}">
                        @foreach($item['users'] as $user)
                            <option value="{{$user['id']}}">{{$user['username']}}（{{$user['mobile']}}）</option>
                        @endforeach
                    </optgroup>
                @endforeach
            </select>
        </div>
    </div>
    @if ($ophandle == 1)
    <div class="form-group" style="display: none" id="transfer">
        <label class="col-sm-2 control-label is-required">转派部门：</label>
        <div class="col-sm-9">
            <select name="struct_ids" xm-select="struct_ids" xm-select-search>
                <option value="">请选择</option>
                @if(!empty($structs))
                    <optgroup label="科室">
                        @foreach($structs as $id=>$name)
                            <option value="{{$id}}" @if(in_array($id, $selectStructIds)) selected  disabled @endif>{{$name}}</option>
                        @endforeach
                    </optgroup>
                @endif
                @if (!empty($community))
                    <optgroup label="社区">
                        @foreach($community as $id=>$name)
                            <option value="{{$id}}" @if(in_array($id, $selectStructIds)) selected disabled @endif>{{$name}}</option>
                        @endforeach
                    </optgroup>
                @endif
            </select>
        </div>
    </div>
    @else
    <div class="form-group" style="display: none" id="report">
        <label class="col-sm-2 control-label is-required">上报人员：</label>
        <div class="col-sm-9">
            <select name="leader_ids" xm-select="leader_ids" xm-select-search xm-select-disabled>
                <option value="">请选择</option>
                @foreach($structLeader as $leader)
                    <option value="{{$leader['id']}}" selected disabled>{{$leader['username']}}</option>
                @endforeach
            </select>
        </div>
    </div>
    @endif
    <div class="form-group">
        <label class="col-sm-2 control-label">处理意见：</label>
        <div class="col-sm-9">
            <textarea rows="3" placeholder="请输入"  name="note"  class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            $('input[name=type]').on('ifChecked', function () {
                if ($(this).val() == 1) {
                    $('#assign').show();
                    $('#report').hide();
                    $('#transfer').hide();
                } else {
                    $('#assign').hide();
                    $('#report').show();
                    $('#transfer').show();
                }
            })
        });
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.eventstruct.ophandle')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
