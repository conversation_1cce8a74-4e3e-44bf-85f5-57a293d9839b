@extends('admin.layout.form')
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('content')
    <script>
        var annexIndex = 0;
    </script>
<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-2 control-label">处置意见：</label>
        <div class="col-sm-9">
            <div class="form-control-static">{{$info['dispose_note']}}</div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">附件：</label>
        <div class="col-sm-9">
            @if ($info['dispose_annex'])
                <div class="b5uploadfilebox" style="display: inline-flex">
                    <div class="b5uploadlistbox" >
                        @foreach($info['dispose_annex'] as $file)
                            <div class="b5upload_li">
                                <div class="b5upload_filetype "></div>
                                <div class="b5upload_filename">
                                    <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</form>
@stop

