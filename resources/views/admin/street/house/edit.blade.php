@php use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.form')
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@include('widget.asset.summernote')
@section('content')
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <input type="hidden" name="res_id" value="{{$info['res_id']}}" />
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">楼栋：</label>
            <div class="col-sm-3">
                <input type="number" name="building" value="{{$info['building']}}" class="form-control" placeholder="请输入楼栋[数字]" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请填写阿拉伯数字</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">单元：</label>
            <div class="col-sm-3">
                <input type="number" name="unit" value="{{$info['unit']}}" class="form-control" placeholder="请输入单元[数字]" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请填写阿拉伯数字</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">楼层：</label>
            <div class="col-sm-3">
                <input type="number" name="floor" value="{{$info['floor']}}" class="form-control" placeholder="请输入楼层[数字]" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请填写阿拉伯数字</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">房号：</label>
            <div class="col-sm-3">
                <input type="number" name="sn" value="{{$info['sn']}}" class="form-control" placeholder="请输入楼层[房号]" required autocomplete="off"/>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请填写阿拉伯数字</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">房屋用途：</label>
            <div class="col-sm-3">
                <select name="use_type" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(Functions::getDictDate("res_use") as $type=>$name)
                        <option value="{{$type}}" @if($type == $info['use_type']) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">使用类型：</label>
            <div class="col-sm-8">
                <select name="type" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(Functions::getDictDate("res_type") as $type=>$name)
                        <option value="{{$type}}" @if($type == $info['type']) selected @endif>{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
