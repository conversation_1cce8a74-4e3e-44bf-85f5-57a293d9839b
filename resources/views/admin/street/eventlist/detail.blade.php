
@extends('admin.layout.form')
@include('widget.asset.viewer')
@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }
        .multiple-title{
            cursor: pointer;
            padding: 10px;
        }
        .multiple-title.active{
            color: #1d6eff;
            background-color: #f0f2f5;
            border-radius: 5px;
        }

        .b5uploadimgbox .b5uploadlistbox img{
            cursor: zoom-in;
        }
        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            background-color: #f7f8fa;
            padding: 20px 5px;
        }
        .detail-box .arrange {
            padding-left: 10px;
            padding-right: 10px;
        }
        .detail-box .arrange:first-child{
            padding-left: 0;
        }
        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding: 20px;
        }

        .btn-xs{
            font-size: 12px;
        }
        .xgry{
            display: none;
        }
        .flip-clock-label {
            display: none;
        }
        .header-title {
            font-style: normal;
            font-weight: 500;
            font-size: 22px;
            line-height: 40px;
            padding-left: 10px;
            color: #000000;
        }
        .mbig-btn {
            background-color: #fff;
            border: 1px solid #2256FF;
            color: #2256FF !important;
            padding: 8px 5px;
            border-radius: 7px;
            margin: 0 5px;
            display: inline-block;
            min-width: 96px;
            text-align: center;
        }
        .mbig-btn:hover{
            background-color: #2256FF;
            border-color: #2256FF;
            color: #FFFFFF !important;
        }
        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
        }
        .butuchu {
            color: #000000;
            font-size: 14px;

        }
        .butuchu1 {
            color: #999999;
            font-size: 14px;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 20px;
            height: 20px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .history-edit {
            color: #1d6eff;
            background-color: #f0f2f5;
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
            text-align: center;
            line-height: 24px;
        }
        .table.table-tr-b>tbody>tr>td {
            border: none;
            border-bottom: 1px solid #ecedf1 !important;
        }
        .table.table-tr-b>thead>tr>th {
            border: none;
            background-color: #f7f8fa;
            color: black;
            height: 45px;
        }
        #countdown {
            background-color: #2357ff;
            color: #fff;
            padding: 6px 10px 6px 20px;
            border-radius: 7px;
            letter-spacing: 10px;
            text-align: center;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color: #f1f8fe;
            margin: 0px 1px -8px 0px;
            padding: 15px;
            border-radius: 4px;
        }
        .tooltip-inner {
            padding: 4px 7px;
            margin: 2px;
            line-height: 14px;
        }
        .form-control-static {
            word-break: break-all;
        }
    </style>
@append
@section('content')
{{--<div class="row">--}}
{{--    <span class="header-title">事件详情</span>--}}
{{--    <span  class="pull-right" style="float: right;padding-right: 30px;padding-top: 10px">--}}
{{--            <a href="javascript:void(0)" class="mbig-btn" id="show-video-map"><i class="fa fa-file-movie-o"></i>视频和地图</a>--}}
{{--            @if($info['buttons']['assign'] && hasPerm('info:inforeport:assign'))--}}
{{--                <a href="javascript:void(0)" id="btn-assign" class="mbig-btn"><i class="fa fa-user-plus"></i> 增派</a>--}}
{{--            @endif--}}
{{--        </span>--}}
{{--</div>--}}
<div class="row detail-box">
    <div class="col-sm-6 arrange" id="base-info">
        <div class="item">
            <form action="" class="form-horizontal" style="padding-bottom: 10px;">
                <div>
                    <div class="row">
                        <div class="item-head-title">事件信息</div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">事件标题：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static">{{$info['title']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">事发地点：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static">
                                <a href="javascript:void(0)" style="color: #000;" onclick="mapMarker()">{{$info['address']}} <i class="fa fa-map-marker"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">事件类别：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['category_name']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">所属社区：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static">{{$info['community_names']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">事件描述：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static">{{$info['desc']}}</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">附件：</label>
                        <div class="col-sm-10">
                            @if ($info['annex'])
                                <div class="b5uploadfilebox" style="display: inline-flex">
                                    <div class="b5uploadlistbox" >
                                        @foreach($info['annex'] as $file)
                                            <div class="b5upload_li">
                                                <div class="b5upload_filetype "></div>
                                                <div class="b5upload_filename">
                                                    <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="row">
                        <div class="item-head-title">事件处置</div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">时间状态：</label>
                        <div class="col-sm-2">
                            <div class="form-control-static">{{$info['time_status_text']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">事件状态：</label>
                        <div class="col-sm-2">
                            <div class="form-control-static">{{$info['status_text']}}</div>
                        </div>
                        <label class="col-sm-2 control-label">处置类型：</label>
                        <div class="col-sm-2">
                            <div class="form-control-static">{{$info['disposal_type_text']}}</div>
                        </div>
                    </div>
                    @if ($info['disposal_type'] == \App\Extends\Services\Street\EventService::DISPOSAL_TYPE_ZCL)
                        <div class="form-group">
                            <label class="col-sm-2 control-label">处置意见：</label>
                            <div class="col-sm-10">
                                <div class="form-control-static">{{$info['disposal_note']}}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">附件：</label>
                            <div class="col-sm-10">
                                @if ($info['disposal_annex'])
                                    <div class="b5uploadfilebox" style="display: inline-flex">
                                        <div class="b5uploadlistbox" >
                                            @foreach($info['disposal_annex'] as $file)
                                                <div class="b5upload_li">
                                                    <div class="b5upload_filetype "></div>
                                                    <div class="b5upload_filename">
                                                        <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="form-group">
                            <div class="col-sm-12  table-striped" style="padding-top:7px;" >
                                <table class="table table-hover">
                                    <thead>
                                    <tr>
                                        <th>处理部门</th>
                                        <th>人员</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($eventStruct as $item)
                                        <tr>
                                            <td>{{$item['struct']['name'] ?? ''}}</td>
                                            <td>
                                                @foreach($item['node_users'] as $value)
                                                    <div>
                                                        {{$value['type']}}:
                                                        @foreach($value['users'] as $user)
                                                            <span>{{$user['username']}}
                                                                <a href="javascript:;" data-mobile="{{$user['mobile']}}" data-username="{{$user['username']}}" class="btn btn-bd btn-xs yuyin"><i class="fa fa-phone" title="语音" ></i></a>
                                                            </span>
                                                        @endforeach
                                                    </div>
                                                @endforeach
                                            </td>
                                            <td>{{$item['status_text']}}</td>
                                            <td>
                                                @if(!empty($item['operates']['show_dispose']) && hasPerm('street:eventstruct:disposedetail'))
                                                    <a href="javascript:void(0)" onclick="disposedetail({{$item['id']}})" class="btn btn-xs">处置详情</a>
                                                @endif
                                                @if(!empty($item['operates']['audit']) && hasPerm('street:eventstruct:disposeaudit'))
                                                    <a href="javascript:void(0)" onclick="disposeaudit({{$item['id']}})" class="btn btn-xs">审核</a>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                    <div id="btns">
                        @if(!empty($buttons['cancel']) && hasPerm('street:eventlist:cancel'))
                            <a href="javascript:void(0)" onclick="cancel()" class="mbig-btn">取消事件</a>
                        @endif
                        @if(!empty($buttons['reissue']) && hasPerm('street:eventlist:reissue'))
                            <a href="javascript:void(0)" onclick="reissue()" class="mbig-btn">重新发起</a>
                        @endif
                        @if(!empty($buttons['backassign']) && hasPerm('street:eventstruct:backassign'))
                            <a href="javascript:void(0)" onclick="backassign()" class="mbig-btn">回退</a>
                        @endif
                        @if(!empty($buttons['fgback']) && hasPerm('street:eventlist:fgback'))
                            <a href="javascript:void(0)" onclick="fgback()" class="mbig-btn">回退</a>
                        @endif
                        @if(!empty($buttons['ophandle']) && hasPerm('street:eventstruct:ophandle'))
                            <a href="javascript:void(0)" onclick="ophandle()" class="mbig-btn">处理</a>
                        @endif
                        @if(!empty($buttons['slhandle']) && hasPerm('street:eventstruct:slhandle'))
                            <a href="javascript:void(0)" onclick="slhandle()" class="mbig-btn">处理</a>
                        @endif
                        @if(!empty($buttons['fghandle']) && hasPerm('street:eventlist:fghandle'))
                            <a href="javascript:void(0)" onclick="fghandle()" class="mbig-btn">处理</a>
                        @endif
                        @if(!empty($buttons['zyhandle']) && hasPerm('street:eventlist:zyhandle'))
                            <a href="javascript:void(0)" onclick="zyhandle()" class="mbig-btn">处理</a>
                        @endif
                        @if(!empty($buttons['dispose']) && hasPerm('street:eventstruct:dispose'))
                            <a href="javascript:void(0)" onclick="dispose()" class="mbig-btn">处置</a>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-6 arrange" id="flow">
        <div class="item">
            <div style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-bottom: 10px;">
                <div style="display: flex">
                    <strong class="item-head-title">事件流转</strong>
                </div>
                <div class="tree-switch" id="switch-flow" style="float: right">
                    <div class="tree-switch-btn active" data-value="tree" type="button">树状</div>
                    <div class="tree-switch-btn" data-value="table" type="button">列表</div>
                </div>
            </div>
            <div class="flow-tree">
                <ul class="layui-timeline">
                    @foreach($flowList as $item)
                        <li class="layui-timeline-item">
                            <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                            <div class="layui-timeline-content layui-text">
                                <div class="flow-item">
                                    <p class="butuchu">{{$item['create_time']}}</p>
                                    <div style="display: inline-block;">
                                        <div class="butuchu">{{$item['struct_name']}}  {{$item['username']}}</div>
                                    </div>
                                    <div style="display: inline-block;position: relative">
                                        @foreach($item['operate'] as $operate)
                                            <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                                <div class="tooltip-inner" style=" background-color:{{$operate['color']}};">{{$operate['name']}}</div>
                                            </div>
                                        @endforeach
                                    </div>
                                    @foreach($item['content'] as $key=>$note)
                                        @if ($key == '附件')
                                            <div>
                                                <div class="b5uploadfilebox" style="display: inline-flex">
                                                    <div class="b5uploadlistbox" >
                                                        @foreach($note as $file)
                                                            <div class="b5upload_li">
                                                                <div class="b5upload_filetype "></div>
                                                                <div class="b5upload_filename">
                                                                    <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        @else
                                            <div>
                                                <div class="butuchu">{{$key}}：{{$note}}</div>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
            <div class="flow-table table-striped" style="display: none;">
                <table class="table table-hover ">
                    <thead style="background-color: #eff3f8;" >
                    <tr>
                        <th>操作时间</th>
                        <th>操作人</th>
                        <th style="min-width: 100px;">操作类型</th>
                        <th>操作信息</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($flowList as $item)
                        <tr>
                            <td>{{$item['create_time']}}</td>
                            <td>
                                <div class="butuchu">{{$item['struct_name']}}  {{$item['username']}}</div>
                            </td>
                            <td>
                                <div style="display: inline-block;position: relative">
                                    @foreach($item['operate'] as $operate)
                                        <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                            <div class="tooltip-inner" style=" background-color:{{$operate['color']}};">{{$operate['name']}}</div>
                                        </div>
                                    @endforeach
                                </div>
                            </td>
                            <td>
                                @foreach($item['content'] as $key=>$note)
                                    @if ($key == '附件')
                                        <div>
                                            <div class="b5uploadfilebox" style="display: inline-flex">
                                                <div class="b5uploadlistbox" >
                                                    @foreach($note as $file)
                                                        <div class="b5upload_li">
                                                            <div class="b5upload_filetype "></div>
                                                            <div class="b5upload_filename">
                                                                <a href="{{$file['url']}}" target="_blank">{{$file['originName']}}</a>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <div>
                                            <div class="butuchu">{{$key}}：{{$note}}</div>
                                        </div>
                                    @endif
                                @endforeach
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" data-backdrop="static" data-keyboard="false" style="padding-top: 170px;" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" >
    <div class="modal-dialog" style="width: 260px;background-color: rgb(208,208,208);" role="document">
        <div class="modal-content" style="height: 560px;background-color: #f1f1f1">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">语音通话</h4>
            </div>
            <div class="modal-body" style="height: 426px;text-align: center;line-height: 80px;font-size: 26px;">
                <p id="phone-username"></p>
                <p id="phone-mobile"></p>
                <p id="phone-status"></p>
            </div>
            <div class="modal-footer" style="text-align: center;">
                <button type="button" class="btn btn-warning on-hook" data-dismiss="modal">结束通话</button>
            </div>
        </div>
    </div>
</div>
@stop
@section('script')
    <script>

        $(function () {

            $(document).on("click",".b5uploadimgbox .b5uploadlistbox",function (){
                var id = $(this).attr("id");
                new Viewer(document.getElementById(id),{title:false});
            });
            $('#switch-flow').on('click', '.tree-switch-btn',function (){
                let value = $(this).data('value');
                $(this).addClass('active');
                if (value == 'tree')
                {
                    $('#switch-flow .tree-switch-btn[data-value=table]').removeClass('active');
                    $('.flow-table').hide();
                    $('#switch-flow .tree-switch-btn[data-value=summary]').removeClass('active');
                    $('.flow-summaryview').hide();
                    $('.flow-tree').show();
                }
                if (value == 'table')
                {
                    $('#switch-flow .tree-switch-btn[data-value=tree]').removeClass('active');
                    $('.flow-tree').hide();
                    $('#switch-flow .tree-switch-btn[data-value=summary]').removeClass('active');
                    $('.flow-summaryview').hide();
                    $('.flow-table').show();
                }
                if (value == 'summary')
                {
                    $('#switch-flow .tree-switch-btn[data-value=table]').removeClass('active');
                    $('.flow-table').hide();
                    $('#switch-flow .tree-switch-btn[data-value=tree]').removeClass('active');
                    $('.flow-tree').hide();
                    $('.flow-summaryview').show();
                }
                $("#tree-switch-btn-value").val(value);
            });

            @hasPerm("info:inforeport:assign")
            $('#btn-assign').click(function () {
                {{--$.modal.openFull("增派", '{{route('info.inforeport.assign')}}?id={{$info['id']}}');--}}
            });
            @endhasPerm

            $(document).on("click",".yuyin",function (){
                var mobile = $(this).data('mobile');
                var username = $(this).data('username');
                $.modal.confirm("您确定要拨打电话" + mobile + "("+username+")吗？", function() {
                    $("#phone-username").html(username);
                    $("#phone-mobile").html(mobile);
                    $("#exampleModal").modal("toggle");
                    websocket = new WebSocket("wss://jszhdd.cdqingyang.gov.cn/wss");
                    websocket.onopen = function () {
                        msg1 = {
                            "Action":"Login",
                            "GongHao":"18000",
                            "FenJi":"8002",
                            "PlatFormCode":"Default"};
                        websocket.send(JSON.stringify(msg1));
                        msg = {
                            "Action":"DialOut",
                            "GongHao":"18000",
                            "FenJi":"8002",
                            "PlatFormCode":"Default",
                            "Params":mobile};
                        websocket.send(JSON.stringify(msg));
                    }
                    websocket.onmessage = (event) => {
                        result = event.data.match(/\((.*?)\)/)
                        if (!result[1]){
                            $("#phone-status").html("通话失败");
                        }
                        resultJosn = JSON.parse(result[1]);
                        console.log(resultJosn)
                        if(resultJosn['Action'] == 'Login'){
                            $("#phone-status").html("登录CTI......");
                        }
                        if(resultJosn['Action'] == 'RegNumberState'){
                            $("#phone-status").html("坐席排队中......");
                        }
                        if(resultJosn['Action'] == 'DialOut'){
                            $("#phone-status").html("正在呼叫......");
                        }
                        if(resultJosn['Action'] == 'BeginTalking'){
                            $("#phone-status").html("通话中......");
                        }
                        if(resultJosn['Action'] == 'UserRingEnd'){
                            $("#phone-status").html("未接挂机");
                        }
                        if(resultJosn['Action'] == 'TalkingEnd'){
                            $("#phone-status").html("通话结束");
                        }
                    }
                    websocket.onerror = (error) => {
                        console.log('WebSocket 出错:', error);
                    };
                });
            });
            $(document).on("click",".danbing",function (){
                var mobile = $(this).data('mobile');
                $.modal.alert('正在单兵'+mobile+'，请稍后...')
            });

        });
        function refresh() {
            window.location.reload();
        }
        function cancel() {
            $.modal.confirm("确认取消该事件吗？", function() {
                $.operate.saveTabRefresh('{{route('street.eventlist.cancel')}}', {"id": {{$info['id']}} });
            });
        }
        function reissue() {
            $.modal.openFull("重新发起事件", '{{route('street.eventlist.reissue')}}?id={{$info['id']}}');
        }
        function backassign() {
            $.modal.open("回退", '{{route('street.eventstruct.backassign')}}?id={{$struct['id']??''}}', 400, 400);
        }
        function fgback() {
            $.modal.open("回退", '{{route('street.eventlist.fgback')}}?id={{$info['id']??''}}', 400, 400);
        }
        function ophandle() {
            $.modal.open("处理", '{{route('street.eventstruct.ophandle')}}?id={{$struct['id']??''}}');
        }
        function slhandle() {
            $.modal.open("处理", '{{route('street.eventstruct.slhandle')}}?id={{$struct['id']??''}}');
        }
        function fghandle() {
            $.modal.open("处理", '{{route('street.eventlist.fghandle')}}?id={{$info['id']??''}}');
        }
        function zyhandle() {
            $.modal.open("处理", '{{route('street.eventlist.zyhandle')}}?id={{$info['id']??''}}');
        }
        function dispose() {
            $.modal.open("处置", '{{route('street.eventstruct.dispose')}}?id={{$struct['id']??''}}');
        }
        function disposeaudit(id) {
            $.modal.open("审核", '{{route('street.eventstruct.disposeaudit')}}?id='+id);
        }
        function disposedetail(id) {
            $.modal.openOptions({
                title: '处置详情',
                url: '{{route('street.eventstruct.disposedetail')}}?id='+id,
                btn:['关闭'],
                yes:function(index, layero){
                    layer.close(index);
                }
            });
        }
        function mapMarker() {
            $.modal.openOptions({
                title: '事发地点',
                url: '{{route('admin.lnglat')}}?lnglat={{$info['lnglat']}}',
                btn:['关闭'],
                yes:function(index, layero){
                    layer.close(index);
                }
            });
        }
    </script>
@stop
