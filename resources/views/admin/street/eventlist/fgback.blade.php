@extends('admin.layout.form')

@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-1 control-label is-required">回退原因：</label>
        <div class="col-sm-10">
            <textarea rows="9" placeholder="请输入"  name="note"  class="form-control" required autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>

        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.eventstruct.backassign')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
