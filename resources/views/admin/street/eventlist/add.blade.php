@extends('admin.layout.form')
@include("widget.asset.select2")
@include('widget.asset.dragula')
@include('widget.asset.upload')
@section('css_common')
    <style>
        .item-title{
            font-size: 18px;
            padding-bottom: 15px;
        }
    </style>
@append
@section('content')
    <script>
        var annexIndex = 0;
    </script>
<form class="form-horizontal m" id="form-add">
    <div class="item-title">事件信息</div>
    <div class="form-group">
        <label class="col-sm-1 control-label is-required">事件标题：</label>
        <div class="col-sm-7">
            <input type="text" name="title" value="" class="form-control" required autocomplete="off" placeholder="请输入" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-1 control-label is-required">发生地址：</label>
        <div class="col-sm-7">
            <div class="input-group">
                <input type="text" name="address" value="" class="form-control" required autocomplete="off" placeholder="请输入" />
                <div class="input-group-addon"><i class="fa fa-map-marker"></i></div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-1 control-label">经纬度：</label>
        <div class="col-sm-3">
            <div class="input-group">
                <input type="text" name="lnglat" id="lnglat" value="" readonly class="form-control" autocomplete="off" placeholder="请选择" />
                <div class="input-group-addon"><i class="fa fa-map-marker"></i></div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-1 control-label is-required">事件类别：</label>
        <div class="col-sm-3">
            <select name="category_id" xm-select="category_id" xm-select-search xm-select-radio>
                <option value="">请选择</option>
                @foreach($categoryList as $item)
                    @if (!empty($item['children']))
                        @foreach($item['children'] as $value)
                            <option value="{{$value['id']}}">{{$item['name']}} - {{$value['name']}}</option>
                        @endforeach
                    @endif
                @endforeach
            </select>
        </div>
        <label class="col-sm-1 control-label">所属社区：</label>
        <div class="col-sm-3">
            <select name="cids" xm-select="cids" xm-select-search>
                <option value="">请选择</option>
                @foreach($community as $id=>$name)
                    <option value="{{$id}}">{{$name}}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-1 control-label is-required">事件描述：</label>
        <div class="col-sm-10">
            <textarea rows="3" placeholder="请输入"  name="desc"  class="form-control" required autocomplete="off"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-1 control-label">附件：</label>
        <div class="col-sm-8">
            <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                <button type="button" class="btn-b5upload btn btn-warning btn-sm" id="annex" data-exts=""  data-multi="9"  data-cat="work" data-inputname=""><i class="fa fa-upload"></i> 上传文件</button>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 最多上传9个文件</span>
                <div class="b5uploadlistbox annex_filelist" id="annex_filelist"></div>
            </div>
        </div>
    </div>
    <div class="item-title">事件处置</div>
    <div class="form-group">
        <label class="col-sm-1 control-label is-required">处置类型：</label>
        <div class="col-sm-10">
            <label class="radio-inline">
                <input type="radio" name="disposal_type" required value="1" checked> 下派
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-1 control-label is-required">处理部门：</label>
        <div class="col-sm-3">
            <select name="structs" xm-select="structs" xm-select-search>
                <option value="">请选择</option>
                @if(!empty($structs))
                    <optgroup label="科室">
                        @foreach($structs as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </optgroup>
                @endif
                @if (!empty($community))
                    <optgroup label="社区">
                        @foreach($community as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </optgroup>
                @endif
            </select>
        </div>
        <label class="col-sm-1 control-label is-required">办结时间：</label>
        <div class="col-sm-3">
            <div class="input-group">
                <input type="text" name="completion_time" value="" readonly class="form-control time-input" required data-type="datetime" data-btn="now|confirm" data-format="yyyy-MM-dd HH:mm" autocomplete="off" placeholder="请选择" />
                <div class="input-group-addon"><i class="fa fa-calendar"></i></div>
            </div>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            b5uploadfileinit('annex',uploadAfter);
            dragula([annex_filelist]);

            $('#lnglat').click(function () {
                let lnglat = $(this).val();
                $.modal.openOptions({
                    title: '经纬度选择',
                    url: '{{route('admin.lnglat')}}?lnglat='+lnglat,
                    callBack: function (index, layero) {
                        var iframeWin = window[layero.find('iframe')[0]['name']]; //得到iframe页的窗口对象，执行iframe页的方法：
                        $('#lnglat').val(iframeWin.getLngLat())
                        layer.close(index);
                    }
                });
            });
        });
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
        function uploadAfter(id, data) {
            annexIndex++;
            let ext = data.ext;
            let url = data.url;
            let filename = data.originName;
            if(!filename){
                filename = getFileName(url);
            }
            var classname = getExtClass(url);
            var html='<div class="b5upload_li">' +
                '           <input type="hidden" name="annex['+annexIndex+'][url]" value="'+url+'">' +
                '           <input type="hidden" name="annex['+annexIndex+'][originName]" value="'+filename+'">' +
                '           <input type="hidden" name="annex['+annexIndex+'][ext]" value="'+ext+'">' +
                '           <div class="b5upload_filetype '+classname+'"></div>' +
                '           <div class="b5upload_filename">'+filename+
                '        </div>' +
                '               <div class="b5upload_fileop">' +
                '                   <a href="javascript:;" onclick="b5uploadImgRemove(this)"><i class="fa fa-trash-o"></i>删除</a>' +
                '                  <a href="'+url+'" target="_blank"><i class="fa fa-hand-o-right"></i>查看</a>' +
                '               </div>' +
                '      </div>';
            b5uploadhtmlshow(id,html);
        }

    </script>
@stop
