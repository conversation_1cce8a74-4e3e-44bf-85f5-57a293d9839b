@extends('admin.layout.form')

@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">处理方式：</label>
        <div class="col-sm-8">
            <label class="radio-box">
                <input type="radio" name="type" required value="1" checked> 下派
            </label>
            @if ($fghandle == 2)
                <label class="radio-box">
                    <input type="radio" name="type" required value="2"> 上报
                </label>
            @endif
        </div>
    </div>
    <div id="assign">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">处理科室：</label>
            <div class="col-sm-8">
                <select name="struct_ids" xm-select="struct_ids" xm-select-search>
                    <option value="">请选择</option>
                    @foreach($structs as $item)
                        <option value="{{$item['id']}}">{{$item['name']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        @if ($fghandle == 2)
        <div class="form-group">
            <label class="col-sm-3 control-label">协同分管领导：</label>
            <div class="col-sm-8">
                <select name="cooperate_fg_ids" xm-select="cooperate_fg_ids" xm-select-search>
                    <option value="">请选择</option>
                    @foreach($fg_leaders as $item)
                        <option value="{{$item['id']}}" @if(in_array($item['id'], $structFgIds)) selected  disabled @endif>{{$item['username']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        @endif
    </div>

    @if ($fghandle == 2)
    <div class="form-group" style="display: none" id="report">
        <label class="col-sm-3 control-label is-required">上报人员：</label>
        <div class="col-sm-8">
            <select name="leader_ids" xm-select="leader_ids" xm-select-search>
                <option value="">请选择</option>
                @foreach($main_leaders as $leader)
                    <option value="{{$leader['id']}}">{{$leader['username']}} 主要领导</option>
                @endforeach
            </select>
        </div>
    </div>
    @endif
    <div class="form-group">
        <label class="col-sm-3 control-label">处理意见：</label>
        <div class="col-sm-8">
            <textarea rows="3" placeholder="请输入"  name="note"  class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            $('input[name=type]').on('ifChecked', function () {
                if ($(this).val() == 1) {
                    $('#report').hide();
                    $('#assign').show();
                } else {
                    $('#assign').hide();
                    $('#report').show();
                }
            })
        });
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.eventlist.fghandle')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
