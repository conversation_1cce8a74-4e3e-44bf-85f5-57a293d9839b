@extends('admin.layout.form')

@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">处理方式：</label>
        <div class="col-sm-8">
            <label class="radio-box">
                <input type="radio" name="assign" required value="1" checked> 下派
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">下派类型：</label>
        <div class="col-sm-8">
            <label class="radio-box">
                <input type="radio" name="type" required value="1" checked> 分管领导
            </label>
            <label class="radio-box">
                <input type="radio" name="type" required value="2"> 科室
            </label>
        </div>
    </div>
    <div id="assign">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">分管领导：</label>
            <div class="col-sm-8">
                <select name="fg_ids" xm-select="fg_ids" xm-select-search>
                    <option value="">请选择</option>
                    @foreach($fg_leaders as $item)
                        <option value="{{$item['id']}}">{{$item['username']}}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>

    <div class="form-group" style="display: none" id="report">
        <label class="col-sm-3 control-label is-required">处理科室：</label>
        <div class="col-sm-8">
            <select name="struct_ids" xm-select="struct_ids" xm-select-search>
                <option value="">请选择</option>
                @foreach($structs as $id=>$name)
                    <option value="{{$id}}">{{$name}}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">处理意见：</label>
        <div class="col-sm-8">
            <textarea rows="3" placeholder="请输入"  name="note"  class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $(function () {
            $('input[name=type]').on('ifChecked', function () {
                if ($(this).val() == 1) {
                    $('#report').hide();
                    $('#assign').show();
                } else {
                    $('#assign').hide();
                    $('#report').show();
                }
            })
        });
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.eventlist.zyhandle')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
