@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
<div class="col-sm-12 search-collapse">
    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 10px;">
        <li role="presentation" class="active">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(1)">处置中 <span id="status1">0</span></a>
        </li>

        <li role="presentation" class="">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(2)">已回退 <span id="status2">0</span></a>
        </li>

        <li role="presentation" class="">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(3)">已结案 <span id="status3">0</span></a>
        </li>
        <li role="presentation" class="">
            <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(4)">已取消 <span id="status4">0</span></a>
        </li>
    </ul>
    <form id="role-form">
        <input type="hidden" id="status" name="status" value="1">
        <div class="select-list">
            <ul>
                <li><input type="text" name="like[title]" value="" placeholder="标题" autocomplete="off"></li>
                <li class="select-time">
                    <input type="text" name="between[create_time][start]" id="startTime" placeholder="发起开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="between[create_time][end]" id="endTime" placeholder="发起结束时间" readonly>
                </li>
                <li>
                    <select name="cid" class="select2">
                        <option value="">所属社区</option>
                        @foreach($community as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                </li>
                <li>
                    <select name="time_status" class="select2">
                        <option value="">时间状态</option>
                        @foreach($timeStatusMap as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                </li>
                <li>
                    <select name="where[category_id]" class="select2">
                        <option value="">事件类别</option>
                        @foreach($categoryList as $item)
                            @if (!empty($item['children']))
                                @foreach($item['children'] as $value)
                                    <option value="{{$value['id']}}">{{$item['name']}} - {{$value['name']}}</option>
                                @endforeach
                            @endif
                        @endforeach
                    </select>
                </li>
                <li>
                    <select name="where[disposal_type]" class="select2">
                        <option value="">处置类型</option>
                        @foreach($disposalTypeMap as $id=>$name)
                            <option value="{{$id}}">{{$name}}</option>
                        @endforeach
                    </select>
                </li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
    @hasPerm("street:eventlist:add")
{{--    仅城运中心经办人可见--}}
    @if ($admin->is_city_centre_operator)
    <a class="btn btn-warning" onclick="$.operate.addFull()">新增</a>
    @endif
    @endhasPerm
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "事件",
                sortName:'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {field: 'title', title: '事件标题', formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }},
                    {field: 'user_name', title: '发起人', align: 'left'},
                    {field: 'create_time', title: '发起时间', align: 'left', sortable: true},
                    {field: 'community_names', title: '所属社区',formatter: function (value, row, index) {
                            return $.table.tooltip(value,25);
                        }},
                    {field: 'category_name', title: '事件类别', align: 'left'},
                    {field: 'disposal_type_text', title: '处置类型', align: 'left'},
                    {field: 'time_status_text', title: '时间状态', align: 'left'},
                    {field: 'status_text',title: '事件状态'},
                    {field: 'update_time', title: '更新时间', align: 'left', sortable: true,visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("street:eventlist:detail")
                            actions.push('<a class="btn  btn-xs" href="javascript:;" onclick="detail(\'' + row.id + '\')"> 详情</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ],
                responseHandler:function (res) {
                    if (res.code === 0) {
                        $.each(res.extend, function (i, item) {
                            $('#'+i).html(item);
                        });
                    }
                }
            };
            $.table.init(options);
        });
        function detail(id) {
            $.modal.openTab("事件详情", '{{route('street.eventlist.detail')}}?id='+id);
            storage.set('publicPath', '{{route('street.eventlist.detail')}}?id='+id);
        }
        function switch_status(status) {
            $('#status').val(status);
            $.table.search();
        }
    </script>
@stop

