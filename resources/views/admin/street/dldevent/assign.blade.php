@extends('admin.layout.form')

@section('content')

<form class="form-horizontal m" id="form-add">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div id="assign">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">派发网格员：</label>
            <div class="col-sm-8">
                <select name="disposal_user_id" xm-select="disposal_user_id" xm-select-search xm-select-radio>
                    @foreach($grids as $grid)
                        <optgroup label="{{$grid['name']}}">
                        @foreach($grid['user'] as $user)
                            <option value="{{$user['id']}}">{{$user['username']}}</option>
                        @endforeach
                        </optgroup>
                    @endforeach
                </select>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">备注：</label>
        <div class="col-sm-8">
            <textarea rows="3" placeholder="请输入"  name="note"  class="form-control" autocomplete="off"></textarea>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.dldevent.assign')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
