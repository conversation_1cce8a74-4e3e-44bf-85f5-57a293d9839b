@extends('admin.layout.form')
@section('content')
    <form class="form-horizontal m" id="form-add">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">状态：</label>
            <div class="col-sm-8">
                <label class="radio-box"><input type="radio" checked value="1" name="status">通过</label>
                <label class="radio-box"><input type="radio" value="2" name="status">不通过</label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea rows="3" placeholder="请输入"  name="note"  class="form-control" autocomplete="off"></textarea>
            </div>
        </div>
    </form>
@stop
@section('script')
    <script>
        $("#form-add").validate();
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabOpen('{{route('street.dldevent.verify')}}', $('#form-add').serialize());
            }
        }
    </script>
@stop
