@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.form')
@include("widget.asset.select2")
@section('content')
    <form class="form-horizontal m" id="form-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">小区名称：</label>
            <div class="col-sm-8">
                <input type="text" name="name" value="" class="form-control" placeholder="请输入小区名称" required
                       autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">小区地址：</label>
            <div class="col-sm-8">
                <input type="text" name="address" value="" class="form-control" placeholder="请输入小区地址" required
                       autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">经纬度：</label>
            <div class="col-sm-4">
                <div class="input-group">
                    <input type="text" name="lnglat" id="lnglat" value="" readonly class="form-control" required
                           autocomplete="off" placeholder="请选择"/>
                    <div class="input-group-addon"><i class="fa fa-map-marker"></i></div>
                </div>
            </div>
        </div>
        @if(C_ID == 0)
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">所属社区：</label>
                <div class="col-sm-3">
                    <select name="cid" class="select2" required>
                        <option value="">选择社区</option>
                        @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                            <option value="{{$type}}">{{$name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        @else
            <input type="hidden" name="cid" value="{{C_ID}}"/>
        @endif
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">小区状态：</label>
            <div class="col-sm-3">
                <select name="status" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(Functions::getDictDate("res_status") as $type=>$name)
                        <option value="{{$type}}">{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">产权年限：</label>
            <div class="col-sm-8">
                <select name="tenure[]" class="select2" data-width="100%" multiple="true" required>
                    @foreach(Functions::getDictDate("res_tenure") as $type=>$name)
                        <option value="{{$type}}">{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">房屋种类：</label>
            <div class="col-sm-3">
                <select name="category" class="select2" required>
                    <option value="">请选择</option>
                    @foreach(Functions::getDictDate("res_category") as $type=>$name)
                        <option value="{{$type}}">{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">小区标签：</label>
            <div class="col-sm-8">
                <select name="tags[]" class="select2" data-width="100%" multiple="true" required>
                    @foreach(Functions::getDictDate("res_tag") as $type=>$name)
                        <option value="{{$type}}">{{$name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">建筑面积：</label>
            <div class="col-sm-3">
                <input type="text" name="build_area" value="" class="form-control" placeholder="请输入建筑面积"
                       autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">占地面积：</label>
            <div class="col-sm-3">
                <input type="text" name="cover_area" value="" class="form-control" placeholder="请输入占地面积"
                       autocomplete="off"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">容积率：</label>
            <div class="col-sm-3">
                <input type="text" name="rate" value="" class="form-control" placeholder="请输入容积率"
                       autocomplete="off"/>
            </div>
        </div>
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }

        $('#lnglat').click(function () {
            let lnglat = $(this).val();
            $.modal.openOptions({
                title: '经纬度选择',
                url: '{{route('admin.lnglat')}}?lnglat=' + lnglat,
                callBack: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']]; //得到iframe页的窗口对象，执行iframe页的方法：
                    $('#lnglat').val(iframeWin.getLngLat())
                    layer.close(index);
                }
            });
        });
    </script>
@stop
