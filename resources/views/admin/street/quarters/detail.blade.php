@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.form')
@include("widget.asset.select2")
@section('content')
    <style>
        .bg-thead {
            background-color: #eff3f8;
        }

        .fixed-table-toolbar {
            display: none
        }
    </style>
    <h3>{{$info['name']}}小区详情</h3>
    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
        <ul class="layui-tab-title">
            <li class="layui-this">基本信息</li>
            <li>管理服务团队</li>
            @hasPerm("street:house:index")
            <li>房屋信息</li>
            @endhasPerm
        </ul>
        <div class="layui-tab-content" style="height: 480px;">
            <div class="layui-tab-item layui-show">
                <form class="form-horizontal m" id="form-edit">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">小区名称：</label>
                        <div class="col-sm-8">
                            {{$info['name']}}
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">小区地址：</label>
                        <div class="col-sm-8">
                            {{$info['address']}}
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">经纬度：</label>
                        <div class="col-sm-4">
                            <div class="input-group">
                                {{$info['longitude']}},{{$info['latitude']}}
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">其他信息：</label>
                        <div class="col-sm-8" style="margin-top: 7px;">
                            建筑面积：{{$info['build_area']}} 占地面积：{{$info['cover_area']}} 容积率：{{$info['rate']}}
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">所属社区：</label>
                        <div class="col-sm-3">
                            <select disabled class="select2" required>
                                <option value="">选择社区</option>
                                @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                                    <option value="{{$type}}"
                                            @if($type == $info['cid'])selected @endif>{{$name}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">小区状态：</label>
                        <div class="col-sm-3">
                            <select disabled class="select2" required>
                                <option value="">请选择</option>
                                @foreach(Functions::getDictDate("res_status") as $type=>$name)
                                    <option value="{{$type}}"
                                            @if($type == $info['status'])selected @endif>{{$name}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">产权年限：</label>
                        <div class="col-sm-3">
                            <select class="select2" multiple="true" required>
                                <option value="">请选择</option>
                                @foreach(Functions::getDictDate("res_tenure") as $type=>$name)
                                    <option value="{{$type}}"
                                            @if(in_array($type,explode(",",$info['tenure']))) selected @endif>{{$name}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">房屋种类：</label>
                        <div class="col-sm-3">
                            <select disabled class="select2" required>
                                <option value="">请选择</option>
                                @foreach(Functions::getDictDate("res_category") as $type=>$name)
                                    <option value="{{$type}}"
                                            @if($type == $info['category'])selected @endif>{{$name}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">小区标签：</label>
                        <div class="col-sm-8">
                            <select class="select2" multiple="true" required>
                                <option value="">请选择</option>
                                @foreach(Functions::getDictDate("res_tag") as $type=>$name)
                                    <option value="{{$type}}"
                                            @if(in_array($type,explode(",",$info['tags']))) selected @endif>{{$name}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="layui-tab-item">
                <div class="layui-tab layui-tab-card">
                    <ul class="layui-tab-title">
                        <li class="layui-this">一般网格({{$info['grid_user'] ? count($info['grid_user']) : 0}}人)</li>
                        <li>党支部({{$info['party_user'] ? count($info['party_user']) : 0}}人)</li>
                        <li>业(院)委会({{$info['yywh_user'] ? count($info['yywh_user']) : 0}}人)</li>
                        <li>物业公司({{$info['wygs_user'] ? count($info['wygs_user']) : 0}}人)</li>
                        <li>居民小组({{$info['juxz_user'] ? count($info['juxz_user']) : 0}}组)</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <table class="table table-hover" style="width: 98%;max-width: 98%;margin-bottom: 20px;margin-left: 15px;">
                                <thead class="bg-thead">
                                <tr>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">姓名</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">手机号码</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                @if(!empty($info['grid_user']))
                                @foreach($info['grid_user'] as $item)
                                <tr>
                                    <td style="text-align: left; ">{{$item['username']}}</td>
                                    <td style="text-align: left; ">{{$item['mobile']}}</td>
                                </tr>
                                @endforeach
                                @else
                                    <tr>
                                        <td colspan="2" style="text-align: center; ">暂无数据</td>
                                    </tr>
                                @endif
                                </tbody>
                            </table>
                        </div>
                        <div class="layui-tab-item">
                            <div style="padding-bottom: 20px">
                                <h4 style="margin-left: 10px;">党支部名称：@if($info['party_name'])
                                        {{$info['party_name']}}
                                    @else
                                        暂未设置
                                    @endif</h4>
                            </div>
                            <table class="table table-hover" style="width: 98%;max-width: 98%;margin-bottom: 20px;margin-left: 15px;">
                                <thead class="bg-thead">
                                <tr>
                                    <th style="text-align: left;">
                                        <div class="th-inner" style="font-weight: normal;">姓名</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                    <th style="text-align: left;">
                                        <div class="th-inner" style="font-weight: normal;">职务</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                    <th style="text-align: left;">
                                        <div class="th-inner" style="font-weight: normal;">手机号码</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                @if(!empty($info['party_user']))
                                @foreach($info['party_user'] as $item)
                                    <tr>
                                        <td style="text-align: left; ">{{$item['user_name']}}</td>
                                        <td style="text-align: left; ">{{$item['poster']}}</td>
                                        <td style="text-align: left; ">{{$item['user_phone']}}</td>
                                    </tr>
                                @endforeach
                                @else
                                    <tr>
                                        <td colspan="3" style="text-align: center; ">暂无数据</td>
                                    </tr>
                                @endif
                                </tbody>
                            </table>
                        </div>
                        <div class="layui-tab-item">
                            <table class="table table-hover" style="width: 98%;max-width: 98%;margin-bottom: 20px;margin-left: 15px;">
                                <thead class="bg-thead">
                                <tr>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">姓名</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">职务</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">手机号码</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                @if(!empty($info['yywh_user']))
                                @foreach($info['yywh_user'] as $item)
                                    <tr>
                                        <td style="text-align: left; ">{{$item['user_name']}}</td>
                                        <td style="text-align: left; ">{{$item['poster']}}</td>
                                        <td style="text-align: left; ">{{$item['user_phone']}}</td>
                                    </tr>
                                @endforeach
                                @else
                                    <tr>
                                        <td colspan="3" style="text-align: center; ">暂无数据</td>
                                    </tr>
                                @endif
                                </tbody>
                            </table>
                        </div>
                        <div class="layui-tab-item">
                            <div style="padding-bottom: 20px">
                                <h4 style="margin-left: 10px;">物业公司名称：@if($info['property_name'])
                                        {{$info['property_name']}}
                                    @else
                                        暂未设置
                                    @endif</h4>
                            </div>
                            <table class="table table-hover" style="width: 98%;max-width: 98%;margin-bottom: 20px;margin-left: 15px;">
                                <thead class="bg-thead">
                                <tr>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">姓名</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">职务</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                    <th style="text-align: left; ">
                                        <div class="th-inner" style="font-weight: normal;">手机号码</div>
                                        <div class="fht-cell"></div>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                @if(!empty($info['wygs_user']))
                                @foreach($info['wygs_user'] as $item)
                                    <tr>
                                        <td style="text-align: left; ">{{$item['user_name']}}</td>
                                        <td style="text-align: left; ">{{$item['poster']}}</td>
                                        <td style="text-align: left; ">{{$item['user_phone']}}</td>
                                    </tr>
                                @endforeach
                                @else
                                    <tr>
                                        <td colspan="3" style="text-align: center; ">暂无数据</td>
                                    </tr>
                                @endif
                                </tbody>
                            </table>
                        </div>
                        <div class="layui-tab-item">
                            @foreach($info['juxz_user'] as $group=>$item)
                                <div style="padding-bottom: 20px">
                                    <h4 style="margin-left: 10px;">{{$group}}</h4>
                                </div>
                                <table class="table table-hover" style="width: 98%;max-width: 98%;margin-bottom: 20px;margin-left: 15px;">
                                    <thead class="bg-thead">
                                    <tr>
                                        <th style="text-align: left; ">
                                            <div class="th-inner" style="font-weight: normal;">姓名</div>
                                            <div class="fht-cell"></div>
                                        </th>
                                        <th style="text-align: left; ">
                                            <div class="th-inner" style="font-weight: normal;">职务</div>
                                            <div class="fht-cell"></div>
                                        </th>
                                        <th style="text-align: left; ">
                                            <div class="th-inner" style="font-weight: normal;">手机号码</div>
                                            <div class="fht-cell"></div>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($item as $itemSec)
                                        <tr>
                                            <td style="text-align: left; ">{{$itemSec['user_name']}}</td>
                                            <td style="text-align: left; ">{{$itemSec['poster']}}</td>
                                            <td style="text-align: left; ">{{$itemSec['user_phone']}}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-tab-item">
                <div class="col-sm-12 search-collapse">
                    <form id="role-form">
                        <div class="select-list">
                            <ul>
                                <li id="department-li">
                                    <input type="hidden" name="house_sn" id="department" value="">
                                    <span class="select2 select2-container select2-container--bootstrap select2-container--below"
                                          id="department_dropdown" dir="ltr">
                                        <span class="selection">
                                            <span class="select2-selection select2-selection--single" role="combobox"
                                                  aria-haspopup="true" aria-expanded="false" tabindex="0"
                                                  aria-labelledby="select2-whereevent_status-jc-container">
                                                <span class="select2-selection__rendered" id="department_text"
                                                      role="textbox" aria-readonly="true" title="楼栋-单元-楼层">楼栋-单元-楼层</span>
                                                <span class="select2-selection__arrow" role="presentation">
                                                    <b role="presentation"></b>
                                                </span>
                                            </span>
                                        </span>
                                        <span class="dropdown-wrapper" aria-hidden="true"></span>
                                    </span>
                                <li>
                                <li>
                                    <select name="is_kong" class="select2">
                                        <option value="">是否空房</option>
                                        <option value="1">是</option>
                                        <option value="2">否</option>
                                    </select>
                                <li>
                                <li>
                                    <select name="where[use_type]" class="select2">
                                        <option value="">房屋用途</option>
                                        @foreach(Functions::getDictDate('res_use') as $type=>$name)
                                            <option value="{{$type}}">{{$name}}</option>
                                        @endforeach
                                    </select>
                                <li>
                                <li>
                                    <select name="where[type]" class="select2">
                                        <option value="">使用类型</option>
                                        @foreach(Functions::getDictDate('res_type') as $type=>$name)
                                            <option value="{{$type}}">{{$name}}</option>
                                        @endforeach
                                    </select>
                                <li>
                                <li>
                                    <a class="btn btn-warning btn-rounded btn-sm"
                                       onclick="$.table.search('role-form','bootstrap-table')"><i
                                                class="fa fa-search"></i> 搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                                class="fa fa-refresh"></i> 重置</a>
                                    @hasPerm("street:house:add")
                                    <a class="btn btn-warning btn-sm" onclick="$.operate.add()"><i
                                                class="fa fa-plus"></i> 新增</a>
                                    @endhasPerm
                                    @hasPerm("street:quarters:import")
                                    <x-import-template-item
                                            :extend="['url'=>route('street.quarters.import'),'class' => 'btn-sm','id'=>'quarters-upload','title'=>'批量导入小区','temp_url' => asset('template/小区模板.xlsx'), 'tips'=>'严格按照要求导入']"/>
                                    @endhasPerm
                                    @hasPerm("street:house:export")
                                    <a class="btn btn-warning single  btn-sm"
                                       onclick="$.table.exportExcel('role-form')"><i class="fa fa-download"
                                                                                     aria-hidden="true"></i> 导出</a>
                                    @endhasPerm
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
    <script>
        layui.use('element', function () {
            var $ = layui.jquery,
                element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
        });
        $(function () {
            var options = {
                modalName: "房屋",
                sortName: 'building',
                sortOrder: "asc",
                url: '{{route('street.house.index')}}?res_id={{$info['id']}}',
                createUrl: '{{route('street.house.add')}}?res_id={{$info['id']}}',
                updateUrl: '{{route('street.house.edit')}}?id=%id%',
                removeUrl: '{{route('street.house.drop')}}',
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', sortable: true, visible: false},
                    {
                        field: 'building', title: '楼栋', align: "left", formatter: function (value, row, index) {
                            return value + "栋";
                        }
                    },
                    {
                        field: 'unit', title: '单元', align: 'left', formatter: function (value, row, index) {
                            return value + "单元";
                        }
                    },
                    {
                        field: 'floor', title: '楼层', align: 'left', formatter: function (value, row, index) {
                            return value + "楼";
                        }
                    },
                    {field: 'sn', title: '房号', align: 'left'},
                    {field: 'use_type_text', title: '房屋用途', align: 'left'},
                    {field: 'type_text', title: '使用类型', align: 'left'},
                    {field: 'personnel_num', title: '人数', align: 'left'},
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true, visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function (value, row, index) {
                            var actions = [];
                        @hasPerm("street:house:edit")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')">编辑</a> ');
                        @endhasPerm
                        @hasPerm("street:house:drop")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                        @endhasPerm
                        @hasPerm("street:resident:index")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="detail(\'' + row.id + '\')">居民</a> ');
                        @endhasPerm
                        return actions.join('');
                        }
                    }
                ],
            };
            $.table.init(options);
        });
        layui.use(['dropdown'], function () {
            var dropdown = layui.dropdown
            dropdown.render({
                elem: '#department_dropdown'
                // ,trigger: 'hover'
                , clickParent: false
                , data: @json($info['house'])
                , click: function (obj) {
                    $('#department_text').html(obj.titles);
                    $('#department').val(obj.ids);
                }
                , ready: function (elemPanel, elem) {
                    // console.log(elemPanel); //得到组件面板的 DOM 对象
                    // console.log(elem); //得到基础参数 elem 所绑定的元素 DOM 对象
                }
            });
        });
        function detail(id) {
            $.modal.openTab("小区居民", '{{route('street.resident.index')}}?house_id=' + id, true);
        }

        function reset(){
            $('#department_text').html("楼栋-单元-楼层-房号");
            $('#department').val("");
            $("#department-li").html('');
            $.form.reset()
        }
    </script>
@stop
