@php
    use App\Extends\Helpers\Admin\LoginAuth;
    use App\Extends\Helpers\Functions;
 @endphp
@extends('admin.layout.layout')

@include('widget.asset.viewer')
@include('widget.asset.select2')
@section('content')
    <div class="col-sm-12 search-collapse">
        <form id="role-form">
            <div class="select-list">
                <ul>
                    @if(C_ID == 0)
                        <li>
                            <select name="where[cid]" class="select2">
                                <option value="">所有社区</option>
                                @foreach(LoginAuth::admin()->auth_communitys as $type=>$name)
                                    <option value="{{$type}}">{{$name}}</option>
                                @endforeach
                            </select>
                        <li>
                            @else
                                <input type="hidden" name="where[cid]" value="{{C_ID}}"/>
                    @endif
                    <li>
                        <select name="where[tenure]" class="select2">
                            <option value="">产权年限</option>
                            @foreach(Functions::getDictDate('res_tenure') as $type=>$name)
                                <option value="{{$type}}">{{$name}}</option>
                            @endforeach
                        </select>
                    <li>
                    <li>
                        <select name="where[category]" class="select2">
                            <option value="">房屋种类</option>
                            @foreach(Functions::getDictDate('res_category') as $type=>$name)
                                <option value="{{$type}}">{{$name}}</option>
                            @endforeach
                        </select>
                    <li>
                    <li>
                        <select name="like[tags]" class="select2">
                            <option value="">小区标签</option>
                            @foreach(Functions::getDictDate('res_tag') as $type=>$name)
                                <option value="{{$type}}">{{$name}}</option>
                            @endforeach
                        </select>
                    <li>
                    <li><input type="text" name="like[name]" value="" placeholder="小区名称"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("street:quarters:add")
        <a class="btn btn-warning btn-xs" onclick="$.operate.add()"><i class="fa fa-plus"></i> 新增</a>
        @endhasPerm
        @hasPerm("street:quarters:edit")
        <a class="btn btn-warning single disabled btn-xs" onclick="$.operate.edit('',this)"><i class="fa fa-edit"
                                                                                               aria-hidden="true"></i>
            编辑</a>
        @endhasPerm
        @hasPerm("street:quarters:import")
        <x-import-template-item
                :extend="['url'=>route('street.quarters.import'),'id'=>'quarters-upload','title'=>'批量导入小区','temp_url' => asset('template/小区模板.xlsx'), 'tips'=>'严格按照要求导入']"/>
        @endhasPerm
        @hasPerm("street:quarters:export")
        <a class="btn btn-warning  btn-sm" onclick="$.table.exportExcel()"><i class="fa fa-download"
                                                                              aria-hidden="true"></i> 导出</a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        $(function () {
            var options = {
                modalName: "小区",
                sortName: 'id',
                sortOrder: "desc",
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', sortable: true, visible: false},
                    {field: 'name_text', title: '社区', align: "left"},
                    {field: 'name', title: '小区', align: 'left'},
                    {field: 'address', title: '地址', align: 'left'},
                    {
                        field: 'longitude',
                        title: '经纬度(高德)',
                        align: 'left',
                        formatter: function (value, row, index) {
                            return value + "," + row.latitude;
                        }
                    },
                    {field: 'status_text', title: '小区状态', align: 'left'},
                    {field: 'tenure_text', title: '产权年限', align: 'left'},
                    {field: 'category_text', title: '房屋种类', align: 'left'},
                    {field: 'tags_text', title: '小区标签', align: 'left'},
                    {field: 'create_time', title: '创建时间', align: 'left', sortable: true, visible: false},
                    {field: 'update_time', title: '更新时间', align: 'left', visible: false},
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function (value, row, index) {
                            var actions = [];
                            @hasPerm("street:quarters:detail")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="detail(\'' + row.id + '\')">详情</a> ');
                            @endhasPerm
                            @hasPerm("street:quarters:edit")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.edit(\'' + row.id + '\')">编辑</a> ');
                            @endhasPerm
                            @hasPerm("street:quarters:drop")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.remove(\'' + row.id + '\')">删除</a> ');
                            @endhasPerm
                        return actions.join('');
                        }
                    }
                ],
            };
            $.table.init(options);
        });

        function detail(id) {
            $.modal.openTab("小区详情", '{{route('street.quarters.detail')}}?id=' + id, true);
        }
    </script>
@stop

