@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}


@section('content')
<form class="form-horizontal m" id="form-edit">
    <input type="hidden" name="id" value="{{$info['id']}}">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">修改人员类型：1=管理员,2=前台人员：</label>
        <div class="col-sm-8">
            <input type="text" name="user_type" value="{{$info['user_type']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">人员Id：</label>
        <div class="col-sm-8">
            <input type="number" name="user_id" value="{{$info['user_id']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">更新前值：</label>
        <div class="col-sm-8">
            <input type="text" name="old_value" value="{{$info['old_value']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">更新后值：</label>
        <div class="col-sm-8">
            <input type="text" name="new_value" value="{{$info['new_value']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">商铺ID：</label>
        <div class="col-sm-8">
            <input type="number" name="shop_id" value="{{$info['shop_id']}}" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">字段名：</label>
        <div class="col-sm-8">
            <input type="text" name="field_name" value="{{$info['field_name']}}" class="form-control" required autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-edit").validate();

        {{--$("#XX").val(["{{$info['xx']}}"]).trigger("change");--}}

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oesUrl, $('#form-edit').serialize());
            }
        }
    </script>
@stop
