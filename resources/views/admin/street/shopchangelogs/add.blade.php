@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
{{--@include("widget.asset.select2")--}}

@section('content')
<form class="form-horizontal m" id="form-add">
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">修改人员类型：1=管理员,2=前台人员：</label>
        <div class="col-sm-8">
            <input type="text" name="user_type" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">人员Id：</label>
        <div class="col-sm-8">
            <input type="number" name="user_id" value="" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">更新前值：</label>
        <div class="col-sm-8">
            <input type="text" name="old_value" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">更新后值：</label>
        <div class="col-sm-8">
            <input type="text" name="new_value" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">商铺ID：</label>
        <div class="col-sm-8">
            <input type="number" name="shop_id" value="" class="form-control number" required autocomplete="off"/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label is-required">字段名：</label>
        <div class="col-sm-8">
            <input type="text" name="field_name" value="" class="form-control" required autocomplete="off"/>
        </div>
    </div>
</form>
@stop

@section('script')
    <script>
        $("#form-add").validate();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(oasUrl, $('#form-add').serialize());
            }
        }
    </script>
@stop
