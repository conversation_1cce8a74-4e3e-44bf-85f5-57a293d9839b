@extends('admin.layout.form')
@section('css_common')
    <style>
        .form-group .col-xs-3{
            padding-right:0;
            width: auto;
        }
    </style>
@append
@section('content')
    <form class="form-horizontal m" id="form-add">
        <input type="hidden" name="id" value="{{$info['id']}}">
        <div class="form-group">
            <label class="col-xs-3 control-label is-required">审核结果：</label>
            <div class="col-xs-8">
                <label class="radio-box">
                    <input type="radio" name="status" required value="2" /> 通过
                </label>
                <label class="radio-box">
                    <input type="radio" name="status" required value="3" /> 驳回
                </label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label">审核意见：</label>
            <div class="col-xs-8">
                <textarea name="note" class="form-control" rows="3" placeholder="请输入"></textarea>
            </div>
        </div>
        @if ($signature)
        <div class="form-group">
            <label class="col-xs-3 control-label is-required">电子签名：</label>
            <div class="col-xs-8">
                <input type="hidden" name="signature" value="{{$signature}}">
                <img src="{{$signature}}" height="100px" />
            </div>
        </div>
        @endif
    </form>
@stop

@section('script')
    <script>
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save('{{route("street.{$app}.audit")}}', $('#form-add').serialize(),function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgReload('保存成功', modal_status.SUCCESS);
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    }  else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                });
            }
        }
    </script>
@stop
