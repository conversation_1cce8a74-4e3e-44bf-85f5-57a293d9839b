@extends('admin.layout.form')
{{--如果需要下拉选择组件,请开启下面这一行--}}
@include("widget.asset.select2")
@include('widget.asset.upload')
@section('css_common')
    <style>
        .row{
            margin: 0 0 10px 0;
        }
        .item-head-title{
            font-weight: 400;
            font-style: normal;
            font-size: 18px;
            color: #000000;
        }

        .b5uploadimgbox .b5uploadlistbox img{
            cursor: zoom-in;
        }
        .detail-box {
            display: flex; /* 启用Flexbox布局 */
            flex-wrap: wrap; /* 允许子元素换行 */
            /*background-color: #f7f8fa;*/
            /*padding: 20px 5px;*/
        }

        .detail-box .item {
            flex: 1; /* 自动填充剩余空间 */
            width: 100%;
            background: #fff;
            border-radius: 6px;
            margin-left: 5px;
            padding: 20px;
        }
        #flow {
            border-left: 1px solid #e5e5e5;
        }
        .btn-xs{
            font-size: 14px;
        }

        .col-sm-2.control-label {
            padding-right: 0;
            padding-left: 0;
        }
        .form-control-static {
            color: #000000
        }
        .form-group {
            margin-bottom: 0;
        }
        .tuchu {
            color: #000000;
            font-weight: bold;
        }
        .butuchu {
            color: #999999;
            font-size: 12px;
        }
        .btn-bd {
            background-color: #ffffff;
            color: #2256FF !important;
            border: 1px solid #2256FF;
            border-radius: 12px !important;
            width: 24px;
            height: 24px;
        }
        .btn-bd:hover {
            background-color: #2256FF;
            color: #ffffff !important;
        }
        .btn-bd i {
            padding: 0;
        }
        .tree-switch {
            padding: 4px 10px;
            background-color: #f2f3f8;
            display: flex;
        }
        .tree-switch>.tree-switch-btn {
            display: inline-block;
            padding: 4px 10px;
            cursor: pointer;
            margin: 2px;
        }
        .tree-switch>.tree-switch-btn.active {
            background-color: #fff;
            color: #2256FF;
        }
        .tree-switch>.tree-switch-btn:hover {
            background-color: #fff;
            color: #2256FF;
        }
        .flow-item {
            background-color:#f1f8fe ;
            margin: -6px;
            padding: 5px;
            border-radius: 4px;
        }
        .tooltip-inner {
            /*padding: 6px 11px;*/
            margin: 2px;
        }
        #base-info .col-sm-2 {
            width: auto;
        }
        .form-horizontal .row .form-group {
             margin-right: 0;
             margin-left: 0;
        }
        .mbig-btn {
            background-color: #fff;
            border: 1px solid #2256FF;
            color: #2256FF !important;
            padding: 8px 5px;
            border-radius: 7px;
            margin: 0 5px;
            display: inline-block;
            min-width: 96px;
            text-align: center;
        }
        .mbig-btn:hover{
            background-color: #2256FF;
            border-color: #2256FF;
            color: #FFFFFF !important;
        }
        #btns{
            margin-top: 20px;
        }
    </style>
@append
@section('content')
    <div class="row detail-box">
        <div class="col-sm-6 arrange" id="base-info">
            <div class="item">
                <form action="" class="form-horizontal" style="padding-bottom: 10px;">
                    <div>
                        <div class="row">
                            <div class="item-head-title">申请信息</div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">申请类型：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">
                                        {{$info['type_text']}}
                                        @if($info['type'] == \App\Extends\Services\Street\KaoqinApplyService::TYPE_QJ)
                                            - {{$info['leave_type_text']}}
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">开始时间：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{$info['start_date']}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">结束时间：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{$info['end_date']}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">事由：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static">{{$info['desc']}}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件：</label>
                                <div class="col-sm-10">
                                    @if ($info['annex'])
                                        <div class="b5uploadfilebox" style="display: inline-flex">
                                            <div class="b5uploadlistbox" >
                                                @foreach($info['annex'] as $file)
                                                    <div class="b5upload_li">
                                                        <div class="b5upload_filetype "></div>
                                                        <div class="b5upload_filename">
                                                            <a href="{{$file['url']}}">{{$file['originName']}}</a>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div id="btns">
                            @if(!empty($info['btns']['audit']) && hasPerm("street:{$app}:audit"))
                                <a href="javascript:void(0)" onclick="audit()" class="mbig-btn">审批</a>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="col-sm-6 arrange" id="flow">
            <div class="item">
                <div style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;margin-bottom: 10px;">
                    <div style="display: flex;align-items: center">
                        <strong class="item-head-title">审批流程</strong>
                    </div>
                    <div class="tree-switch" id="switch-flow" style="float: right">
                        <div class="tree-switch-btn active" data-value="tree" type="button">树状</div>
                        <div class="tree-switch-btn" data-value="table" type="button">列表</div>
                    </div>
                </div>
                <div class="flow-tree">
                    <ul class="layui-timeline">
                        @foreach($audit_list as $item)
                            <li class="layui-timeline-item">
                                <i class="fa fa-circle-o layui-icon layui-timeline-axis" style="font-size: 12px"></i>
                                <div class="layui-timeline-content layui-text">
                                    <div class="flow-item">
                                        <p class="tuchu">{{$item['audit_time']}}</p>
{{--                                        <p class="butuchu">{{$item['user']['depNames']}}</p>--}}
                                        <div style="display: inline-block;position: relative">
                                            {{$item['struct_name']}} - {{$item['username']}}
                                            <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                                <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                                            </div>
                                        </div>
                                        @if ($item['note'] != '')
                                            <p class="tuchu">{{$item['note']}}</p>
                                        @endif
                                        @if($item['signature'] != '') <p><img style="height: 50px" src="{{$item['signature']}}" alt=""></p> @endif
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="flow-table" style="display: none;">
                    <table class="table table-hover table-tr-b">
                        <thead style="background-color: #eff3f8;" >
                        <tr>
                            <th>操作时间</th>
                            <th>操作人</th>
                            <th style="min-width: 100px;">操作类型</th>
                            <th>操作信息</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($audit_list as $item)
                            <tr>
                                <td>{{$item['audit_time']}}</td>
                                <td>
                                    {{$item['struct_name']}} - {{$item['username']}}
                                </td>
                                <td>
                                    <div style="display: inline-block;position: relative">
                                        <div class="tooltip fade right in" role="tooltip" style="display: inline-block;position: relative"  >
                                            <div class="tooltip-inner" style=" background-color: #0076f6;">{{$item['status_text']}}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if ($item['note'] != '')
                                        <p>{{$item['note']}}</p>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
@stop

@section('script')
    <script>

    $(function () {
        $('#switch-flow').on('click', '.tree-switch-btn',function (){
            let value = $(this).data('value');
            let hideValue = value === 'tree' ? 'table' : 'tree';
            $(this).addClass('active');
            $('#switch-flow .tree-switch-btn[data-value='+hideValue+']').removeClass('active');
            $('.flow-'+value).show();
            $('.flow-'+hideValue).hide();
        });
    });
    function audit() {
        $.modal.open("审核", '{{route("street.{$app}.audit")}}?id={{$info['id']}}', '600', '400');
    }
    </script>
@stop

