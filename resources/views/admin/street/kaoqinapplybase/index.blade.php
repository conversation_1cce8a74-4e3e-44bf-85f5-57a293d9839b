@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
<div class="col-sm-12 search-collapse">
    <form id="role-form">
        <div class="select-list">
            <ul>
                <li class="select-time">
                    <label>申请时间： </label>
                    <input type="text" name="between[create_time][start]" id="startTime" placeholder="开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="between[create_time][end]" id="endTime" placeholder="结束时间" readonly>
                </li>
                <li class="select-time">
                    <label>{{$kaoqinTypeText}}时间： </label>
                    <input type="text" name="start_date" id="startTime" placeholder="开始时间" readonly>
                    <span>-</span>
                    <input type="text" name="end_date" id="endTime" placeholder="结束时间" readonly>
                </li>
                <li>
                    <select name="where[status]" class="select2">
                        <option value="">所有状态</option>
                        @foreach($statusList as $key=>$value)
                            <option value="{{$key}}">{{$value}}</option>
                        @endforeach
                    </select>
                <li>
                <li>
                    <select name="where[user_type]" class="select2">
                        <option value="">人员类型</option>
                        @foreach($userTypeList as $key=>$value)
                            <option value="{{$key}}">{{$value}}</option>
                        @endforeach
                    </select>
                <li>
                @if($kaoqinType == \App\Extends\Services\Street\KaoqinApplyService::TYPE_QJ)
                    <li>
                        <select name="where[leave_type]" class="select2">
                            <option value="">请假类型</option>
                            @foreach($leaveTypeList as $key=>$value)
                                <option value="{{$value['id']}}">{{$value['name']}}</option>
                            @endforeach
                        </select>
                    <li>
                @endif
                <li>
                    <input type="text"  name="like[username]"  placeholder="姓名" value="" autocomplete="off">
                </li>
                <li>
                    <input type="text"  name="like[mobile]"  placeholder="手机号" value="" autocomplete="off">
                </li>
                <li>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                </li>
            </ul>
        </div>
    </form>
</div>
<div class="btn-group-sm" id="toolbar" role="group">
    <a class="btn btn-warning btn-sm" id="table-export" data-name="{{$kaoqinTypeText}}记录" data-type="excel"><i class="fa fa-download"></i>
        导出
    </a>
</div>
<div class="col-sm-12 select-table table-striped">
    <table id="bootstrap-table"></table>
</div>
@stop

@section('script')
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/bootstrap-table-export.js')}}"></script>
    <script src="{{asset('static/plugins/bootstrap-table/extensions/export/tableExport.js')}}"></script>
    <script>
        $(function () {
            var options = {
                modalName: "{{$kaoqinTypeText}}",
                sortName:'id',
                sortOrder: "desc",
                showExport: true,
                exportTypes: ['csv', 'excel'],
                columns: [
                    {checkbox: true},
                    {field: 'id', title: 'ID', align: 'left', sortable: true,visible: false},
                    {field: 'user_type_text', title: '人员类型', align: 'left'},
                    {field: 'username', title: '申请人', align: 'left'},
                    {field: 'create_time', title: '申请时间', align: 'left'},
                    @if ($kaoqinType == \App\Extends\Services\Street\KaoqinApplyService::TYPE_QJ)
                        {field: 'leave_type_text', title: '请假类型', align: 'left'},
                    @endif
                    {field: 'start_date', title: '开始时间', align: 'left'},
                    {field: 'end_date', title: '结束时间', align: 'left'},
                    {
                        field: 'status',
                        title: '状态',
                        formatter: function (value, row, index) {
                            if (value == 1) {
                                return "待审批"
                            } else if (value == 2) {
                                return "同意"
                            } else if (value == 3) {
                                return "拒绝"
                            } else if (value == 4) {
                                return "撤销"
                            }
                        }
                    },
                    {
                        title: '操作',
                        align: 'left',
                        formatter: function(value, row, index) {
                            var actions = [];
                        @hasPerm("street:{$app}:detail")
                            actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.detail(\'' + row.id + '\')"> 详情</a> ');
                        @endhasPerm
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
        });
    </script>
@stop

