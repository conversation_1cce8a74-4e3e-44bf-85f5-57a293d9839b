<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>地址查询</title>
    <meta name="renderer" content="webkit">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="{{asset('static/plugins/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/fontawesome/css/font-awesome.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/style.css')}}" rel="stylesheet"/>
    <script type="text/javascript" src="{{asset('static/plugins/supermap-iclient/dist/leaflet/include-leaflet.js')}}"></script>
</head>
<body>
<div class="col-sm-12" style="display: flex;padding: 20px;text-align: center;justify-content: center">
    <input type="text" value="" placeholder="输入地址搜索" class="form-control" autocomplete="off" style="width: 300px;display: inline-block;margin-right: 10px;" id="keywords" >
    <button type="button" class="btn btn-success btn-sm" style="display: inline-block;margin-right: 10px;" onclick="search()">搜索</button>
    <input type="text" value="{{$input['lnglat'] ?? ''}}" placeholder="经纬度获取结果" readonly class="form-control" style="width: 300px;display: inline-block;margin-right: 10px;" id="lnglat" >

</div>
<div id="map" style="width: 100%;height: 85vh"></div>
</body>
<script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
<script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
<script src="{{asset('static/plugins/bootstrap/js/bootstrap.min.js')}}"></script>
<script src="{{asset('static/plugins/three-js/three.min.js')}}"></script>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script src="{{asset('static/admin/js/iframe-ui.js')}}"></script>
<script src="{{asset('static/admin/js/common.js')}}"></script>

<script>
    var map,
        mapUrl = "{{env('YZT_STREET_URL')}}",
         url = "{{env('YZT_DISTRICT_URL')}}";
    // 用于保存当前 marker 的变量
    var currentMarker = null;
    @php($lnglat = isset($input['lnglat']) && $input['lnglat'] != '' ? explode(',',$input['lnglat']) : [])
    @php($center = explode(',', env('YZT_CENTER_POINT','104.0638303756715,30.659859309043362')))
    L.supermap.initMap(url,{
        mapOptions: {
            target: 'map',
            center: L.latLng({{$center[1]}}, {{$center[0]}}),
            zoom: 12
        }
    }).then((res) => {
        map = res.map

        var overlayLayer = new L.supermap.TiledMapLayer(mapUrl, {
            transparent: true,  // 叠加层透明，显示底图
            opacity: 0.4        // 可选：调整不透明度
        });
        overlayLayer.addTo(map);
        map.on('click', function (e) {
            // 移除之前的 marker（如果存在）
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            $('#lnglat').val(e.latlng.lng+','+e.latlng.lat);
            currentMarker = L.marker([e.latlng.lat, e.latlng.lng]).addTo(map);
        });
        @if($lnglat)
        currentMarker = L.marker([{{$lnglat[1]}}, {{$lnglat[0]}}]).addTo(map);
        map.setView(L.latLng({{$lnglat[1]}}, {{$lnglat[0]}}));
        @endif
    });
    function search() {
        var keywords = $('#keywords').val();
        if (keywords === '') {
            $.modal.msgError('请输入关键字');
        }
        $.ajax({
            url: "{{route('admin.searchAddress')}}",
            type: 'POST',
            data: {
                address: keywords
            },
            success: function (res) {
            }
        });
    }
</script>

</html>