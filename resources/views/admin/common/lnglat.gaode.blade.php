<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{$system_name}}</title>
    <meta name="renderer" content="webkit">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="{{asset('static/plugins/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/plugins/fontawesome/css/font-awesome.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('static/admin/css/style.css')}}" rel="stylesheet"/>
    <style type="text/css">
        #container {
            width: 100%;
            height: 86vh;
        }
    </style>
</head>
<body>
<div class="col-sm-12" style="display: flex;padding: 20px;text-align: center;justify-content: center">
    <input type="text" value="" placeholder="输入地址搜索" class="form-control" style="width: 300px;display: inline-block;margin-right: 10px;" id="search" >
    <button type="button" class="btn btn-success btn-sm" style="display: inline-block;margin-right: 10px;" onclick="search()">搜索</button>
    <input type="text" value="{{$input['lnglat'] ?? ''}}" placeholder="经纬度获取结果" readonly class="form-control" style="width: 200px;display: inline-block;margin-right: 10px;" id="lnglat" >

</div>
<div id="container"></div>

</body>
<script type="text/javascript">
    window._AMapSecurityConfig = {
        securityJsCode: "{{env('GAODE_SECRET')}}",
    };
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key={{env("GAODE_KEY")}}"></script>
<script src="{{asset('static/plugins/jquery/jquery.min.js')}}"></script>
<script src="{{asset('static/plugins/jquery/jquery-migrate.min.js')}}"></script>
<script src="{{asset('static/plugins/bootstrap/js/bootstrap.min.js')}}"></script>
<script src="{{asset('static/plugins/three-js/three.min.js')}}"></script>
<script src="{{asset('static/plugins/layui/layui.js')}}"></script>
<script src="{{asset('static/admin/js/iframe-ui.js')}}"></script>
<script src="{{asset('static/admin/js/common.js')}}"></script>

<script type="text/javascript">
    var placeSearch;
    var marker;

    var map = new AMap.Map("container", {
        zoom: 13,
        resizeEnable: true,
        mapStyle: "amap://styles/normal",
        center: [104.062415,30.674583], //地图中心点
    });
    $(function () {
        @if (isset($input['lnglat']) && $input['lnglat'] != '')
            @php($lnglat = explode(',',$input['lnglat']))
            resetMark({{$lnglat[0]}}, {{$lnglat[1]}});
        @endif
    });
    map.on('click', function (e) {
        // console.log(e);
        resetMark(e.lnglat.getLng(), e.lnglat.getLat(), false);
    });
    // map.on('mousemove', function (e) {
    //     console.log(e);
    // });

    AMap.plugin(["AMap.PlaceSearch"], function() {
        //构造地点查询类
        placeSearch = new AMap.PlaceSearch({
            pageSize: 1, // 单页显示结果条数
            pageIndex: 1, // 页码
            city: "028", // 兴趣点城市
            citylimit: true,  //是否强制限制在设置的城市内搜索
            autoFitView: false // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
        });


    });
    function search() {
        var keyword = $('#search').val();
        if (keyword === '') {
            $.modal.msgError('请输入地址');
            return false;
        }
        //关键字查询
        placeSearch.search(keyword, function (status, result) {
            if (status !== 'complete' || result.info !== 'OK') {
                $.modal.msgError('查询失败');
                return false;
            }
            var location = result.poiList.pois[0].location;
            if (!location) {
                $.modal.msgError('查询失败');
            }
            resetMark(location.lng, location.lat, );
        });
    }
    function clearMarker() {
        if (marker) {
            map.remove(marker);
        }
    }
    function resetMark(lng, lat, center = true) {
        clearMarker();
        position = new AMap.LngLat(lng, lat);
        center && map.setCenter(position);
        $('#lnglat').val(lng+', '+lat);
        marker = new AMap.Marker({
            position: position,
            // offset: new AMap.Pixel(-13, -30)
        });
        map.add(marker);
    }
    function getLngLat() {
        return $('#lnglat').val();
    }
</script>
</html>