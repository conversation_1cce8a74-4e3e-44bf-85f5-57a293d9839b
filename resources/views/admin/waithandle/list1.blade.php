@foreach($list as $item)
    <div class="ibox" onclick="$.modal.openCloseFull('详情','{{$item['admin_url']}}')" style="position: relative;">
        <div class="ibox-title">
            <span class="label label-warning pull-right">{{$item['group_type_text']}}</span>
            <h5>{{$item['title']}}</h5>
        </div>
        <div class="ibox-content">
            @if ($item['group_type'] == 2 && in_array($item['group_type_status'], [2, 3]))
                <div>{{$item['content']['content']}}</div>
                <div>社区：{{$item['content']['community_name']}}</div>
            @else
                @if ($item['group_type'] == 3 && $item['group_type_status'] == 2)
                <div style="position: absolute;top: 15px;right: 20px">
                    <img style="width: 80px;" src="{{asset('static/images/dxg.png')}}">
                </div>
                @endif
                <div>
                    <label class="btn btn-success btn-outline btn-xs mr10">{{$item['content']['level_text'] ?? ''}}</label>
                    <label class="btn btn-success btn-outline btn-xs mr10">{{$item['content']['category_text'] ?? ''}}</label>
                </div>
                <div>完成时间：{{$item['content']['completion_date'] ?? ''}}</div>
            @endif

            <div class="row m-t-sm">
                <div class="col-sm-6">
                    <img src="{{$item['user_info']['avatar'] ?? ''}}" class="user-image">{{$item['user_info']['username'] ?? ''}}
                </div>
                <div class="col-sm-6 text-right">
                    {{$item['create_time']}}
                </div>
            </div>
        </div>
    </div>
@endforeach