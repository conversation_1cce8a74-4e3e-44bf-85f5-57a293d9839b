@foreach($list as $item)
    @if ($item['group'] == \App\Models\System\AdminWaitHandle::GROUP_SJ)
    <div class="ibox" onclick="$.modal.openCloseFull('详情','{{$item['admin_url']}}')">
        <div class="ibox-title">
            <span class="label label-warning pull-right">{{$item['group_type_text']}}</span>
            <div>
                <label class="btn btn-success btn-outline btn-xs mr10">{{$item['content']['disposal_type'] ?? ''}}</label>
                @if($item['content']['time_status'] != 1)
                <label class="btn btn-success btn-outline btn-xs mr10">{{$item['content']['time_status_text'] ?? ''}}</label>
                @endif
                <label>{{$item['content']['category_text'] ?? ''}}</label>
            </div>
        </div>
        <div class="ibox-content">
            <div>{{$item['content']['title']}}</div>
            @if ($item['group_type'] == 3)
                <div>处置说明：{{$item['content']['dispose_note']}}</div>
                <div>处置人：{{$item['content']['dispose_user']}}</div>
            @else
                @if ($item['group_type'] == 1 && $item['group_type_status'] == 1)
                    <div style="position: absolute;top: 20px;right: 20px">
                        <img style="width: 80px;" src="{{asset('static/images/bh.png')}}">
                    </div>
                @endif

                <div>所属社区：{{$item['content']['communities'] ?? ''}}</div>
                <div>{{$item['content']['content'] ?? ''}}</div>
            @endif

            <div class="row m-t-sm">
                <div class="col-sm-6">
                    <img src="{{$item['user_info']['avatar'] ?? ''}}" class="user-image">{{$item['user_info']['username'] ?? ''}}
                </div>
                <div class="col-sm-6 text-right">
                    {{$item['create_time']}}
                </div>
            </div>
        </div>
    </div>
    @elseif($item['group'] == \App\Models\System\AdminWaitHandle::GROUP_DLD)
        <div class="ibox" onclick="$.modal.openCloseFull('详情','{{$item['admin_url']}}')">
            <div class="ibox-title">
                <span class="label label-warning pull-right">{{$item['group_type_text']}}</span>
                <div>
                    <label class="btn btn-success btn-outline btn-xs mr10">{{$item['content']['disposal_type_text'] ?? ''}}</label>
                    <label>{{$item['content']['category_name'] ?? ''}}</label>
                </div>
            </div>
            <div class="ibox-content">
                <div>{{$item['content']['title']}}</div>
                <div>所属社区：{{$item['content']['category_name'] ?? ''}}</div>
                <div>{{$item['content']['content'] ?? ''}}</div>
                <div class="row m-t-sm">
                    <div class="col-sm-6">
                        <img src="{{$item['user_info']['avatar'] ?? ''}}" class="user-image">{{$item['user_info']['username'] ?? ''}}
                    </div>
                    <div class="col-sm-6 text-right">
                        {{$item['create_time']}}
                    </div>
                </div>
            </div>
        </div>
    @elseif($item['group'] == \App\Models\System\AdminWaitHandle::GROUP_SZCG)
        <div class="ibox" onclick="$.modal.openCloseFull('详情','{{$item['admin_url']}}')">
            <div class="ibox-title">
                <span class="label label-warning pull-right">{{$item['group_type_text']}}</span>
                <h5>{{$item['title']}}</h5>
            </div>
            <div class="ibox-content">
                <div>问题来源：{{$item['content']['event_src_name'] ?? ''}}</div>
                <div class="deadline_second" data-value="{{$item['content']['deadline_second'] ?? 0}}">剩余时间：<span>{{\App\Extends\Helpers\Functions::formatTime($item['content']['deadline_second'] ?? 0)}}</span></div>
                <div>问题描述：{{$item['content']['event_desc'] ?? ''}}</div>
            </div>
        </div>
    @endif
@endforeach