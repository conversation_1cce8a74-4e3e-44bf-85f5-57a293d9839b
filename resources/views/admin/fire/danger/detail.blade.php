@extends('admin.layout.form')
@section('content')
    <form class="form-horizontal m">
        <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">隐患信息</h3>
        <div class="form-group">
            <label class="col-sm-2 control-label">隐患点位：</label>
            <div class="col-sm-3" style="padding-top: 8px;">
                {{$info['risk']['name']}}
            </div>
            <label class="col-sm-2 control-label">所属社区：</label>
            <div class="col-sm-4" style="padding-top: 8px;">
                {{$info['c_name']}}
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">所属行业：</label>
            <div class="col-sm-3" style="padding-top: 8px;">
                {{$info['industry_name']}}
            </div>
            <label class="col-sm-2 control-label">风险等级：</label>
            <div class="col-sm-4" style="padding-top: 8px;">
                {{$info['level_name']}}
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">负责人：</label>
            <div class="col-sm-3" style="padding-top: 8px;">
                {{$info['risk']['responsible']}} {{$info['risk']['responsible_phone']}}
            </div>
            <label class="col-sm-2 control-label">安全负责人：</label>
            <div class="col-sm-4" style="padding-top: 8px;">
                {{$info['risk']['safe_responsible']}} {{$info['risk']['safe_responsible_phone']}}
            </div>
        </div>
        @if($info['patroldetail']['is_danger'] == 1)
            <div class="form-group">
                <label class="col-sm-2 control-label">隐患级别：</label>
                <div class="col-sm-3" style="padding-top: 8px;">
                    @if($info['level'] == 1) 一般隐患 @else 重大隐患 @endif
                </div>
                <label class="col-sm-2 control-label">隐患类型：</label>
                <div class="col-sm-4" style="padding-top: 8px;">
                    {{$info['danger_type_text']}}
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">隐患描述：</label>
                <div class="col-sm-9" style="padding-top: 8px;">
                    @if($info['patroldetail']['check_cate'] == 1) {{$info['patroldetail']['check_note']}} @endif
                    @if($info['patroldetail']['check_cate'] == 2) {{$info['check_info']}} @endif
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">隐患照片：</label>
                <div class="col-sm-9" style="padding-top: 8px;">
                    @if($info['patroldetail']['check_images'])
                        @foreach(explode(",",$info['patroldetail']['check_images']) as $img)
                            <a href="{{$img}}" target="_blank"> <img src="{{$img}}" height="60px"></a>
                        @endforeach
                    @endif
                </div>
            </div>
        @endif
        @if($info['patroldetail']['is_danger'] == 0)
            <div class="form-group">
                <label class="col-sm-2 control-label">检查情况：</label>
                <div class="col-sm-9" style="padding-top: 8px;">
                    {{$info['patroldetail']['check_note']}}
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">检查照片：</label>
                <div class="col-sm-9" style="padding-top: 8px;">
                    @if($info['patroldetail']['check_images'])
                        @foreach(explode(",",$info['patroldetail']['check_images']) as $img)
                            <a href="{{$img}}" target="_blank"> <img src="{{$img}}" height="60px"></a>
                        @endforeach
                    @endif
                </div>
            </div>
        @endif
        <h3 style="border-left: 5px solid #2256ff;padding-left: 10px;">整改信息</h3>
        <div class="form-group">
            <label class="col-sm-2 control-label">整改类型：</label>
            <div class="col-sm-3" style="padding-top: 8px;">
                @if($info['revise_type'] == 1) 现场整改 @else 限期整改 @endif
            </div>
            @if($info['revise_type'] == 2)
            <label class="col-sm-2 control-label">整改期限：</label>
            <div class="col-sm-4" style="padding-top: 8px;">
                {{$info['revise_date']}}
            </div>
            @endif
        </div>
        @if($info['revise_op_date'])
        <div class="form-group">
            <label class="col-sm-2 control-label">整改时间：</label>
            <div class="col-sm-3" style="padding-top: 8px;">
                {{$info['revise_op_date']}}
            </div>
        </div>
        @endif
        @if($info['revise_fund'])
            <div class="form-group">
                <label class="col-sm-2 control-label">整改资金：</label>
                <div class="col-sm-3" style="padding-top: 8px;">
                    {{$info['revise_fund']}}
                </div>
            </div>
        @endif
        @if($info['revise_note'])
        <div class="form-group">
            <label class="col-sm-2 control-label">整改描述：</label>
            <div class="col-sm-9" style="padding-top: 8px;">
                {{$info['revise_note']}}
            </div>
        </div>
        @endif
        @if($info['revise_images'])
        <div class="form-group">
            <label class="col-sm-2 control-label">整改照片：</label>
            <div class="col-sm-9" style="padding-top: 8px;">
                @if($info['revise_images'] ?? "")
                    @foreach(explode(",",$info['revise_images']) as $img)
                        <a href="{{$img}}" target="_blank"> <img src="{{$img}}" height="60px"></a>
                    @endforeach
                @endif
            </div>
        </div>
        @endif
    </form>
@stop