@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <div class="col-sm-12 search-collapse">
        <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 10px;">
            <li role="presentation" class="active">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(1)">全部
                    <span id="status1">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(2)">未整改
                    <span id="status2">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(3)">即将逾期
                    <span id="status3">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(4)">逾期未整改
                    <span id="status4">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(5)">已整改
                    <span id="status5">0</span></a>
            </li>
        </ul>
        <form id="role-form">
            <input type="hidden" id="status" name="status" value="1">
            <div class="select-list">
                <ul>
                    <li><input type="text" style="width: 320px" name="create_time" id="create_time" value="" readonly placeholder="上报时间"></li>
                    <li><input type="text" name="username" value="" placeholder="上报人" autocomplete="off"></li>
                    <li>
                        <select name="cid" id="cid" class="select2">
                            <option value="">所属社区</option>
                            @foreach(\App\Extends\Helpers\Admin\LoginAuth::admin()->auth_communitys as $id=>$name)
                                <option value="{{$id}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li>
                        <select name="where[level]" class="select2">
                            <option value="">隐患级别</option>
                            <option value="1" >一般隐患</option>
                            <option value="2" >重大隐患</option>
                        </select>
                    <li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="btn-group-sm" id="toolbar" role="group">
        @hasPerm("{$group}:{$app}:export")
        <a href="javascript:void(0)" class="btn btn-warning" onclick="$.operate.exportExcel()">
            <i class="fa fa-download"></i> 导出
        </a>
        @endhasPerm
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#create_time'
                ,type: 'datetime'
                , range: "~"
            });
        });
        $(function () {
            var options = {
                modalName: "隐患记录",
                sortName: 'create_time',
                sortOrder: "desc",
                detailUrl:'{{route('fire.danger.detail')}}?id=%id%',
                columns: [
                    {field: 'adduser.username', title: '上报人', align: 'left'},
                    {field: 'create_time', title: '上报时间', align: 'left', sortable: true},
                    {field: 'community.name', title: '所属社区', align: 'center'},
                    {field: 'risk.name', title: '隐患点位', align: 'center'},
                    {field: 'level', title: '隐患级别', align: 'center',formatter: function (value, row, index){
                            if (value == 1) {
                                return "一般隐患"
                            } else {
                                return "重大隐患"
                            }
                        }},
                    {field: 'check_info', title: '隐患描述', align: 'center', formatter: function (value, row, index) {
                        if (row.patroldetail.check_cate == 1) {
                            return $.table.tooltip(row.patroldetail.check_note, 20);
                        }
                            return $.table.tooltip(value, 20);
                        }},
                    {field: 'risk.responsible', title: '整改责任人', align: 'center', formatter: function (value, row, index) {
                            return value+" "+row.risk.responsible_phone;
                        }},
                    {field: 'is_revise', title: '整改状态', align: 'center',formatter: function (value, row, index){
                            if (value == 1) {
                                return "已整改"
                            } else {
                                return "未整改"
                            }
                    }},
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            @hasPerm("fire:danger:detail")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="$.operate.detail(\'' + row.id + '\')"> 详情</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ],
                responseHandler: function (res) {
                    if (res.code === 0) {
                        $.each(res.extend, function (i, item) {
                            $('#' + i).html(item);
                        });
                    }
                }
            };
            $.table.init(options);
        });
        function switch_status(status) {
            $('#status').val(status);
            $.table.search();
        }
    </script>
@stop

