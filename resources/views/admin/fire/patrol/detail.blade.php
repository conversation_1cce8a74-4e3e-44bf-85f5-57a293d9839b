@php use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.form')
@section('content')
    <style>
        .ul_list{
            border: 1px solid #eae2e2;height: 36px;line-height: 36px;padding-left: 20px;margin-bottom: 5px;
        }
        .ul_list a{
            float: right;padding-right: 20px
        }
    </style>
    <div class="row">
        <div class="col-md-6" style="border-right: 1px solid #eae2e2">
            <h3>安全巡查详情</h3>
            <form class="form-horizontal m">
                <div class="form-group">
                    <label class="col-sm-3 control-label">风险场所：</label>
                    <div class="col-sm-3" style="padding-top: 8px;">
                        {{$info['risk']['name']}}
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">场所地址：</label>
                    <div class="col-sm-9" style="padding-top: 8px;">
                        {{$info['risk']['address']}}
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">巡查人员：</label>
                    <div class="col-sm-9" style="padding-top: 8px;">
                        {{$info['main_user_name']}}(主办人)、{{$info['patrol_user_name']}}
                    </div>
                </div>
                @if($info['cate2list'])
                <div class="form-group">
                    <label class="col-sm-3 control-label">行业基础检查：</label>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-9">
                        <ul>
                            @foreach($info['cate2list'] as $key=>$cate2)
                            <li class="ul_list">{{$key+1}}、{{Functions::splicString($cate2['check_content'],32)}}<a onclick="patroldetail({{$cate2['id']}},2)">@if($cate2['is_danger'])有隐患 >@else无隐患 >@endif</a></li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endif
                @if($info['cate1list'])
                <div class="form-group">
                    <label class="col-sm-3 control-label">自定义检查：</label>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-9">
                        <ul>
                            @foreach($info['cate1list'] as $key=>$cate1)
                                <li class="ul_list" style="background-color: #f7f7f7;">{{Functions::splicString($cate1['check_note'],32)}}<a onclick="patroldetail({{$cate1['id']}},1)">@if($cate1['is_danger'])有隐患 >@else无隐患 >@endif</a></li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endif
                @if($info['sign_images'])
                    <div class="form-group">
                        <label class="col-sm-3 control-label">场所负责人签字：</label>
                        <div class="col-sm-9" style="padding-top: 8px;">
                            <a href="{{$info['sign_images']}}" target="_blank"> <img src="{{$info['sign_images']}}" height="30px"></a>
                        </div>
                    </div>
                @endif
            </form>
        </div>
        <div class="col-md-6" id="patrol-detail">

        </div>
    </div>
@stop

@section('script')
    <script>
        function patroldetail(id,type){
            if (type == 1){
                $('#patrol-detail').load('{{route('fire.patrol.ajaxcate1')}}?id='+id);
            }
            if (type == 2){
                $('#patrol-detail').load('{{route('fire.patrol.ajaxcate2')}}?id='+id);
            }
        }
    </script>
@stop



