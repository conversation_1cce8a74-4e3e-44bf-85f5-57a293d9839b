@php use App\Extends\Helpers\Admin\LoginAuth;use App\Extends\Helpers\Functions; @endphp
@extends('admin.layout.layout')
@include('widget.asset.select2')
@section('content')
    <style>
        .fixed-table-toolbar {
            display: none
        }
    </style>
    <div class="col-sm-12 search-collapse">
        <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 10px;">
            <li role="presentation" class="active">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(1)">全部
                    <span id="status1">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(3)">有隐患
                    <span id="status3">0</span></a>
            </li>
            <li role="presentation" class="">
                <a role="tab" data-toggle="tab" aria-controls="aa" aria-expanded="true" onclick="switch_status(2)">无隐患
                    <span id="status2">0</span></a>
            </li>
        </ul>
        <form id="role-form">
            <input type="hidden" id="status" name="status" value="1">
            <div class="select-list">
                <ul>
                    <li><input type="text" name="create_time" style="width: 320px" id="month" readonly
                               value="{{$input['month'] ?? ''}}" placeholder="选择巡查时间"></li>
                    <li>
                        <select name="cid" class="select2">
                            <option value="">所属社区</option>
                            @foreach(LoginAuth::admin()->auth_communitys as $id=>$name)
                                <option value="{{$id}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li>
                        <select name="risk_type" class="select2">
                            <option value="">场所类型</option>
                            @foreach($riskTypes as $type)
                                <option value="{{$type['code']}}">{{$type['name']}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li>
                        <select name="level" class="select2">
                            <option value="">风险等级</option>
                            @foreach(Functions::getDictDate("risk_risk_level") as $id=>$name)
                                <option value="{{$id}}">{{$name}}</option>
                            @endforeach
                        </select>
                    </li>
                    <li><input type="text" name="keywords" placeholder="搜索" autocomplete="off"></li>
                    <li>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        {{--@hasPerm("grid:watchman:export")
                        <a class="btn btn-warning btn-sm" onclick="$.table.exportExcel()"><i class="fa fa-download"></i>
                            导出</a>
                        @endhasPerm--}}
                    </li>
                </ul>
            </div>
        </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
@stop

@section('script')
    <script>
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            //年月选择器
            laydate.render({
                elem: '#month'
                , type: 'datetime'
                , range: "~"
            });
        });
        $(function () {
            var options = {
                modalName: "安全巡查",
                sortName: 'create_time',
                sortOrder: "desc",
                columns: [
                    {field: 'patrol_user_name', title: '巡查人', align: 'left'},
                    {field: 'create_time', title: '巡查时间', align: 'left', sortable: true},
                    {field: 'risk.name', title: '巡查点位', align: 'left', sortable: true},
                    {field: 'c_name', title: '所属社区', align: 'left'},
                    {field: 'risk_type', title: '场所类型', align: 'left'},
                    {field: 'level_name', title: '风险等级', align: 'left'},
                    {
                        field: 'is_danger', title: '有无隐患', align: 'left', formatter: function (value, row, index) {
                            if (value == 1) {
                                return "有隐患"
                            } else {
                                return "无隐患"
                            }
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            @hasPerm("fire:patrol:detail")
                                actions.push('<a class="btn btn-xs" href="javascript:;" onclick="clickdetail('+row.id+')"> 详情</a> ');
                            @endhasPerm
                            return actions.join('');
                        }
                    }
                ],
                responseHandler: function (res) {
                    if (res.code === 0) {
                        $.each(res.extend, function (i, item) {
                            $('#' + i).html(item);
                        });
                    }
                }
            };
            $.table.init(options);
        });
        function clickdetail(id){
            $.modal.openTab("安全巡查详情",'{{route('fire.patrol.detail')}}?id=' + id);
        }
        function switch_status(status) {
            $('#status').val(status);
            $.table.search();
        }
    </script>
@stop

