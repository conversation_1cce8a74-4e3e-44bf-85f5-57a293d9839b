@if($info)
    <h3>行业基础检查</h3>
    <form class="form-horizontal m">
        <div class="form-group">
            <div class="col-sm-11" style="padding-top: 8px;">
                {{$info['check_content']}}
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">检查条款内容：</label>
            <div class="col-sm-9" style="padding-top: 8px;">
                {{$info['check_clause']}}
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">检查照片：</label>
            <div class="col-sm-9" style="padding-top: 8px;">
                @if($info['check_images'])
                    @foreach(explode(",",$info['check_images']) as $img)
                        <a href="{{$img}}" target="_blank"> <img src="{{$img}}" height="60px"></a>
                    @endforeach
                @endif
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">备注：</label>
            <div class="col-sm-9" style="padding-top: 8px;">
                {{$info['check_note']}}
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">是否存在隐患：</label>
            <div class="col-sm-3" style="padding-top: 8px;">
                @if($info['is_danger'] == 1) 有隐患 @else 无隐患 @endif
            </div>
        </div>
        @if($info['is_danger'] == 1)
            <div class="form-group">
                <label class="col-sm-2 control-label">隐患级别：</label>
                <div class="col-sm-3" style="padding-top: 8px;">
                    @if($danger['level'] == 1) 一般隐患 @else 重大隐患 @endif
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">隐患描述：</label>
                <div class="col-sm-9" style="padding-top: 8px;">
                    {{$danger['check_info']}}
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">整改类型：</label>
                <div class="col-sm-3" style="padding-top: 8px;">
                    @if($danger['revise_type'] == 1) 现场整改 @else 限期整改 @endif
                </div>
                @if($danger['revise_type'] == 2)
                    <label class="col-sm-2 control-label">整改期限：</label>
                    <div class="col-sm-4" style="padding-top: 8px;">
                        {{$danger['revise_date']}}
                    </div>
                @endif
            </div>
            @if($danger['revise_op_date'])
                <div class="form-group">
                    <label class="col-sm-2 control-label">整改时间：</label>
                    <div class="col-sm-3" style="padding-top: 8px;">
                        {{$danger['revise_op_date']}}
                    </div>
                </div>
            @endif
            @if($danger['revise_fund'])
                <div class="form-group">
                    <label class="col-sm-2 control-label">整改资金：</label>
                    <div class="col-sm-3" style="padding-top: 8px;">
                        {{$danger['revise_fund']}}
                    </div>
                </div>
            @endif
            @if($danger['revise_note'])
                <div class="form-group">
                    <label class="col-sm-2 control-label">整改描述：</label>
                    <div class="col-sm-9" style="padding-top: 8px;">
                        {{$danger['revise_note']}}
                    </div>
                </div>
            @endif
            @if($danger['revise_images'])
                <div class="form-group">
                    <label class="col-sm-2 control-label">整改照片：</label>
                    <div class="col-sm-9" style="padding-top: 8px;">
                        @if($danger['revise_images'] ?? "")
                            @foreach(explode(",",$danger['revise_images']) as $img)
                                <a href="{{$img}}" target="_blank"> <img src="{{$img}}" height="60px"></a>
                            @endforeach
                        @endif
                    </div>
                </div>
            @endif
        @endif
    </form>
@else
    信息不存在
@endif