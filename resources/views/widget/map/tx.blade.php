@php
    $widget_data['address_name'] = $widget_data['address_name']??'address';
    $widget_data['address_value'] = $widget_data['address_value']??'';
    $widget_data['ln_name'] = $widget_data['ln_name']??'ln';
    $widget_data['ln_value'] = $widget_data['ln_value']??'';
    $widget_data['required'] = $widget_data['required']??0;
    $widget_data['disable'] = $widget_data['disable']??0;
    $p = '';
    if(!empty($widget_data['ln_value']) && $widget_data['ln_value'] != ',') {
        $p = explode(',', $widget_data['ln_value']);
    }
@endphp
@section('css_common')
<style>
    .anchorBL{display:none;}
</style>
<script charset="utf-8" src="https://map.qq.com/api/gljs?v=1.6.5.exp&libraries=service&key=HPPBZ-2PKK3-M6H3A-RB6SA-SR3VS-LWF6J"></script>
@append
<div class="form-group mb0">
    <label class="col-sm-3 control-label @if($widget_data['required']) is-required @endif ">{{$widget_data['title']}}：</label>
    <div class="col-sm-8 mb15">
        <div style="float: left;">
            <div style="float: left;">
                @if (!$widget_data['disable'])
                <input type="text" name="{{$widget_data['address_name']}}" value="{{$widget_data['address_value']}}" id="where" class="form-control" placeholder="请输入地址" @if($widget_data['required']) required @endif  autocomplete="off">
                @else
                    <div class="form-control-static">{{$widget_data['address_value']}}</div>
                @endif
            </div>
            @if (!$widget_data['disable'])
                <div style="float: left;margin-left: 5px;">
                    <input type="button" value="地图上找" class="form-control " onClick="searchByKeyword();" style="background-color: #1ab394;border-color: #1ab394;color: #FFFFFF;" />
                </div>
            @endif
        </div>
        <div style="float: right ;">
            @if (!$widget_data['disable'])
            <input type="text"
                   name="{{$widget_data['ln_name']}}"
                   value="{{$widget_data['ln_value']}}"
                   id="lonlat" class="form-control" placeholder="点击地图自动获取经纬度"  readonly autocomplete="off" @if($widget_data['required']) required @endif>
            @else
                <div class="form-control-static">{{$widget_data['ln_value']}}</div>
            @endif
        </div>
        <div style="width:470px;height:340px;border:1px solid gray;margin-top: 40px;" id="container"></div>
    </div>
</div>

@section('script_before')


    <script type="text/javascript">
        @if ($p)
        var center = new TMap.LatLng({{$p[1]}}, {{$p[0]}});//设置中心点坐标
        @else
        var center = new TMap.LatLng(30.665638, 103.990197);//设置中心点坐标
        @endif
        var search = new TMap.service.Search({ pageSize: 10 });
        // 初始化地图
        var map = new TMap.Map('container', {
            // zoom: 17, // 设置地图缩放
            center: center, // 设置地图中心点坐标，
            pitch: 0, // 俯仰度
            rotation: 0, // 旋转角度
        });
        var infoWindowList = Array(10);
        // MultiMarker文档地址：https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocMarker
        var marker = null;
        var markers = new TMap.MultiMarker({
            map: map,
            geometries: [],
        });
        @if ($p)
            createMarker({{$p[0]}},{{$p[1]}})
        @endif
        // var url3;
        // TMap.event.addListener(map, "click", function (e) {
        //     document.getElementById("lonlat").value = e.latLng.getLng().toFixed(6) + "," + e.latLng.getLat().toFixed(6);
        //     url3 = encodeURI("https://apis.map.qq.com/ws/geocoder/v1/?location=" + e.latLng.getLat() + "," + e.latLng.getLng() + "&key=K76BZ-W3O2Q-RFL5S-GXOPR-3ARIT-6KFE5&output=jsonp&&callback=?");
        //     $.getJSON(url3, function (result) {
        //         if(result.result!=undefined){
        //             document.getElementById("where").value = result.result.address;
        //         }else{
        //             document.getElementById("where").value = "";
        //         }
        //
        //     })
        // });
        map.on("click", (evt) => {
            var lng = evt.latLng.getLng().toFixed(6);
            var lat = evt.latLng.getLat().toFixed(6);
            document.getElementById("lonlat").value = lng + "," + lat;
            removeMarker();
            createMarker( lng,  lat);

            // url3 = encodeURI("https://apis.map.qq.com/ws/geocoder/v1/?location=" + evt.latLng.getLat() + "," + evt.latLng.getLng() + "&key=K76BZ-W3O2Q-RFL5S-GXOPR-3ARIT-6KFE5&output=jsonp&&callback=?");
            // $.getJSON(url3, function (result) {
            //     if(result.result!=undefined){
            //         document.getElementById("where").value = result.result.address;
            //     }else{
            //         document.getElementById("where").value = "";
            //     }
            // })
        });
        function removeMarker() {
            if (marker) {
                marker.setMap(null);
                marker = null;
            }
        }
        function createMarker(lng, lat) {
            if (!marker) {
                marker = new TMap.MultiMarker({
                    id: 'marker-layer',
                    map: map,
                    styles: {
                        "marker": new TMap.MarkerStyle({
                            "width": 25,
                            "height": 35,
                            "anchor": { x: 16, y: 32 },
                        })
                    },
                    geometries: [{
                        "id": 'marker',
                        "position": new TMap.LatLng(lat, lng)
                    }]
                });
            }
        }
        function searchByKeyword() {
            infoWindowList.forEach((infoWindow) => {
                infoWindow.close();
            });
            infoWindowList.length = 0;
            markers.setGeometries([]);
            // 在地图显示范围内以给定的关键字搜索地点
            search.searchRegion({
                    keyword: document.getElementById('where').value,
                    cityName:'成都市',
                    bounds: map.getBounds(),
                }).then((result) => {
                    result.data.forEach((item, index) => {
                        if (index === 0) {
                            var lng = item.location.lng.toFixed(6);
                            var lat = item.location.lat.toFixed(6);
                            document.getElementById("lonlat").value = lng + "," + lat;
                            map.setCenter(new TMap.LatLng(lat, lng));
                        }
                        var geometries = markers.getGeometries();
                        var infoWindow = new TMap.InfoWindow({
                            map: map,
                            position: item.location,
                            content: `<h3>${item.title}</h3><p>地址：${item.address}</p><p>电话：${item.tel}</p>`,
                            offset: { x: 0, y: -50 },
                        }); // 新增信息窗体显示地标的名称与地址、电话等信息
                        infoWindow.close();
                        infoWindowList[index] = infoWindow;
                        geometries.push({
                            id: String(index), // 点标注数据数组
                            position: item.location,
                            src:'',
                        });
                        markers.updateGeometries(geometries); // 绘制地点标注
                        markers.on('click', (e) => {
                            infoWindowList[Number(e.geometry.id)].open();
                        }); // 点击标注显示信息窗体
                    });
                });
        }
    </script>
@append
