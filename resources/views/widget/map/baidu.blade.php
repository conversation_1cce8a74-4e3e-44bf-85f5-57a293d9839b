@php
    $widget_data['address_name'] = $widget_data['address_name']??'address';
    $widget_data['address_value'] = $widget_data['address_value']??'';
    $widget_data['ln_name'] = $widget_data['ln_name']??'ln';
    $widget_data['ln_value'] = $widget_data['ln_value']??'';
    $widget_data['required'] = $widget_data['required']??0;
    $widget_data['disable'] = $widget_data['disable']??0;
@endphp
<style>
    .anchorBL{display:none;}
</style>
<div class="form-group mb0">
    <label class="col-sm-3 control-label @if($widget_data['required']) is-required @endif ">{{$widget_data['title']}}：</label>
    <div class="col-sm-8 mb15">
        <div style="float: left;">
            <div style="float: left;">
                @if (!$widget_data['disable'])
                <input type="text" name="{{$widget_data['address_name']}}" value="{{$widget_data['address_value']}}" id="where" class="form-control" placeholder="请输入地址" @if($widget_data['required']) required @endif  autocomplete="off">
                @else
                    <div class="form-control-static">{{$widget_data['address_value']}}</div>
                @endif
            </div>
            @if (!$widget_data['disable'])
                <div style="float: left;margin-left: 5px;">
                    <input type="button" value="地图上找" class="form-control " onClick="sear(document.getElementById('where').value);" style="background-color: #1ab394;border-color: #1ab394;color: #FFFFFF;" />
                </div>
            @endif
        </div>
        <div style="float: right ;">
            @if (!$widget_data['disable'])
            <input type="text"
                   name="{{$widget_data['ln_name']}}"
                   value="{{$widget_data['ln_value']}}"
                   id="lonlat" class="form-control" placeholder="点击地图自动获取经纬度"  readonly autocomplete="off" @if($widget_data['required']) required @endif>
            @else
                <div class="form-control-static">{{$widget_data['ln_value']}}</div>
            @endif
        </div>
        <div style="width:470px;height:340px;border:1px solid gray;margin-top: 40px;" id="container"></div>
    </div>
</div>

@section('script_before')
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=gSKdo5XExiu0pydZfDhZLXc5cF4TtNWK&type=webgl"></script>

    <script>
        var marker;
        var point;
        var map = new BMapGL.Map("container");//在指定的容器内创建地图实例
        map.setDefaultCursor("crosshair");//设置地图默认的鼠标指针样式
        map.enableScrollWheelZoom();//启用滚轮放大缩小，默认禁用。
        @if(!empty($widget_data['ln_value']) && $widget_data['ln_value'] != ',')
                @php($p = explode(',', $widget_data['ln_value']))
            point = new BMapGL.Point({{$p[0]}}, {{$p[1]}});
            marker = new BMapGL.Marker(point);        // 创建标注
            map.addOverlay(marker);
        @endif
        @if(isset($p))
            map.centerAndZoom(new BMapGL.Point({{$p[0]}},{{$p[1]}}), 13);
        @else
            map.centerAndZoom(new BMapGL.Point(104.072312,30.663517), 13);
        @endif

        map.addControl(new BMapGL.NavigationControl());
        @if (!$widget_data['disable'])
            // 将标注添加到地图中
            map.addEventListener("click", function(e){//地图单击事件
                map.removeOverlay(marker);
                var lng = e.latlng.lng.toFixed(6);
                var lat = e.latlng.lat.toFixed(6);
                document.getElementById("lonlat").value =  lng + "," + lat;
                point = new BMapGL.Point(lng, lat);
                marker = new BMapGL.Marker(point);        // 创建标注
                map.addOverlay(marker);
            });
        function iploac(result){//根据IP设置地图中心
            var cityName = '天府广场';//result.name;
            map.setCenter(cityName);
        }
        // var myCity = new BMapGL.LocalCity();
        // myCity.get(iploac);
        function sear(result){//地图搜索
            var local = new BMapGL.LocalSearch(map, {
                renderOptions:{map: map}
            });
            local.search(result);
        }
        @endif

    </script>
@append
