@foreach($data as $item)
    @if($item['item_type'] == 'input')
        <div class="form-group draggable ui-draggable dropped" data-type="input" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <input type="text" name="" class="form-control" placeholder="请输入文本">
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'textarea')
        <div class="form-group draggable ui-draggable dropped" data-type="textarea" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <textarea type="text" name="" class="form-control" placeholder="请输入文本"></textarea>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'file')
        <div class="form-group draggable ui-draggable dropped" data-type="file" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                    <button type="button" class="btn-b5upload btn btn-warning btn-sm" id="files_upload" data-exts="3" data-multi="3" data-cat="demo" data-inputname="">
                        <i class="fa fa-upload"></i> 上传文件
                    </button>
                    <div class="uploadimg_link" style="width: 150px">
                        <input type="text" min="1" placeholder="最多上传数量" style="width: 150px" class="form-control file_num" value="{{$item['extend']['file_num'] ?? ''}}">
                    </div>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'location')
        <div class="form-group draggable ui-draggable dropped" data-type="location" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="定位选择">
                    <span class="input-group-addon"><i class="fa fa-location-arrow"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'radio')
        <div class="form-group draggable ui-draggable dropped" data-type="radio" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                @foreach($item['extend'] as $extend)
                    <label class="radio-box" style="line-height: normal;" data-index="{{$extend['index']}}"><input type="radio" disabled><span class="editable">{{$extend['title']}}</span><span class="remove-label-box" title="删除该项"> X</span></label>
                @endforeach
                <span class="add-radio-box" title="增加选项"> +</span></div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'checkbox')
        <div class="form-group draggable ui-draggable dropped" data-type="checkbox" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                @foreach($item['extend'] as $extend)
                    <label class="check-box" style="line-height: normal;" data-index="{{$extend['index']}}"><input type="checkbox" disabled><span class="editable">{{$extend['title']}}</span><span class="remove-label-box" title="删除该项"> X</span></label>
                @endforeach
                <span class="add-checkbox-box" title="增加选项"> +</span></div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label></div>
    @elseif($item['item_type'] == 'date')
        <div class="form-group draggable ui-draggable dropped" data-type="date" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control time-input" placeholder="日期选择 2023-06-21" lay-key="1">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'date_range')
        <div class="form-group draggable ui-draggable dropped" data-type="date_range" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control date-range-input" placeholder="日期范围选择 2023-06-21~2023-06-25" lay-key="2">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'datetime')
        <div class="form-group draggable ui-draggable dropped" data-type="datetime" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control datetime-input" placeholder="时间选择 2023-06-21 22:10:00" lay-key="3">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'datetime_range')
        <div class="form-group draggable ui-draggable dropped" data-type="datetime_range" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control datetime-range-input" placeholder="时间范围选择 2023-06-21 20:11:59~2023-06-25 23:20:22" lay-key="4">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['item_type'] == 'select_restaurant')
        <div class="form-group draggable ui-draggable dropped" data-type="select_restaurant" data-index="{{$item['item_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['required']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['item_title']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="form-control-static">选择餐饮店</div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @endif
@endforeach