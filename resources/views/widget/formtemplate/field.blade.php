@foreach($data as $item)
    @if($item['field_type'] == '1')
        <div class="form-group draggable ui-draggable dropped" data-type="1" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <input type="text" name="" class="form-control" placeholder="请输入文本">
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '2')
        <div class="form-group draggable ui-draggable dropped" data-type="2" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <textarea type="text" name="" class="form-control" placeholder="请输入文本"></textarea>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '13')
        <div class="form-group draggable ui-draggable dropped" data-type="13" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                    <button type="button" class="btn-b5upload btn btn-primary btn-sm" id="files_upload" data-exts="3" data-multi="3" data-cat="demo" data-inputname="">
                        <i class="fa fa-upload"></i> 上传文件
                    </button>
                    <div class="uploadimg_link" style="width: 150px">
                        <input type="text" min="1" placeholder="最多上传数量" style="width: 150px" class="form-control file_num" value="{{$item['extend']['file_num'] ?? ''}}">
                    </div>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '12')
        <div class="form-group draggable ui-draggable dropped" data-type="12" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="b5uploadmainbox b5uploadfilebox" data-type="image">
                    <button type="button" class="btn-b5upload btn btn-primary btn-sm" id="files_upload" data-exts="3" data-multi="3" data-cat="demo" data-inputname="">
                        <i class="fa fa-upload"></i> 上传图片
                    </button>
                    <div class="uploadimg_link" style="width: 150px">
                        <input type="text" min="1" placeholder="最多上传数量" style="width: 150px" class="form-control image_num" value="{{$item['extend']['file_num'] ?? ''}}">
                    </div>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '11')
        <div class="form-group draggable ui-draggable dropped" data-type="11" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="定位选择">
                    <span class="input-group-addon"><i class="fa fa-location-arrow"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '3' || $item['field_type'] == '5')
        <div class="form-group draggable ui-draggable dropped" data-type="3" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                @foreach($item['extend'] as $extend)
                    <label class="radio-box"  style="line-height: normal" data-index="{{$extend['index']}}"><input type="radio" disabled><span class="editable">{{$extend['title']}}</span><span class="remove-label-box" title="删除该项"> X</span></label>
                @endforeach
                <span class="add-radio-box" title="增加选项"> +</span></div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '4' || $item['field_type'] == '6')
        <div class="form-group draggable ui-draggable dropped" data-type="4" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                @foreach($item['extend'] as $extend)
                    <label class="check-box" style="line-height: normal" data-index="{{$extend['index']}}"><input type="checkbox" disabled><span class="editable">{{$extend['title']}}</span><span class="remove-label-box" title="删除该项"> X</span></label>
                @endforeach
                <span class="add-checkbox-box" title="增加选项"> +</span></div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label></div>
    @elseif($item['field_type'] == '7')
        <div class="form-group draggable ui-draggable dropped" data-type="7" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control time-input" placeholder="日期选择" lay-key="1">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '8')
        <div class="form-group draggable ui-draggable dropped" data-type="8" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control date-range-input" placeholder="日期范围选择" lay-key="2">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '9')
        <div class="form-group draggable ui-draggable dropped" data-type="9" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control datetime-input" placeholder="时间选择" lay-key="3">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @elseif($item['field_type'] == '10')
        <div class="form-group draggable ui-draggable dropped" data-type="10" data-index="{{$item['max_index']}}" style="position: static;">
            <label class="col-sm-3 is-required">
                <input type="checkbox" class="input-required" @if($item['is_nust']) checked @endif title="勾选代表该项必填">
                <span class="editable editable-click">{{$item['field_name']}}</span>：
            </label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input type="text" class="form-control datetime-range-input" placeholder="时间范围选择" lay-key="4">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
            <label class="col-sm-1 control-label"><a class="remove-link">移除</a></label>
        </div>
    @endif
@endforeach