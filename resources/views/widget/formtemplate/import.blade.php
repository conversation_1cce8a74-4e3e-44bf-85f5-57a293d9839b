<?php
    $widget_data['url'] = $widget_data['url'] ?? '';
    $widget_data['id'] = $widget_data['id'] ?? 'file';
    $widget_data['tips'] = $widget_data['tips'] ?? '';
    $widget_data['title'] = $widget_data['title'] ?? '';
    $widget_data['temp_url'] = $widget_data['temp_url'] ?? '';
    $widget_data['class'] = $widget_data['class'] ?? '';
?>
<a class="btn btn-warning <?=$widget_data['class']?>" onclick="importExcel()"><i class="fa fa-upload" aria-hidden="true"></i> 导入</a>
<script>
    function importExcel() {
        $.modal.layerinit(function (layer) {
            layer.open({
                type: 1,
                area: ['400px', '260px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: '<?=$widget_data['title']?>',
                content: $('#importTpl').html(),
                btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                // 弹层外区域关闭
                shadeClose: true,
                btn1: function (index, layero) {
                    var file = layero.find('#file-<?=$widget_data['title']?>').val();
                    if (file == '' && !$.common.endWith(file, '.xlsx', 'i')) {
                        $.modal.msgWarning("请选择后缀为 xlsx”的文件。");
                        return false;
                    }
                    var index = layer.load(2, {shade: false});
                    $.modal.disable();
                    var formData = new FormData(layero.find('form')[0]);
                    $.ajax({
                        url: '<?=$widget_data['url']?>',
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        type: 'POST',
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                $.modal.closeAll();
                                if (result.data.errorNum > 0) {
                                    $.modal.layerinit(function (layer) {
                                        layer.alert(result.msg, {
                                            icon: $.modal.icon('warning'),
                                            title: "系统提示",
                                            btn: ['下载', '取消'],
                                            btnclass: ['btn btn-primary'],
                                        },function (index) {
                                            window.open(result.data.error_url)
                                        });
                                    });
                                } else {
                                    $.modal.alertSuccess(result.msg);
                                    $.table.refresh();
                                }
                            } else if (result.code == web_status.WARNING) {
                                layer.close(index);
                                $.modal.enable();
                                $.modal.alertWarning(result.msg)
                            } else {
                                layer.close(index);
                                $.modal.enable();
                                $.modal.alertError(result.msg);
                            }
                        },
                        complete: function () {
                            layero.find("#file-<?=$widget_data['title']?>").val("")
                        }
                    });
                }
            });
        });
    }
</script>
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10 ml5">
        <div class="col-xs-offset-1">
            <input type="file" id="file-<?=$widget_data['title']?>" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
        </div>
        <div class="col-xs-offset-1 mt10" style="color: red;">
            <p>点击下载模板：
                <a download href="<?=$widget_data['temp_url']?>" target="_blank" style="color: darkblue;font-weight: bold"><i class="fa fa-file-excel-o"></i>下载模板</a></p>
            <p><?=$widget_data['tips']?></p>
        </div>
    </form>
</script>