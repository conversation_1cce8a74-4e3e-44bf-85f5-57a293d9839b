@php
    $disable = $extend['disable']??'0';
@endphp

@foreach($data as $item)
    @if($item['item_type'] == 'input')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @if ($disable)
                    <div class="form-control-static">{{$values[$item['item_index']]['value'] ?? ''}}</div>
                @else
                    <input type="text" name="extend[{{$item['item_index']}}]" value="{{$values[$item['item_index']]['value'] ?? ''}}" class="form-control" @if($item['required']) required @endif autocomplete="off">
                @endif
            </div>
        </div>
    @elseif($item['item_type'] == 'textarea')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @if ($disable)
                    <div class="form-control-static">{{$values[$item['item_index']]['value'] ?? ''}}</div>
                @else
                    <textarea name="extend[{{$item['item_index']}}]" class="form-control" @if($item['required']) required @endif>{{$values[$item['item_index']]['value'] ?? ''}}</textarea>
                @endif
            </div>
        </div>
    @elseif($item['item_type'] == 'file')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                <?php
                    if(isset($values[$item['item_index']]['value'])){
                        if(is_string($values[$item['item_index']]['value'])){
                            $values[$item['item_index']]['value'] = explode(',', $values[$item['item_index']]['value']);
                        }
                        $widget_data_url = [];
                        foreach ($values[$item['item_index']]['value'] as $widget_data_val){
                            $widget_data_url[] = \App\Extends\Helpers\Functions::getFileUrl($widget_data_val);
                        }
                        $values[$item['item_index']]['value'] = implode(',', $widget_data_url);
                    }
                ?>
                <input type="hidden" class="extend_file" data-index="{{$item['item_index']}}"   name="extend[{{$item['item_index']}}]" value="{{$values[$item['item_index']]['value'] ?? ''}}" id="extend_file_input_{{$item['item_index']}}"  @if($item['required']) required @endif>
                <div class="b5uploadmainbox b5uploadfilebox" data-type="file">
                    <button type="button" class="btn-b5upload btn btn-primary btn-sm" @if ($disable) data-hide="1" @endif id="extend_file_button_{{$item['item_index']}}" data-exts="txt|rar|doc|png"  data-multi="{{empty($item['extend']['file_num']) ? 10 : $item['extend']['file_num']}}"  data-cat="store"><i class="fa fa-upload"></i> 上传文件</button>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>格式为txt|rar|doc|png；{{!empty($item['extend']['file_num']) ?"最大上传{$item['extend']['file_num']}个；" : ''}}大小不能超过100M</span>
                    <div class="b5uploadlistbox extend_file_{{$item['item_index']}}_filelist" id="extend_file_{{$item['item_index']}}_filelist"></div>
                </div>
            </div>
        </div>
    @elseif($item['item_type'] == 'radio')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @foreach($item['extend'] as $extend)
                    <label class="radio-box">
                        <input type="radio" name="extend[{{$item['item_index']}}]" @if($item['required']) required @endif value="{{$extend['index']}}"
                                @if(isset($values[$item['item_index']]) && $values[$item['item_index']]['value'] == $extend['index'])
                                    checked
                                @endif
                        @if ($disable)
                            disabled
                       @endif
                        /> {{$extend['title']}}
                    </label>
                @endforeach
            </div>
        </div>
    @elseif($item['item_type'] == 'checkbox')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @foreach($item['extend'] as $extend)
                    <label class="check-box">
                        <input type="checkbox" name="extend[{{$item['item_index']}}][]" value="{{$extend['index']}}" @if($item['required']) required @endif
                            @if(isset($values[$item['item_index']]) && in_array($extend['index'], explode(',', $values[$item['item_index']]['value'])))
                            checked
                            @endif
                            @if ($disable)
                            disabled
                                @endif
                        /> {{$extend['title']}}
                    </label>
                @endforeach
            </div>
        </div>
    @elseif($item['item_type'] == 'date')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @if ($disable)
                    <div class="form-control-static">{{$values[$item['item_index']]['value'] ?? ''}}</div>
                @else
                    <input type="text" name="extend[{{$item['item_index']}}]" value="{{$values[$item['item_index']]['value'] ?? ''}}" readonly class="form-control date-input" @if($item['required']) required @endif autocomplete="off"/>
                @endif
            </div>
        </div>
    @elseif($item['item_type'] == 'date_range')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @if ($disable)
                    <div class="form-control-static">{{$values[$item['item_index']]['value'] ?? ''}}</div>
                @else
                    <input type="text" name="extend[{{$item['item_index']}}]" value="{{$values[$item['item_index']]['value'] ?? ''}}" readonly class="form-control date-range-input" @if($item['required']) required @endif autocomplete="off"/>
                @endif
            </div>
        </div>
    @elseif($item['item_type'] == 'datetime')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @if ($disable)
                    <div class="form-control-static">{{$values[$item['item_index']]['value'] ?? ''}}</div>
                @else
                    <input type="text" name="extend[{{$item['item_index']}}]" value="{{$values[$item['item_index']]['value'] ?? ''}}" readonly class="form-control datetime-input" @if($item['required']) required @endif autocomplete="off"/>
                @endif
            </div>
        </div>
    @elseif($item['item_type'] == 'datetime_range')
        <div class="form-group">
            <label class="col-sm-3 control-label @if($item['required']) is-required @endif ">{{$item['item_title']}}：</label>
            <div class="col-sm-8">
                @if ($disable)
                    <div class="form-control-static">{{$values[$item['item_index']]['value'] ?? ''}}</div>
                @else
                    <input type="text" name="extend[{{$item['item_index']}}]" value="{{$values[$item['item_index']]['value'] ?? ''}}" readonly class="form-control datetime-range-input" @if($item['required']) required @endif autocomplete="off"/>
                @endif
            </div>
        </div>
    @endif
@endforeach