
@section('script_before')
    <script>

        function operateTypeExtendInit() {
            if ($.fn.iCheck !== undefined) {
                $("#operate_type_extend .check-box:not(.noicheck),.radio-box:not(.noicheck)").each(function() {
                    $(this).iCheck({
                        checkboxClass: 'icheckbox-blue',
                        radioClass: 'iradio-blue',
                    })
                })
            }
            layui.use('laydate', function() {
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#operate_type_extend .date-input'
                    ,type: 'date'
                });
                laydate.render({
                    elem: '#operate_type_extend .date-range-input'
                    ,type: 'date'
                    ,range: '~'
                });
                laydate.render({
                    elem: '#operate_type_extend .datetime-input'
                    ,type: 'datetime'
                });
                laydate.render({
                    elem: '#operate_type_extend .datetime-range-input'
                    ,type: 'datetime'
                    ,range: '~'
                });
            });
            $.each($('#operate_type_extend .extend_file'), function (i, item) {
                var obj = $(item);
                var index = obj.data('index');
                var input_id = 'extend_file_input_'+index;
                var button_id = 'extend_file_button_'+index;
                var filelist_id = 'extend_file_'+index+'_filelist';
                var btn = $('#'+button_id);
                var hide = btn.data('hide');
                if (!hide) {
                    b5uploadfileinit(button_id);
                    if (btn.data('multi') != 1) {
                        dragula([document.querySelector('#'+filelist_id)])
                    }
                } else {
                    btn.hide();
                    btn.next().hide();
                }

                var file = $('#'+input_id).val();
                if (file) {
                    $.each(file.split(','), function (i, f) {
                        b5uploadhtmlshow(button_id,b5uploadfilehtml(f,button_id,f, f,hide));
                    })
                }
            })
        }
        function operateTypeExtendSubmitBefore() {
            $.each($('#operate_type_extend .extend_file'), function (i, item) {
                var obj = $(item);
                var index = obj.data('index');
                var input_id = 'extend_file_input_'+index;
                var button_id = 'extend_file_button_'+index;
                var files = [];
                $("input[name='"+button_id+"[]']").each(function () {
                    files.push($(this).val())
                });
                $("#"+input_id).val(files.join(','));
            })
        }
    </script>
@append
