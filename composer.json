{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "ext-curl": "*", "ext-openssl": "*", "barryvdh/laravel-debugbar": "^3.6", "cboden/ratchet": "^0.4.4", "endroid/qr-code": "^4.8", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "laravel/framework": "^9.2", "laravel/sanctum": "^2.14.1", "laravel/tinker": "^2.7", "laravolt/avatar": "^5.0", "maxlcoder/laravel-dm8": "^1.0", "mews/captcha": "^3.2", "overtrue/laravel-wechat": "^6.1", "phpoffice/phpspreadsheet": "^1.22"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.12", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"easywechat-composer/easywechat-composer": true}}, "minimum-stability": "dev", "prefer-stable": true}