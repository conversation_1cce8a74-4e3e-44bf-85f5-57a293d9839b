html{
    -ms-touch-action:none;
    -ms-content-zooming: none;
    /* overflow:hidden; */
}
#map {
    position:relative;
    height: 560px;
    margin-bottom: 15px;
    clear: both;
}
#func{
    margin-top: -56px;
    float: right;
    margin-right: 55px;
    z-index: 900;
}
#func > button {
    margin-top: 20px;
    margin-right: 20px;
    margin-bottom: 9px;
}
#func > div {
    margin-top: 20px;
    float: left;
    margin-right: 20px;
}
#func > input {
    margin-top: 20px;
    margin-right: 20px;
    width: 100px;
    float: left;
}
#func > select {
    margin-top: 20px;
    margin-right: 20px;
    width: 100px;
    float: left;
}
h3 {
    margin-bottom: 10px;
}

#myTab, #myTabContent {
    font-size: 14px;
}

.nav-tabs {
    margin-bottom: 0;
}

h3 > #demo-name {
    margin-right: 10px;
}
h3 > a {
    font-weight: normal;
    text-decoration: none;
}
/*less中设置*/
@media (max-width: 979px) {
.navbar-fixed-top {
    margin-bottom: 0;
}
}

.page-header {
    padding: 5px 10px;
    border: 1px solid #d6e3f1;
}

.nav {
    margin-bottom: 0;
}
.footer {
    margin-top: 20px;
    text-align: center;
}
@media (min-width: 768px) {
    .container .hero-unit{
        padding: 60px 400px 60px 60px;
        background:url(../images/home_banner2.png) no-repeat scroll right bottom transparent;
    }
}

@media (min-width: 768px) {
    .container,
    .navbar-fixed-top .container,
    .navbar-fixed-bottom .container {
        width: 760px;
    }
}
@media (min-width: 992px) {
    .container,
    .navbar-fixed-top .container,
    .navbar-fixed-bottom .container {
        width: 960px;
    }
}
@media (min-width: 1200px) {
    .container,
    .navbar-fixed-top .container,
    .navbar-fixed-bottom .container {
        width: 1170px;
    }
}

@media (min-width: 1600px) {
    .container,
    .navbar-fixed-top .container,
    .navbar-fixed-bottom .container {
        width: 1220px;
    }
}

.container .hero-unit{
    border: 1px solid #D6E3F1;
    border-radius: 0px;
}
p {
    text-indent: 2em;
}

.navbar-inner > .container {
    background-color: #61b3e3;
    background-image: -moz-linear-gradient(top, #61b3e3, #2f7fba);
    background-image: -ms-linear-gradient(top, #61b3e3, #2f7fba);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#61b3e3), to(#2f7fba));
    background-image: -webkit-linear-gradient(top, #61b3e3, #2f7fba);
    background-image: -o-linear-gradient(top, #61b3e3, #2f7fba);
    background-image: linear-gradient(top, #61b3e3, #2f7fba);
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#61b3e3', endColorstr='#2f7fba', GradientType=0);

    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.navbar .nav .active > a,
.navbar .nav .active > a:hover {
    background-image: -moz-linear-gradient(top, #519ad6, #2167ad);
    background-image: -ms-linear-gradient(top, #519ad6, #2167ad);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#519ad6), to(#2167ad));
    background-image: -webkit-linear-gradient(top, #519ad6, #2167ad);
    background-image: -o-linear-gradient(top, #519ad6, #2167ad);
    background-image: linear-gradient(top, #519ad6, #2167ad);
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#519ad6', endColorstr='#2167ad', GradientType=0);
    background-color: #2167ad;
}

#titleContent li a {
    font-size: 15px;
}

.container > .brand {
    color: white;
}
#sourceCode {
  float: right;
  margin-top: -30px;
  margin-right:40px;
}
#skip{
    float: right;
    margin-top:-30px;
    margin-right:4px;
    color: #08c;
}
/*开发指南和产品介绍css*/
h2.title {
    background-color: #d6e3f1;
    margin: 15px 0px;
}
h4.thirdTitle{
    margin-left: 25px;
}
#content {
    padding: 0px 10px;
    text-algin: center;
    border: 1px solid #d6e3f1;
}
/*图片居中*/
div.pageImage {
    text-align: center;
}
img {
    margin:auto;
}
img.icon{
    border:2px solid #d6e3f1;
}
/*产品指南目录列表样式*/
#togglelink{
    vertical-align: 30%;
    border-radius: 0px;
}
#lists {
    overflow: hidden;
    margin: 5px auto 10px auto;
    font-size: 15px;
}
ul {
    list-style-type:none;
}
ul li {
    line-height: 26px;
}
#lists span {
    padding-right:5px;
}
/**/
ul.circle {
    list-style-type: circle;
}
ul.disc {
    list-style-type: disc;
}
/*首页top背景*/
.container .topBanner{
    background-image: url(../images/home_banner.jpg);
    background-repeat: no-repeat;
}
.qByBoundsBoxDiv {
    background-color: #B7CFE8;
    filter:alpha(opacity:50);
    border: 2px solid #304DBE;
}
/*公交换乘首列背景图片*/
.step_transfer{
    background-image: url(../images/step_trans.png);
    background-repeat: no-repeat;
    width: 23px;
    background-position: 7px 7px;
}
.bus_transfer{
    background-image: url(../images/bus_trans.png);
    background-repeat: no-repeat;
    width: 23px;
    background-position: 7px 7px;
}
.start_transfer{
    background-image: url(../images/start_trans.png);
    background-repeat: no-repeat;
    width: 23px;
    background-position: 7px 7px;
}
.end_transfer{
    background-image: url(../images/end_trans.png);
    background-repeat: no-repeat;
    width: 23px;
    background-position: 7px 7px;
}
#trafficRes > tbody > tr > td{
    text-align: left;
}
input{
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    border:1px solid #000000;
    width:160px;
    padding:1px;
    margin:0px;
}
.start_end_input {
    width:213px;
    height:40px;
}
.sugpopup{
    position: relative;
    width: 170px;
    color: #004a7e;
    font-size: 12px;
    font-family: Arial, Helvetica, sans-serif;
    top: -10px;
    left: 40px;
}
.sugpopup.show{
    background-color: white;
    filter: alpha(opacity=100);
    border: 1px solid #004a7e;
    z-index: 9999;
}

ul.dropList{
    list-style:none;
    margin:0px;
    padding:0;
    color:#004a7e;
}
ul.dropList li {
    padding: 0 6px;
}
li.mouseOver{
    background-color:#004a7e;
    color:#FFFFFF;
}

li.dropListMouseOver {
    background-color:#004a7e;
    color:#FFFFFF;
}

.transferSolution {
    border: 2px solid #EEE;
    cursor: pointer;
    margin:2px;
}
.transferIndex {
    padding: 12px 0;
    text-weight:bold;
}
.busLink {
    text-decoration: none;
    margin: 1px 2px;
    cursor: pointer;
}
.bgSpan {
    background-color: blue;
    opacity: 0.6;
    padding: 4px;
    color: white;
    border-radius: 4px;
}
.transferGuides {
  padding: 2px;
  margin: 20px 2px 10px 2px;
  border-bottom: 1px solid #eeeeee;
  width: 97%;
}
.showWindow:hover{color:#FF0000}

.win_bg{background:#CCC; opacity:0.2; filter:alpha(opacity=20); position:absolute; left:0px; top:0px; width:100%; height:100%; z-index:998;}
.winTitle{background:#4192c9; height:20px; line-height:20px}
.winTitle .title_left{font-weight:bold; color:#FFF; padding-left:5px; float:left}
.winTitle .title_right{float:right; padding-right:3px;}
.winTitle .title_right a{color:#FFF; text-decoration:none; padding-right:3px;}
.winTitle .title_right a:hover{text-decoration:underline; color:#FF0000; padding-right:3px;}
.transferPreference {
    border: 1px solid #D6E3F1;
    height: 20px;
    margin: 1px 30%;
    padding: 0 12px;
}
.winContent{padding:5px; overflow-y:auto; height:550px;}
.popupWindow {
    right:20px;
    top:100px;
    position: absolute;
    width: 400px;
    height: 600px;
    border: 2px solid #D6E3F1;
    background: #FFF;
    z-index: 9999;
}
.floatLeft {
    float: left;
}
.print-header{
    margin: auto;
    width: 936px;
    height: 522px;
    top: 50px;
    position: relative;
}
.superD{
    width: 100%;
    height: 24px;
    line-height: 24px;
    border-bottom: 3px solid #B2B2B2;
    font-size: 14px;
    margin-bottom: 6px;
}
.superD h3{
    float: left;
    font-size: 14px;
}
.superD span{
    float: right;
    margin-right: 4px;
}
#superft{
    content: '.';
    height: 77px;
    display: block;
    overflow: hidden;
    clear: both;
}
.printClose{
    margin-bottom: 20px;
    float: right;
}
.printClose span{
    background-position: 0px 0px;
    cursor: pointer;
    margin-right: 0px;
    margin-top: 5px;
    display: inline-block;
    width: 69px;
    height: 29px;
    background-image: url(../images/print.png);
}

#btn-success2 {
    display: inline-block;
    padding: 4px 10px 4px;
    text-align: center;
    cursor: pointer;
    position: relative;
    left: 150px;
    z-index:2004;
    width: 30px;
    height: 18px;
    margin-top: 5px;
}

div.smCustomControlMousePosition {
    left: 600px;
    top:280px;
    display: block;
    position: absolute;
    font-family: Arial;
    font-size: smaller;
}


.nav-pills > li >a{
    padding: 0px;
    padding-left: 4px;
    padding-right:5px;
    margin-bottom: 8px;
    margin-right: 0px;
    border-radius: 0px;
    font-size: 13px;
    color: #6E7B8B;
    border-right: 1px solid    #e1e1e8;

}
.nav-pills > li > a:hover{
    color:   #08c;
    text-decoration: underline;
    background-color: #ffffff;
}
.nav-pills > .active > a{
    color:#08c;
    background-color: #ffffff;
}
.nav-pills > .active > a:hover {
    color: #08c;
    background-color: #ffffff;
}

div.container-fluid{
    padding-left: 0px;
    padding-right: 0px;
}
div.row-fluid div.span2{
    font-size:13px;
    width: 115px;
    color:#4682B4;
    padding-left: 4px;
    padding-top: 3px;
}
div.row-fluid div.span10{
    margin-left: 0px;
    margin-top:8px;
    padding-bottom: 2px;
}

div.span2 ul.nav.nav-pills  span{
    width:9px;
    font-weight:normal;
    font-size:16px;
    color:#aaa;
    margin-top:5px;
    padding-left:5px;
    padding-right: 10px;
    float:right;
}
.row-fluid{
    border-bottom: 1px solid #e1e1e8;
}
