/*通用样式*/
* {
    margin: 0;
    padding: 0;
    border: 0;
}

#code_close,
#code_open {
    cursor: pointer;
}

#code_area #d_menu > div > a:hover {
    cursor: pointer;
    font-weight: bold;
}

#container-main {
    position: relative;
    margin: 0 auto;
    width: 100%;
    height: 575px;
    border: 0;
}

/*源码编辑面板*/
#code_area {
    display: none;
    position: relative;
    width: 37%;
    height: 100%;
    float: left;
}

#openbar {
    padding: 0;
    height: 36px;
    overflow: hidden;
}

#openbar > p {
    line-height: 36px;
    background-color: #C2E3FA;
    text-indent: 1em;
}

#code {
    width: 96.6%;
    height: 91%;
}

.CodeMirror {
    width: 100%;
    height: 93.5%;
}

#code_area .copy-success {
    display:none;
    position: absolute;
    width: 180px;
    height: 45px;
    text-align: center;
    line-height: 45px;
    top: 20%;
    left: 33%;
    color: #fff;
    background-color: #0089CD;
    box-shadow: 0 2px 4px 0 rgba(0,136,205,0.50);
    font-size: 16px;
    border-radius: 7px;
    z-index: 999;
}

#code_area #d_menu {
    float: right;
    width: 163px;
}

#code_area #d_menu div {
    float: left;
    margin-right: 7px;
}

#code_area #d_menu i {
    display: block;
    float: left;
    margin-top: 11px;
    margin-right: 3px;
}

#code_area #d_menu div a {
    line-height: 36px;
    text-decoration: none;
    padding-bottom: 2px;
}

/*运行 重置 复制 按钮*/
#code_area #d_clip_container a {
    background: url(../images/result.png) no-repeat 0 0.4px;
    color: #3189f3;
    padding-left: 16px;
}

#code_area #d_run_container a {
    background: url(../images/result.png) no-repeat 0 -24px;
    color: #3189f3;
    padding-left: 17px;
}

#code_area #d_refresh_container a {
    background: url(../images/result.png) no-repeat 0 -48px;
    color: #3189f3;
    padding-left: 20px;
}

/*源码编辑器开关按钮*/
#code_close {
    display: none;
    background: url(../images/result.png) no-repeat 0 -142px;
    width: 14px;
    height: 90px;
    position: absolute;
    right: -16px;
    z-index: 9999;
    top: 50%;
    margin-top: -45px;
}

#code_open {
    display: none;
    background: url(../images/result.png) no-repeat 0 -242px;
    width: 14px;
    height: 90px;
    position: absolute;
    z-index: 9999;
    top: 50%;
    margin-top: -45px;
}

/*地图面板*/
#mapContent {
    width: 99.8%;
    height: 99%;
    float: right;
    padding-top: 5px;
}

#examplesIframe {
    width: 99.8%;
    height: 99%;
}

/**clear float**/
.clearfix {
    display: block;
    zoom: 1;
}

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

/*拖拽框*/
#drag {
    display: none;
    width: 3px;
    height: 100%;
    background: #eee;
    cursor: w-resize;
    float: left;
}

#overiframe {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 999;
    display: none;
    -webkit-user-select: none;
}

/**设置滚动条的样式**/
::-webkit-scrollbar {
    width: 10px;
}

/**滚动槽**/
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #d1cfcf;
    border-radius: 10px;
}

/**滚动条滑块**/
::-webkit-scrollbar-thumb {
    border-radius: 12px;
    background: #f7f7f7;
    -webkit-box-shadow: inset 0 0 6px #d1cfcf;
}

::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(245, 245, 245, 0.4);
}

