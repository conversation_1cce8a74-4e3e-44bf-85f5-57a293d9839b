/*!
 * jQuery contextMenu - Plugin for simple contextMenu handling
 *
 * Version: git-master
 *
 * Authors: <AUTHORS>
 * Web: http://medialize.github.com/jQuery-contextMenu/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *   GPL v3 http://opensource.org/licenses/GPL-3.0
 *
 */

.context-menu-list {
    margin:0; 
    padding:0;
    
    min-width: 98px;
    max-width: 250px;
    display: inline-block;
    position: absolute;
    list-style-type: none;
    
    border: 1px solid #DDD;
    background: #EEE;
    
    -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
       -moz-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
        -ms-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
         -o-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
    
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 15px;
}

.context-menu-item {
    padding: 2px 2px 2px 24px;
    background-color: #F5FFFA;
    position: relative;
    -webkit-user-select: none;
       -moz-user-select: -moz-none;
        -ms-user-select: none;
            user-select: none;
}

.context-menu-separator {
    padding-bottom:0;
    border-bottom: 1px solid #DDD;
}

.context-menu-item > label > input,
.context-menu-item > label > textarea {
    -webkit-user-select: text;
       -moz-user-select: text;
        -ms-user-select: text;
            user-select: text;
}

.context-menu-item.hover {
    cursor: pointer;
    background-color: #B0E2FF;
}

.context-menu-item.disabled {
    color: #666;
}

.context-menu-input.hover,
.context-menu-item.disabled.hover {
    cursor: default;
    background-color: #EEE;
}

.context-menu-submenu:after {
    content: ">";
    color: #666;
    position: absolute;
    top: 0;
    right: 3px;
    z-index: 1;
}

/* icons
    #protip:
    In case you want to use sprites for icons (which I would suggest you do) have a look at
    http://css-tricks.com/13224-pseudo-spriting/ to get an idea of how to implement 
    .context-menu-item.icon:before {}
 */
.context-menu-item.icon { min-height: 21px; background-repeat: no-repeat; background-position: 4px 5px;  }
.context-menu-item.icon-zoomin{ background-image: url(../images/zoomin.png);padding-top: 3px;padding-bottom: 3px; padding-left:28px;}
.context-menu-item.icon-zoomout { background-image: url(../images/zoomout.png);padding-top:3px;padding-bottom: 3px;padding-left:28px; }
.context-menu-item.icon-edit { background-image: url(../images/page_white_edit.png); }
.context-menu-item.icon-cut { background-image: url(../images/cut.png); }
.context-menu-item.icon-copy { background-image: url(../images/page_white_copy.png); }
.context-menu-item.icon-paste { background-image: url(../images/page_white_paste.png); }
.context-menu-item.icon-delete { background-image: url(../images/page_white_delete.png); }
.context-menu-item.icon-add { background-image: url(../images/page_white_add.png); }
.context-menu-item.icon-quit { background-image: url(../images/door.png); }

/* vertically align inside labels */
.context-menu-input > label > * { vertical-align: top; }

/* position checkboxes and radios as icons */
.context-menu-input > label > input[type="checkbox"],
.context-menu-input > label > input[type="radio"] {
    margin-left: -17px;
}
.context-menu-input > label > span {
    margin-left: 5px;
}

.context-menu-input > label,
.context-menu-input > label > input[type="text"],
.context-menu-input > label > textarea,
.context-menu-input > label > select {
    display: block;
    width: 100%;
    
    -webkit-box-sizing: border-box;
       -moz-box-sizing: border-box;
        -ms-box-sizing: border-box;
         -o-box-sizing: border-box;
            box-sizing: border-box;
}

.context-menu-input > label > textarea {
    height: 100px;
}
.context-menu-item > .context-menu-list {
    display: none;
    /* re-positioned by js */
    right: -5px;
    top: 5px;
}

.context-menu-item.hover > .context-menu-list {
    display: block;
}

.context-menu-accesskey {
    text-decoration: underline;
}
