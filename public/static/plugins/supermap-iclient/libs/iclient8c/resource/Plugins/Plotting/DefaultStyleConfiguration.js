
/**
 * Namespace: DefaultStyleConfiguration
 * 标绘对象默认风格的配置文件
 */
DefaultStyleConfiguration = {
    /*当前版本号*/
    'version': "810-14419-2840",

    /*标号最大缩放比例*/
    'maxScale': 5,

    /*标号最小缩放比例*/
    'minScale': 0,

    /*标号随图缩放*/
    'scaleByMap': false,

    /*标号大小，单位：pixel*/
    'dotSymbolSize': 40,

    /*标号与注记之间的距离，单位：pixel*/
    'dotTextSpace': 7,

    /*生成旗面文字的字号*/
    'flagTextSize': 60,

    /*线宽，单位：pixel*/
    'strokeWidth': 3,

    /*线色*/
    'strokeColor': "#ff0000",

    /*线透明度，范围为：0-1*/
    'strokeOpacity': 1.0,

    /*线型，dot,dash,dashdot,longdash,longdashdot,solid*/
    'strokeDashstyle': "solid",

    /*线连接拐点处理，butt，round，square*/
    'strokeLinecap': "round",

    /*填充*/
    'fill': false,

    /*填充色*/
    'fillColor': "#ff0000",

    /*填充透明度，范围为：0-1*/
    'fillOpacity': 0.31,

    /*字体颜色*/
    'fontColor': "#000000",

    /*字体透明度，范围为：0-1*/
    'fontOpacity': 1,

    /*字体描边是否启用*/
    'fontStroke': false,

    /*字体描边颜色*/
    'fontStrokeColor': "#ff0000",

    /*字体描边宽度*/
    'fontStrokeWidth': "1px",

    /*文字背景是否启用*/
    'fontBackground': false,

    /*文字背景色*/
    'fontBackgroundColor': "#ff0000",

    /*文字阴影是否启用*/
    'fontShadow': false,

    /*文字阴影色*/
    'fontShadowColor': "#000000",

    /*文字阴影X方向偏移量，单位是：Pixel*/
    'fontShadowOffsetX': 0,

    /*文字阴影Y方向偏移量，单位是：Pixel*/
    'fontShadowOffsetY': 0,

    /*字间距*/
    'fontSpace': 0,

    /*字宽百分比*/
    'fontPercent': 100,

    /*字体大小*/
    'fontSize': 14,

    /*字体类型*/
    'fontFamily': "微软雅黑",

    /*字体粗细*/
    'fontWeight': "bold",

    /*字体样式*/
    'fontStyle': "",

    /*衬线类型*/
    'surroundType': 0,

    /*衬线颜色*/
    'surroundLineColor': "#ffff00",

    /*衬线线宽*/
    'surroundLineWidth': 4,

    /*衬线透明度*/
    'surroundLineColorOpacity': 1,

    /*填充背景色*/
    'fillBackColor': "#ff0000",

    /*填充背景透明度*/
    'fillBackOpacity': 1,

    /*渐变填充样式*/
    'fillGradientMode': "NONE",

    /*渐变填充角度偏移*/
    'fillAngle': 0,

    /*渐变填充竖直偏移*/
    'fillCenterOffsetX': 0,

    /*渐变填充竖直偏移*/
    'fillCenterOffsetY': 0,

    /*线色渐变填充*/
    'lineColorFill':false,

    /* 线色渐变前景色*/
    'lineForeColor':"#ff0000",

    /*
    * APIProperty:lineColorOpacity
    * 线色渐变透明度
    */
    'lineColorOpacity':1.0,

    /*线色渐变填充模式*/
    'lineColorGradientMode':"NONE",

    /*线色渐变填充背景透明度*/
    'lineColorBackOpacity':0.31,

    /*线色渐变填充背景色*/
    'lineColorBackColor':"#ff0000"
};
