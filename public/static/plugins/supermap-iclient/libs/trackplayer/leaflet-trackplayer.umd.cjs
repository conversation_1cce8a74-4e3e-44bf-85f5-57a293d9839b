(function(M,k){typeof exports=="object"&&typeof module!="undefined"?k(require("leaflet")):typeof define=="function"&&define.amd?define(["leaflet"],k):(M=typeof globalThis!="undefined"?globalThis:M||self,k(M.L))})(this,function(M){"use strict";var K=(M,k,R)=>new Promise((E,S)=>{var O=A=>{try{G(R.next(A))}catch(_){S(_)}},B=A=>{try{G(R.throw(A))}catch(_){S(_)}},G=A=>A.done?E(A.value):Promise.resolve(A.value).then(O,B);G((R=R.apply(M,k)).next())});var k=63710088e-1,R={centimeters:k*100,centimetres:k*100,degrees:k/111325,feet:k*3.28084,inches:k*39.37,kilometers:k/1e3,kilometres:k/1e3,meters:k,metres:k,miles:k/1609.344,millimeters:k*1e3,millimetres:k*1e3,nauticalmiles:k/1852,radians:1,yards:k*1.0936};function E(e,s,t){t===void 0&&(t={});var n={type:"Feature"};return(t.id===0||t.id)&&(n.id=t.id),t.bbox&&(n.bbox=t.bbox),n.properties=s||{},n.geometry=e,n}function S(e,s,t){if(t===void 0&&(t={}),!e)throw new Error("coordinates is required");if(!Array.isArray(e))throw new Error("coordinates must be an Array");if(e.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!q(e[0])||!q(e[1]))throw new Error("coordinates must contain numbers");var n={type:"Point",coordinates:e};return E(n,s,t)}function O(e,s,t){if(t===void 0&&(t={}),e.length<2)throw new Error("coordinates must be an array of two or more positions");var n={type:"LineString",coordinates:e};return E(n,s,t)}function B(e,s){s===void 0&&(s="kilometers");var t=R[s];if(!t)throw new Error(s+" units is invalid");return e*t}function G(e,s){s===void 0&&(s="kilometers");var t=R[s];if(!t)throw new Error(s+" units is invalid");return e/t}function A(e){var s=e%(2*Math.PI);return s*180/Math.PI}function _(e){var s=e%360;return s*Math.PI/180}function q(e){return!isNaN(e)&&e!==null&&!Array.isArray(e)}function Q(e){return!!e&&e.constructor===Object}function V(e,s,t){if(e!==null)for(var n,r,h,l,c,f,g,v=0,p=0,P,a=e.type,i=a==="FeatureCollection",o=a==="Feature",u=i?e.features.length:1,d=0;d<u;d++){g=i?e.features[d].geometry:o?e.geometry:e,P=g?g.type==="GeometryCollection":!1,c=P?g.geometries.length:1;for(var y=0;y<c;y++){var m=0,b=0;if(l=P?g.geometries[y]:g,l!==null){f=l.coordinates;var w=l.type;switch(v=0,w){case null:break;case"Point":if(s(f,p,d,m,b)===!1)return!1;p++,m++;break;case"LineString":case"MultiPoint":for(n=0;n<f.length;n++){if(s(f[n],p,d,m,b)===!1)return!1;p++,w==="MultiPoint"&&m++}w==="LineString"&&m++;break;case"Polygon":case"MultiLineString":for(n=0;n<f.length;n++){for(r=0;r<f[n].length-v;r++){if(s(f[n][r],p,d,m,b)===!1)return!1;p++}w==="MultiLineString"&&m++,w==="Polygon"&&b++}w==="Polygon"&&m++;break;case"MultiPolygon":for(n=0;n<f.length;n++){for(b=0,r=0;r<f[n].length;r++){for(h=0;h<f[n][r].length-v;h++){if(s(f[n][r][h],p,d,m,b)===!1)return!1;p++}b++}m++}break;case"GeometryCollection":for(n=0;n<l.geometries.length;n++)if(V(l.geometries[n],s)===!1)return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function X(e,s){var t,n,r,h,l,c,f,g,v,p,P=0,a=e.type==="FeatureCollection",i=e.type==="Feature",o=a?e.features.length:1;for(t=0;t<o;t++){for(c=a?e.features[t].geometry:i?e.geometry:e,g=a?e.features[t].properties:i?e.properties:{},v=a?e.features[t].bbox:i?e.bbox:void 0,p=a?e.features[t].id:i?e.id:void 0,f=c?c.type==="GeometryCollection":!1,l=f?c.geometries.length:1,r=0;r<l;r++){if(h=f?c.geometries[r]:c,h===null){if(s(null,P,g,v,p)===!1)return!1;continue}switch(h.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":{if(s(h,P,g,v,p)===!1)return!1;break}case"GeometryCollection":{for(n=0;n<h.geometries.length;n++)if(s(h.geometries[n],P,g,v,p)===!1)return!1;break}default:throw new Error("Unknown Geometry Type")}}P++}}function Y(e,s){X(e,function(t,n,r,h,l){var c=t===null?null:t.type;switch(c){case null:case"Point":case"LineString":case"Polygon":return s(E(t,r,{bbox:h,id:l}),n,0)===!1?!1:void 0}var f;switch(c){case"MultiPoint":f="Point";break;case"MultiLineString":f="LineString";break;case"MultiPolygon":f="Polygon";break}for(var g=0;g<t.coordinates.length;g++){var v=t.coordinates[g],p={type:f,coordinates:v};if(s(E(p,r),n,g)===!1)return!1}})}function j(e,s){Y(e,function(t,n,r){var h=0;if(t.geometry){var l=t.geometry.type;if(!(l==="Point"||l==="MultiPoint")){var c,f=0,g=0,v=0;if(V(t,function(p,P,a,i,o){if(c===void 0||n>f||i>g||o>v){c=p,f=n,g=i,v=o,h=0;return}var u=O([c,p],t.properties);if(s(u,n,r,o,h)===!1)return!1;h++,c=p})===!1)return!1}}})}function $(e,s,t){var n=t,r=!1;return j(e,function(h,l,c,f,g){r===!1&&t===void 0?n=h:n=s(n,h,l,c,f,g),r=!0}),n}function z(e){if(!e)throw new Error("coord is required");if(!Array.isArray(e)){if(e.type==="Feature"&&e.geometry!==null&&e.geometry.type==="Point")return e.geometry.coordinates;if(e.type==="Point")return e.coordinates}if(Array.isArray(e)&&e.length>=2&&!Array.isArray(e[0])&&!Array.isArray(e[1]))return e;throw new Error("coord must be GeoJSON Point or an Array of numbers")}function tt(e){return e.type==="Feature"?e.geometry:e}var et=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function U(e,s,t){t===void 0&&(t={});var n=z(e),r=z(s),h=_(r[1]-n[1]),l=_(r[0]-n[0]),c=_(n[1]),f=_(r[1]),g=Math.pow(Math.sin(h/2),2)+Math.pow(Math.sin(l/2),2)*Math.cos(c)*Math.cos(f);return B(2*Math.atan2(Math.sqrt(g),Math.sqrt(1-g)),t.units)}function I(e,s,t,n){n===void 0&&(n={});var r=z(e),h=_(r[0]),l=_(r[1]),c=_(t),f=G(s,n.units),g=Math.asin(Math.sin(l)*Math.cos(f)+Math.cos(l)*Math.sin(f)*Math.cos(c)),v=h+Math.atan2(Math.sin(c)*Math.sin(f)*Math.cos(l),Math.cos(f)-Math.sin(l)*Math.sin(g)),p=A(v),P=A(g);return S([p,P],n.properties)}function D(e,s,t){if(t===void 0&&(t={}),t.final===!0)return it(e,s);var n=z(e),r=z(s),h=_(n[0]),l=_(r[0]),c=_(n[1]),f=_(r[1]),g=Math.sin(l-h)*Math.cos(f),v=Math.cos(c)*Math.sin(f)-Math.sin(c)*Math.cos(f)*Math.cos(l-h);return A(Math.atan2(g,v))}function it(e,s){var t=D(s,e);return t=(t+180)%360,t}function st(e,s,t){t===void 0&&(t={});for(var n=tt(e),r=n.coordinates,h=0,l=0;l<r.length&&!(s>=h&&l===r.length-1);l++)if(h>=s){var c=s-h;if(c){var f=D(r[l],r[l-1])-180,g=I(r[l],c,f,t);return g}else return S(r[l])}else h+=U(r[l],r[l+1],t);return S(r[r.length-1])}function Z(e,s){return s===void 0&&(s={}),$(e,function(t,n){var r=n.geometry.coordinates;return t+U(r[0],r[1],s)},0)}function J(e,s,t,n){if(n=n||{},!Q(n))throw new Error("options is invalid");var r,h=[];if(e.type==="Feature")r=e.geometry.coordinates;else if(e.type==="LineString")r=e.coordinates;else throw new Error("input must be a LineString Feature or Geometry");for(var l=r.length,c=0,f,g,v,p=0;p<r.length&&!(s>=c&&p===r.length-1);p++){if(c>s&&h.length===0){if(f=s-c,!f)return h.push(r[p]),O(h);g=D(r[p],r[p-1])-180,v=I(r[p],f,g,n),h.push(v.geometry.coordinates)}if(c>=t)return f=t-c,f?(g=D(r[p],r[p-1])-180,v=I(r[p],f,g,n),h.push(v.geometry.coordinates),O(h)):(h.push(r[p]),O(h));if(c>=s&&h.push(r[p]),p===r.length-1)return O(h);c+=U(r[p],r[p+1],n)}if(c<s&&r.length===l)throw new Error("Start position is beyond line");var P=r[r.length-1];return O([P,P])}(function(e,s){(function(t,n){n(M)})(et,function(t){t=t&&t.hasOwnProperty("default")?t.default:t;function n(a,i){var o=i.x-a.x,u=i.y-a.y;return Math.sqrt(o*o+u*u)}var r=function(i,o){return(Math.atan2(o.y-i.y,o.x-i.x)*180/Math.PI+90+360)%360},h=function(i,o){var u=i.value,d=i.isInPixels;return d?u/o:u};function l(a){if(typeof a=="string"&&a.indexOf("%")!==-1)return{value:parseFloat(a)/100,isInPixels:!1};var i=a?parseFloat(a):0;return{value:i,isInPixels:i>0}}var c=function(i,o){return i.x===o.x&&i.y===o.y};function f(a){return a.reduce(function(i,o,u,d){if(u>0&&!c(o,d[u-1])){var y=d[u-1],m=i.length>0?i[i.length-1].distB:0,b=n(y,o);i.push({a:y,b:o,distA:m,distB:m+b,heading:r(y,o)})}return i},[])}function g(a,i){var o=f(a),u=o.length;if(u===0)return[];var d=o[u-1].distB,y=h(i.offset,d),m=h(i.endOffset,d),b=h(i.repeat,d),w=d*b,x=y>0?d*y:0,C=m>0?d*m:0,F=[],N=x;do F.push(N),N+=w;while(w>0&&N<d-C);var H=0,T=o[0];return F.map(function(W){for(;W>T.distB&&H<u-1;)H++,T=o[H];var rt=(W-T.distA)/(T.distB-T.distA);return{pt:v(T.a,T.b,rt),heading:T.heading}})}function v(a,i,o){return i.x!==a.x?{x:a.x+o*(i.x-a.x),y:a.y+o*(i.y-a.y)}:{x:a.x,y:a.y+(i.y-a.y)*o}}(function(){var a=L.Marker.prototype._initIcon,i=L.Marker.prototype._setPos,o=L.DomUtil.TRANSFORM==="msTransform";L.Marker.addInitHook(function(){var u=this.options.icon&&this.options.icon.options,d=u&&this.options.icon.options.iconAnchor;d&&(d=d[0]+"px "+d[1]+"px"),this.options.rotationOrigin=this.options.rotationOrigin||d||"center bottom",this.options.rotationAngle=this.options.rotationAngle||0,this.on("drag",function(y){y.target._applyRotation()})}),L.Marker.include({_initIcon:function(){a.call(this)},_setPos:function(u){i.call(this,u),this._applyRotation()},_applyRotation:function(){this.options.rotationAngle&&(this._icon.style[L.DomUtil.TRANSFORM+"Origin"]=this.options.rotationOrigin,o?this._icon.style[L.DomUtil.TRANSFORM]="rotate("+this.options.rotationAngle+"deg)":this._icon.style[L.DomUtil.TRANSFORM]+=" rotateZ("+this.options.rotationAngle+"deg)")},setRotationAngle:function(u){return this.options.rotationAngle=u,this.update(),this},setRotationOrigin:function(u){return this.options.rotationOrigin=u,this.update(),this}})})(),t.Symbol=t.Symbol||{},t.Symbol.Dash=t.Class.extend({options:{pixelSize:10,pathOptions:{}},initialize:function(i){t.Util.setOptions(this,i),this.options.pathOptions.clickable=!1},buildSymbol:function(i,o,u,d,y){var m=this.options,b=Math.PI/180;if(m.pixelSize<=1)return t.polyline([i.latLng,i.latLng],m.pathOptions);var w=u.project(i.latLng),x=-(i.heading-90)*b,C=t.point(w.x+m.pixelSize*Math.cos(x+Math.PI)/2,w.y+m.pixelSize*Math.sin(x)/2),F=w.add(w.subtract(C));return t.polyline([u.unproject(C),u.unproject(F)],m.pathOptions)}}),t.Symbol.dash=function(a){return new t.Symbol.Dash(a)},t.Symbol.ArrowHead=t.Class.extend({options:{polygon:!0,pixelSize:10,headAngle:60,pathOptions:{stroke:!1,weight:2}},initialize:function(i){t.Util.setOptions(this,i),this.options.pathOptions.clickable=!1},buildSymbol:function(i,o,u,d,y){return this.options.polygon?t.polygon(this._buildArrowPath(i,u),this.options.pathOptions):t.polyline(this._buildArrowPath(i,u),this.options.pathOptions)},_buildArrowPath:function(i,o){var u=Math.PI/180,d=o.project(i.latLng),y=-(i.heading-90)*u,m=this.options.headAngle/2*u,b=y+m,w=y-m,x=t.point(d.x-this.options.pixelSize*Math.cos(b),d.y+this.options.pixelSize*Math.sin(b)),C=t.point(d.x-this.options.pixelSize*Math.cos(w),d.y+this.options.pixelSize*Math.sin(w));return[o.unproject(x),i.latLng,o.unproject(C)]}}),t.Symbol.arrowHead=function(a){return new t.Symbol.ArrowHead(a)},t.Symbol.Marker=t.Class.extend({options:{markerOptions:{},rotate:!1},initialize:function(i){t.Util.setOptions(this,i),this.options.markerOptions.clickable=!1,this.options.markerOptions.draggable=!1},buildSymbol:function(i,o,u,d,y){return this.options.rotate&&(this.options.markerOptions.rotationAngle=i.heading+(this.options.angleCorrection||0)),t.marker(i.latLng,this.options.markerOptions)}}),t.Symbol.marker=function(a){return new t.Symbol.Marker(a)};var p=function(i){return i instanceof t.LatLng||Array.isArray(i)&&i.length===2&&typeof i[0]=="number"},P=function(i){return Array.isArray(i)&&p(i[0])};t.PolylineDecorator=t.FeatureGroup.extend({options:{patterns:[]},initialize:function(i,o){t.FeatureGroup.prototype.initialize.call(this),t.Util.setOptions(this,o),this._map=null,this._paths=this._initPaths(i),this._bounds=this._initBounds(),this._patterns=this._initPatterns(this.options.patterns)},_initPaths:function(i,o){var u=this;if(P(i)){var d=o?i.concat([i[0]]):i;return[d]}return i instanceof t.Polyline?this._initPaths(i.getLatLngs(),i instanceof t.Polygon):Array.isArray(i)?i.reduce(function(y,m){return y.concat(u._initPaths(m,o))},[]):[]},_initPatterns:function(i){return i.map(this._parsePatternDef)},setPatterns:function(i){this.options.patterns=i,this._patterns=this._initPatterns(this.options.patterns),this.redraw()},setPaths:function(i){this._paths=this._initPaths(i),this._bounds=this._initBounds(),this.redraw()},_parsePatternDef:function(i,o){return{symbolFactory:i.symbol,offset:l(i.offset),endOffset:l(i.endOffset),repeat:l(i.repeat)}},onAdd:function(i){this._map=i,this._draw(),this._map.on("moveend",this.redraw,this)},onRemove:function(i){this._map.off("moveend",this.redraw,this),this._map=null,t.FeatureGroup.prototype.onRemove.call(this,i)},_initBounds:function(){var i=this._paths.reduce(function(o,u){return o.concat(u)},[]);return t.latLngBounds(i)},getBounds:function(){return this._bounds},_buildSymbols:function(i,o,u){var d=this;return u.map(function(y,m){return o.buildSymbol(y,i,d._map,m,u.length)})},_getDirectionPoints:function(i,o){var u=this;if(i.length<2)return[];var d=i.map(function(y){return u._map.project(y)});return g(d,o).map(function(y){return{latLng:u._map.unproject(t.point(y.pt)),heading:y.heading}})},redraw:function(){this._map&&(this.clearLayers(),this._draw())},_getPatternLayers:function(i){var o=this,u=this._map.getBounds().pad(.1);return this._paths.map(function(d){var y=o._getDirectionPoints(d,i).filter(function(m){return u.contains(m.latLng)});return t.featureGroup(o._buildSymbols(d,i.symbolFactory,y))})},_draw:function(){var i=this;this._patterns.map(function(o){return i._getPatternLayers(o)}).forEach(function(o){i.addLayer(t.featureGroup(o))})}}),t.polylineDecorator=function(a,i){return new t.PolylineDecorator(a,i)}})})(),function(){var e=L.Marker.prototype._initIcon,s=L.Marker.prototype._setPos,t=L.DomUtil.TRANSFORM==="msTransform";L.Marker.addInitHook(function(){var n=this.options.icon&&this.options.icon.options,r=n&&this.options.icon.options.iconAnchor;r&&(r=r[0]+"px "+r[1]+"px"),this.options.rotationOrigin=this.options.rotationOrigin||r||"center bottom",this.options.rotationAngle=this.options.rotationAngle||0,this.on("drag",function(h){h.target._applyRotation()})}),L.Marker.include({_initIcon:function(){e.call(this)},_setPos:function(n){s.call(this,n),this._applyRotation()},_applyRotation:function(){this.options.rotationAngle&&(this._icon.style[L.DomUtil.TRANSFORM+"Origin"]=this.options.rotationOrigin,t?this._icon.style[L.DomUtil.TRANSFORM]="rotate("+this.options.rotationAngle+"deg)":this._icon.style[L.DomUtil.TRANSFORM]+=" rotateZ("+this.options.rotationAngle+"deg)")},setRotationAngle:function(n){return this.options.rotationAngle=n,this.update(),this},setRotationOrigin:function(n){return this.options.rotationOrigin=n,this.update(),this}})}(),M.TrackPlayer=class{constructor(e,s={}){var n,r,h,l,c,f,g,v,p,P;let t=M.polyline(e)._latlngs;this.track=O(t.map(({lng:a,lat:i})=>[a,i])),this.distanceSlice=[0],this.track.geometry.coordinates.forEach((a,i,o)=>{if(i!==0){let u=O(o.slice(0,i+1));this.distanceSlice.push(Z(u))}}),this.distance=Z(this.track),this.addedToMap=!1,this.options={speed:(n=s.speed)!=null?n:600,weight:(r=s.weight)!=null?r:8,markerIcon:s.markerIcon,polylineDecoratorOptions:(h=s.polylineDecoratorOptions)!=null?h:{patterns:[{offset:30,repeat:60,symbol:M.Symbol.arrowHead({pixelSize:5,headAngle:75,polygon:!1,pathOptions:{stroke:!0,weight:3,color:"#fff"}})}]},passedLineColor:(l=s.passedLineColor)!=null?l:"#0000ff",notPassedLineColor:(c=s.notPassedLineColor)!=null?c:"#ff0000",panTo:(f=s.panTo)!=null?f:!0,markerRotationOrigin:(g=s.markerRotationOrigin)!=null?g:"center",markerRotationOffset:(v=s.markerRotationOffset)!=null?v:0,markerRotation:(p=s.markerRotation)!=null?p:!0,progress:(P=s.progress)!=null?P:0},this.initProgress=s.progress,this.isPaused=!0,this.walkedDistance=0,this.walkedDistanceTemp=0,this.trackIndex=0,this.listenedEvents={start:[],pause:[],finished:[],progressCallback:[]}}addTo(e){if(this.addedToMap)return;if(this.map=e,this.addedToMap=!0,this.options.markerIcon){let t=this.track.geometry.coordinates[0];if(this.marker=M.marker([t[1],t[0]],{icon:this.options.markerIcon}).addTo(this.map),this.options.markerRotation){let n=this.track.geometry.coordinates;this.marker.setRotationAngle(D(n[0],n[1])/2+this.options.markerRotationOffset/2),this.marker.setRotationOrigin(this.options.markerRotationOrigin)}}let s=this.track.geometry.coordinates.map(([t,n])=>[n,t]);return this.notPassedLine=M.polyline(s,{weight:this.options.weight,color:this.options.notPassedLineColor}).addTo(this.map),this.passedLine=M.polyline([],{weight:this.options.weight,color:this.options.passedLineColor}).addTo(this.map),this.polylineDecorator=M.polylineDecorator(s,this.options.polylineDecoratorOptions).addTo(this.map),this.initProgress&&this.setProgress(this.initProgress),this}remove(){this.addedToMap&&(this.addedToMap=!1,this.polylineDecorator.remove(),this.polylineDecorator=null,this.notPassedLine.remove(),this.notPassedLine=null,this.passedLine.remove(),this.passedLine=null,this.marker&&(this.marker.remove(),this.marker=null),this.finished=!1,this.startTimestamp=0,this.pauseTimestamp=0,this.walkedDistanceTemp=0,this.walkedDistance=0,this.trackIndex=0,this.isPaused=!0,this.options.progress=this.initProgress)}start(){!this.isPaused&&!this.finished||!this.addedToMap||((this.options.progress===0||this.options.progress===1)&&(this.startTimestamp=0,this.pauseTimestamp=0,this.walkedDistanceTemp=0,this.walkedDistance=0),this.isPaused=!1,this.finished=!1,this.pauseTimestamp&&this.startTimestamp&&(this.startTimestamp=this.startTimestamp+(Date.now()-this.pauseTimestamp)),this.startAction(),this.listenedEvents.start.forEach(e=>e()),this.initProgress&&this.setProgress(this.initProgress))}pause(){this.isPaused||this.finished||(this.pauseTimestamp=Date.now(),this.isPaused=!0,this.listenedEvents.pause.forEach(e=>e()))}startAction(){let e=this.distance,s=t=>{if(t&&this.addedToMap){let n=e/this.options.speed*3600*1e3;this.startTimestamp||(this.startTimestamp=t);let r=t-this.startTimestamp;this.walkedDistance=e*(r/n)+this.walkedDistanceTemp,this.playAction()}!this.isPaused&&!this.finished&&requestAnimationFrame(s)};s()}playAction(e=!1){if(this.isPaused&&!e)return;let s=this.distance;this.trackIndex=this.distanceSlice.findIndex((r,h,l)=>{var c;return this.walkedDistance>=r&&this.walkedDistance<((c=l[h+1])!=null?c:1/0)});let[t,n]=st(this.track,this.walkedDistance).geometry.coordinates;if(this.markerPoint=[n,t],this.options.panTo&&this.map.panTo(this.markerPoint,{animate:!1}),this.marker&&this.marker.setLatLng(this.markerPoint),this.walkedDistance>=s)this.notPassedLine.setLatLngs([]);else{let r=J(this.track,this.walkedDistance);this.notPassedLine.setLatLngs(r.geometry.coordinates.map(([h,l])=>[l,h]))}if(this.walkedDistance>0){let r=J(this.track,0,this.walkedDistance);this.passedLine.setLatLngs(r.geometry.coordinates.map(([h,l])=>[l,h]))}else this.passedLine.setLatLngs([]);if(this.polylineDecorator.setPaths([...this.passedLine.getLatLngs(),...this.notPassedLine.getLatLngs()]),this.walkedDistance<s&&this.options.markerRotation&&this.marker){let r=0;r=D(S([t,n]),S(this.track.geometry.coordinates[this.trackIndex+1])),this.marker.setRotationAngle(r/2+this.options.markerRotationOffset/2)}if(this.options.progress=Math.min(1,this.walkedDistance/s),this.listenedEvents.progressCallback.forEach(r=>r(this.options.progress,M.latLng(...this.markerPoint),this.trackIndex)),this.walkedDistance>=s&&(this.walkedDistance=s,this.finished=!0,this.listenedEvents.finished.forEach(r=>r()),this.options.markerRotation&&this.marker)){let r=this.track.geometry.coordinates,h=D(S(r.at(-2)),S(r.at(-1)));this.marker.setRotationAngle(h/2+this.options.markerRotationOffset/2)}}setSpeedAction(e){this.options.speed=e,this.walkedDistanceTemp=this.walkedDistance,this.startTimestamp=0}setSpeed(e,s=20){return K(this,null,function*(){s&&(clearTimeout(this.setSpeedTimeout),yield new Promise(t=>{this.setSpeedTimeout=setTimeout(t,s)})),this.setSpeedAction(e)})}setProgress(e){this.addedToMap&&(this.options.progress==1&&e==1||this.options.progress==0&&e==0||(this.options.progress=e,this.walkedDistanceTemp=this.distance*e,this.startTimestamp=0,(this.isPaused||this.finished)&&(this.walkedDistance=this.walkedDistanceTemp,this.isPaused?this.playAction(!0):(this.finished=!1,this.isPaused=!1,this.startAction()))))}on(e,s){switch(e){case"start":this.listenedEvents.start.push(s);break;case"pause":this.listenedEvents.pause.push(s);break;case"finished":this.listenedEvents.finished.push(s);break;case"progress":this.listenedEvents.progressCallback.push(s);break}}off(e,s){if(!s){this.listenedEvents[e]=[];return}switch(e){case"start":this.listenedEvents.start=this.listenedEvents.start.filter(t=>t!==s);break;case"pause":this.listenedEvents.pause=this.listenedEvents.pause.filter(t=>t!==s);break;case"finished":this.listenedEvents.finished=this.listenedEvents.finished.filter(t=>t!==s);break;case"progress":this.listenedEvents.progressCallback=this.listenedEvents.progressCallback.filter(t=>t!==s);break}}}});
