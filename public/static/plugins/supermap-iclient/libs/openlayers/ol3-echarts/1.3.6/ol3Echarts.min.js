/*!
 * author: FDD <<EMAIL>> 
 * ol3-echarts v1.3.6
 * build-time: 2018-12-22 14:19
 * LICENSE: MIT
 * (c) 2017-2018 https://sakitam-fdd.github.io/ol3Echarts
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("echarts"),require("openlayers")):"function"==typeof define&&define.amd?define(["echarts","openlayers"],e):t.ol3Echarts=e(t.echarts,t.ol)}(this,function(a,h){"use strict";a=a&&a.hasOwnProperty("default")?a.default:a,h=h&&h.hasOwnProperty("default")?h.default:h;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s=function(t){var e=void 0===t?"undefined":n(t);return null!==t&&("object"===e||"function"===e)},p=function t(e,n){for(var o in n)s(n[o])&&s(e[o])?t(e[o],n[o]):e[o]=n[o];return e},c=function(t){var e,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:document;return e=void 0,n&&/^#([\w-]+)$/.test(t)?(e=n.getElementById(RegExp.$1))?[e]:[]:Array.prototype.slice.call(/^\.([\w-]+)$/.test(t)?n.getElementsByClassName(RegExp.$1):/^[\w-]+$/.test(t)?n.getElementsByTagName(t):n.querySelectorAll(t))},t=function(t,e,n){if(t&&e){if(t.map&&t.map===Array.prototype.map)return t.map(e,n);for(var o=[],r=0,i=t.length;r<i;r++)o.push(e.call(n,t[r],r,t));return o}},o=function(t,e){var n=Array.prototype.slice.call(arguments,2);return function(){return t.apply(e,n.concat(Array.prototype.slice.call(arguments)))}},u=function(t,e,n){for(var o=[[],e[0],e[1]],r=o[0],i=o[1],s=o[2],a=0;a<t.length;a+=2){var h=t.charCodeAt(a)-64,p=t.charCodeAt(a+1)-64;h=h>>1^-(1&h),p=p>>1^-(1&p),i=h+=i,s=p+=s,r.push([h/n,p/n])}return r};var d=function(i){var s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=function(){this._mapOffset=[0,0],this.dimensions=["lng","lat"],this.projCode_=this._getProjectionCode()};return n.dimensions=n.prototype.dimensions=["lng","lat"],n.prototype.getZoom=function(){return i.getView().getZoom()},n.prototype.setZoom=function(t){return i.getView().setZoom(t)},n.prototype.getViewRectAfterRoam=function(){return this.getViewRect().clone()},n.prototype.setMapOffset=function(t){this._mapOffset=t},n.prototype.dataToPoint=function(t){t&&Array.isArray(t)&&0<t.length&&(t=t.map(function(t){return"string"==typeof t&&(t=Number(t)),t}));var e=s.source||"EPSG:4326",n=s.destination||this.projCode_,o=i.getPixelFromCoordinate(h.proj.transform(t,e,n)),r=this._mapOffset;return[o[0]-r[0],o[1]-r[1]]},n.prototype._getProjectionCode=function(){return i?i.getView()&&i.getView().getProjection().getCode():"EPSG:3857"},n.prototype.pointToData=function(t){var e=this._mapOffset;return i.getCoordinateFromPixel([t[0]+e[0],t[1]+e[1]])},n.prototype.getViewRect=function(){var t=i.getSize();return new a.graphic.BoundingRect(0,0,t[0],t[1])},n.prototype.getRoamTransform=function(){return a.matrix.create()},n.prototype.prepareCustoms=function(t){var e=this.getViewRect();return{coordSys:{type:"openlayers",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:o(this.dataToPoint,this),size:o(n.dataToCoordSize,this)}}},n.dataToCoordSize=function(i,s){return s=s||[0,0],t([0,1],function(t){var e=s[t],n=i[t]/2,o=[],r=[];return o[t]=e-n,r[t]=e+n,o[1-t]=r[1-t]=s[1-t],Math.abs(this.dataToPoint(o)[t]-this.dataToPoint(r)[t])},this)},n.create=function(t,e){t.eachSeries(function(t){"openlayers"===t.get("coordinateSystem")&&(t.coordinateSystem=new n(i))})},n},l=Object.freeze({pie:function(t,e,n){return e.center=n.dataToPoint(e.coordinates),e},bar:function(o,t,r){return s(o.grid)&&!Array.isArray(o.grid)?console.log(o):Array.isArray(o.grid)&&(o.grid=o.grid.map(function(t,e){var n=r.dataToPoint(o.series[e].coordinates);return t.left=n[0]-parseFloat(t.width)/2,t.top=n[1]-parseFloat(t.height)/2,t})),t}}),f={forcedRerender:!1,forcedPrecomposeRerender:!1,hideOnZooming:!1,hideOnMoving:!1,hideOnRotating:!1,convertTypes:["pie","line","bar"]},e=function(r){function i(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=arguments[2];!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,r.call(this));return o.$options=p(f,e),o.$chartOptions=t,o.$chart=null,o.$Map=null,o._isRegistered=!1,o._incremental=[],o._coordinateSystem=null,n&&o.appendTo(n),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,r),i.prototype.appendTo=function(t){if(!(t&&t instanceof h.Map))throw new Error("not map object");this.$Map=t,this.$Map.once("postrender",this.render,this),this.$Map.renderSync(),this._unRegisterEvents(),this._registerEvents()},i.prototype.getChartOptions=function(){return this.$chartOptions},i.prototype.setChartOptions=function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return this.$chartOptions=t,this.$Map.once("postrender",this.render,this),this.$Map.renderSync(),this},i.prototype.appendData=function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];return t&&(e&&(this._incremental=function(t,e){for(var n=0,o=void 0,r=t.length;n<r;n++)t[n].seriesIndex===e.seriesIndex&&(o=n);return void 0===o?t.push(e):t[o]=e,t}(this._incremental,{data:t.data,seriesIndex:t.seriesIndex})),this.$chart.appendData({data:t.data.copyWithin(),seriesIndex:t.seriesIndex})),this},i.prototype.clear=function(){this._incremental=[],this.$chart.clear()},i.prototype.getMap=function(){return this.$Map},i.prototype._isVisible=function(){return this.$container&&""===this.$container.style.display},i.prototype.show=function(){this.$container&&(this.$container.style.display="")},i.prototype.hide=function(){this.$container&&(this.$container.style.display="none")},i.prototype.remove=function(){this.$chart.clear(),this.$chart.dispose(),this._unRegisterEvents(),this._incremental=[],delete this.$chart,delete this.$Map,this.$container.parentNode.removeChild(this.$container)},i.prototype.showLoading=function(){this.$chart&&this.$chart.showLoading()},i.prototype.hideLoading=function(){this.$chart&&this.$chart.hideLoading()},i.prototype._createLayerContainer=function(t,e){var n=t.getViewport(),o=this.$container=document.createElement("div");o.style.position="absolute",o.style.top="0px",o.style.left="0px",o.style.right="0px",o.style.bottom="0px";var r=c(e.target,n);if(r&&r[0]&&r[0]instanceof Element)r[0].appendChild(o);else{var i=c(".ol-overlaycontainer",n);i&&i[0]&&i[0]instanceof Element?i[0].appendChild(o):n.appendChild(o)}},i.prototype._resizeContainer=function(){var t=this.getMap().getSize();this.$container.style.height=t[1]+"px",this.$container.style.width=t[0]+"px"},i.prototype._clearAndRedraw=function(){if(this.$chart&&(!this.$container||"none"!==this.$container.style.display)&&(this.dispatchEvent({type:"redraw",source:this}),this.$options.forcedRerender&&this.$chart.clear(),this.$chart.resize(),this.$chartOptions&&(this._registerMap(),this.$chart.setOption(this.reConverData(this.$chartOptions),!1),this._incremental&&0<this._incremental.length)))for(var t=0;t<this._incremental.length;t++)this.appendData(this._incremental[t],!1)},i.prototype.onResize=function(){this._resizeContainer(),this._clearAndRedraw(),this.dispatchEvent({type:"change:size",source:this})},i.prototype.onZoomEnd=function(){this.$options.hideOnZooming&&this.show(),this._clearAndRedraw(),this.dispatchEvent({type:"zoomend",source:this})},i.prototype.onDragRotateEnd=function(){this.$options.hideOnRotating&&this.show(),this._clearAndRedraw(),this.dispatchEvent({type:"change:rotation",source:this})},i.prototype.onMoveStart=function(){this.$options.hideOnMoving&&this.hide(),this.dispatchEvent({type:"movestart",source:this})},i.prototype.onMoveEnd=function(){this.$options.hideOnMoving&&this.show(),this._clearAndRedraw(),this.dispatchEvent({type:"moveend",source:this})},i.prototype.onCenterChange=function(t){this._clearAndRedraw(),this.dispatchEvent({type:"change:center",source:this})},i.prototype._registerEvents=function(){var t=this.$Map,e=t.getView();this.$options.forcedPrecomposeRerender&&t.on("precompose",this.reRender,this),t.on("change:size",this.onResize,this),e.on("change:resolution",this.onZoomEnd,this),e.on("change:center",this.onCenterChange,this),e.on("change:rotation",this.onDragRotateEnd,this),t.on("movestart",this.onMoveStart,this),t.on("moveend",this.onMoveEnd,this)},i.prototype._unRegisterEvents=function(){var t=this.$Map,e=t.getView();t.un("change:size",this.onResize,this),this.$options.forcedPrecomposeRerender&&t.un("precompose",this.reRender,this),e.un("change:resolution",this.onZoomEnd,this),e.un("change:center",this.onCenterChange,this),e.un("change:rotation",this.onDragRotateEnd,this),t.un("movestart",this.onMoveStart,this),t.un("moveend",this.onMoveEnd,this)},i.prototype._registerMap=function(){this._isRegistered||(a.registerCoordinateSystem("openlayers",d(this.getMap(),this.$options)),this._isRegistered=!0);var t=this.$chartOptions.series;if(t&&s(t))for(var e=t.length-1;0<=e;e--)-1<this.$options.convertTypes.indexOf(t[e].type)||(t[e].coordinateSystem="openlayers"),t[e].animation=!1},i.prototype.reConverData=function(t){var e=t.series;if(e&&0<e.length){if(!this._coordinateSystem){var n=d(this.getMap(),this.$options);this._coordinateSystem=new n}if(e&&s(e))for(var o=e.length-1;0<=o;o--)-1<this.$options.convertTypes.indexOf(e[o].type)&&e[o]&&e[o].hasOwnProperty("coordinates")&&(e[o]=l[e[o].type](t,e[o],this._coordinateSystem))}return t},i.prototype.render=function(){this.$container||(this._createLayerContainer(this.$Map,this.$options),this._resizeContainer()),this.$chart?this._isVisible()&&(this.$chart.resize(),this.reRender()):(this.$chart=a.init(this.$container),this.$chartOptions&&(this._registerMap(),this.$chart.setOption(this.reConverData(this.$chartOptions),!1)))},i.prototype.reRender=function(){this._clearAndRedraw()},i}(h.Object);return e.getTarget=c,e.merge=p,e.map=t,e.bind=o,e.formatGeoJSON=function(t){var e=function(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,o=0;o<n.length;o++)for(var r=n[o].geometry,i=[r.coordinates,r.encodeOffsets],s=i[0],a=i[1],h=0;h<s.length;h++){var p=s[h];if("Polygon"===r.type)s[h]=u(p,a[h],e);else if("MultiPolygon"===r.type)for(var c=0;c<p.length;c++){var d=p[c];p[c]=u(d,a[h][c],e)}}return t.UTF8Encoding=!1,t}(t);return{type:"FeatureCollection",crs:{},features:a.util.map(a.util.filter(e.features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,o=n.coordinates,r=[];return"Polygon"===n.type&&r.push(o[0]),"MultiPolygon"===n.type&&a.util.each(o,function(t){t[0]&&r.push(t[0])}),{type:"Feature",geometry:{type:"Polygon",coordinates:r},properties:e}})}},e});