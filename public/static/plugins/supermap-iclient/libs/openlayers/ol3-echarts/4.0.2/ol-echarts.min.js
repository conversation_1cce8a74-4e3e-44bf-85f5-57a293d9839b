/*!
 * author: sakitam-fdd <<EMAIL>> 
 * ol-echarts v4.0.2
 * build-time: 2025-6-5 11:2
 * LICENSE: MIT
 * (c) 2017-2025 https://sakitam-fdd.github.io/ol3Echarts
 * Copyright 2000 - 2024 SuperMap Software Co. Ltd
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("ol"),require("ol/util"),require("ol/proj"),require("echarts")):"function"==typeof define&&define.amd?define(["ol","ol/util","ol/proj","echarts"],e):(t=t||self).EChartsLayer=e(t.ol,t.ol.util,t.ol.proj,t.echarts)}(this,(function(t,e,i,n){"use strict";function r(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],o=e[0]*i[2]+e[2]*i[3],s=e[1]*i[2]+e[3]*i[3],a=e[0]*i[4]+e[2]*i[5]+e[4],h=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=o,t[3]=s,t[4]=a,t[5]=h,t}function o(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function s(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t}var a=function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},h=5e-5;function c(t){return t>h||t<-5e-5}var p=[],u=[],d=[1,0,0,1,0,0],l=Math.abs,y=function(){function t(){}var e;return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return c(this.rotation)||c(this.x)||c(this.y)||c(this.scaleX-1)||c(this.scaleY-1)||c(this.skewX)||c(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;e||t?(i=i||[1,0,0,1,0,0],e?this.getLocalTransform(i):a(i),t&&(e?r(i,t,i):function(t,e){t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5]}(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)):i&&a(i)},t.prototype._resolveGlobalScaleRatio=function(t){var e,i,n,r,o,s,a,h,c,u=this.globalScaleRatio;if(null!=u&&1!==u){this.getGlobalScale(p);var d=p[0]<0?-1:1,l=p[1]<0?-1:1,y=((p[0]-d)*u+d)/p[0]||0,f=((p[1]-l)*u+l)/p[1]||0;t[0]*=y,t[1]*=y,t[2]*=f,t[3]*=f}this.invTransform=this.invTransform||[1,0,0,1,0,0],e=this.invTransform,n=(i=t)[0],r=i[2],o=i[4],s=i[1],a=i[3],h=i[5],(c=n*a-s*r)&&(c=1/c,e[0]=a*c,e[1]=-s*c,e[2]=-r*c,e[3]=n*c,e[4]=(r*h-a*o)*c,e[5]=(s*o-n*h)*c)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),r=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(r),e=Math.sqrt(e),this.skewX=r,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(r(u,t.invTransform,e),e=u);var i=this.originX,n=this.originY;(i||n)&&(d[4]=i,d[5]=n,r(u,e,d),u[4]-=i,u[5]-=n,e=u),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&s(i,i,n),i},t.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&s(i,i,n),i},t.prototype.getLineScale=function(){var t=this.transform;return t&&l(t[0]-1)>1e-10&&l(t[3]-1)>1e-10?Math.sqrt(l(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){!function(t,e){for(var i=0;i<f.length;i++){var n=f[i];t[n]=e[n]}}(this,t)},t.getLocalTransform=function(t,e){e=e||[];var i,n,r,o,s,a,h,c,p,u,d,l=t.originX||0,y=t.originY||0,f=t.scaleX,g=t.scaleY,v=t.anchorX,m=t.anchorY,w=t.rotation||0,x=t.x,E=t.y,$=t.skewX?Math.tan(t.skewX):0,M=t.skewY?Math.tan(-t.skewY):0;if(l||y||v||m){var _=l+v,b=y+m;e[4]=-_*f-$*b*g,e[5]=-b*g-M*_*f}else e[4]=e[5]=0;return e[0]=f,e[3]=g,e[1]=M*f,e[2]=$*g,w&&(i=e,r=w,o=(n=e)[0],s=n[2],a=n[4],h=n[1],c=n[3],p=n[5],u=Math.sin(r),d=Math.cos(r),i[0]=o*d+h*u,i[1]=-o*u+h*d,i[2]=s*d+c*u,i[3]=-s*u+d*c,i[4]=d*a+u*p,i[5]=d*p-u*a),e[4]+=l+x,e[5]+=y+E,e},t.initDefaultProps=((e=t.prototype).scaleX=e.scaleY=e.globalScaleRatio=1,void(e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0)),t}(),f=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var g=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,i){t.x=e,t.y=i},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},t.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},t.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},t.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},t.lerp=function(t,e,i,n){var r=1-n;t.x=r*e.x+n*i.x,t.y=r*e.y+n*i.y},t}(),v=Math.min,m=Math.max,w=new g,x=new g,E=new g,$=new g,M=new g,_=new g,b=function(){function t(t,e,i,n){i<0&&(t+=i,i=-i),n<0&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return t.prototype.union=function(t){var e=v(t.x,this.x),i=v(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=m(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=m(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,r=[1,0,0,1,0,0];return o(r,r,[-e.x,-e.y]),function(t,e,i){var n=i[0],r=i[1];t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r}(r,r,[i,n]),o(r,r,[t.x,t.y]),r},t.prototype.intersect=function(e,i){if(!e)return!1;e instanceof t||(e=t.create(e));var n=this,r=n.x,o=n.x+n.width,s=n.y,a=n.y+n.height,h=e.x,c=e.x+e.width,p=e.y,u=e.y+e.height,d=!(o<h||c<r||a<p||u<s);if(i){var l=1/0,y=0,f=Math.abs(o-h),v=Math.abs(c-r),m=Math.abs(a-p),w=Math.abs(u-s),x=Math.min(f,v),E=Math.min(m,w);o<h||c<r?x>y&&(y=x,f<v?g.set(_,-f,0):g.set(_,v,0)):x<l&&(l=x,f<v?g.set(M,f,0):g.set(M,-v,0)),a<p||u<s?E>y&&(y=E,m<w?g.set(_,0,-m):g.set(_,0,w)):x<l&&(l=x,m<w?g.set(M,0,m):g.set(M,0,-w))}return i&&g.copy(i,d?M:_),d},t.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,i,n){if(n){if(n[1]<1e-5&&n[1]>-1e-5&&n[2]<1e-5&&n[2]>-1e-5){var r=n[0],o=n[3],s=n[4],a=n[5];return e.x=i.x*r+s,e.y=i.y*o+a,e.width=i.width*r,e.height=i.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}w.x=E.x=i.x,w.y=$.y=i.y,x.x=$.x=i.x+i.width,x.y=E.y=i.y+i.height,w.transform(n),$.transform(n),x.transform(n),E.transform(n),e.x=v(w.x,x.x,E.x,$.x),e.y=v(w.y,x.y,E.y,$.y);var h=m(w.x,x.x,E.x,$.x),c=m(w.y,x.y,E.y,$.y);e.width=h-e.x,e.height=c-e.y}else e!==i&&t.copy(e,i)},t}(),S=t=>{var e=typeof t;return null!==t&&("object"===e||"function"===e)},T=(t,e)=>(Object.keys(e).forEach((i=>{S(e[i])&&S(t[i])?T(t[i],e[i]):t[i]=e[i]})),t),C=function(t,e,...i){return function(...n){return t.apply(e,i.concat(Array.prototype.slice.call(n)))}},R=function(t,e){for(var i,n=0,r=t.length;n<r;n++)if(t[n].index===e.index){i=n;break}return void 0===i?t.push(e):t[i]=e,t},O=function(){return function t(e){return e?(e^16*Math.random()>>e/4).toString(16):([1e7]+-[1e3]+-4e3+-8e3+-1e11).replace(/[018]/g,t)}()};function V(t,e){t.forEach((t=>{e[t]&&(e[t]=e[t].bind(e))}))}function X(t){return t&&t.parentNode?t.parentNode.removeChild(t):null}function Y(t,e){var i=new MouseEvent(t,{bubbles:!0,cancelable:!0,button:e.pointerEvent.button,buttons:e.pointerEvent.buttons,clientX:e.pointerEvent.clientX,clientY:e.pointerEvent.clientY,zrX:e.pointerEvent.offsetX,zrY:e.pointerEvent.offsetY,movementX:e.pointerEvent.movementX,movementY:e.pointerEvent.movementY,relatedTarget:e.pointerEvent.relatedTarget,screenX:e.pointerEvent.screenX,screenY:e.pointerEvent.screenY,view:window});return i.zrX=e.pointerEvent.offsetX,i.zrY=e.pointerEvent.offsetY,i.event=i,i}var A=(t,e,i)=>{for(var n=[],r=[e[0],e[1]],o=r[0],s=r[1],a=0;a<t.length;a+=2){var h=t.charCodeAt(a)-64,c=t.charCodeAt(a+1)-64;h=h>>1^-(1&h),c=c>>1^-(1&c),o=h+=o,s=c+=s,n.push([h/i,c/i])}return n};var z=Object.freeze({pie:function(t,e,i){return e.center=i.dataToPoint(e.coordinates),e},bar:function(t,e,i){return S(t.grid)&&!Array.isArray(t.grid)?console.log(t):Array.isArray(t.grid)&&(t.grid=t.grid.map(((e,n)=>{var r=i.dataToPoint(t.series[n].coordinates);return e.left=r[0]-parseFloat(e.width)/2,e.top=r[1]-parseFloat(e.height)/2,e}))),e},line:function(t,e,i){return S(t.grid)&&!Array.isArray(t.grid)?console.log(t):Array.isArray(t.grid)&&(t.grid=t.grid.map(((e,n)=>{var r=i.dataToPoint(t.series[n].coordinates);return e.left=r[0]-parseFloat(e.width)/2,e.top=r[1]-parseFloat(e.height)/2,e}))),e}}),P={forcedRerender:!1,forcedPrecomposeRerender:!1,hideOnZooming:!1,hideOnMoving:!1,hideOnRotating:!1,convertTypes:["pie","line","bar"],insertFirst:!1,stopEvent:!1,polyfillEvents:function(t,e){for(var i=t.split("."),n=e.split("."),r=0;r<3;r++){var o=Number(i[r]),s=Number(n[r]);if(o>s)return 1;if(s>o)return-1;if(!isNaN(o)&&isNaN(s))return 1;if(isNaN(o)&&!isNaN(s))return-1}return 0}(e.VERSION,"6.1.1")<=0};class k extends t.Object{constructor(t,e,i){var n=Object.assign(P,e);super(n),this._options=n,this._chartOptions=t,this.set("chartOptions",t),this.$chart=null,this.$container=void 0,this._isRegistered=!1,this._initEvent=!1,this._incremental=[],this._coordinateSystem=null,this.coordinateSystemId="",this.prevVisibleState="",V(["redraw","onResize","onZoomEnd","onCenterChange","onDragRotateEnd","onMoveStart","onMoveEnd","mouseDown","mouseUp","onClick","mouseMove"],this),i&&this.setMap(i)}appendTo(t,e=!1){this.setMap(t,e)}getMap(){return this._map}setMap(e,i=!1){if(!e||!(i||e instanceof t.Map))throw new Error("not ol map object");this._map=e,this._map.once("postrender",(()=>{this.handleMapChanged()})),this._map.renderSync()}getChartOptions(){return this.get("chartOptions")}setChartOptions(t={}){return this._chartOptions=t,this.set("chartOptions",t),this.clearAndRedraw(),this}appendData(t,e=!0){return t&&(e&&(this._incremental=R(this._incremental,{index:this._incremental.length,data:t.data,seriesIndex:t.seriesIndex})),this.$chart.appendData({data:t.data.copyWithin(),seriesIndex:t.seriesIndex})),this}clear(t){t||(this._incremental=[]),this.$chart&&this.$chart.clear()}remove(){this.clear(),this.$chart&&this.$chart.dispose(),this._initEvent&&this.$container&&(this.$container&&X(this.$container),this.unBindEvent()),delete this.$chart,delete this._map}show(){this.setVisible(!0)}innerShow(){this.$container&&(this.$container.style.display=this.prevVisibleState,this.prevVisibleState="")}hide(){this.setVisible(!1)}innerHide(){this.$container&&(this.prevVisibleState=this.$container.style.display,this.$container.style.display="none")}isVisible(){return this.$container&&"none"!==this.$container.style.display}showLoading(){this.$chart&&this.$chart.showLoading()}hideLoading(){this.$chart&&this.$chart.hideLoading()}setZIndex(t){this.$container&&("number"==typeof t&&(t=String(t)),this.$container.style.zIndex=t)}getZIndex(){return this.$container&&this.$container.style.zIndex}setVisible(t){t?(this.$container&&(this.$container.style.display=""),this._chartOptions=this.getChartOptions(),this.clearAndRedraw()):(this.$container&&(this.$container.style.display="none"),this.clear(!0),this._chartOptions={},this.clearAndRedraw())}render(){!this.$chart&&this.$container?(this.$chart=n.init(this.$container),this._chartOptions&&(this.registerMap(),this.$chart.setOption(this.convertData(this._chartOptions),!1)),this.dispatchEvent({type:"load",source:this,value:this.$chart})):this.isVisible()&&this.redraw()}redraw(){this.clearAndRedraw()}updateViewSize(t){this.$container&&(this.$container.style.width=`${t[0]}px`,this.$container.style.height=`${t[1]}px`,this.$container.setAttribute("width",String(t[0])),this.$container.setAttribute("height",String(t[1])))}onResize(t){var e=this.getMap();if(e){var i=e.getSize();this.updateViewSize(i),this.clearAndRedraw(),t&&this.dispatchEvent({type:"change:size",source:this,value:i})}}onZoomEnd(){this._options.hideOnZooming&&this.innerShow();var t=this.getMap();t&&t.getView()&&(this.clearAndRedraw(),this.dispatchEvent({type:"zoomend",source:this,value:t.getView().getZoom()}))}onDragRotateEnd(){this._options.hideOnRotating&&this.innerShow();var t=this.getMap();t&&t.getView()&&(this.clearAndRedraw(),this.dispatchEvent({type:"change:rotation",source:this,value:t.getView().getRotation()}))}onMoveStart(){this._options.hideOnMoving&&this.innerHide();var t=this.getMap();t&&t.getView()&&this.dispatchEvent({type:"movestart",source:this,value:t.getView().getCenter()})}onMoveEnd(){this._options.hideOnMoving&&this.innerShow();var t=this.getMap();t&&t.getView()&&(this.clearAndRedraw(),this.dispatchEvent({type:"moveend",source:this,value:t.getView().getCenter()}))}onClick(t){this.$chart&&this.$chart.getZr().painter.getViewportRoot().dispatchEvent(Y("click",t))}mouseDown(t){this.$chart&&this.$chart.getZr().painter.getViewportRoot().dispatchEvent(Y("mousedown",t))}mouseUp(t){this.$chart&&this.$chart.getZr().painter.getViewportRoot().dispatchEvent(Y("mouseup",t))}mouseMove(t){if(this.$chart)for(var e=t.originalEvent.target;e;){if("ol-overlaycontainer-stopevent"===e.className)return void this.$chart.getZr().painter.getViewportRoot().dispatchEvent(Y("mousemove",t));e=e.parentElement}}onCenterChange(){var t=this.getMap();t&&t.getView()&&(this.clearAndRedraw(),this.dispatchEvent({type:"change:center",source:this,value:t.getView().getCenter()}))}handleMapChanged(){var t=this.getMap();if(this._initEvent&&this.$container&&(this.$container&&X(this.$container),this.unBindEvent()),this.$container||(this.createLayerContainer(),this.onResize(!1)),t){var e=this._options.stopEvent?t.getOverlayContainerStopEvent():t.getOverlayContainer();this._options.insertFirst?e.insertBefore(this.$container,e.childNodes[0]||null):e.appendChild(this.$container),this.render(),this.bindEvent(t)}}createLayerContainer(){this.$container=document.createElement("div"),this.$container.style.position="absolute",this.$container.style.top="0px",this.$container.style.left="0px",this.$container.style.right="0px",this.$container.style.bottom="0px",this.$container.style.pointerEvents="auto"}bindEvent(t){var e=t.getView();this._options.forcedPrecomposeRerender&&t.on("precompose",this.redraw),t.on("change:size",this.onResize),e.on("change:resolution",this.onZoomEnd),e.on("change:center",this.onCenterChange),e.on("change:rotation",this.onDragRotateEnd),t.on("movestart",this.onMoveStart),t.on("moveend",this.onMoveEnd),this._options.polyfillEvents&&(t.on("pointerdown",this.mouseDown),t.on("pointerup",this.mouseUp),t.on("pointermove",this.mouseMove),t.on("click",this.onClick)),this._initEvent=!0}unBindEvent(){var t=this.getMap();if(t){var e=t.getView();e&&(t.un("precompose",this.redraw),t.un("change:size",this.onResize),e.un("change:resolution",this.onZoomEnd),e.un("change:center",this.onCenterChange),e.un("change:rotation",this.onDragRotateEnd),t.un("movestart",this.onMoveStart),t.un("moveend",this.onMoveEnd),this._options.polyfillEvents&&(t.un("pointerdown",this.mouseDown),t.un("pointerup",this.mouseUp),t.un("pointermove",this.mouseMove),t.un("click",this.onClick)),this._initEvent=!1)}}clearAndRedraw(){if(this.$chart&&this.isVisible()){if(this._options.forcedRerender&&this.$chart.clear(),this.$chart.resize(),this._chartOptions&&(this.registerMap(),this.$chart.setOption(this.convertData(this._chartOptions),!1),this._incremental&&this._incremental.length>0))for(var t=0;t<this._incremental.length;t++)this.appendData(this._incremental[t],!1);this.dispatchEvent({type:"redraw",source:this})}}registerMap(){if(this._isRegistered||(this.coordinateSystemId=`openlayers_${O()}`,n.registerCoordinateSystem(this.coordinateSystemId,this.getCoordinateSystem(this._options)),this._isRegistered=!0),this._chartOptions){var t=this._chartOptions.series;if(t&&S(t)){var e=this._options.convertTypes;if(e)for(var i=t.length-1;i>=0;i--)e.indexOf(t[i].type)>-1||(t[i].coordinateSystem=this.coordinateSystemId),t[i].animation=!1}}}convertData(t){var e=t.series;if(e&&e.length>0){if(!this._coordinateSystem){var i=this.getCoordinateSystem(this._options);this._coordinateSystem=new i(this.getMap())}if(e&&S(e)){var n=this._options.convertTypes;if(n)for(var r=e.length-1;r>=0;r--)n.indexOf(e[r].type)>-1&&e[r]&&e[r].hasOwnProperty("coordinates")&&(e[r]=z[e[r].type](t,e[r],this._coordinateSystem))}}return t}getCoordinateSystem(t){var e=this.getMap(),n=this.coordinateSystemId;class r{constructor(t){this._mapOffset=[0,0],this.dimensions=["lng","lat"],this._roamTransformable=new y,this._rawTransformable=new y,this.map=t,this.dimensions=["lng","lat"],this.projCode=r.getProjectionCode(this.map)}getZoom(){return this.map.getView().getZoom()}setZoom(t){return this.map.getView().setZoom(t)}getViewRectAfterRoam(){return this.getViewRect().clone()}setMapOffset(t){this._mapOffset=t}dataToPoint(e){var n;if(e&&Array.isArray(e)&&e.length>0){n=e.map((t=>"string"==typeof t?Number(t):t));var r=t&&t.source||"EPSG:4326",o=t&&t.destination||this.projCode,s=n,a=i.get(r),h=i.get(o);a&&h&&a.getCode()!==h.getCode()&&(s=i.transform(n,r,o));var c=this.map.getPixelFromCoordinate(s),p=this._mapOffset;return[c[0]-p[0],c[1]-p[1]]}return[0,0]}pointToData(t){var e=this._mapOffset;return this.map.getCoordinateFromPixel([t[0]+e[0],t[1]+e[1]])}setViewRect(){var t=this.map.getSize();this._viewRect=new b(0,0,t[0],t[1])}getViewRect(){return this._viewRect||this.setViewRect(),this._viewRect}getRoamTransform(){return this._roamTransformable.getLocalTransform()}prepareCustoms(){var t=this.getViewRect();return{coordSys:{type:n,x:t.x,y:t.y,width:t.width,height:t.height},api:{coord:C(this.dataToPoint,this),size:C(this.dataToCoordsSize,this)}}}dataToCoordsSize(t,e=[0,0]){return[0,1].map((i=>{var n=e[i],r=[],o=[],s=t[i]/2;r[i]=n-s,o[i]=n+s,r[1-i]=e[1-i],o[1-i]=e[1-i];var a=this.dataToPoint(r)[i]-this.dataToPoint(o)[i];return Math.abs(a)}))}getTransformInfo(){var t=this._rawTransformable,e=this._roamTransformable,i=new y;return i.transform=e.transform,i.decomposeTransform(),{roam:{x:i.x,y:i.y,scaleX:i.scaleX,scaleY:i.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}}}return r.dimensions=r.prototype.dimensions||["lng","lat"],r.create=function(t){t.eachSeries((t=>{t.get("coordinateSystem")===n&&(t.coordinateSystem=new r(e))}))},r.getProjectionCode=function(t){return t?t.getView()&&t.getView().getProjection().getCode():"EPSG:3857"},r}dispatchEvent(t){return super.dispatchEvent(t)}set(t,e,i){return super.set(t,e,i)}get(t){return super.get(t)}unset(t,e){return super.unset(t,e)}on(t,e){return super.on(t,e)}un(t,e){return super.un(t,e)}}return k.formatGeoJSON=function(t){return{type:"FeatureCollection",crs:{},features:(t=>{if((t=>!t.UTF8Encoding)(t))return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var i=t.features,n=0;n<i.length;n++)for(var r=i[n].geometry,o=[r.coordinates,r.encodeOffsets],s=o[0],a=o[1],h=0;h<s.length;h++){var c=s[h];if("Polygon"===r.type)s[h]=A(c,a[h],e);else if("MultiPolygon"===r.type)for(var p=0;p<c.length;p++){var u=c[p];c[p]=A(u,a[h][p],e)}}return t.UTF8Encoding=!1,t})(t).features.filter((t=>t.geometry&&t.properties&&t.geometry.coordinates.length>0)).map((t=>{var e=t.properties,i=t.geometry,n=i.coordinates,r=[];return"Polygon"===i.type&&r.push(n[0]),"MultiPolygon"===i.type&&n.forEach((t=>{t[0]&&r.push(t[0])})),{properties:e,type:"Feature",geometry:{type:"Polygon",coordinates:r}}}))}},k.bind=C,k.merge=T,k.uuid=O,k.bindAll=V,k.arrayAdd=R,k.removeNode=X,k.isObject=S,k}));
