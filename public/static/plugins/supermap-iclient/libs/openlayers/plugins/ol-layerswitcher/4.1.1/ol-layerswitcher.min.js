/**
 * Minified by jsDelivr using Terser v5.15.1.
 * Original file: /npm/ol-layerswitcher@4.1.1/dist/ol-layerswitcher.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("ol/control/Control"),require("ol/Observable"),require("ol/layer/Group")):"function"==typeof define&&define.amd?define(["ol/control/Control","ol/Observable","ol/layer/Group"],t):e.LayerSwitcher=t(e.ol.control.Control,e.ol.Observable,e.ol.layer.Group)}(this,(function(e,t,s){"use strict";e="default"in e?e.default:e,s="default"in s?s.default:s;const i="layer-switcher-";class n extends e{constructor(e){const t=Object.assign({},e),s=document.createElement("div");super({element:s,target:t.target}),this.activationMode=t.activationMode||"mouseover",this.startActive=!0===t.startActive,this.label=void 0!==t.label?t.label:"",this.collapseLabel=void 0!==t.collapseLabel?t.collapseLabel:"»",this.tipLabel=t.tipLabel?t.tipLabel:"Legend",this.collapseTipLabel=t.collapseTipLabel?t.collapseTipLabel:"Collapse legend",this.groupSelectStyle=n.getGroupSelectStyle(t.groupSelectStyle),this.reverse=!1!==t.reverse,this.mapListeners=[],this.hiddenClassName="ol-unselectable ol-control layer-switcher",n.isTouchDevice_()&&(this.hiddenClassName+=" touch"),this.shownClassName="shown",s.className=this.hiddenClassName,this.button=document.createElement("button"),s.appendChild(this.button),this.panel=document.createElement("div"),this.panel.className="panel",s.appendChild(this.panel),n.enableTouchScroll_(this.panel),s.classList.add(i+"group-select-style-"+this.groupSelectStyle),s.classList.add(i+"activation-mode-"+this.activationMode),"click"===this.activationMode?(s.classList.add("activationModeClick"),this.button.onclick=e=>{const t=e||window.event;this.element.classList.contains(this.shownClassName)?this.hidePanel():this.showPanel(),t.preventDefault()}):(this.button.onmouseover=()=>{this.showPanel()},this.button.onclick=e=>{const t=e||window.event;this.showPanel(),t.preventDefault()},this.panel.onmouseout=e=>{this.panel.contains(e.relatedTarget)||this.hidePanel()}),this.updateButton()}setMap(e){for(let e=0;e<this.mapListeners.length;e++)t.unByKey(this.mapListeners[e]);this.mapListeners.length=0,super.setMap(e),e&&(this.startActive?this.showPanel():this.renderPanel(),"click"!==this.activationMode&&this.mapListeners.push(e.on("pointerdown",(()=>{this.hidePanel()}))))}showPanel(){this.element.classList.contains(this.shownClassName)||(this.element.classList.add(this.shownClassName),this.updateButton(),this.renderPanel()),this.dispatchEvent("show")}hidePanel(){this.element.classList.contains(this.shownClassName)&&(this.element.classList.remove(this.shownClassName),this.updateButton()),this.dispatchEvent("hide")}updateButton(){this.element.classList.contains(this.shownClassName)?(this.button.textContent=this.collapseLabel,this.button.setAttribute("title",this.collapseTipLabel),this.button.setAttribute("aria-label",this.collapseTipLabel)):(this.button.textContent=this.label,this.button.setAttribute("title",this.tipLabel),this.button.setAttribute("aria-label",this.tipLabel))}renderPanel(){this.dispatchEvent("render"),n.renderPanel(this.getMap(),this.panel,{groupSelectStyle:this.groupSelectStyle,reverse:this.reverse}),this.dispatchEvent("rendercomplete")}static renderPanel(e,t,s){const i=new Event("render");for(t.dispatchEvent(i),(s=s||{}).groupSelectStyle=n.getGroupSelectStyle(s.groupSelectStyle),n.ensureTopVisibleBaseLayerShown(e,s.groupSelectStyle);t.firstChild;)t.removeChild(t.firstChild);n.forEachRecursive(e,(function(e,t,s){e.set("indeterminate",!1)})),"children"===s.groupSelectStyle||"none"===s.groupSelectStyle?n.setGroupVisibility(e):"group"===s.groupSelectStyle&&n.setChildVisibility(e);const o=document.createElement("ul");t.appendChild(o),n.renderLayers_(e,e,o,s,(function(i){n.renderPanel(e,t,s)}));const l=new Event("rendercomplete");t.dispatchEvent(l)}static isBaseGroup(e){if(e instanceof s){const t=e.getLayers().getArray();return t.length&&"base"===t[0].get("type")}return!1}static setGroupVisibility(e){n.getGroupsAndLayers(e,(function(e){return e instanceof s&&!e.get("combine")&&!n.isBaseGroup(e)})).reverse().forEach((function(e){const t=e.getLayersArray().map((function(e){return e.getVisible()}));t.every((function(e){return!0===e}))?(e.setVisible(!0),e.set("indeterminate",!1)):t.every((function(e){return!1===e}))?(e.setVisible(!1),e.set("indeterminate",!1)):(e.setVisible(!0),e.set("indeterminate",!0))}))}static setChildVisibility(e){n.getGroupsAndLayers(e,(function(e){return e instanceof s&&!e.get("combine")&&!n.isBaseGroup(e)})).forEach((function(e){const t=e,s=t.getVisible(),i=t.get("indeterminate");t.getLayers().getArray().forEach((function(e){e.set("indeterminate",!1),s&&!i||!e.getVisible()||e.set("indeterminate",!0)}))}))}static ensureTopVisibleBaseLayerShown(e,t){let s;n.forEachRecursive(e,(function(e,t,i){"base"===e.get("type")&&e.getVisible()&&(s=e)})),s&&n.setVisible_(e,s,!0,t)}static getGroupsAndLayers(e,t){const s=[];return t=t||function(e,t,s){return!0},n.forEachRecursive(e,(function(e,i,n){e.get("title")&&t(e,i,n)&&s.push(e)})),s}static setVisible_(e,t,i,o){t.setVisible(i),i&&"base"===t.get("type")&&n.forEachRecursive(e,(function(e,s,i){e!=t&&"base"===e.get("type")&&e.setVisible(!1)})),t instanceof s&&!t.get("combine")&&"children"===o&&t.getLayers().forEach((s=>{n.setVisible_(e,s,t.getVisible(),o)}))}static renderLayer_(e,t,o,l,a){const r=document.createElement("li"),c=t.get("title"),h=n.uuid(),d=document.createElement("label");if(t instanceof s&&!t.get("combine")){const s=n.isBaseGroup(t);if(r.classList.add("group"),s&&r.classList.add(i+"base-group"),t.get("fold")){r.classList.add(i+"fold"),r.classList.add(i+t.get("fold"));const e=document.createElement("button");e.onclick=function(e){const s=e||window.event;n.toggleFold_(t,r),s.preventDefault()},r.appendChild(e)}if(!s&&"none"!=l.groupSelectStyle){const s=document.createElement("input");s.type="checkbox",s.id=h,s.checked=t.getVisible(),s.indeterminate=t.get("indeterminate"),s.onchange=function(s){const i=s.target;n.setVisible_(e,t,i.checked,l.groupSelectStyle),a(t)},r.appendChild(s),d.htmlFor=h}d.innerHTML=c,r.appendChild(d);const o=document.createElement("ul");r.appendChild(o),n.renderLayers_(e,t,o,l,a)}else{r.className="layer";const s=document.createElement("input");"base"===t.get("type")?s.type="radio":s.type="checkbox",s.id=h,s.checked=t.get("visible"),s.indeterminate=t.get("indeterminate"),s.onchange=function(s){const i=s.target;n.setVisible_(e,t,i.checked,l.groupSelectStyle),a(t)},r.appendChild(s),d.htmlFor=h,d.innerHTML=c;const i=e.getView().getResolution();if(i>=t.getMaxResolution()||i<t.getMinResolution())d.className+=" disabled";else if(t.getMinZoom&&t.getMaxZoom){const s=e.getView().getZoom();(s<=t.getMinZoom()||s>t.getMaxZoom())&&(d.className+=" disabled")}r.appendChild(d)}return r}static renderLayers_(e,t,s,i,o){let l=t.getLayers().getArray().slice();i.reverse&&(l=l.reverse());for(let t,a=0;a<l.length;a++)t=l[a],t.get("title")&&s.appendChild(n.renderLayer_(e,t,a,i,o))}static forEachRecursive(e,t){e.getLayers().forEach((function(e,i,o){t(e,i,o),e instanceof s&&n.forEachRecursive(e,t)}))}static uuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}static enableTouchScroll_(e){if(n.isTouchDevice_()){let t=0;e.addEventListener("touchstart",(function(e){t=this.scrollTop+e.touches[0].pageY}),!1),e.addEventListener("touchmove",(function(e){this.scrollTop=t-e.touches[0].pageY}),!1)}}static isTouchDevice_(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}static toggleFold_(e,t){t.classList.remove(i+e.get("fold")),e.set("fold","open"===e.get("fold")?"close":"open"),t.classList.add(i+e.get("fold"))}static getGroupSelectStyle(e){return["none","children","group"].indexOf(e)>=0?e:"children"}}return window.ol&&window.ol.control&&(window.ol.control.LayerSwitcher=n),n}));
//# sourceMappingURL=/sm/3c614d5875123d9fb135f5e6098efe2807ce6ebee4bf0ba3c465d62b526cd5d8.map