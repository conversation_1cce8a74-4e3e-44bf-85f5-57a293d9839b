/**
 * Minified by jsDelivr using Terser v5.3.5.
 * Original file: /npm/ol-layerswitcher@3.8.3/dist/ol-layerswitcher.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("ol/control/Control"),require("ol/Observable"),require("ol/layer/Group")):"function"==typeof define&&define.amd?define(["ol/control/Control","ol/Observable","ol/layer/Group"],t):e.LayerSwitcher=t(e.ol.control.Control,e.ol.Observable,e.ol.layer.Group)}(this,(function(e,t,n){"use strict";e="default"in e?e.default:e,n="default"in n?n.default:n;var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=function e(t,n,i){null===t&&(t=Function.prototype);var r=Object.getOwnPropertyDescriptor(t,n);if(void 0===r){var o=Object.getPrototypeOf(t);return null===o?void 0:e(o,n,i)}if("value"in r)return r.value;var a=r.get;return void 0!==a?a.call(i):void 0},o="layer-switcher-",a=function(e){function a(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a);var t=Object.assign({},e),n=t.tipLabel?t.tipLabel:"Legend",i=t.collapseTipLabel?t.collapseTipLabel:"Collapse legend",r=document.createElement("div"),l=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,{element:r,target:t.target}));l.activationMode=t.activationMode||"mouseover",l.startActive=!0===t.startActive;var s=void 0!==t.label?t.label:"",c=void 0!==t.collapseLabel?t.collapseLabel:"»";l.groupSelectStyle=a.getGroupSelectStyle(t.groupSelectStyle),l.reverse=!1!==t.reverse,l.mapListeners=[],l.hiddenClassName="ol-unselectable ol-control layer-switcher",a.isTouchDevice_()&&(l.hiddenClassName+=" touch"),l.shownClassName="shown",r.className=l.hiddenClassName;var u=document.createElement("button");return u.setAttribute("title",n),u.setAttribute("aria-label",n),r.appendChild(u),l.panel=document.createElement("div"),l.panel.className="panel",r.appendChild(l.panel),a.enableTouchScroll_(l.panel),u.textContent=s,r.classList.add(o+"group-select-style-"+l.groupSelectStyle),r.classList.add(o+"activation-mode-"+l.activationMode),"click"===l.activationMode?(r.classList.add("activationModeClick"),l.startActive&&(u.textContent=c,u.setAttribute("title",i),u.setAttribute("aria-label",i)),u.onclick=function(e){var t=e||window.event;l.element.classList.contains(l.shownClassName)?(l.hidePanel(),u.textContent=s,u.setAttribute("title",n),u.setAttribute("aria-label",n)):(l.showPanel(),u.textContent=c,u.setAttribute("title",i),u.setAttribute("aria-label",i)),t.preventDefault()}):(u.onmouseover=function(){l.showPanel()},u.onclick=function(e){var t=e||window.event;l.showPanel(),t.preventDefault()},l.panel.onmouseout=function(e){l.panel.contains(e.relatedTarget)||l.hidePanel()}),l}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(a,e),i(a,[{key:"setMap",value:function(e){for(var n=this,i=0;i<this.mapListeners.length;i++)t.unByKey(this.mapListeners[i]);this.mapListeners.length=0,r(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"setMap",this).call(this,e),e&&(this.startActive?this.showPanel():this.renderPanel(),"click"!==this.activationMode&&this.mapListeners.push(e.on("pointerdown",(function(){n.hidePanel()}))))}},{key:"showPanel",value:function(){this.element.classList.contains(this.shownClassName)||(this.element.classList.add(this.shownClassName),this.renderPanel())}},{key:"hidePanel",value:function(){this.element.classList.contains(this.shownClassName)&&this.element.classList.remove(this.shownClassName)}},{key:"renderPanel",value:function(){this.dispatchEvent("render"),a.renderPanel(this.getMap(),this.panel,{groupSelectStyle:this.groupSelectStyle,reverse:this.reverse}),this.dispatchEvent("rendercomplete")}}],[{key:"renderPanel",value:function(e,t,n){var i=new Event("render");for(t.dispatchEvent(i),(n=n||{}).groupSelectStyle=a.getGroupSelectStyle(n.groupSelectStyle),a.ensureTopVisibleBaseLayerShown(e,n.groupSelectStyle);t.firstChild;)t.removeChild(t.firstChild);a.forEachRecursive(e,(function(e,t,n){e.set("indeterminate",!1)})),"children"===n.groupSelectStyle||"none"===n.groupSelectStyle?a.setGroupVisibility(e):"group"===n.groupSelectStyle&&a.setChildVisibility(e);var r=document.createElement("ul");t.appendChild(r),a.renderLayers_(e,e,r,n,(function(i){a.renderPanel(e,t,n)}));var o=new Event("rendercomplete");t.dispatchEvent(o)}},{key:"isBaseGroup",value:function(e){if(e instanceof n){var t=e.getLayers().getArray();return t.length&&"base"===t[0].get("type")}return!1}},{key:"setGroupVisibility",value:function(e){a.getGroupsAndLayers(e,(function(e){return e instanceof n&&!e.get("combine")&&!a.isBaseGroup(e)})).reverse().forEach((function(e){var t=e.getLayersArray().map((function(e){return e.getVisible()}));t.every((function(e){return!0===e}))?(e.setVisible(!0),e.set("indeterminate",!1)):t.every((function(e){return!1===e}))?(e.setVisible(!1),e.set("indeterminate",!1)):(e.setVisible(!0),e.set("indeterminate",!0))}))}},{key:"setChildVisibility",value:function(e){a.getGroupsAndLayers(e,(function(e){return e instanceof n&&!e.get("combine")&&!a.isBaseGroup(e)})).forEach((function(e){var t=e,n=t.getVisible(),i=t.get("indeterminate");t.getLayers().getArray().forEach((function(e){e.set("indeterminate",!1),n&&!i||!e.getVisible()||e.set("indeterminate",!0)}))}))}},{key:"ensureTopVisibleBaseLayerShown",value:function(e,t){var n=void 0;a.forEachRecursive(e,(function(e,t,i){"base"===e.get("type")&&e.getVisible()&&(n=e)})),n&&a.setVisible_(e,n,!0,t)}},{key:"getGroupsAndLayers",value:function(e,t){var n=[];return t=t||function(e,t,n){return!0},a.forEachRecursive(e,(function(e,i,r){e.get("title")&&t(e,i,r)&&n.push(e)})),n}},{key:"setVisible_",value:function(e,t,i,r){t.setVisible(i),i&&"base"===t.get("type")&&a.forEachRecursive(e,(function(e,n,i){e!=t&&"base"===e.get("type")&&e.setVisible(!1)})),t instanceof n&&!t.get("combine")&&"children"===r&&t.getLayers().forEach((function(n){a.setVisible_(e,n,t.getVisible(),r)}))}},{key:"renderLayer_",value:function(e,t,i,r,l){var s=document.createElement("li"),c=t.get("title"),u=a.uuid(),d=document.createElement("label");if(t instanceof n&&!t.get("combine")){var p=a.isBaseGroup(t);if(s.classList.add("group"),p&&s.classList.add(o+"base-group"),t.get("fold")){s.classList.add(o+"fold"),s.classList.add(o+t.get("fold"));var f=document.createElement("button");f.onclick=function(e){var n=e||window.event;a.toggleFold_(t,s),n.preventDefault()},s.appendChild(f)}if(!p&&"none"!=r.groupSelectStyle){var h=document.createElement("input");h.type="checkbox",h.id=u,h.checked=t.getVisible(),h.indeterminate=t.get("indeterminate"),h.onchange=function(n){var i=n.target;a.setVisible_(e,t,i.checked,r.groupSelectStyle),l(t)},s.appendChild(h),d.htmlFor=u}d.innerHTML=c,s.appendChild(d);var v=document.createElement("ul");s.appendChild(v),a.renderLayers_(e,t,v,r,l)}else{s.className="layer";var y=document.createElement("input");"base"===t.get("type")?(y.type="radio",y.name="base"):y.type="checkbox",y.id=u,y.checked=t.get("visible"),y.indeterminate=t.get("indeterminate"),y.onchange=function(n){var i=n.target;a.setVisible_(e,t,i.checked,r.groupSelectStyle),l(t)},s.appendChild(y),d.htmlFor=u,d.innerHTML=c;var g=e.getView().getResolution();(g>t.getMaxResolution()||g<t.getMinResolution())&&(d.className+=" disabled"),s.appendChild(d)}return s}},{key:"renderLayers_",value:function(e,t,n,i,r){var o=t.getLayers().getArray().slice();i.reverse&&(o=o.reverse());for(var l,s=0;s<o.length;s++)(l=o[s]).get("title")&&n.appendChild(a.renderLayer_(e,l,s,i,r))}},{key:"forEachRecursive",value:function(e,t){e.getLayers().forEach((function(e,i,r){t(e,i,r),e instanceof n&&a.forEachRecursive(e,t)}))}},{key:"uuid",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}},{key:"enableTouchScroll_",value:function(e){if(a.isTouchDevice_()){var t=0;e.addEventListener("touchstart",(function(e){t=this.scrollTop+e.touches[0].pageY}),!1),e.addEventListener("touchmove",(function(e){this.scrollTop=t-e.touches[0].pageY}),!1)}}},{key:"isTouchDevice_",value:function(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}},{key:"toggleFold_",value:function(e,t){t.classList.remove(o+e.get("fold")),e.set("fold","open"===e.get("fold")?"close":"open"),t.classList.add(o+e.get("fold"))}},{key:"getGroupSelectStyle",value:function(e){return["none","children","group"].indexOf(e)>=0?e:"children"}}]),a}(e);return window.ol&&window.ol.control&&(window.ol.control.LayerSwitcher=a),a}));
//# sourceMappingURL=/sm/a8622bbe1810de6b4b346bc2467a4728f3b93065d4771c9a5967dd59c70febde.map