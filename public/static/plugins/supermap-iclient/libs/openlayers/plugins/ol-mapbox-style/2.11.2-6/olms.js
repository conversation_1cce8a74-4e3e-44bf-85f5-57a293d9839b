/*!
 * 
 *     ol-mapbox-style
 *     Copyright 2016-present Boundless Spatial
 *     Copyright© 2000-2018 SuperMap Software Co. Ltd
 *     github: https://github.com/boundlessgeo/ol-mapbox-style
 *     github: https://github.com/SuperMap/ol-mapbox-style
 *     license: BSD 2-Clause "Simplified" License
 *     version: v2.11.2-6
 * 
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("ol/style/Style"),require("ol/style/Fill"),require("ol/style/Stroke"),require("ol/style/Icon"),require("ol/style/Text"),require("ol/style/Circle"),require("ol/geom/Point"),require("ol/proj"),require("ol/tilegrid"),require("ol/Map"),require("ol/format/GeoJSON"),require("ol/format/MVT"),require("ol/Observable"),require("ol/layer/Tile"),require("ol/layer/Vector"),require("ol/layer/VectorTile"),require("ol/source/TileJSON"),require("ol/source/Vector"),require("ol/source/VectorTile")):"function"==typeof define&&define.amd?define(["ol/style/Style","ol/style/Fill","ol/style/Stroke","ol/style/Icon","ol/style/Text","ol/style/Circle","ol/geom/Point","ol/proj","ol/tilegrid","ol/Map","ol/format/GeoJSON","ol/format/MVT","ol/Observable","ol/layer/Tile","ol/layer/Vector","ol/layer/VectorTile","ol/source/TileJSON","ol/source/Vector","ol/source/VectorTile"],t):"object"==typeof exports?exports.olms=t(require("ol/style/Style"),require("ol/style/Fill"),require("ol/style/Stroke"),require("ol/style/Icon"),require("ol/style/Text"),require("ol/style/Circle"),require("ol/geom/Point"),require("ol/proj"),require("ol/tilegrid"),require("ol/Map"),require("ol/format/GeoJSON"),require("ol/format/MVT"),require("ol/Observable"),require("ol/layer/Tile"),require("ol/layer/Vector"),require("ol/layer/VectorTile"),require("ol/source/TileJSON"),require("ol/source/Vector"),require("ol/source/VectorTile")):e.olms=t(e.ol.style.Style,e.ol.style.Fill,e.ol.style.Stroke,e.ol.style.Icon,e.ol.style.Text,e.ol.style.Circle,e.ol.geom.Point,e.ol.proj,e.ol.tilegrid,e.ol.Map,e.ol.format.GeoJSON,e.ol.format.MVT,e.ol.Observable,e.ol.layer.Tile,e.ol.layer.Vector,e.ol.layer.VectorTile,e.ol.source.TileJSON,e.ol.source.Vector,e.ol.source.VectorTile)}(window,(function(e,t,r,n,i,o,a,s,l,u,c,p,d,h,f,y,m,v,g){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=27)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=t.WEBGL_EXTENSIONS=t.WEBGL_MAX_TEXTURE_SIZE=t.HAS_WEBGL=t.DEBUG_WEBGL=void 0,t.inherits=function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e},t.getUid=function(e){return e.ol_uid||(e.ol_uid=++u)};var n=r(55),i=(t.DEBUG_WEBGL=!0,void 0),o=void 0,a=!1;if("undefined"!=typeof window&&"WebGLRenderingContext"in window)try{var s=document.createElement("CANVAS"),l=(0,n.getContext)(s,{failIfMajorPerformanceCaveat:!0});l&&(t.HAS_WEBGL=a=!0,t.WEBGL_MAX_TEXTURE_SIZE=i=l.getParameter(l.MAX_TEXTURE_SIZE),t.WEBGL_EXTENSIONS=o=l.getSupportedExtensions())}catch(e){}t.HAS_WEBGL=a,t.WEBGL_MAX_TEXTURE_SIZE=i,t.WEBGL_EXTENSIONS=o;t.VERSION="v4.6.4";var u=0},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cosh=void 0,t.clamp=function(e,t,r){return Math.min(Math.max(e,t),r)},t.roundUpToPowerOfTwo=function(e){return(0,n.assert)(0<e,29),Math.pow(2,Math.ceil(Math.log(e)/Math.LN2))},t.squaredSegmentDistance=function(e,t,r,n,o,a){var s=o-r,l=a-n;if(0!==s||0!==l){var u=((e-r)*s+(t-n)*l)/(s*s+l*l);u>1?(r=o,n=a):u>0&&(r+=s*u,n+=l*u)}return i(e,t,r,n)},t.squaredDistance=i,t.solveLinearSystem=function(e){for(var t=e.length,r=0;r<t;r++){for(var n=r,i=Math.abs(e[r][r]),o=r+1;o<t;o++){var a=Math.abs(e[o][r]);a>i&&(i=a,n=o)}if(0===i)return null;var s=e[n];e[n]=e[r],e[r]=s;for(var l=r+1;l<t;l++)for(var u=-e[l][r]/e[r][r],c=r;c<t+1;c++)r==c?e[l][c]=0:e[l][c]+=u*e[r][c]}for(var p=new Array(t),d=t-1;d>=0;d--){p[d]=e[d][t]/e[d][d];for(var h=d-1;h>=0;h--)e[h][t]-=e[h][d]*p[d]}return p},t.toDegrees=function(e){return 180*e/Math.PI},t.toRadians=function(e){return e*Math.PI/180},t.modulo=function(e,t){var r=e%t;return r*t<0?r+t:r},t.lerp=function(e,t,r){return e+r*(t-e)};var n=r(8);t.cosh="cosh"in Math?Math.cosh:function(e){var t=Math.exp(e);return(t+1/t)/2};function i(e,t,r,n){var i=r-e,o=n-t;return i*i+o*o}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={IDLE:0,LOADING:1,LOADED:2,ERROR:3,EMPTY:4,ABORT:5}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={CHANGE:"change",CLEAR:"clear",CONTEXTMENU:"contextmenu",CLICK:"click",DBLCLICK:"dblclick",DRAGENTER:"dragenter",DRAGOVER:"dragover",DROP:"drop",ERROR:"error",KEYDOWN:"keydown",KEYPRESS:"keypress",LOAD:"load",MOUSEDOWN:"mousedown",MOUSEMOVE:"mousemove",MOUSEOUT:"mouseout",MOUSEUP:"mouseup",MOUSEWHEEL:"mousewheel",MSPOINTERDOWN:"MSPointerDown",RESIZE:"resize",TOUCHSTART:"touchstart",TOUCHMOVE:"touchmove",TOUCHEND:"touchend",WHEEL:"wheel"}},function(e,t,r){"use strict";function n(e,t,r){return e+"/"+t+"/"+r}Object.defineProperty(t,"__esModule",{value:!0}),t.createOrUpdate=function(e,t,r,n){return void 0!==n?(n[0]=e,n[1]=t,n[2]=r,n):[e,t,r]},t.getKeyZXY=n,t.getKey=function(e){return n(e[0],e[1],e[2])},t.fromKey=function(e){return e.split("/").map(Number)},t.hash=function(e){return(e[1]<<e[0])+e[2]},t.quadKey=function(e){var t=e[0],r=new Array(t),n=1<<t-1,i=void 0,o=void 0;for(i=0;i<t;++i)o=48,e[1]&n&&(o+=1),e[2]&n&&(o+=2),r[i]=String.fromCharCode(o),n>>=1;return r.join("")},t.withinExtentAndZ=function(e,t){var r=e[0],n=e[1],i=e[2];if(t.getMinZoom()>r||r>t.getMaxZoom())return!1;var o=t.getExtent(),a=void 0;a=o?t.getTileRangeForExtentAndZ(o,r):t.getFullTileRange(r);return!a||a.containsXY(n,i)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.METERS_PER_UNIT=void 0,t.cloneTransform=f,t.identityTransform=y,t.addProjection=m,t.addProjections=v,t.get=g,t.getPointResolution=function(e,t,r,i){e=g(e);var o=void 0,a=e.getPointResolutionFunc();if(a)o=a(t,r);else{if(e.getUnits()==c.default.DEGREES&&!i||i==c.default.DEGREES)o=t;else{var s=k(e,g("EPSG:4326")),l=[r[0]-t/2,r[1],r[0]+t/2,r[1],r[0],r[1]-t/2,r[0],r[1]+t/2];l=s(l,l,2);var p=(0,n.getDistance)(l.slice(0,2),l.slice(2,4)),d=(0,n.getDistance)(l.slice(4,6),l.slice(6,8));o=(p+d)/2;var h=i?u.METERS_PER_UNIT[i]:e.getMetersPerUnit();void 0!==h&&(o/=h)}}return o},t.addEquivalentProjections=b,t.addEquivalentTransforms=x,t.clearAllProjections=function(){p.clear(),(0,d.clear)()},t.createProjection=function(e,t){return e?"string"==typeof e?g(e):e:g(t)},t.createTransformFromCoordinateTransform=w,t.addCoordinateTransforms=function(e,t,r,n){var i=g(e),o=g(t);(0,d.add)(i,o,w(r)),(0,d.add)(o,i,w(n))},t.fromLonLat=function(e,t){return S(e,"EPSG:4326",void 0!==t?t:"EPSG:3857")},t.toLonLat=function(e,t){var r=S(e,void 0!==t?t:"EPSG:3857","EPSG:4326"),n=r[0];(n<-180||n>180)&&(r[0]=(0,o.modulo)(n+180,360)-180);return r},t.equivalent=function(e,t){if(e===t)return!0;var r=e.getUnits()===t.getUnits();return(e.getCode()===t.getCode()||k(e,t)===f)&&r},t.getTransformFromProjections=k,t.getTransform=_,t.transform=S,t.transformExtent=function(e,t,r){var n=_(t,r);return(0,i.applyTransform)(e,n)},t.transformWithProjections=function(e,t,r){return k(t,r)(e)},t.addCommon=T;var n=r(62),i=r(6),o=r(1),a=r(65),s=r(66),l=h(r(14)),u=r(9),c=h(u),p=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}(r(67)),d=r(68);function h(e){return e&&e.__esModule?e:{default:e}}function f(e,t,r){var n=void 0;if(void 0!==t){for(var i=0,o=e.length;i<o;++i)t[i]=e[i];n=t}else n=e.slice();return n}function y(e,t,r){if(void 0!==t&&e!==t){for(var n=0,i=e.length;n<i;++n)t[n]=e[n];e=t}return e}function m(e){p.add(e.getCode(),e),(0,d.add)(e,e,f)}function v(e){e.forEach(m)}function g(e){var t=null;if(e instanceof l.default)t=e;else if("string"==typeof e){var r=e;t=p.get(r)}return t}function b(e){v(e),e.forEach((function(t){e.forEach((function(e){t!==e&&(0,d.add)(t,e,f)}))}))}function x(e,t,r,n){e.forEach((function(e){t.forEach((function(t){(0,d.add)(e,t,r),(0,d.add)(t,e,n)}))}))}function w(e){return function(t,r,n){for(var i=t.length,o=void 0!==n?n:2,a=void 0!==r?r:new Array(i),s=0;s<i;s+=o){var l=e([t[s],t[s+1]]);a[s]=l[0],a[s+1]=l[1];for(var u=o-1;u>=2;--u)a[s+u]=t[s+u]}return a}}function k(e,t){var r=e.getCode(),n=t.getCode(),i=(0,d.get)(r,n);return i||(i=y),i}function _(e,t){return k(g(e),g(t))}function S(e,t,r){return _(t,r)(e,void 0,e.length)}function T(){b(a.PROJECTIONS),b(s.PROJECTIONS),x(s.PROJECTIONS,a.PROJECTIONS,a.fromEPSG4326,a.toEPSG4326)}t.METERS_PER_UNIT=u.METERS_PER_UNIT,T()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boundingExtent=function(e){for(var t=u(),r=0,n=e.length;r<n;++r)d(t,e[r]);return t},t.buffer=function(e,t,r){return r?(r[0]=e[0]-t,r[1]=e[1]-t,r[2]=e[2]+t,r[3]=e[3]+t,r):[e[0]-t,e[1]-t,e[2]+t,e[3]+t]},t.clone=function(e,t){return t?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):e.slice()},t.closestSquaredDistanceXY=function(e,t,r){var n=void 0,i=void 0;n=t<e[0]?e[0]-t:e[2]<t?t-e[2]:0;i=r<e[1]?e[1]-r:e[3]<r?r-e[3]:0;return n*n+i*i},t.containsCoordinate=function(e,t){return s(e,t[0],t[1])},t.containsExtent=function(e,t){return e[0]<=t[0]&&t[2]<=e[2]&&e[1]<=t[1]&&t[3]<=e[3]},t.containsXY=s,t.coordinateRelationship=l,t.createEmpty=u,t.createOrUpdate=c,t.createOrUpdateEmpty=p,t.createOrUpdateFromCoordinate=function(e,t){var r=e[0],n=e[1];return c(r,n,r,n,t)},t.createOrUpdateFromCoordinates=function(e,t){return h(p(t),e)},t.createOrUpdateFromFlatCoordinates=function(e,t,r,n,i){return f(p(i),e,t,r,n)},t.createOrUpdateFromRings=function(e,t){return y(p(t),e)},t.equals=function(e,t){return e[0]==t[0]&&e[2]==t[2]&&e[1]==t[1]&&e[3]==t[3]},t.extend=function(e,t){t[0]<e[0]&&(e[0]=t[0]);t[2]>e[2]&&(e[2]=t[2]);t[1]<e[1]&&(e[1]=t[1]);t[3]>e[3]&&(e[3]=t[3]);return e},t.extendCoordinate=d,t.extendCoordinates=h,t.extendFlatCoordinates=f,t.extendRings=y,t.extendXY=m,t.forEachCorner=function(e,t,r){var n=void 0;if(n=t.call(r,g(e)))return n;if(n=t.call(r,b(e)))return n;if(n=t.call(r,_(e)))return n;if(n=t.call(r,k(e)))return n;return!1},t.getArea=v,t.getBottomLeft=g,t.getBottomRight=b,t.getCenter=function(e){return[(e[0]+e[2])/2,(e[1]+e[3])/2]},t.getCorner=function(e,t){var r=void 0;t===i.default.BOTTOM_LEFT?r=g(e):t===i.default.BOTTOM_RIGHT?r=b(e):t===i.default.TOP_LEFT?r=k(e):t===i.default.TOP_RIGHT?r=_(e):(0,n.assert)(!1,13);return r},t.getEnlargedArea=function(e,t){var r=Math.min(e[0],t[0]),n=Math.min(e[1],t[1]),i=Math.max(e[2],t[2]),o=Math.max(e[3],t[3]);return(i-r)*(o-n)},t.getForViewAndSize=function(e,t,r,n,i){var o=t*n[0]/2,a=t*n[1]/2,s=Math.cos(r),l=Math.sin(r),u=o*s,p=o*l,d=a*s,h=a*l,f=e[0],y=e[1],m=f-u+h,v=f-u-h,g=f+u-h,b=f+u+h,x=y-p-d,w=y-p+d,k=y+p+d,_=y+p-d;return c(Math.min(m,v,g,b),Math.min(x,w,k,_),Math.max(m,v,g,b),Math.max(x,w,k,_),i)},t.getHeight=x,t.getIntersectionArea=function(e,t){return v(w(e,t))},t.getIntersection=w,t.getMargin=function(e){return S(e)+x(e)},t.getSize=function(e){return[e[2]-e[0],e[3]-e[1]]},t.getTopLeft=k,t.getTopRight=_,t.getWidth=S,t.intersects=T,t.isEmpty=E,t.returnOrUpdate=function(e,t){return t?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):e},t.scaleFromCenter=function(e,t){var r=(e[2]-e[0])/2*(t-1),n=(e[3]-e[1])/2*(t-1);e[0]-=r,e[2]+=r,e[1]-=n,e[3]+=n},t.intersectsSegment=function(e,t,r){var n=!1,i=l(e,t),a=l(e,r);if(i===o.default.INTERSECTING||a===o.default.INTERSECTING)n=!0;else{var s=e[0],u=e[1],c=e[2],p=e[3],d=t[0],h=t[1],f=r[0],y=r[1],m=(y-h)/(f-d),v=void 0,g=void 0;a&o.default.ABOVE&&!(i&o.default.ABOVE)&&(n=(v=f-(y-p)/m)>=s&&v<=c),n||!(a&o.default.RIGHT)||i&o.default.RIGHT||(n=(g=y-(f-c)*m)>=u&&g<=p),n||!(a&o.default.BELOW)||i&o.default.BELOW||(n=(v=f-(y-u)/m)>=s&&v<=c),n||!(a&o.default.LEFT)||i&o.default.LEFT||(n=(g=y-(f-s)*m)>=u&&g<=p)}return n},t.applyTransform=function(e,t,r){var n=[e[0],e[1],e[0],e[3],e[2],e[1],e[2],e[3]];return t(n,n,2),function(e,t,r){var n=Math.min.apply(null,e),i=Math.min.apply(null,t),o=Math.max.apply(null,e),a=Math.max.apply(null,t);return c(n,i,o,a,r)}([n[0],n[2],n[4],n[6]],[n[1],n[3],n[5],n[7]],r)};var n=r(8),i=a(r(25)),o=a(r(64));function a(e){return e&&e.__esModule?e:{default:e}}function s(e,t,r){return e[0]<=t&&t<=e[2]&&e[1]<=r&&r<=e[3]}function l(e,t){var r=e[0],n=e[1],i=e[2],a=e[3],s=t[0],l=t[1],u=o.default.UNKNOWN;return s<r?u|=o.default.LEFT:s>i&&(u|=o.default.RIGHT),l<n?u|=o.default.BELOW:l>a&&(u|=o.default.ABOVE),u===o.default.UNKNOWN&&(u=o.default.INTERSECTING),u}function u(){return[1/0,1/0,-1/0,-1/0]}function c(e,t,r,n,i){return i?(i[0]=e,i[1]=t,i[2]=r,i[3]=n,i):[e,t,r,n]}function p(e){return c(1/0,1/0,-1/0,-1/0,e)}function d(e,t){t[0]<e[0]&&(e[0]=t[0]),t[0]>e[2]&&(e[2]=t[0]),t[1]<e[1]&&(e[1]=t[1]),t[1]>e[3]&&(e[3]=t[1])}function h(e,t){for(var r=0,n=t.length;r<n;++r)d(e,t[r]);return e}function f(e,t,r,n,i){for(;r<n;r+=i)m(e,t[r],t[r+1]);return e}function y(e,t){for(var r=0,n=t.length;r<n;++r)h(e,t[r]);return e}function m(e,t,r){e[0]=Math.min(e[0],t),e[1]=Math.min(e[1],r),e[2]=Math.max(e[2],t),e[3]=Math.max(e[3],r)}function v(e){var t=0;return E(e)||(t=S(e)*x(e)),t}function g(e){return[e[0],e[1]]}function b(e){return[e[2],e[1]]}function x(e){return e[3]-e[1]}function w(e,t,r){var n=r||[1/0,1/0,-1/0,-1/0];return T(e,t)?(e[0]>t[0]?n[0]=e[0]:n[0]=t[0],e[1]>t[1]?n[1]=e[1]:n[1]=t[1],e[2]<t[2]?n[2]=e[2]:n[2]=t[2],e[3]<t[3]?n[3]=e[3]:n[3]=t[3]):p(n),n}function k(e){return[e[0],e[3]]}function _(e){return[e[2],e[3]]}function S(e){return e[2]-e[0]}function T(e,t){return e[0]<=t[2]&&e[2]>=t[0]&&e[1]<=t[3]&&e[3]>=t[1]}function E(e){return e[2]<e[0]||e[3]<e[1]}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.bindListener=i,t.findListener=o,t.getListeners=a,t.listen=u,t.listenOnce=function(e,t,r,n){return u(e,t,r,n,!0)},t.unlisten=function(e,t,r,n){var i=a(e,t);if(i){var s=o(i,r,n,!0);s&&c(s)}},t.unlistenByKey=c,t.unlistenAll=function(e){var t=s(e);for(var r in t)l(e,r)};var n=r(11);function i(e){var t=function(t){var r=e.listener,n=e.bindTo||e.target;return e.callOnce&&c(e),r.call(n,t)};return e.boundListener=t,t}function o(e,t,r,n){for(var i=void 0,o=0,a=e.length;o<a;++o)if((i=e[o]).listener===t&&i.bindTo===r)return n&&(i.deleteIndex=o),i}function a(e,t){var r=e.ol_lm;return r?r[t]:void 0}function s(e){var t=e.ol_lm;return t||(t=e.ol_lm={}),t}function l(e,t){var r=a(e,t);if(r){for(var i=0,o=r.length;i<o;++i)e.removeEventListener(t,r[i].boundListener),(0,n.clear)(r[i]);r.length=0;var s=e.ol_lm;s&&(delete s[t],0===Object.keys(s).length&&delete e.ol_lm)}}function u(e,t,r,n,a){var l=s(e),u=l[t];u||(u=l[t]=[]);var c=o(u,r,n,!1);return c?a||(c.callOnce=!1):(c={bindTo:n,callOnce:!!a,listener:r,target:e,type:t},e.addEventListener(t,i(c)),u.push(c)),c}function c(e){if(e&&e.target){e.target.removeEventListener(e.type,e.boundListener);var t=a(e.target,e.type);if(t){var r="deleteIndex"in e?e.deleteIndex:t.indexOf(e);-1!==r&&t.splice(r,1),0===t.length&&l(e.target,e.type)}(0,n.clear)(e)}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=function(e,t){if(!e)throw new o.default(t)};var n,i=r(61),o=(n=i)&&n.__esModule?n:{default:n}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={DEGREES:"degrees",FEET:"ft",METERS:"m",PIXELS:"pixels",TILE_PIXELS:"tile-pixels",USFEET:"us-ft"},i=t.METERS_PER_UNIT={};i[n.DEGREES]=2*Math.PI*6370997/360,i[n.FEET]=.3048,i[n.METERS]=1,i[n.USFEET]=1200/3937,t.default=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TRUE=function(){return!0},t.FALSE=function(){return!1},t.UNDEFINED=function(){}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clear=function(e){for(var t in e)delete e[t]},t.getValues=function(e){var t=[];for(var r in e)t.push(e[r]);return t},t.isEmpty=function(e){var t=void 0;for(t in e)return!1;return!t};t.assign="function"==typeof Object.assign?Object.assign:function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var r=Object(e),n=1,i=arguments.length;n<i;++n){var o=arguments[n];if(null!=o)for(var a in o)o.hasOwnProperty(a)&&(r[a]=o[a])}return r}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=l(r(59)),o=r(7),a=r(10),s=l(r(13));function l(e){return e&&e.__esModule?e:{default:e}}var u=function(){i.default.call(this),this.pendingRemovals_={},this.dispatching_={},this.listeners_={}};(0,n.inherits)(u,i.default),u.prototype.addEventListener=function(e,t){var r=this.listeners_[e];r||(r=this.listeners_[e]=[]),-1===r.indexOf(t)&&r.push(t)},u.prototype.dispatchEvent=function(e){var t="string"==typeof e?new s.default(e):e,r=t.type;t.target=this;var n=this.listeners_[r],i=void 0;if(n){r in this.dispatching_||(this.dispatching_[r]=0,this.pendingRemovals_[r]=0),++this.dispatching_[r];for(var o=0,l=n.length;o<l;++o)if(!1===n[o].call(this,t)||t.propagationStopped){i=!1;break}if(--this.dispatching_[r],0===this.dispatching_[r]){var u=this.pendingRemovals_[r];for(delete this.pendingRemovals_[r];u--;)this.removeEventListener(r,a.UNDEFINED);delete this.dispatching_[r]}return i}},u.prototype.disposeInternal=function(){(0,o.unlistenAll)(this)},u.prototype.getListeners=function(e){return this.listeners_[e]},u.prototype.hasListener=function(e){return e?e in this.listeners_:Object.keys(this.listeners_).length>0},u.prototype.removeEventListener=function(e,t){var r=this.listeners_[e];if(r){var n=r.indexOf(t);e in this.pendingRemovals_?(r[n]=a.UNDEFINED,++this.pendingRemovals_[e]):(r.splice(n,1),0===r.length&&delete this.listeners_[e])}},t.default=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.stopPropagation=function(e){e.stopPropagation()},t.preventDefault=function(e){e.preventDefault()};var n=function(e){this.propagationStopped,this.type=e,this.target=null};n.prototype.preventDefault=n.prototype.stopPropagation=function(){this.propagationStopped=!0},t.default=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(9),i=function(e){this.code_=e.code,this.units_=e.units,this.extent_=void 0!==e.extent?e.extent:null,this.worldExtent_=void 0!==e.worldExtent?e.worldExtent:null,this.axisOrientation_=void 0!==e.axisOrientation?e.axisOrientation:"enu",this.global_=void 0!==e.global&&e.global,this.canWrapX_=!(!this.global_||!this.extent_),this.getPointResolutionFunc_=e.getPointResolution,this.defaultTileGrid_=null,this.metersPerUnit_=e.metersPerUnit};i.prototype.canWrapX=function(){return this.canWrapX_},i.prototype.getCode=function(){return this.code_},i.prototype.getExtent=function(){return this.extent_},i.prototype.getUnits=function(){return this.units_},i.prototype.getMetersPerUnit=function(){return this.metersPerUnit_||n.METERS_PER_UNIT[this.units_]},i.prototype.getWorldExtent=function(){return this.worldExtent_},i.prototype.getAxisOrientation=function(){return this.axisOrientation_},i.prototype.isGlobal=function(){return this.global_},i.prototype.setGlobal=function(e){this.global_=e,this.canWrapX_=!(!e||!this.extent_)},i.prototype.getDefaultTileGrid=function(){return this.defaultTileGrid_},i.prototype.setDefaultTileGrid=function(e){this.defaultTileGrid_=e},i.prototype.setExtent=function(e){this.extent_=e,this.canWrapX_=!(!this.global_||!e)},i.prototype.setWorldExtent=function(e){this.worldExtent_=e},i.prototype.setGetPointResolution=function(e){this.getPointResolutionFunc_=e},i.prototype.getPointResolutionFunc=function(){return this.getPointResolutionFunc_},t.default=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.buffer=function(e,t,r){void 0===r&&(r=[0,0]);return r[0]=e[0]+2*t,r[1]=e[1]+2*t,r},t.hasArea=function(e){return e[0]>0&&e[1]>0},t.scale=function(e,t,r){void 0===r&&(r=[0,0]);return r[0]=e[0]*t+.5|0,r[1]=e[1]*t+.5|0,r},t.toSize=function(e,t){return Array.isArray(e)?e:(void 0===t?t=[e,e]:t[0]=t[1]=e,t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getForProjection=function(e){var t=e.getDefaultTileGrid();t||(t=f(e),e.setDefaultTileGrid(t));return t},t.wrapX=function(e,t,r){var n=t[0],i=e.getTileCoordCenter(t),a=y(r);if((0,o.containsCoordinate)(a,i))return t;var s=(0,o.getWidth)(a),l=Math.ceil((a[0]-i[0])/s);return i[0]+=s*l,e.getTileCoordForCoordAndZ(i,n)},t.createForExtent=d,t.createXYZ=function(e){var t={};(0,s.assign)(t,void 0!==e?e:{}),void 0===t.extent&&(t.extent=(0,l.get)("EPSG:3857").getExtent());return t.resolutions=h(t.extent,t.maxZoom,t.tileSize),delete t.maxZoom,new c.default(t)},t.createForProjection=f,t.extentFromProjection=y;var n=r(26),i=r(15),o=r(6),a=p(r(25)),s=r(11),l=r(5),u=p(r(9)),c=p(r(80));function p(e){return e&&e.__esModule?e:{default:e}}function d(e,t,r,n){var i=void 0!==n?n:a.default.TOP_LEFT,s=h(e,t,r);return new c.default({extent:e,origin:(0,o.getCorner)(e,i),resolutions:s,tileSize:r})}function h(e,t,r){for(var a=void 0!==t?t:n.DEFAULT_MAX_ZOOM,s=(0,o.getHeight)(e),l=(0,o.getWidth)(e),u=(0,i.toSize)(void 0!==r?r:n.DEFAULT_TILE_SIZE),c=Math.max(l/u[0],s/u[1]),p=a+1,d=new Array(p),h=0;h<p;++h)d[h]=c/Math.pow(2,h);return d}function f(e,t,r,n){return d(y(e),t,r,n)}function y(e){var t=(e=(0,l.get)(e)).getExtent();if(!t){var r=180*l.METERS_PER_UNIT[u.default.DEGREES]/e.getMetersPerUnit();t=(0,o.createOrUpdate)(-r,-r,r,r)}return t}},function(e,t,r){"use strict";(function(e,r){Object.defineProperty(t,"__esModule",{value:!0});var n=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var r=[],n=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(n=(a=s.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){i=!0,o=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw o}}return r}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function a(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var l={$version:8,$root:{version:{required:!0,type:"enum",values:[8],doc:"Style specification version number. Must be 8.",example:8},name:{type:"string",doc:"A human-readable name for the style.",example:"Bright"},metadata:{type:"*",doc:"Arbitrary properties useful to track with the stylesheet, but do not influence rendering. Properties should be prefixed to avoid collisions, like 'mapbox:'."},center:{type:"array",value:"number",doc:"Default map center in longitude and latitude.  The style center will be used only if the map has not been positioned by other means (e.g. map options or user interaction).",example:[-73.9749,40.7736]},zoom:{type:"number",doc:"Default zoom level.  The style zoom will be used only if the map has not been positioned by other means (e.g. map options or user interaction).",example:12.5},bearing:{type:"number",default:0,period:360,units:"degrees",doc:'Default bearing, in degrees. The bearing is the compass direction that is "up"; for example, a bearing of 90° orients the map so that east is up. This value will be used only if the map has not been positioned by other means (e.g. map options or user interaction).',example:29},pitch:{type:"number",default:0,units:"degrees",doc:"Default pitch, in degrees. Zero is perpendicular to the surface, for a look straight down at the map, while a greater value like 60 looks ahead towards the horizon. The style pitch will be used only if the map has not been positioned by other means (e.g. map options or user interaction).",example:50},light:{type:"light",doc:"The global light source.",example:{anchor:"viewport",color:"white",intensity:.4}},terrain:{type:"terrain",doc:"A global modifier that elevates layers and markers based on a DEM data source."},fog:{type:"fog",doc:"A global effect that fades layers and markers based on their distance to the camera. The fog can be used to approximate the effect of atmosphere on distant objects and enhance the depth perception of the map when used with terrain or 3D features. Note: fog is renamed to atmosphere in the Android and iOS SDKs and planned to be changed in GL-JS v.3.0.0."},sources:{required:!0,type:"sources",doc:"Data source specifications.",example:{"mapbox-streets":{type:"vector",url:"mapbox://mapbox.mapbox-streets-v6"}}},sprite:{type:"string",doc:"A base URL for retrieving the sprite image and metadata. The extensions `.png`, `.json` and scale factor `@2x.png` will be automatically appended. This property is required if any layer uses the `background-pattern`, `fill-pattern`, `line-pattern`, `fill-extrusion-pattern`, or `icon-image` properties. The URL must be absolute, containing the [scheme, authority and path components](https://en.wikipedia.org/wiki/URL#Syntax).",example:"mapbox://sprites/mapbox/bright-v8"},glyphs:{type:"string",doc:"A URL template for loading signed-distance-field glyph sets in PBF format. The URL must include `{fontstack}` and `{range}` tokens. This property is required if any layer uses the `text-field` layout property. The URL must be absolute, containing the [scheme, authority and path components](https://en.wikipedia.org/wiki/URL#Syntax).",example:"mapbox://fonts/mapbox/{fontstack}/{range}.pbf"},transition:{type:"transition",doc:"A global transition definition to use as a default across properties, to be used for timing transitions between one value and the next when no property-specific transition is set. Collision-based symbol fading is controlled independently of the style's `transition` property.",example:{duration:300,delay:0}},projection:{type:"projection",doc:"The projection the map should be rendered in. Supported projections are Mercator, Globe, Albers, Equal Earth, Equirectangular (WGS84), Lambert conformal conic, Natural Earth, and Winkel Tripel. Terrain, sky and fog are supported by only Mercator and globe. CustomLayerInterface is not supported outside of Mercator.",example:{name:"albers",center:[-154,50],parallels:[55,65]}},layers:{required:!0,type:"array",value:"layer",doc:"Layers will be drawn in the order of this array.",example:[{id:"water",source:"mapbox-streets","source-layer":"water",type:"fill",paint:{"fill-color":"#00ffff"}}]}},sources:{"*":{type:"source",doc:"Specification of a data source. For vector and raster sources, either TileJSON or a URL to a TileJSON must be provided. For image and video sources, a URL must be provided. For GeoJSON sources, a URL or inline GeoJSON must be provided."}},source:["source_vector","source_raster","source_raster_dem","source_geojson","source_video","source_image"],source_vector:{type:{required:!0,type:"enum",values:{vector:{doc:"A vector tile source."}},doc:"The type of the source."},url:{type:"string",doc:"A URL to a TileJSON resource. Supported protocols are `http:`, `https:`, and `mapbox://<Tileset ID>`."},tiles:{type:"array",value:"string",doc:"An array of one or more tile source URLs, as in the TileJSON spec."},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129],doc:"An array containing the longitude and latitude of the southwest and northeast corners of the source's bounding box in the following order: `[sw.lng, sw.lat, ne.lng, ne.lat]`. When this property is included in a source, no tiles outside of the given bounds are requested by Mapbox GL."},scheme:{type:"enum",values:{xyz:{doc:"Slippy map tilenames scheme."},tms:{doc:"OSGeo spec scheme."}},default:"xyz",doc:"Influences the y direction of the tile coordinates. The global-mercator (aka Spherical Mercator) profile is assumed."},minzoom:{type:"number",default:0,doc:"Minimum zoom level for which tiles are available, as in the TileJSON spec."},maxzoom:{type:"number",default:22,doc:"Maximum zoom level for which tiles are available, as in the TileJSON spec. Data from tiles at the maxzoom are used when displaying the map at higher zoom levels."},attribution:{type:"string",doc:"Contains an attribution to be displayed when the map is shown to a user."},promoteId:{type:"promoteId",doc:"A property to use as a feature id (for feature state). Either a property name, or an object of the form `{<sourceLayer>: <propertyName>}`. If specified as a string for a vector tile source, the same property is used across all its source layers. If specified as an object only specified source layers will have id overriden, others will fallback to original feature id"},volatile:{type:"boolean",default:!1,doc:"A setting to determine whether a source's tiles are cached locally.","sdk-support":{"basic functionality":{android:"9.3.0",ios:"5.10.0"}}},"*":{type:"*",doc:"Other keys to configure the data source."}},source_raster:{type:{required:!0,type:"enum",values:{raster:{doc:"A raster tile source."}},doc:"The type of the source."},url:{type:"string",doc:"A URL to a TileJSON resource. Supported protocols are `http:`, `https:`, and `mapbox://<Tileset ID>`."},tiles:{type:"array",value:"string",doc:"An array of one or more tile source URLs, as in the TileJSON spec."},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129],doc:"An array containing the longitude and latitude of the southwest and northeast corners of the source's bounding box in the following order: `[sw.lng, sw.lat, ne.lng, ne.lat]`. When this property is included in a source, no tiles outside of the given bounds are requested by Mapbox GL."},minzoom:{type:"number",default:0,doc:"Minimum zoom level for which tiles are available, as in the TileJSON spec."},maxzoom:{type:"number",default:22,doc:"Maximum zoom level for which tiles are available, as in the TileJSON spec. Data from tiles at the maxzoom are used when displaying the map at higher zoom levels."},tileSize:{type:"number",default:512,units:"pixels",doc:"The minimum visual size to display tiles for this layer. Only configurable for raster layers."},scheme:{type:"enum",values:{xyz:{doc:"Slippy map tilenames scheme."},tms:{doc:"OSGeo spec scheme."}},default:"xyz",doc:"Influences the y direction of the tile coordinates. The global-mercator (aka Spherical Mercator) profile is assumed."},attribution:{type:"string",doc:"Contains an attribution to be displayed when the map is shown to a user."},volatile:{type:"boolean",default:!1,doc:"A setting to determine whether a source's tiles are cached locally.","sdk-support":{"basic functionality":{android:"9.3.0",ios:"5.10.0"}}},"*":{type:"*",doc:"Other keys to configure the data source."}},source_raster_dem:{type:{required:!0,type:"enum",values:{"raster-dem":{doc:"A RGB-encoded raster DEM source"}},doc:"The type of the source."},url:{type:"string",doc:"A URL to a TileJSON resource. Supported protocols are `http:`, `https:`, and `mapbox://<Tileset ID>`."},tiles:{type:"array",value:"string",doc:"An array of one or more tile source URLs, as in the TileJSON spec."},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129],doc:"An array containing the longitude and latitude of the southwest and northeast corners of the source's bounding box in the following order: `[sw.lng, sw.lat, ne.lng, ne.lat]`. When this property is included in a source, no tiles outside of the given bounds are requested by Mapbox GL."},minzoom:{type:"number",default:0,doc:"Minimum zoom level for which tiles are available, as in the TileJSON spec."},maxzoom:{type:"number",default:22,doc:"Maximum zoom level for which tiles are available, as in the TileJSON spec. Data from tiles at the maxzoom are used when displaying the map at higher zoom levels."},tileSize:{type:"number",default:512,units:"pixels",doc:"The minimum visual size to display tiles for this layer. Only configurable for raster layers."},attribution:{type:"string",doc:"Contains an attribution to be displayed when the map is shown to a user."},encoding:{type:"enum",values:{terrarium:{doc:"Terrarium format PNG tiles. See https://aws.amazon.com/es/public-datasets/terrain/ for more info."},mapbox:{doc:"Mapbox Terrain RGB tiles. See https://www.mapbox.com/help/access-elevation-data/#mapbox-terrain-rgb for more info."}},default:"mapbox",doc:"The encoding used by this source. Mapbox Terrain RGB is used by default"},volatile:{type:"boolean",default:!1,doc:"A setting to determine whether a source's tiles are cached locally.","sdk-support":{"basic functionality":{android:"9.3.0",ios:"5.10.0"}}},"*":{type:"*",doc:"Other keys to configure the data source."}},source_geojson:{type:{required:!0,type:"enum",values:{geojson:{doc:"A GeoJSON data source."}},doc:"The data type of the GeoJSON source."},data:{type:"*",doc:"A URL to a GeoJSON file, or inline GeoJSON."},maxzoom:{type:"number",default:18,doc:"Maximum zoom level at which to create vector tiles (higher means greater detail at high zoom levels)."},attribution:{type:"string",doc:"Contains an attribution to be displayed when the map is shown to a user."},buffer:{type:"number",default:128,maximum:512,minimum:0,doc:"Size of the tile buffer on each side. A value of 0 produces no buffer. A value of 512 produces a buffer as wide as the tile itself. Larger values produce fewer rendering artifacts near tile edges and slower performance."},filter:{type:"*",doc:"An expression for filtering features prior to processing them for rendering."},tolerance:{type:"number",default:.375,doc:"Douglas-Peucker simplification tolerance (higher means simpler geometries and faster performance)."},cluster:{type:"boolean",default:!1,doc:"If the data is a collection of point features, setting this to true clusters the points by radius into groups. Cluster groups become new `Point` features in the source with additional properties:\n * `cluster` Is `true` if the point is a cluster \n * `cluster_id` A unqiue id for the cluster to be used in conjunction with the [cluster inspection methods](https://www.mapbox.com/mapbox-gl-js/api/#geojsonsource#getclusterexpansionzoom)\n * `point_count` Number of original points grouped into this cluster\n * `point_count_abbreviated` An abbreviated point count"},clusterRadius:{type:"number",default:50,minimum:0,doc:"Radius of each cluster if clustering is enabled. A value of 512 indicates a radius equal to the width of a tile."},clusterMaxZoom:{type:"number",doc:"Max zoom on which to cluster points if clustering is enabled. Defaults to one zoom less than maxzoom (so that last zoom features are not clustered). Clusters are re-evaluated at integer zoom levels so setting clusterMaxZoom to 14 means the clusters will be displayed until z15."},clusterMinPoints:{type:"number",doc:"Minimum number of points necessary to form a cluster if clustering is enabled. Defaults to `2`."},clusterProperties:{type:"*",doc:'An object defining custom properties on the generated clusters if clustering is enabled, aggregating values from clustered points. Has the form `{"property_name": [operator, map_expression]}`. `operator` is any expression function that accepts at least 2 operands (e.g. `"+"` or `"max"`) — it accumulates the property value from clusters/points the cluster contains; `map_expression` produces the value of a single point.\n\nExample: `{"sum": ["+", ["get", "scalerank"]]}`.\n\nFor more advanced use cases, in place of `operator`, you can use a custom reduce expression that references a special `["accumulated"]` value, e.g.:\n`{"sum": [["+", ["accumulated"], ["get", "sum"]], ["get", "scalerank"]]}`'},lineMetrics:{type:"boolean",default:!1,doc:"Whether to calculate line distance metrics. This is required for line layers that specify `line-gradient` values."},generateId:{type:"boolean",default:!1,doc:"Whether to generate ids for the geojson features. When enabled, the `feature.id` property will be auto assigned based on its index in the `features` array, over-writing any previous values."},promoteId:{type:"promoteId",doc:"A property to use as a feature id (for feature state). Either a property name, or an object of the form `{<sourceLayer>: <propertyName>}`."}},source_video:{type:{required:!0,type:"enum",values:{video:{doc:"A video data source."}},doc:"The data type of the video source."},urls:{required:!0,type:"array",value:"string",doc:"URLs to video content in order of preferred format."},coordinates:{required:!0,doc:"Corners of video specified in longitude, latitude pairs.",type:"array",length:4,value:{type:"array",length:2,value:"number",doc:"A single longitude, latitude pair."}}},source_image:{type:{required:!0,type:"enum",values:{image:{doc:"An image data source."}},doc:"The data type of the image source."},url:{required:!0,type:"string",doc:"URL that points to an image."},coordinates:{required:!0,doc:"Corners of image specified in longitude, latitude pairs.",type:"array",length:4,value:{type:"array",length:2,value:"number",doc:"A single longitude, latitude pair."}}},layer:{id:{type:"string",doc:"Unique layer name.",required:!0},type:{type:"enum",values:{fill:{doc:"A filled polygon with an optional stroked border.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}}},line:{doc:"A stroked line.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}}},symbol:{doc:"An icon or a text label.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}}},circle:{doc:"A filled circle.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}}},heatmap:{doc:"A heatmap.","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"fill-extrusion":{doc:"An extruded (3D) polygon.","sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}}},raster:{doc:"Raster map textures such as satellite imagery.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}}},hillshade:{doc:"Client-side hillshading visualization based on DEM data. Currently, the implementation only supports Mapbox Terrain RGB and Mapzen Terrarium tiles.","sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},background:{doc:"The background color or pattern of the map.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}}},sky:{doc:"A spherical dome around the map that is always rendered behind all other layers.","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}}}},doc:"Rendering type of this layer.",required:!0},metadata:{type:"*",doc:"Arbitrary properties useful to track with the layer, but do not influence rendering. Properties should be prefixed to avoid collisions, like 'mapbox:'."},source:{type:"string",doc:"Name of a source description to be used for this layer. Required for all layer types except `background`."},"source-layer":{type:"string",doc:"Layer to use from a vector tile source. Required for vector tile sources; prohibited for all other source types, including GeoJSON sources."},minzoom:{type:"number",minimum:0,maximum:24,doc:"The minimum zoom level for the layer. At zoom levels less than the minzoom, the layer will be hidden."},maxzoom:{type:"number",minimum:0,maximum:24,doc:"The maximum zoom level for the layer. At zoom levels equal to or greater than the maxzoom, the layer will be hidden."},filter:{type:"filter",doc:'An expression specifying conditions on source features. Only features that match the filter are displayed. Zoom expressions in filters are only evaluated at integer zoom levels. The `["feature-state", ...]` expression is not supported in filter expressions.  The `["pitch"]` and `["distance-from-center"]` expressions are supported only for filter expressions on the symbol layer.'},layout:{type:"layout",doc:"Layout properties for the layer."},paint:{type:"paint",doc:"Default paint properties for this layer."}},layout:["layout_fill","layout_line","layout_circle","layout_heatmap","layout_fill-extrusion","layout_symbol","layout_raster","layout_hillshade","layout_background","layout_sky"],layout_background:{visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},"property-type":"constant"}},layout_sky:{visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},"property-type":"constant"}},layout_fill:{"fill-sort-key":{type:"number",doc:"Sorts features in ascending order based on this value. Features with a higher sort key will appear above features with a lower sort key.","sdk-support":{"basic functionality":{js:"1.2.0",android:"9.1.0",ios:"5.8.0",macos:"0.15.0"},"data-driven styling":{js:"1.2.0",android:"9.1.0",ios:"5.8.0",macos:"0.15.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},"property-type":"constant"}},layout_circle:{"circle-sort-key":{type:"number",doc:"Sorts features in ascending order based on this value. Features with a higher sort key will appear above features with a lower sort key.","sdk-support":{"basic functionality":{js:"1.2.0",android:"9.2.0",ios:"5.9.0",macos:"0.16.0"},"data-driven styling":{js:"1.2.0",android:"9.2.0",ios:"5.9.0",macos:"0.16.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},"property-type":"constant"}},layout_heatmap:{visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},"property-type":"constant"}},"layout_fill-extrusion":{visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},"property-type":"constant"},"fill-extrusion-edge-radius":{type:"number",private:!0,default:0,minimum:0,maximum:1,doc:"Radius of a fill extrusion edge in meters. If not zero, rounds extrusion edges for a smoother appearance.","sdk-support":{"basic functionality":{js:"v2.10.0",android:"10.7.0",ios:"10.7.0"}},"property-type":"constant"}},layout_line:{"line-cap":{type:"enum",values:{butt:{doc:"A cap with a squared-off end which is drawn to the exact endpoint of the line."},round:{doc:"A cap with a rounded end which is drawn beyond the endpoint of the line at a radius of one-half of the line's width and centered on the endpoint of the line."},square:{doc:"A cap with a squared-off end which is drawn beyond the endpoint of the line at a distance of one-half of the line's width."}},default:"butt",doc:"The display of line endings.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"2.3.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"line-join":{type:"enum",values:{bevel:{doc:"A join with a squared-off end which is drawn beyond the endpoint of the line at a distance of one-half of the line's width."},round:{doc:"A join with a rounded end which is drawn beyond the endpoint of the line at a radius of one-half of the line's width and centered on the endpoint of the line."},miter:{doc:"A join with a sharp, angled corner which is drawn with the outer sides beyond the endpoint of the path until they meet."}},default:"miter",doc:"The display of lines when joining.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.40.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"line-miter-limit":{type:"number",default:2,doc:"Used to automatically convert miter joins to bevel joins for sharp angles.",requires:[{"line-join":"miter"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-round-limit":{type:"number",default:1.05,doc:"Used to automatically convert round joins to miter joins for shallow angles.",requires:[{"line-join":"round"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-sort-key":{type:"number",doc:"Sorts features in ascending order based on this value. Features with a higher sort key will appear above features with a lower sort key.","sdk-support":{"basic functionality":{js:"1.2.0",android:"9.1.0",ios:"5.8.0",macos:"0.15.0"},"data-driven styling":{js:"1.2.0",android:"9.1.0",ios:"5.8.0",macos:"0.15.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},"property-type":"constant"}},layout_symbol:{"symbol-placement":{type:"enum",values:{point:{doc:"The label is placed at the point where the geometry is located."},line:{doc:"The label is placed along the line of the geometry. Can only be used on `LineString` and `Polygon` geometries."},"line-center":{doc:"The label is placed at the center of the line of the geometry. Can only be used on `LineString` and `Polygon` geometries. Note that a single feature in a vector tile may contain multiple line geometries."}},default:"point",doc:"Label placement relative to its geometry.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"`line-center` value":{js:"0.47.0",android:"6.4.0",ios:"4.3.0",macos:"0.10.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-spacing":{type:"number",default:250,minimum:1,units:"pixels",doc:"Distance between two symbol anchors.",requires:[{"symbol-placement":"line"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"symbol-avoid-edges":{type:"boolean",default:!1,doc:"If true, the symbols will not cross tile edges to avoid mutual collisions. Recommended in layers that don't have enough padding in the vector tile to prevent collisions, or if it is a point symbol layer placed after a line symbol layer. When using a client that supports global collision detection, like Mapbox GL JS version 0.42.0 or greater, enabling this property is not needed to prevent clipped labels at tile boundaries.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-sort-key":{type:"number",doc:"Sorts features in ascending order based on this value. Features with lower sort keys are drawn and placed first.  When `icon-allow-overlap` or `text-allow-overlap` is `false`, features with a lower sort key will have priority during placement. When `icon-allow-overlap` or `text-allow-overlap` is set to `true`, features with a higher sort key will overlap over features with a lower sort key.","sdk-support":{"basic functionality":{js:"0.53.0",android:"7.4.0",ios:"4.11.0",macos:"0.14.0"},"data-driven styling":{js:"0.53.0",android:"7.4.0",ios:"4.11.0",macos:"0.14.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"symbol-z-order":{type:"enum",values:{auto:{doc:"Sorts symbols by `symbol-sort-key` if set. Otherwise, sorts symbols by their y-position relative to the viewport if `icon-allow-overlap` or `text-allow-overlap` is set to `true` or `icon-ignore-placement` or `text-ignore-placement` is `false`."},"viewport-y":{doc:"Sorts symbols by their y-position relative to the viewport if `icon-allow-overlap` or `text-allow-overlap` is set to `true` or `icon-ignore-placement` or `text-ignore-placement` is `false`."},source:{doc:"Sorts symbols by `symbol-sort-key` if set. Otherwise, no sorting is applied; symbols are rendered in the same order as the source data."}},default:"auto",doc:"Determines whether overlapping symbols in the same layer are rendered in the order that they appear in the data source or by their y-position relative to the viewport. To control the order and prioritization of symbols otherwise, use `symbol-sort-key`.","sdk-support":{"basic functionality":{js:"0.49.0",android:"6.6.0",ios:"4.5.0",macos:"0.12.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-allow-overlap":{type:"boolean",default:!1,doc:"If true, the icon will be visible even if it collides with other previously drawn symbols.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-ignore-placement":{type:"boolean",default:!1,doc:"If true, other symbols can be visible even if they collide with the icon.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-optional":{type:"boolean",default:!1,doc:"If true, text will display without their corresponding icons when the icon collides with other symbols and the text does not.",requires:["icon-image","text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-rotation-alignment":{type:"enum",values:{map:{doc:"When `symbol-placement` is set to `point`, aligns icons east-west. When `symbol-placement` is set to `line` or `line-center`, aligns icon x-axes with the line."},viewport:{doc:"Produces icons whose x-axes are aligned with the x-axis of the viewport, regardless of the value of `symbol-placement`."},auto:{doc:"When `symbol-placement` is set to `point`, this is equivalent to `viewport`. When `symbol-placement` is set to `line` or `line-center`, this is equivalent to `map`."}},default:"auto",doc:"In combination with `symbol-placement`, determines the rotation behavior of icons.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"`auto` value":{js:"0.25.0",android:"4.2.0",ios:"3.4.0",macos:"0.3.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-size":{type:"number",default:1,minimum:0,units:"factor of the original icon size",doc:"Scales the original size of the icon by the provided factor. The new pixel size of the image will be the original pixel size multiplied by `icon-size`. 1 is the original size; 3 triples the size of the image.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.35.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-text-fit":{type:"enum",values:{none:{doc:"The icon is displayed at its intrinsic aspect ratio."},width:{doc:"The icon is scaled in the x-dimension to fit the width of the text."},height:{doc:"The icon is scaled in the y-dimension to fit the height of the text."},both:{doc:"The icon is scaled in both x- and y-dimensions."}},default:"none",doc:"Scales the icon to fit around the associated text.",requires:["icon-image","text-field"],"sdk-support":{"basic functionality":{js:"0.21.0",android:"4.2.0",ios:"3.4.0",macos:"0.2.1"},"stretchable icons":{js:"1.6.0",android:"9.2.0",ios:"5.8.0",macos:"0.15.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-text-fit-padding":{type:"array",value:"number",length:4,default:[0,0,0,0],units:"pixels",doc:"Size of the additional area added to dimensions determined by `icon-text-fit`, in clockwise order: top, right, bottom, left.",requires:["icon-image","text-field",{"icon-text-fit":["both","width","height"]}],"sdk-support":{"basic functionality":{js:"0.21.0",android:"4.2.0",ios:"3.4.0",macos:"0.2.1"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-image":{type:"resolvedImage",doc:"Name of image in sprite to use for drawing an image background.",tokens:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.35.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-rotate":{type:"number",default:0,period:360,units:"degrees",doc:"Rotates the icon clockwise.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.21.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-padding":{type:"number",default:2,minimum:0,units:"pixels",doc:"Size of the additional area around the icon bounding box used for detecting symbol collisions.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-keep-upright":{type:"boolean",default:!1,doc:"If true, the icon may be flipped to prevent it from being rendered upside-down.",requires:["icon-image",{"icon-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-offset":{type:"array",value:"number",length:2,default:[0,0],doc:"Offset distance of icon from its anchor. Positive values indicate right and down, while negative values indicate left and up. Each component is multiplied by the value of `icon-size` to obtain the final offset in pixels. When combined with `icon-rotate` the offset will be as if the rotated direction was up.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-anchor":{type:"enum",values:{center:{doc:"The center of the icon is placed closest to the anchor."},left:{doc:"The left side of the icon is placed closest to the anchor."},right:{doc:"The right side of the icon is placed closest to the anchor."},top:{doc:"The top of the icon is placed closest to the anchor."},bottom:{doc:"The bottom of the icon is placed closest to the anchor."},"top-left":{doc:"The top left corner of the icon is placed closest to the anchor."},"top-right":{doc:"The top right corner of the icon is placed closest to the anchor."},"bottom-left":{doc:"The bottom left corner of the icon is placed closest to the anchor."},"bottom-right":{doc:"The bottom right corner of the icon is placed closest to the anchor."}},default:"center",doc:"Part of the icon placed closest to the anchor.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.40.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"},"data-driven styling":{js:"0.40.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-pitch-alignment":{type:"enum",values:{map:{doc:"The icon is aligned to the plane of the map."},viewport:{doc:"The icon is aligned to the plane of the viewport."},auto:{doc:"Automatically matches the value of `icon-rotation-alignment`."}},default:"auto",doc:"Orientation of icon when map is pitched.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.39.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-pitch-alignment":{type:"enum",values:{map:{doc:"The text is aligned to the plane of the map."},viewport:{doc:"The text is aligned to the plane of the viewport."},auto:{doc:"Automatically matches the value of `text-rotation-alignment`."}},default:"auto",doc:"Orientation of text when map is pitched.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.21.0",android:"4.2.0",ios:"3.4.0",macos:"0.2.1"},"`auto` value":{js:"0.25.0",android:"4.2.0",ios:"3.4.0",macos:"0.3.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotation-alignment":{type:"enum",values:{map:{doc:"When `symbol-placement` is set to `point`, aligns text east-west. When `symbol-placement` is set to `line` or `line-center`, aligns text x-axes with the line."},viewport:{doc:"Produces glyphs whose x-axes are aligned with the x-axis of the viewport, regardless of the value of `symbol-placement`."},auto:{doc:"When `symbol-placement` is set to `point`, this is equivalent to `viewport`. When `symbol-placement` is set to `line` or `line-center`, this is equivalent to `map`."}},default:"auto",doc:"In combination with `symbol-placement`, determines the rotation behavior of the individual glyphs forming the text.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"`auto` value":{js:"0.25.0",android:"4.2.0",ios:"3.4.0",macos:"0.3.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-field":{type:"formatted",default:"",tokens:!0,doc:"Value to use for a text label. If a plain `string` is provided, it will be treated as a `formatted` with default/inherited formatting options. SDF images are not supported in formatted text and will be ignored.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-font":{type:"array",value:"string",default:["Open Sans Regular","Arial Unicode MS Regular"],doc:"Font stack to use for displaying text.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-size":{type:"number",default:16,minimum:0,units:"pixels",doc:"Font size.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.35.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-width":{type:"number",default:10,minimum:0,units:"ems",doc:"The maximum line width for text wrapping.",requires:["text-field",{"symbol-placement":["point"]}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.40.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-line-height":{type:"number",default:1.2,units:"ems",doc:"Text leading value for multi-line text.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"2.3.0",android:"10.0.0",ios:"10.0.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-letter-spacing":{type:"number",default:0,units:"ems",doc:"Text tracking amount.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.40.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-justify":{type:"enum",values:{auto:{doc:"The text is aligned towards the anchor position."},left:{doc:"The text is aligned to the left."},center:{doc:"The text is centered."},right:{doc:"The text is aligned to the right."}},default:"center",doc:"Text justification options.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.39.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"},auto:{js:"0.54.0",android:"7.4.0",ios:"4.10.0",macos:"0.14.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-radial-offset":{type:"number",units:"ems",default:0,doc:"Radial offset of text, in the direction of the symbol's anchor. Useful in combination with `text-variable-anchor`, which defaults to using the two-dimensional `text-offset` if present.","sdk-support":{"basic functionality":{js:"0.54.0",android:"7.4.0",ios:"4.10.0",macos:"0.14.0"},"data-driven styling":{js:"0.54.0",android:"7.4.0",ios:"4.10.0",macos:"0.14.0"}},requires:["text-field"],"property-type":"data-driven",expression:{interpolated:!0,parameters:["zoom","feature"]}},"text-variable-anchor":{type:"array",value:"enum",values:{center:{doc:"The center of the text is placed closest to the anchor."},left:{doc:"The left side of the text is placed closest to the anchor."},right:{doc:"The right side of the text is placed closest to the anchor."},top:{doc:"The top of the text is placed closest to the anchor."},bottom:{doc:"The bottom of the text is placed closest to the anchor."},"top-left":{doc:"The top left corner of the text is placed closest to the anchor."},"top-right":{doc:"The top right corner of the text is placed closest to the anchor."},"bottom-left":{doc:"The bottom left corner of the text is placed closest to the anchor."},"bottom-right":{doc:"The bottom right corner of the text is placed closest to the anchor."}},requires:["text-field",{"symbol-placement":["point"]}],doc:"To increase the chance of placing high-priority labels on the map, you can provide an array of `text-anchor` locations: the renderer will attempt to place the label at each location, in order, before moving onto the next label. Use `text-justify: auto` to choose justification based on anchor position. To apply an offset, use the `text-radial-offset` or the two-dimensional `text-offset`.","sdk-support":{"basic functionality":{js:"0.54.0",android:"7.4.0",ios:"4.10.0",macos:"0.14.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-anchor":{type:"enum",values:{center:{doc:"The center of the text is placed closest to the anchor."},left:{doc:"The left side of the text is placed closest to the anchor."},right:{doc:"The right side of the text is placed closest to the anchor."},top:{doc:"The top of the text is placed closest to the anchor."},bottom:{doc:"The bottom of the text is placed closest to the anchor."},"top-left":{doc:"The top left corner of the text is placed closest to the anchor."},"top-right":{doc:"The top right corner of the text is placed closest to the anchor."},"bottom-left":{doc:"The bottom left corner of the text is placed closest to the anchor."},"bottom-right":{doc:"The bottom right corner of the text is placed closest to the anchor."}},default:"center",doc:"Part of the text placed closest to the anchor.",requires:["text-field",{"!":"text-variable-anchor"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.39.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-angle":{type:"number",default:45,units:"degrees",doc:"Maximum angle change between adjacent characters.",requires:["text-field",{"symbol-placement":["line","line-center"]}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-writing-mode":{type:"array",value:"enum",values:{horizontal:{doc:"If a text's language supports horizontal writing mode, symbols would be laid out horizontally."},vertical:{doc:"If a text's language supports vertical writing mode, symbols would be laid out vertically."}},doc:"The property allows control over a symbol's orientation. Note that the property values act as a hint, so that a symbol whose language doesn’t support the provided orientation will be laid out in its natural orientation. Example: English point symbol will be rendered horizontally even if array value contains single 'vertical' enum value. For symbol with point placement, the order of elements in an array define priority order for the placement of an orientation variant. For symbol with line placement, the default text writing mode is either ['horizontal', 'vertical'] or ['vertical', 'horizontal'], the order doesn't affect the placement.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"1.3.0",android:"8.3.0",ios:"5.3.0",macos:"0.15.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotate":{type:"number",default:0,period:360,units:"degrees",doc:"Rotates the text clockwise.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.35.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-padding":{type:"number",default:2,minimum:0,units:"pixels",doc:"Size of the additional area around the text bounding box used for detecting symbol collisions.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-keep-upright":{type:"boolean",default:!0,doc:"If true, the text may be flipped vertically to prevent it from being rendered upside-down.",requires:["text-field",{"text-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-transform":{type:"enum",values:{none:{doc:"The text is not altered."},uppercase:{doc:"Forces all letters to be displayed in uppercase."},lowercase:{doc:"Forces all letters to be displayed in lowercase."}},default:"none",doc:"Specifies how to capitalize text, similar to the CSS `text-transform` property.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-offset":{type:"array",doc:"Offset distance of text from its anchor. Positive values indicate right and down, while negative values indicate left and up. If used with text-variable-anchor, input values will be taken as absolute values. Offsets along the x- and y-axis will be applied automatically based on the anchor position.",value:"number",units:"ems",length:2,default:[0,0],requires:["text-field",{"!":"text-radial-offset"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.35.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-allow-overlap":{type:"boolean",default:!1,doc:"If true, the text will be visible even if it collides with other previously drawn symbols.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-ignore-placement":{type:"boolean",default:!1,doc:"If true, other symbols can be visible even if they collide with the text.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-optional":{type:"boolean",default:!1,doc:"If true, icons will display without their corresponding text when the text collides with other symbols and the icon does not.",requires:["text-field","icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},"property-type":"constant"}},layout_raster:{visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},"property-type":"constant"}},layout_hillshade:{visibility:{type:"enum",values:{visible:{doc:"The layer is shown."},none:{doc:"The layer is not shown."}},default:"visible",doc:"Whether this layer is displayed.","sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},"property-type":"constant"}},filter:{type:"array",value:"*",doc:"A filter selects specific features from a layer."},filter_symbol:{type:"boolean",doc:'Expression which determines whether or not to display a symbol. Symbols support dynamic filtering, meaning this expression can use the `["pitch"]` and `["distance-from-center"]` expressions to reference the current state of the view.',default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature","pitch","distance-from-center"]}},filter_fill:{type:"boolean",doc:'Expression which determines whether or not to display a polygon. Fill layer does NOT support dynamic filtering, meaning this expression can NOT use the `["pitch"]` and `["distance-from-center"]` expressions to reference the current state of the view.',default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature"]}},filter_line:{type:"boolean",doc:'Expression which determines whether or not to display a Polygon or LineString. Line layer does NOT support dynamic filtering, meaning this expression can NOT use the `["pitch"]` and `["distance-from-center"]` expressions to reference the current state of the view.',default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature"]}},filter_circle:{type:"boolean",doc:'Expression which determines whether or not to display a circle. Circle layer does NOT support dynamic filtering, meaning this expression can NOT use the `["pitch"]` and `["distance-from-center"]` expressions to reference the current state of the view.',default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature"]}},"filter_fill-extrusion":{type:"boolean",doc:'Expression which determines whether or not to display a Polygon. Fill-extrusion layer does NOT support dynamic filtering, meaning this expression can NOT use the `["pitch"]` and `["distance-from-center"]` expressions to reference the current state of the view.',default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature"]}},filter_heatmap:{type:"boolean",doc:'Expression used to determine whether a point is being displayed or not. Heatmap layer does NOT support dynamic filtering, meaning this expression can NOT use the `["pitch"]` and `["distance-from-center"]` expressions to reference the current state of the view.',default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature"]}},filter_operator:{type:"enum",values:{"==":{doc:'`["==", key, value]` equality: `feature[key] = value`'},"!=":{doc:'`["!=", key, value]` inequality: `feature[key] ≠ value`'},">":{doc:'`[">", key, value]` greater than: `feature[key] > value`'},">=":{doc:'`[">=", key, value]` greater than or equal: `feature[key] ≥ value`'},"<":{doc:'`["<", key, value]` less than: `feature[key] < value`'},"<=":{doc:'`["<=", key, value]` less than or equal: `feature[key] ≤ value`'},in:{doc:'`["in", key, v0, ..., vn]` set inclusion: `feature[key] ∈ {v0, ..., vn}`'},"!in":{doc:'`["!in", key, v0, ..., vn]` set exclusion: `feature[key] ∉ {v0, ..., vn}`'},all:{doc:'`["all", f0, ..., fn]` logical `AND`: `f0 ∧ ... ∧ fn`'},any:{doc:'`["any", f0, ..., fn]` logical `OR`: `f0 ∨ ... ∨ fn`'},none:{doc:'`["none", f0, ..., fn]` logical `NOR`: `¬f0 ∧ ... ∧ ¬fn`'},has:{doc:'`["has", key]` `feature[key]` exists'},"!has":{doc:'`["!has", key]` `feature[key]` does not exist'},within:{doc:'`["within", object]` feature geometry is within object geometry'}},doc:"The filter operator."},geometry_type:{type:"enum",values:{Point:{doc:"Filter to point geometries."},LineString:{doc:"Filter to line geometries."},Polygon:{doc:"Filter to polygon geometries."}},doc:"The geometry type for the filter to select."},function:{expression:{type:"expression",doc:"An expression."},stops:{type:"array",doc:"An array of stops.",value:"function_stop"},base:{type:"number",default:1,minimum:0,doc:"The exponential base of the interpolation curve. It controls the rate at which the result increases. Higher values make the result increase more towards the high end of the range. With `1` the stops are interpolated linearly."},property:{type:"string",doc:"The name of a feature property to use as the function input.",default:"$zoom"},type:{type:"enum",values:{identity:{doc:"Return the input value as the output value."},exponential:{doc:"Generate an output by interpolating between stops just less than and just greater than the function input."},interval:{doc:"Return the output value of the stop just less than the function input."},categorical:{doc:"Return the output value of the stop equal to the function input."}},doc:"The interpolation strategy to use in function evaluation.",default:"exponential"},colorSpace:{type:"enum",values:{rgb:{doc:"Use the RGB color space to interpolate color values"},lab:{doc:"Use the LAB color space to interpolate color values."},hcl:{doc:"Use the HCL color space to interpolate color values, interpolating the Hue, Chroma, and Luminance channels individually."}},doc:"The color space in which colors interpolated. Interpolating colors in perceptual color spaces like LAB and HCL tend to produce color ramps that look more consistent and produce colors that can be differentiated more easily than those interpolated in RGB space.",default:"rgb"},default:{type:"*",required:!1,doc:"A value to serve as a fallback function result when a value isn't otherwise available. It is used in the following circumstances:\n* In categorical functions, when the feature value does not match any of the stop domain values.\n* In property and zoom-and-property functions, when a feature does not contain a value for the specified property.\n* In identity functions, when the feature value is not valid for the style property (for example, if the function is being used for a `circle-color` property but the feature property value is not a string or not a valid color).\n* In interval or exponential property and zoom-and-property functions, when the feature value is not numeric.\nIf no default is provided, the style property's default is used in these circumstances."}},function_stop:{type:"array",minimum:0,maximum:24,value:["number","color"],length:2,doc:"Zoom level and value pair."},expression:{type:"array",value:"*",minimum:1,doc:"An expression defines a function that can be used for data-driven style properties or feature filters."},expression_name:{doc:"",type:"enum",values:{let:{doc:'Binds expressions to named variables, which can then be referenced in the result expression using ["var", "variable_name"].',group:"Variable binding","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},var:{doc:'References variable bound using "let".',group:"Variable binding","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},literal:{doc:"Provides a literal array or object value.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},array:{doc:"Asserts that the input is an array (optionally with a specific item type and length).  If, when the input expression is evaluated, it is not of the asserted type, then this assertion will cause the whole expression to be aborted.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},at:{doc:"Retrieves an item from an array.",group:"Lookup","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},in:{doc:"Determines whether an item exists in an array or a substring exists in a string. In the specific case when the second and third arguments are string literals, you must wrap at least one of them in a [`literal`](#types-literal) expression to hint correct interpretation to the [type system](#type-system).",group:"Lookup","sdk-support":{"basic functionality":{js:"1.6.0",android:"9.1.0",ios:"5.8.0",macos:"0.15.0"}}},"index-of":{doc:"Returns the first position at which an item can be found in an array or a substring can be found in a string, or `-1` if the input cannot be found. Accepts an optional index from where to begin the search.",group:"Lookup","sdk-support":{"basic functionality":{js:"1.10.0",android:"10.0.0",ios:"10.0.0"}}},slice:{doc:"Returns an item from an array or a substring from a string from a specified start index, or between a start index and an end index if set. The return value is inclusive of the start index but not of the end index.",group:"Lookup","sdk-support":{"basic functionality":{js:"1.10.0",android:"10.0.0",ios:"10.0.0"}}},case:{doc:"Selects the first output whose corresponding test condition evaluates to true, or the fallback value otherwise.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},match:{doc:'Selects the output for which the label value matches the input value, or the fallback value if no match is found. The input can be any expression (for example, `["get", "building_type"]`). Each label must be unique, and must be either:\n - a single literal value; or\n - an array of literal values, the values of which must be all strings or all numbers (for example `[100, 101]` or `["c", "b"]`).\n\nThe input matches if any of the values in the array matches using strict equality, similar to the `"in"` operator.\nIf the input type does not match the type of the labels, the result will be the fallback value.',group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},coalesce:{doc:"Evaluates each expression in turn until the first valid value is obtained. Invalid values are `null` and [`'image'`](#types-image) expressions that are unavailable in the style. If all values are invalid, `coalesce` returns the first value listed.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},step:{doc:'Produces discrete, stepped results by evaluating a piecewise-constant function defined by pairs of input and output values ("stops"). The `input` may be any numeric expression (e.g., `["get", "population"]`). Stop inputs must be numeric literals in strictly ascending order. Returns the output value of the stop just less than the input, or the first output if the input is less than the first stop.',group:"Ramps, scales, curves","sdk-support":{"basic functionality":{js:"0.42.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},interpolate:{doc:'Produces continuous, smooth results by interpolating between pairs of input and output values ("stops"). The `input` may be any numeric expression (e.g., `["get", "population"]`). Stop inputs must be numeric literals in strictly ascending order. The output type must be `number`, `array<number>`, or `color`.\n\nInterpolation types:\n- `["linear"]`: Interpolates linearly between the pair of stops just less than and just greater than the input.\n- `["exponential", base]`: Interpolates exponentially between the stops just less than and just greater than the input. `base` controls the rate at which the output increases: higher values make the output increase more towards the high end of the range. With values close to 1 the output increases linearly.\n- `["cubic-bezier", x1, y1, x2, y2]`: Interpolates using the cubic bezier curve defined by the given control points.',group:"Ramps, scales, curves","sdk-support":{"basic functionality":{js:"0.42.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"interpolate-hcl":{doc:'Produces continuous, smooth results by interpolating between pairs of input and output values ("stops"). Works like `interpolate`, but the output type must be `color`, and the interpolation is performed in the Hue-Chroma-Luminance color space.',group:"Ramps, scales, curves","sdk-support":{"basic functionality":{js:"0.49.0"}}},"interpolate-lab":{doc:'Produces continuous, smooth results by interpolating between pairs of input and output values ("stops"). Works like `interpolate`, but the output type must be `color`, and the interpolation is performed in the CIELAB color space.',group:"Ramps, scales, curves","sdk-support":{"basic functionality":{js:"0.49.0"}}},ln2:{doc:"Returns mathematical constant ln(2).",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},pi:{doc:"Returns the mathematical constant pi.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},e:{doc:"Returns the mathematical constant e.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},typeof:{doc:"Returns a string describing the type of the given value.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},string:{doc:"Asserts that the input value is a string. If multiple values are provided, each one is evaluated in order until a string is obtained. If none of the inputs are strings, the expression is an error.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},number:{doc:"Asserts that the input value is a number. If multiple values are provided, each one is evaluated in order until a number is obtained. If none of the inputs are numbers, the expression is an error.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},boolean:{doc:"Asserts that the input value is a boolean. If multiple values are provided, each one is evaluated in order until a boolean is obtained. If none of the inputs are booleans, the expression is an error.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},object:{doc:"Asserts that the input value is an object. If multiple values are provided, each one is evaluated in order until an object is obtained. If none of the inputs are objects, the expression is an error.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},collator:{doc:"Returns a `collator` for use in locale-dependent comparison operations. The `case-sensitive` and `diacritic-sensitive` options default to `false`. The `locale` argument specifies the IETF language tag of the locale to use. If none is provided, the default locale is used. If the requested locale is not available, the `collator` will use a system-defined fallback locale. Use `resolved-locale` to test the results of locale fallback behavior.",group:"Types","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}},format:{doc:'Returns a `formatted` string for displaying mixed-format text in the `text-field` property. The input may contain a string literal or expression, including an [`\'image\'`](#types-image) expression. Strings may be followed by a style override object that supports the following properties:\n- `"text-font"`: Overrides the font stack specified by the root layout property.\n- `"text-color"`: Overrides the color specified by the root paint property.\n- `"font-scale"`: Applies a scaling factor on `text-size` as specified by the root layout property.',group:"Types","sdk-support":{"basic functionality":{js:"0.48.0",android:"6.7.0",ios:"4.6.0",macos:"0.12.0"},"text-font":{js:"0.48.0",android:"6.7.0",ios:"4.6.0",macos:"0.12.0"},"font-scale":{js:"0.48.0",android:"6.7.0",ios:"4.6.0",macos:"0.12.0"},"text-color":{js:"1.3.0",android:"7.3.0",ios:"4.10.0",macos:"0.14.0"},image:{js:"1.6.0",android:"8.6.0",ios:"5.7.0",macos:"0.15.0"}}},image:{doc:"Returns a [`ResolvedImage`](/mapbox-gl-js/style-spec/types/#resolvedimage) for use in [`icon-image`](/mapbox-gl-js/style-spec/layers/#layout-symbol-icon-image), `*-pattern` entries, and as a section in the [`'format'`](#types-format) expression. A [`'coalesce'`](#coalesce) expression containing `image` expressions will evaluate to the first listed image that is currently in the style. This validation process is synchronous and requires the image to have been added to the style before requesting it in the `'image'` argument.",group:"Types","sdk-support":{"basic functionality":{js:"1.4.0",android:"8.6.0",ios:"5.7.0",macos:"0.15.0"}}},"number-format":{doc:"Converts the input number into a string representation using the providing formatting rules. If set, the `locale` argument specifies the locale to use, as a BCP 47 language tag. If set, the `currency` argument specifies an ISO 4217 code to use for currency-style formatting. If set, the `unit` argument specifies a [simple ECMAScript unit](https://tc39.es/proposal-unified-intl-numberformat/section6/locales-currencies-tz_proposed_out.html#sec-issanctionedsimpleunitidentifier) to use for unit-style formatting. If set, the `min-fraction-digits` and `max-fraction-digits` arguments specify the minimum and maximum number of fractional digits to include.",group:"Types","sdk-support":{"basic functionality":{js:"0.54.0",android:"8.4.0",ios:"5.4.0",macos:"0.15.0"}}},"to-string":{doc:'Converts the input value to a string. If the input is `null`, the result is `""`. If the input is a [`boolean`](#types-boolean), the result is `"true"` or `"false"`. If the input is a number, it is converted to a string as specified by the ["NumberToString" algorithm](https://tc39.github.io/ecma262/#sec-tostring-applied-to-the-number-type) of the ECMAScript Language Specification. If the input is a [`color`](#color), it is converted to a string of the form `"rgba(r,g,b,a)"`, where `r`, `g`, and `b` are numerals ranging from 0 to 255, and `a` ranges from 0 to 1. If the input is an [`\'image\'`](#types-image) expression, `\'to-string\'` returns the image name. Otherwise, the input is converted to a string in the format specified by the [`JSON.stringify`](https://tc39.github.io/ecma262/#sec-json.stringify) function of the ECMAScript Language Specification.',group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"to-number":{doc:'Converts the input value to a number, if possible. If the input is `null` or `false`, the result is 0. If the input is `true`, the result is 1. If the input is a string, it is converted to a number as specified by the ["ToNumber Applied to the String Type" algorithm](https://tc39.github.io/ecma262/#sec-tonumber-applied-to-the-string-type) of the ECMAScript Language Specification. If multiple values are provided, each one is evaluated in order until the first successful conversion is obtained. If none of the inputs can be converted, the expression is an error.',group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"to-boolean":{doc:"Converts the input value to a boolean. The result is `false` when then input is an empty string, 0, `false`, `null`, or `NaN`; otherwise it is `true`.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"to-rgba":{doc:"Returns a four-element array containing the input color's red, green, blue, and alpha components, in that order.",group:"Color","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"to-color":{doc:"Converts the input value to a color. If multiple values are provided, each one is evaluated in order until the first successful conversion is obtained. If none of the inputs can be converted, the expression is an error.",group:"Types","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},rgb:{doc:"Creates a color value from red, green, and blue components, which must range between 0 and 255, and an alpha component of 1. If any component is out of range, the expression is an error.",group:"Color","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},rgba:{doc:"Creates a color value from red, green, blue components, which must range between 0 and 255, and an alpha component which must range between 0 and 1. If any component is out of range, the expression is an error.",group:"Color","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},get:{doc:"Retrieves a property value from the current feature's properties, or from another object if a second argument is provided. Returns `null` if the requested property is missing.",group:"Lookup","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},has:{doc:"Tests for the presence of an property value in the current feature's properties, or from another object if a second argument is provided.",group:"Lookup","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},length:{doc:"Returns the length of an array or string.",group:"Lookup","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},properties:{doc:'Returns the feature properties object.  Note that in some cases, it may be more efficient to use `["get", "property_name"]` directly.',group:"Feature data","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"feature-state":{doc:"Retrieves a property value from the current feature's state. Returns `null` if the requested property is not present on the feature's state. A feature's state is not part of the GeoJSON or vector tile data, and must be set programmatically on each feature. Features are identified by their `id` attribute, which must be an integer or a string that can be cast to an integer. Note that [\"feature-state\"] can only be used with paint properties that support data-driven styling.",group:"Feature data","sdk-support":{"basic functionality":{js:"0.46.0",android:"10.0.0",ios:"10.0.0"}}},"geometry-type":{doc:"Returns the feature's geometry type: `Point`, `LineString` or `Polygon`. `Multi*` feature types return the singular forms.",group:"Feature data","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},id:{doc:"Returns the feature's id, if it has one.",group:"Feature data","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},zoom:{doc:'Returns the current zoom level.  Note that in style layout and paint properties, ["zoom"] may only appear as the input to a top-level "step" or "interpolate" expression.',group:"Camera","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},pitch:{doc:'Returns the current pitch in degrees. `["pitch"]` may only be used in the `filter` expression for a `symbol` layer.',group:"Camera","sdk-support":{"basic functionality":{js:"2.6.0",android:"10.9.0",ios:"10.9.0"}}},"distance-from-center":{doc:'Returns the distance of a `symbol` instance from the center of the map. The distance is measured in pixels divided by the height of the map container. It measures 0 at the center, decreases towards the camera and increase away from the camera. For example, if the height of the map is 1000px, a value of -1 means 1000px away from the center towards the camera, and a value of 1 means a distance of 1000px away from the camera from the center. `["distance-from-center"]` may only be used in the `filter` expression for a `symbol` layer.',group:"Camera","sdk-support":{"basic functionality":{js:"2.6.0",android:"10.9.0",ios:"10.9.0"}}},"heatmap-density":{doc:"Returns the kernel density estimation of a pixel in a heatmap layer, which is a relative measure of how many data points are crowded around a particular pixel. Can only be used in the `heatmap-color` property.",group:"Heatmap","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"line-progress":{doc:"Returns the progress along a gradient line. Can only be used in the `line-gradient` property.",group:"Feature data","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.5.0",ios:"4.6.0",macos:"0.12.0"}}},"sky-radial-progress":{doc:"Returns the distance of a point on the sky from the sun position. Returns 0 at sun position and 1 when the distance reaches `sky-gradient-radius`. Can only be used in the `sky-gradient` property.",group:"sky","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}}},accumulated:{doc:"Returns the value of a cluster property accumulated so far. Can only be used in the `clusterProperties` option of a clustered GeoJSON source.",group:"Feature data","sdk-support":{"basic functionality":{js:"0.53.0",android:"8.4.0",ios:"5.5.0",macos:"0.15.0"}}},"+":{doc:"Returns the sum of the inputs.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"*":{doc:"Returns the product of the inputs.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"-":{doc:"For two inputs, returns the result of subtracting the second input from the first. For a single input, returns the result of subtracting it from 0.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"/":{doc:"Returns the result of floating point division of the first input by the second.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"%":{doc:"Returns the remainder after integer division of the first input by the second.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"^":{doc:"Returns the result of raising the first input to the power specified by the second.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},sqrt:{doc:"Returns the square root of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.42.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},log10:{doc:"Returns the base-ten logarithm of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},ln:{doc:"Returns the natural logarithm of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},log2:{doc:"Returns the base-two logarithm of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},sin:{doc:"Returns the sine of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},cos:{doc:"Returns the cosine of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},tan:{doc:"Returns the tangent of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},asin:{doc:"Returns the arcsine of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},acos:{doc:"Returns the arccosine of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},atan:{doc:"Returns the arctangent of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},min:{doc:"Returns the minimum value of the inputs.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},max:{doc:"Returns the maximum value of the inputs.",group:"Math","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},round:{doc:'Rounds the input to the nearest integer. Halfway values are rounded away from zero. For example, `["round", -1.5]` evaluates to -2.',group:"Math","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},abs:{doc:"Returns the absolute value of the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},ceil:{doc:"Returns the smallest integer that is greater than or equal to the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},floor:{doc:"Returns the largest integer that is less than or equal to the input.",group:"Math","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},distance:{doc:"Returns the shortest distance in meters between the evaluated feature and the input geometry. The input value can be a valid GeoJSON of type `Point`, `MultiPoint`, `LineString`, `MultiLineString`, `Polygon`, `MultiPolygon`, `Feature`, or `FeatureCollection`. Distance values returned may vary in precision due to loss in precision from encoding geometries, particularly below zoom level 13.",group:"Math","sdk-support":{"basic functionality":{android:"9.2.0",ios:"5.9.0",macos:"0.16.0"}}},"==":{doc:"Returns `true` if the input values are equal, `false` otherwise. The comparison is strictly typed: values of different runtime types are always considered unequal. Cases where the types are known to be different at parse time are considered invalid and will produce a parse error. Accepts an optional `collator` argument to control locale-dependent string comparisons.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},collator:{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}},"!=":{doc:"Returns `true` if the input values are not equal, `false` otherwise. The comparison is strictly typed: values of different runtime types are always considered unequal. Cases where the types are known to be different at parse time are considered invalid and will produce a parse error. Accepts an optional `collator` argument to control locale-dependent string comparisons.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},collator:{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}},">":{doc:"Returns `true` if the first input is strictly greater than the second, `false` otherwise. The arguments are required to be either both strings or both numbers; if during evaluation they are not, expression evaluation produces an error. Cases where this constraint is known not to hold at parse time are considered in valid and will produce a parse error. Accepts an optional `collator` argument to control locale-dependent string comparisons.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},collator:{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}},"<":{doc:"Returns `true` if the first input is strictly less than the second, `false` otherwise. The arguments are required to be either both strings or both numbers; if during evaluation they are not, expression evaluation produces an error. Cases where this constraint is known not to hold at parse time are considered in valid and will produce a parse error. Accepts an optional `collator` argument to control locale-dependent string comparisons.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},collator:{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}},">=":{doc:"Returns `true` if the first input is greater than or equal to the second, `false` otherwise. The arguments are required to be either both strings or both numbers; if during evaluation they are not, expression evaluation produces an error. Cases where this constraint is known not to hold at parse time are considered in valid and will produce a parse error. Accepts an optional `collator` argument to control locale-dependent string comparisons.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},collator:{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}},"<=":{doc:"Returns `true` if the first input is less than or equal to the second, `false` otherwise. The arguments are required to be either both strings or both numbers; if during evaluation they are not, expression evaluation produces an error. Cases where this constraint is known not to hold at parse time are considered in valid and will produce a parse error. Accepts an optional `collator` argument to control locale-dependent string comparisons.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},collator:{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}},all:{doc:"Returns `true` if all the inputs are `true`, `false` otherwise. The inputs are evaluated in order, and evaluation is short-circuiting: once an input expression evaluates to `false`, the result is `false` and no further input expressions are evaluated.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},any:{doc:"Returns `true` if any of the inputs are `true`, `false` otherwise. The inputs are evaluated in order, and evaluation is short-circuiting: once an input expression evaluates to `true`, the result is `true` and no further input expressions are evaluated.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"!":{doc:"Logical negation. Returns `true` if the input is `false`, and `false` if the input is `true`.",group:"Decision","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},within:{doc:"Returns `true` if the evaluated feature is fully contained inside a boundary of the input geometry, `false` otherwise. The input value can be a valid GeoJSON of type `Polygon`, `MultiPolygon`, `Feature`, or `FeatureCollection`. Supported features for evaluation:\n- `Point`: Returns `false` if a point is on the boundary or falls outside the boundary.\n- `LineString`: Returns `false` if any part of a line falls outside the boundary, the line intersects the boundary, or a line's endpoint is on the boundary.",group:"Decision","sdk-support":{"basic functionality":{js:"1.9.0",android:"9.1.0",ios:"5.8.0",macos:"0.15.0"}}},"is-supported-script":{doc:"Returns `true` if the input string is expected to render legibly. Returns `false` if the input string contains sections that cannot be rendered without potential loss of meaning (e.g. Indic scripts that require complex text shaping, or right-to-left scripts if the the `mapbox-gl-rtl-text` plugin is not in use in Mapbox GL JS).",group:"String","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.6.0",ios:"4.1.0",macos:"0.8.0"}}},upcase:{doc:"Returns the input string converted to uppercase. Follows the Unicode Default Case Conversion algorithm and the locale-insensitive case mappings in the Unicode Character Database.",group:"String","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},downcase:{doc:"Returns the input string converted to lowercase. Follows the Unicode Default Case Conversion algorithm and the locale-insensitive case mappings in the Unicode Character Database.",group:"String","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},concat:{doc:"Returns a `string` consisting of the concatenation of the inputs. Each input is converted to a string as if by `to-string`.",group:"String","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}}},"resolved-locale":{doc:"Returns the IETF language tag of the locale being used by the provided `collator`. This can be used to determine the default system locale, or to determine if a requested locale was successfully loaded.",group:"String","sdk-support":{"basic functionality":{js:"0.45.0",android:"6.5.0",ios:"4.2.0",macos:"0.9.0"}}}}},fog:{range:{type:"array",default:[.5,10],minimum:-20,maximum:20,length:2,value:"number","property-type":"data-constant",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},doc:"The start and end distance range in which fog fades from fully transparent to fully opaque. The distance to the point at the center of the map is defined as zero, so that negative range values are closer to the camera, and positive values are farther away.",example:[.5,10],"sdk-support":{"basic functionality":{js:"2.3.0",android:"10.6.0",ios:"10.6.0"}}},color:{type:"color","property-type":"data-constant",default:"#ffffff",expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"The color of the atmosphere region immediately below the horizon and within the `range` and above the horizon and within `horizon-blend`. Using opacity is recommended only for smoothly transitioning fog on/off as anything less than 100% opacity results in more tiles loaded and drawn.","sdk-support":{"basic functionality":{js:"2.3.0",android:"10.6.0",ios:"10.6.0"}}},"high-color":{type:"color","property-type":"data-constant",default:"#245cdf",expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"The color of the atmosphere region above the horizon, `high-color` extends further above the horizon than the `color` property and its spread can be controlled with `horizon-blend`. The opacity can be set to `0` to remove the high atmosphere color contribution.","sdk-support":{"basic functionality":{js:"2.9.0",android:"10.6.0",ios:"10.6.0"}}},"space-color":{type:"color","property-type":"data-constant",default:["interpolate",["linear"],["zoom"],4,"#010b19",7,"#367ab9"],expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"The color of the region above the horizon and after the end of the `horizon-blend` contribution. The opacity can be set to `0` to have a transparent background.","sdk-support":{"basic functionality":{js:"2.9.0",android:"10.6.0",ios:"10.6.0"}}},"horizon-blend":{type:"number","property-type":"data-constant",default:["interpolate",["linear"],["zoom"],4,.2,7,.1],minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"Horizon blend applies a smooth fade from the color of the atmosphere to the color of space. A value of zero leaves a sharp transition from atmosphere to space. Increasing the value blends the color of atmosphere into increasingly high angles of the sky.","sdk-support":{"basic functionality":{js:"2.3.0",android:"10.6.0",ios:"10.6.0"}}},"star-intensity":{type:"number","property-type":"data-constant",default:["interpolate",["linear"],["zoom"],5,.35,6,0],minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"A value controlling the star intensity where `0` will show no stars and `1` will show stars at their maximum intensity.","sdk-support":{"basic functionality":{js:"2.9.0",android:"10.6.0",ios:"10.6.0"}}}},light:{anchor:{type:"enum",default:"viewport",values:{map:{doc:"The position of the light source is aligned to the rotation of the map."},viewport:{doc:"The position of the light source is aligned to the rotation of the viewport."}},"property-type":"data-constant",transition:!1,expression:{interpolated:!1,parameters:["zoom"]},doc:"Whether extruded geometries are lit relative to the map or viewport.",example:"map","sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}}},position:{type:"array",default:[1.15,210,30],length:3,value:"number","property-type":"data-constant",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},doc:"Position of the light source relative to lit (extruded) geometries, in [r radial coordinate, a azimuthal angle, p polar angle] where r indicates the distance from the center of the base of an object to its light, a indicates the position of the light relative to 0° (0° when `light.anchor` is set to `viewport` corresponds to the top of the viewport, or 0° when `light.anchor` is set to `map` corresponds to due north, and degrees proceed clockwise), and p indicates the height of the light (from 0°, directly above, to 180°, directly below).",example:[1.5,90,80],"sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}}},color:{type:"color","property-type":"data-constant",default:"#ffffff",expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"Color tint for lighting extruded geometries.","sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}}},intensity:{type:"number","property-type":"data-constant",default:.5,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"Intensity of lighting (on a scale from 0 to 1). Higher numbers will present as more extreme contrast.","sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}}}},projection:{name:{type:"enum",values:{albers:{doc:"An Albers equal-area projection centered on the continental United States. You can configure the projection for a different region by setting `center` and `parallels` properties. You may want to set max bounds to constrain the map to the relevant region."},equalEarth:{doc:"An Equal Earth projection."},equirectangular:{doc:"An Equirectangular projection. This projection is very similar to the Plate Carrée projection."},lambertConformalConic:{doc:"A Lambert conformal conic projection. You can configure the projection for a region by setting `center` and `parallels` properties. You may want to set max bounds to constrain the map to the relevant region."},mercator:{doc:"The Mercator projection is the default projection."},naturalEarth:{doc:"A Natural Earth projection."},winkelTripel:{doc:"A Winkel Tripel projection."},globe:{doc:"A globe projection."}},default:"mercator",doc:"The name of the projection to be used for rendering the map.",required:!0,"sdk-support":{"basic functionality":{js:"2.6.0"}}},center:{type:"array",length:2,value:"number","property-type":"data-constant",minimum:[-180,-90],maximum:[180,90],transition:!1,doc:"The reference longitude and latitude of the projection. `center` takes the form of [lng, lat]. This property is only configurable for conic projections (Albers and Lambert Conformal Conic). All other projections are centered on [0, 0].",example:[-96,37.5],requires:[{name:["albers","lambertConformalConic"]}],"sdk-support":{"basic functionality":{js:"2.6.0"}}},parallels:{type:"array",length:2,value:"number","property-type":"data-constant",minimum:[-90,-90],maximum:[90,90],transition:!1,doc:"The standard parallels of the projection, denoting the desired latitude range with minimal distortion. `parallels` takes the form of [lat0, lat1]. This property is only configurable for conic projections (Albers and Lambert Conformal Conic).",example:[29.5,45.5],requires:[{name:["albers","lambertConformalConic"]}],"sdk-support":{"basic functionality":{js:"2.6.0"}}}},terrain:{source:{type:"string",doc:"Name of a source of `raster_dem` type to be used for terrain elevation.",required:!0,"sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}}},exaggeration:{type:"number","property-type":"data-constant",default:1,minimum:0,maximum:1e3,expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"Exaggerates the elevation of the terrain by multiplying the data from the DEM with this value.",requires:["source"],"sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}}}},paint:["paint_fill","paint_line","paint_circle","paint_heatmap","paint_fill-extrusion","paint_symbol","paint_raster","paint_hillshade","paint_background","paint_sky"],paint_fill:{"fill-antialias":{type:"boolean",default:!0,doc:"Whether or not the fill should be antialiased.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-opacity":{type:"number",default:1,minimum:0,maximum:1,doc:"The opacity of the entire fill layer. In contrast to the `fill-color`, this value will also affect the 1px stroke around the fill, if the stroke is used.",transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.21.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-color":{type:"color",default:"#000000",doc:"The color of the filled part of this layer. This color can be specified as `rgba` with an alpha component and the color's opacity will not affect the opacity of the 1px stroke, if it is used.",transition:!0,requires:[{"!":"fill-pattern"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.19.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-outline-color":{type:"color",doc:"The outline color of the fill. Matches the value of `fill-color` if unspecified.",transition:!0,requires:[{"!":"fill-pattern"},{"fill-antialias":!0}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.19.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",doc:"The geometry's offset. Values are [x, y] where negatives indicate left and up, respectively.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-translate-anchor":{type:"enum",values:{map:{doc:"The fill is translated relative to the map."},viewport:{doc:"The fill is translated relative to the viewport."}},doc:"Controls the frame of reference for `fill-translate`.",default:"map",requires:["fill-translate"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-pattern":{type:"resolvedImage",transition:!1,doc:"Name of image in sprite to use for drawing image fills. For seamless patterns, image width and height must be a factor of two (2, 4, 8, ..., 512). Note that zoom-dependent expressions will be evaluated only at integer zoom levels.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.49.0",android:"6.5.0",macos:"0.11.0",ios:"4.4.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"}},"paint_fill-extrusion":{"fill-extrusion-opacity":{type:"number",default:1,minimum:0,maximum:1,doc:"The opacity of the entire fill extrusion layer. This is rendered on a per-layer, not per-feature, basis, and data-driven styling is not available.",transition:!0,"sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-color":{type:"color",default:"#000000",doc:"The base color of the extruded fill. The extrusion's surfaces will be shaded differently based on this color in combination with the root `light` settings. If this color is specified as `rgba` with an alpha component, the alpha component will be ignored; use `fill-extrusion-opacity` to set layer opacity.",transition:!0,requires:[{"!":"fill-extrusion-pattern"}],"sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"},"data-driven styling":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",doc:"The geometry's offset. Values are [x, y] where negatives indicate left and up (on the flat plane), respectively.","sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-translate-anchor":{type:"enum",values:{map:{doc:"The fill extrusion is translated relative to the map."},viewport:{doc:"The fill extrusion is translated relative to the viewport."}},doc:"Controls the frame of reference for `fill-extrusion-translate`.",default:"map",requires:["fill-extrusion-translate"],"sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-pattern":{type:"resolvedImage",transition:!1,doc:"Name of image in sprite to use for drawing images on extruded fills. For seamless patterns, image width and height must be a factor of two (2, 4, 8, ..., 512). Note that zoom-dependent expressions will be evaluated only at integer zoom levels.","sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"},"data-driven styling":{js:"0.49.0",android:"6.5.0",macos:"0.11.0",ios:"4.4.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"fill-extrusion-height":{type:"number",default:0,minimum:0,units:"meters",doc:"The height with which to extrude this layer.",transition:!0,"sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"},"data-driven styling":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-base":{type:"number",default:0,minimum:0,units:"meters",doc:"The height with which to extrude the base of this layer. Must be less than or equal to `fill-extrusion-height`.",transition:!0,requires:["fill-extrusion-height"],"sdk-support":{"basic functionality":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"},"data-driven styling":{js:"0.27.0",android:"5.1.0",ios:"3.6.0",macos:"0.5.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-vertical-gradient":{type:"boolean",default:!0,doc:"Whether to apply a vertical gradient to the sides of a fill-extrusion layer. If true, sides will be shaded slightly darker farther down.",transition:!1,"sdk-support":{"basic functionality":{js:"0.50.0",android:"7.0.0",ios:"4.7.0",macos:"0.13.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-ambient-occlusion-intensity":{"property-type":"data-constant",type:"number",private:!0,default:0,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"Controls the intensity of shading near ground and concave angles between walls. Default value 0.0 disables ambient occlusion and values around 0.3 provide the most plausible results for buildings.","sdk-support":{"basic functionality":{js:"2.10.0",android:"10.7.0",ios:"10.7.0"}}},"fill-extrusion-ambient-occlusion-radius":{"property-type":"data-constant",type:"number",private:!0,default:3,minimum:0,expression:{interpolated:!0,parameters:["zoom"]},transition:!0,doc:"Shades area near ground and concave angles between walls where the radius defines only vertical impact. Default value 3.0 corresponds to height of one floor and brings the most plausible results for buildings.",requires:["fill-extrusion-edge-radius"],"sdk-support":{"basic functionality":{js:"2.10.0",android:"10.7.0",ios:"10.7.0"}}}},paint_line:{"line-opacity":{type:"number",doc:"The opacity at which the line will be drawn.",default:1,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-color":{type:"color",doc:"The color with which the line will be drawn.",default:"#000000",transition:!0,requires:[{"!":"line-pattern"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.23.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",doc:"The geometry's offset. Values are [x, y] where negatives indicate left and up, respectively.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-translate-anchor":{type:"enum",values:{map:{doc:"The line is translated relative to the map."},viewport:{doc:"The line is translated relative to the viewport."}},doc:"Controls the frame of reference for `line-translate`.",default:"map",requires:["line-translate"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"line-width":{type:"number",default:1,minimum:0,transition:!0,units:"pixels",doc:"Stroke thickness.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.39.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-gap-width":{type:"number",default:0,minimum:0,doc:"Draws a line casing outside of a line's actual path. Value indicates the width of the inner gap.",transition:!0,units:"pixels","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-offset":{type:"number",default:0,doc:"The line's offset. For linear features, a positive value offsets the line to the right, relative to the direction of the line, and a negative value to the left. For polygon features, a positive value results in an inset, and a negative value results in an outset.",transition:!0,units:"pixels","sdk-support":{"basic functionality":{js:"0.12.1",android:"3.0.0",ios:"3.1.0",macos:"0.1.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",doc:"Blur applied to the line, in pixels.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-dasharray":{type:"array",value:"number",doc:"Specifies the lengths of the alternating dashes and gaps that form the dash pattern. The lengths are later scaled by the line width. To convert a dash length to pixels, multiply the length by the current line width. Note that GeoJSON sources with `lineMetrics: true` specified won't render dashed lines to the expected scale. Also note that zoom-dependent expressions will be evaluated only at integer zoom levels.",minimum:0,transition:!1,units:"line widths",requires:[{"!":"line-pattern"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"2.3.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"line-pattern":{type:"resolvedImage",transition:!1,doc:"Name of image in sprite to use for drawing image lines. For seamless patterns, image width must be a factor of two (2, 4, 8, ..., 512). Note that zoom-dependent expressions will be evaluated only at integer zoom levels.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.49.0",android:"6.5.0",macos:"0.11.0",ios:"4.4.0"}},expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"line-gradient":{type:"color",doc:'Defines a gradient with which to color a line feature. Can only be used with GeoJSON sources that specify `"lineMetrics": true`.',transition:!1,requires:[{"!":"line-pattern"},{source:"geojson",has:{lineMetrics:!0}}],"sdk-support":{"basic functionality":{js:"0.45.0",android:"6.5.0",ios:"4.4.0",macos:"0.11.0"},"data-driven styling":{}},expression:{interpolated:!0,parameters:["line-progress"]},"property-type":"color-ramp"},"line-trim-offset":{type:"array",value:"number",doc:"The line part between [trim-start, trim-end] will be marked as transparent to make a route vanishing effect. The line trim-off offset is based on the whole line range [0.0, 1.0].",length:2,default:[0,0],minimum:[0,0],maximum:[1,1],transition:!1,requires:[{source:"geojson",has:{lineMetrics:!0}}],"sdk-support":{"basic functionality":{js:"2.9.0",android:"10.5.0",ios:"10.5.0",macos:"10.5.0"}},"property-type":"constant"}},paint_circle:{"circle-radius":{type:"number",default:5,minimum:0,transition:!0,units:"pixels",doc:"Circle radius.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.18.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-color":{type:"color",default:"#000000",doc:"The fill color of the circle.",transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.18.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-blur":{type:"number",default:0,doc:"Amount to blur the circle. 1 blurs the circle such that only the centerpoint is full opacity.",transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.20.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-opacity":{type:"number",doc:"The opacity at which the circle will be drawn.",default:1,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.20.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",doc:"The geometry's offset. Values are [x, y] where negatives indicate left and up, respectively.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"circle-translate-anchor":{type:"enum",values:{map:{doc:"The circle is translated relative to the map."},viewport:{doc:"The circle is translated relative to the viewport."}},doc:"Controls the frame of reference for `circle-translate`.",default:"map",requires:["circle-translate"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-scale":{type:"enum",values:{map:{doc:"Circles are scaled according to their apparent distance to the camera."},viewport:{doc:"Circles are not scaled."}},default:"map",doc:"Controls the scaling behavior of the circle when the map is pitched.","sdk-support":{"basic functionality":{js:"0.21.0",android:"4.2.0",ios:"3.4.0",macos:"0.2.1"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-alignment":{type:"enum",values:{map:{doc:"The circle is aligned to the plane of the map."},viewport:{doc:"The circle is aligned to the plane of the viewport."}},default:"viewport",doc:"Orientation of circle when map is pitched.","sdk-support":{"basic functionality":{js:"0.39.0",android:"5.2.0",ios:"3.7.0",macos:"0.6.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-stroke-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",doc:"The width of the circle's stroke. Strokes are placed outside of the `circle-radius`.","sdk-support":{"basic functionality":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-color":{type:"color",default:"#000000",doc:"The stroke color of the circle.",transition:!0,"sdk-support":{"basic functionality":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-opacity":{type:"number",doc:"The opacity of the circle's stroke.",default:1,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"},"data-driven styling":{js:"0.29.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"}},paint_heatmap:{"heatmap-radius":{type:"number",default:30,minimum:1,transition:!0,units:"pixels",doc:"Radius of influence of one heatmap point in pixels. Increasing the value makes the heatmap smoother, but less detailed. `queryRenderedFeatures` on heatmap layers will return points within this radius.","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},"data-driven styling":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-weight":{type:"number",default:1,minimum:0,transition:!1,doc:"A measure of how much an individual point contributes to the heatmap. A value of 10 would be equivalent to having 10 points of weight 1 in the same spot. Especially useful when combined with clustering.","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},"data-driven styling":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-intensity":{type:"number",default:1,minimum:0,transition:!0,doc:"Similar to `heatmap-weight` but controls the intensity of the heatmap globally. Primarily used for adjusting the heatmap based on zoom level.","sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"heatmap-color":{type:"color",default:["interpolate",["linear"],["heatmap-density"],0,"rgba(0, 0, 255, 0)",.1,"royalblue",.3,"cyan",.5,"lime",.7,"yellow",1,"red"],doc:'Defines the color of each pixel based on its density value in a heatmap.  Should be an expression that uses `["heatmap-density"]` as input.',transition:!1,"sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"},"data-driven styling":{}},expression:{interpolated:!0,parameters:["heatmap-density"]},"property-type":"color-ramp"},"heatmap-opacity":{type:"number",doc:"The global opacity at which the heatmap layer will be drawn.",default:1,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.41.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},paint_symbol:{"icon-opacity":{doc:"The opacity at which the icon will be drawn.",type:"number",default:1,minimum:0,maximum:1,transition:!0,requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-color":{type:"color",default:"#000000",transition:!0,doc:"The color of the icon. This can only be used with [SDF icons](/help/troubleshooting/using-recolorable-images-in-mapbox-maps/).",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,doc:"The color of the icon's halo. Icon halos can only be used with [SDF icons](/help/troubleshooting/using-recolorable-images-in-mapbox-maps/).",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",doc:"Distance of halo to the icon outline.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",doc:"Fade out the halo towards the outside.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",doc:"Distance that the icon's anchor is moved from its original placement. Positive values indicate right and down, while negative values indicate left and up.",requires:["icon-image"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-translate-anchor":{type:"enum",values:{map:{doc:"Icons are translated relative to the map."},viewport:{doc:"Icons are translated relative to the viewport."}},doc:"Controls the frame of reference for `icon-translate`.",default:"map",requires:["icon-image","icon-translate"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-opacity":{type:"number",doc:"The opacity at which the text will be drawn.",default:1,minimum:0,maximum:1,transition:!0,requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-color":{type:"color",doc:"The color with which the text will be drawn.",default:"#000000",transition:!0,overridable:!0,requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,doc:"The color of the text's halo, which helps it stand out from backgrounds.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",doc:"Distance of halo to the font outline. Max text halo width is 1/4 of the font-size.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",doc:"The halo's fadeout distance towards the outside.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"},"data-driven styling":{js:"0.33.0",android:"5.0.0",ios:"3.5.0",macos:"0.4.0"}},expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",doc:"Distance that the text's anchor is moved from its original placement. Positive values indicate right and down, while negative values indicate left and up.",requires:["text-field"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-translate-anchor":{type:"enum",values:{map:{doc:"The text is translated relative to the map."},viewport:{doc:"The text is translated relative to the viewport."}},doc:"Controls the frame of reference for `text-translate`.",default:"map",requires:["text-field","text-translate"],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},paint_raster:{"raster-opacity":{type:"number",doc:"The opacity at which the image will be drawn.",default:1,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-hue-rotate":{type:"number",default:0,period:360,transition:!0,units:"degrees",doc:"Rotates hues around the color wheel.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-min":{type:"number",doc:"Increase or reduce the brightness of the image. The value is the minimum brightness.",default:0,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-max":{type:"number",doc:"Increase or reduce the brightness of the image. The value is the maximum brightness.",default:1,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-saturation":{type:"number",doc:"Increase or reduce the saturation of the image.",default:0,minimum:-1,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-contrast":{type:"number",doc:"Increase or reduce the contrast of the image.",default:0,minimum:-1,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-resampling":{type:"enum",doc:"The resampling/interpolation method to use for overscaling, also known as texture magnification filter",values:{linear:{doc:"(Bi)linear filtering interpolates pixel values using the weighted average of the four closest original source pixels creating a smooth but blurry look when overscaled"},nearest:{doc:"Nearest neighbor filtering interpolates pixel values using the nearest original source pixel creating a sharp but pixelated look when overscaled"}},default:"linear","sdk-support":{"basic functionality":{js:"0.47.0",android:"6.3.0",ios:"4.2.0",macos:"0.9.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"raster-fade-duration":{type:"number",default:300,minimum:0,transition:!1,units:"milliseconds",doc:"Fade duration when a new tile is added.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},paint_hillshade:{"hillshade-illumination-direction":{type:"number",default:335,minimum:0,maximum:359,doc:"The direction of the light source used to generate the hillshading with 0 as the top of the viewport if `hillshade-illumination-anchor` is set to `viewport` and due north if `hillshade-illumination-anchor` is set to `map`.",transition:!1,"sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-illumination-anchor":{type:"enum",values:{map:{doc:"The hillshade illumination is relative to the north direction."},viewport:{doc:"The hillshade illumination is relative to the top of the viewport."}},default:"viewport",doc:"Direction of light source when map is rotated.","sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-exaggeration":{type:"number",doc:"Intensity of the hillshade",default:.5,minimum:0,maximum:1,transition:!0,"sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-shadow-color":{type:"color",default:"#000000",doc:"The shading color of areas that face away from the light source.",transition:!0,"sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-highlight-color":{type:"color",default:"#FFFFFF",doc:"The shading color of areas that faces towards the light source.",transition:!0,"sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-accent-color":{type:"color",default:"#000000",doc:"The shading color used to accentuate rugged terrain like sharp cliffs and gorges.",transition:!0,"sdk-support":{"basic functionality":{js:"0.43.0",android:"6.0.0",ios:"4.0.0",macos:"0.7.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},paint_background:{"background-color":{type:"color",default:"#000000",doc:"The color with which the background will be drawn.",transition:!0,requires:[{"!":"background-pattern"}],"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"background-pattern":{type:"resolvedImage",transition:!1,doc:"Name of image in sprite to use for drawing an image background. For seamless patterns, image width and height must be a factor of two (2, 4, 8, ..., 512). Note that zoom-dependent expressions will be evaluated only at integer zoom levels.","sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"background-opacity":{type:"number",default:1,minimum:0,maximum:1,doc:"The opacity at which the background will be drawn.",transition:!0,"sdk-support":{"basic functionality":{js:"0.10.0",android:"2.0.1",ios:"2.0.0",macos:"0.1.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},paint_sky:{"sky-type":{type:"enum",values:{gradient:{doc:"Renders the sky with a gradient that can be configured with `sky-gradient-radius` and `sky-gradient`."},atmosphere:{doc:"Renders the sky with a simulated atmospheric scattering algorithm, the sun direction can be attached to the light position or explicitly set through `sky-atmosphere-sun`."}},default:"atmosphere",doc:"The type of the sky","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"sky-atmosphere-sun":{type:"array",value:"number",length:2,units:"degrees",minimum:[0,0],maximum:[360,180],transition:!1,doc:"Position of the sun center [a azimuthal angle, p polar angle]. The azimuthal angle indicates the position of the sun relative to 0° north, where degrees proceed clockwise. The polar angle indicates the height of the sun, where 0° is directly above, at zenith, and 90° at the horizon. When this property is ommitted, the sun center is directly inherited from the light position.","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},requires:[{"sky-type":"atmosphere"}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"sky-atmosphere-sun-intensity":{type:"number",requires:[{"sky-type":"atmosphere"}],default:10,minimum:0,maximum:100,transition:!1,doc:"Intensity of the sun as a light source in the atmosphere (on a scale from 0 to a 100). Setting higher values will brighten up the sky.","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},"property-type":"data-constant"},"sky-gradient-center":{type:"array",requires:[{"sky-type":"gradient"}],value:"number",default:[0,0],length:2,units:"degrees",minimum:[0,0],maximum:[360,180],transition:!1,doc:"Position of the gradient center [a azimuthal angle, p polar angle]. The azimuthal angle indicates the position of the gradient center relative to 0° north, where degrees proceed clockwise. The polar angle indicates the height of the gradient center, where 0° is directly above, at zenith, and 90° at the horizon.","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"sky-gradient-radius":{type:"number",requires:[{"sky-type":"gradient"}],default:90,minimum:0,maximum:180,transition:!1,doc:"The angular distance (measured in degrees) from `sky-gradient-center` up to which the gradient extends. A value of 180 causes the gradient to wrap around to the opposite direction from `sky-gradient-center`.","sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"sky-gradient":{type:"color",default:["interpolate",["linear"],["sky-radial-progress"],.8,"#87ceeb",1,"white"],doc:"Defines a radial color gradient with which to color the sky. The color values can be interpolated with an expression using `sky-radial-progress`. The range [0, 1] for the interpolant covers a radial distance (in degrees) of [0, `sky-gradient-radius`] centered at the position specified by `sky-gradient-center`.",transition:!1,requires:[{"sky-type":"gradient"}],"sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"},"data-driven styling":{}},expression:{interpolated:!0,parameters:["sky-radial-progress"]},"property-type":"color-ramp"},"sky-atmosphere-halo-color":{type:"color",default:"white",doc:"A color applied to the atmosphere sun halo. The alpha channel describes how strongly the sun halo is represented in an atmosphere sky layer.",transition:!1,requires:[{"sky-type":"atmosphere"}],"sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},"property-type":"data-constant"},"sky-atmosphere-color":{type:"color",default:"white",doc:"A color used to tweak the main atmospheric scattering coefficients. Using white applies the default coefficients giving the natural blue color to the atmosphere. This color affects how heavily the corresponding wavelength is represented during scattering. The alpha channel describes the density of the atmosphere, with 1 maximum density and 0 no density.",transition:!1,requires:[{"sky-type":"atmosphere"}],"sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},"property-type":"data-constant"},"sky-opacity":{type:"number",default:1,minimum:0,maximum:1,doc:"The opacity of the entire sky layer.",transition:!0,"sdk-support":{"basic functionality":{js:"2.0.0",ios:"10.0.0",android:"10.0.0"}},expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},transition:{duration:{type:"number",default:300,minimum:0,units:"milliseconds",doc:"Time allotted for transitions to complete."},delay:{type:"number",default:0,minimum:0,units:"milliseconds",doc:"Length of time before a transition begins."}},"property-type":{"data-driven":{type:"property-type",doc:"Property is interpolable and can be represented using a property expression."},"color-ramp":{type:"property-type",doc:"Property should be specified using a color ramp from which the output color can be sampled based on a property calculation."},"data-constant":{type:"property-type",doc:"Property is interpolable but cannot be represented using a property expression."},constant:{type:"property-type",doc:"Property is constant across all zoom levels and property values."}},promoteId:{"*":{type:"string",doc:"A name of a feature property to use as ID for feature state."}}},u=/("(?:[^\\"]|\\.)*")|[:,]/g;function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=JSON.stringify([1],void 0,void 0===t.indent?2:t.indent).slice(2,-3),n=""===r?1/0:void 0===t.maxLength?80:t.maxLength,i=t.replacer;return function e(t,a,s){t&&"function"==typeof t.toJSON&&(t=t.toJSON());var l=JSON.stringify(t,i);if(void 0===l)return l;var c=n-a.length-s;if(l.length<=c){var p=l.replace(u,(function(e,t){return t||e+" "}));if(p.length<=c)return p}if(null!=i&&(t=JSON.parse(l),i=void 0),"object"===(void 0===t?"undefined":o(t))&&null!==t){var d=a+r,h=[],f=0,y=void 0,m=void 0;if(Array.isArray(t)){y="[",m="]";for(var v=t.length;f<v;f++)h.push(e(t[f],d,f===v-1?0:1)||"null")}else{y="{",m="}";for(var g=Object.keys(t),b=g.length;f<b;f++){var x=g[f],w=JSON.stringify(x)+": ",k=e(t[x],d,w.length+(f===b-1?0:1));void 0!==k&&h.push(w+k)}}if(h.length>0)return[y,r+h.join(",\n"+d),m].join("\n"+a)}return l}(e,"",0)}function p(e,t){var r={};for(var n in t)void 0!==e[n]&&(r[n]=e[n]);for(var i in e)void 0===r[i]&&(r[i]=e[i]);return r}var d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{},h={},f={exports:{}};/*! https://mths.be/punycode v1.3.2 by @mathias */!function(e,t){!function(r){var n=t&&!t.nodeType&&t,i=e&&!e.nodeType&&e,a="object"==(void 0===d?"undefined":o(d))&&d;a.global!==a&&a.window!==a&&a.self!==a||(r=a);var s,l,u=**********,c=/^xn--/,p=/[^\x20-\x7E]/,h=/[\x2E\u3002\uFF0E\uFF61]/g,f={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},y=Math.floor,m=String.fromCharCode;function v(e){throw RangeError(f[e])}function g(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function b(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+g((e=e.replace(h,".")).split("."),t).join(".")}function x(e){for(var t,r,n=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function w(e){return g(e,(function(e){var t="";return e>65535&&(t+=m((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=m(e)})).join("")}function k(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function _(e,t,r){var n=0;for(e=r?y(e/700):e>>1,e+=y(e/t);e>455;n+=36)e=y(e/35);return y(n+36*e/(e+38))}function S(e){var t,r,n,i,o,a,s,l,c,p,d,h=[],f=e.length,m=0,g=128,b=72;for((r=e.lastIndexOf("-"))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&v("not-basic"),h.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<f;){for(o=m,a=1,s=36;i>=f&&v("invalid-input"),((l=(d=e.charCodeAt(i++))-48<10?d-22:d-65<26?d-65:d-97<26?d-97:36)>=36||l>y((u-m)/a))&&v("overflow"),m+=l*a,!(l<(c=s<=b?1:s>=b+26?26:s-b));s+=36)a>y(u/(p=36-c))&&v("overflow"),a*=p;b=_(m-o,t=h.length+1,0==o),y(m/t)>u-g&&v("overflow"),g+=y(m/t),m%=t,h.splice(m++,0,g)}return w(h)}function T(e){var t,r,n,i,o,a,s,l,c,p,d,h,f,g,b,w=[];for(h=(e=x(e)).length,t=128,r=0,o=72,a=0;a<h;++a)(d=e[a])<128&&w.push(m(d));for(n=i=w.length,i&&w.push("-");n<h;){for(s=u,a=0;a<h;++a)(d=e[a])>=t&&d<s&&(s=d);for(s-t>y((u-r)/(f=n+1))&&v("overflow"),r+=(s-t)*f,t=s,a=0;a<h;++a)if((d=e[a])<t&&++r>u&&v("overflow"),d==t){for(l=r,c=36;!(l<(p=c<=o?1:c>=o+26?26:c-o));c+=36)b=l-p,g=36-p,w.push(m(k(p+b%g,0))),l=y(b/g);w.push(m(k(l,0))),o=_(r,f,n==i),r=0,++n}++r,++t}return w.join("")}if(s={version:"1.3.2",ucs2:{decode:x,encode:w},decode:S,encode:T,toASCII:function(e){return b(e,(function(e){return p.test(e)?"xn--"+T(e):e}))},toUnicode:function(e){return b(e,(function(e){return c.test(e)?S(e.slice(4).toLowerCase()):e}))}},n&&i)if(e.exports==n)i.exports=s;else for(l in s)s.hasOwnProperty(l)&&(n[l]=s[l]);else r.punycode=s}(d)}(f,f.exports);var y={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"===(void 0===e?"undefined":o(e))&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}},m={};function v(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var g=function(e){switch(void 0===e?"undefined":o(e)){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};m.decode=m.parse=function(e,t,r,n){t=t||"&",r=r||"=";var i={};if("string"!=typeof e||0===e.length)return i;var o=/\+/g;e=e.split(t);var a=1e3;n&&"number"==typeof n.maxKeys&&(a=n.maxKeys);var s=e.length;a>0&&s>a&&(s=a);for(var l=0;l<s;++l){var u,c,p,d,h=e[l].replace(o,"%20"),f=h.indexOf(r);f>=0?(u=h.substr(0,f),c=h.substr(f+1)):(u=h,c=""),p=decodeURIComponent(u),d=decodeURIComponent(c),v(i,p)?Array.isArray(i[p])?i[p].push(d):i[p]=[i[p],d]:i[p]=d}return i},m.encode=m.stringify=function(e,t,r,n){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"===(void 0===e?"undefined":o(e))?Object.keys(e).map((function(n){var i=encodeURIComponent(g(n))+r;return Array.isArray(e[n])?e[n].map((function(e){return i+encodeURIComponent(g(e))})).join(t):i+encodeURIComponent(g(e[n]))})).join(t):n?encodeURIComponent(g(n))+r+encodeURIComponent(g(e)):""};var b=f.exports,x=y;function w(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}h.parse=z,h.resolve=function(e,t){return z(e,!1,!0).resolve(t)},h.resolveObject=function(e,t){return e?z(e,!1,!0).resolveObject(t):t},h.format=function(e){x.isString(e)&&(e=z(e));return e instanceof w?e.format():w.prototype.format.call(e)},h.Url=w;var k=/^([a-z0-9.+-]+:)/i,_=/:[0-9]*$/,S=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,T=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),E=["'"].concat(T),j=["%","/","?",";","#"].concat(E),O=["/","?","#"],A=/^[+a-z0-9A-Z_-]{0,63}$/,R=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,C={javascript:!0,"javascript:":!0},M={javascript:!0,"javascript:":!0},I={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},P=m;function z(e,t,r){if(e&&x.isObject(e)&&e instanceof w)return e;var n=new w;return n.parse(e,t,r),n}function L(e){for(var t=0;t<l.layout.length;t++)for(var r in l[l.layout[t]])if(r===e)return l[l.layout[t]][r];for(var n=0;n<l.paint.length;n++)for(var i in l[l.paint[n]])if(i===e)return l[l.paint[n]][i];return null}function N(e,t){for(var r in e.sources)t(e.sources[r])}function F(e,t){var r=!0,n=!1,i=void 0;try{for(var o,a=e.layers[Symbol.iterator]();!(r=(o=a.next()).done);r=!0){t(o.value)}}catch(e){n=!0,i=e}finally{try{!r&&a.return&&a.return()}finally{if(n)throw i}}}function D(e,t,r){function n(e,t){var n=e[t];n&&Object.keys(n).forEach((function(i){r({path:[e.id,t,i],key:i,value:n[i],reference:L(i),set:function(e){n[i]=e}})}))}F(e,(function(e){t.paint&&n(e,"paint"),t.layout&&n(e,"layout")}))}function U(e,t){for(var r in e)0===r.indexOf("layout")&&t(e[r],r)}function q(e,t){for(var r in e)0===r.indexOf("paint")&&t(e[r],r)}function G(e,t){return"string"==typeof t&&"@"===t[0]?G(e,e.constants[t]):t}function B(e,t,r){e[r]=e[t],delete e[t]}function W(e){e.version=8,N(e,(function(e){"video"===e.type&&void 0!==e.url&&B(e,"url","urls"),"video"===e.type&&e.coordinates.forEach((function(e){return e.reverse()}))})),F(e,(function(e){U(e,(function(e){void 0!==e["symbol-min-distance"]&&B(e,"symbol-min-distance","symbol-spacing")})),q(e,(function(e){void 0!==e["background-image"]&&B(e,"background-image","background-pattern"),void 0!==e["line-image"]&&B(e,"line-image","line-pattern"),void 0!==e["fill-image"]&&B(e,"fill-image","fill-pattern")}))})),D(e,{paint:!0,layout:!0},(function(t){var r=G(e,t.value);(function(e){return Array.isArray(e.stops)})(r)&&r.stops.forEach((function(t){t[1]=G(e,t[1])})),t.set(r)})),delete e.constants,F(e,(function(e){U(e,(function(e){delete e["text-max-size"],delete e["icon-max-size"]})),q(e,(function(t){t["text-size"]&&(e.layout||(e.layout={}),e.layout["text-size"]=t["text-size"],delete t["text-size"]),t["icon-size"]&&(e.layout||(e.layout={}),e.layout["icon-size"]=t["icon-size"],delete t["icon-size"])}))})),e.glyphs&&(e.glyphs=function(e){var t=h.parse(e),r=t.pathname.split("/");if("mapbox:"!==t.protocol)return e;if("fontstack"===t.hostname)return n("/{fontstack}/{range}.pbf"===decodeURI(t.pathname)),"mapbox://fonts/mapbox/{fontstack}/{range}.pbf";if("fonts"===t.hostname)return n("v1"===r[1]),n("{fontstack}"===decodeURI(r[3])),n("{range}.pbf"===decodeURI(r[4])),"mapbox://fonts/"+r[2]+"/{fontstack}/{range}.pbf";function n(t){if(!t)throw new Error('Invalid font url: "'+e+'"')}n(!1)}(e.glyphs)),F(e,(function(e){U(e,(function(e){e["text-font"]&&(e["text-font"]=function(e){function t(e){return e.split(",").map((function(e){return e.trim()}))}if(Array.isArray(e))return e;if("string"==typeof e)return t(e);if("object"===(void 0===e?"undefined":o(e)))return e.stops.forEach((function(e){e[1]=t(e[1])})),e;throw new Error("unexpected font value")}(e["text-font"]))}))}));for(var t=0,r=e.layers.length-1;r>=0;r--){if("symbol"!==e.layers[r].type){t=r+1;break}}var n=e.layers.splice(t);return n.reverse(),e.layers=e.layers.concat(n),e}function Y(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=!0,o=!1,a=void 0;try{for(var s,l=r[Symbol.iterator]();!(i=(s=l.next()).done);i=!0){var u=s.value;for(var c in u)e[c]=u[c]}}catch(e){o=!0,a=e}finally{try{!i&&l.return&&l.return()}finally{if(o)throw a}}return e}w.prototype.parse=function(e,t,r){if(!x.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+(void 0===e?"undefined":o(e)));var n=e.indexOf("?"),i=-1!==n&&n<e.indexOf("#")?"?":"#",a=e.split(i);a[0]=a[0].replace(/\\/g,"/");var s=e=a.join(i);if(s=s.trim(),!r&&1===e.split("#").length){var l=S.exec(s);if(l)return this.path=s,this.href=s,this.pathname=l[1],l[2]?(this.search=l[2],this.query=t?P.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var u=k.exec(s);if(u){var c=(u=u[0]).toLowerCase();this.protocol=c,s=s.substr(u.length)}if(r||u||s.match(/^\/\/[^@\/]+@[^@\/]+/)){var p="//"===s.substr(0,2);!p||u&&M[u]||(s=s.substr(2),this.slashes=!0)}if(!M[u]&&(p||u&&!I[u])){for(var d,h,f=-1,y=0;y<O.length;y++){-1!==(m=s.indexOf(O[y]))&&(-1===f||m<f)&&(f=m)}-1!==(h=-1===f?s.lastIndexOf("@"):s.lastIndexOf("@",f))&&(d=s.slice(0,h),s=s.slice(h+1),this.auth=decodeURIComponent(d)),f=-1;for(y=0;y<j.length;y++){var m;-1!==(m=s.indexOf(j[y]))&&(-1===f||m<f)&&(f=m)}-1===f&&(f=s.length),this.host=s.slice(0,f),s=s.slice(f),this.parseHost(),this.hostname=this.hostname||"";var v="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!v)for(var g=this.hostname.split(/\./),w=(y=0,g.length);y<w;y++){var _=g[y];if(_&&!_.match(A)){for(var T="",z=0,L=_.length;z<L;z++)_.charCodeAt(z)>127?T+="x":T+=_[z];if(!T.match(A)){var N=g.slice(0,y),F=g.slice(y+1),D=_.match(R);D&&(N.push(D[1]),F.unshift(D[2])),F.length&&(s="/"+F.join(".")+s),this.hostname=N.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),v||(this.hostname=b.toASCII(this.hostname));var U=this.port?":"+this.port:"",q=this.hostname||"";this.host=q+U,this.href+=this.host,v&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==s[0]&&(s="/"+s))}if(!C[c])for(y=0,w=E.length;y<w;y++){var G=E[y];if(-1!==s.indexOf(G)){var B=encodeURIComponent(G);B===G&&(B=escape(G)),s=s.split(G).join(B)}}var W=s.indexOf("#");-1!==W&&(this.hash=s.substr(W),s=s.slice(0,W));var Y=s.indexOf("?");if(-1!==Y?(this.search=s.substr(Y),this.query=s.substr(Y+1),t&&(this.query=P.parse(this.query)),s=s.slice(0,Y)):t&&(this.search="",this.query={}),s&&(this.pathname=s),I[c]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){U=this.pathname||"";var X=this.search||"";this.path=U+X}return this.href=this.format(),this},w.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",i=!1,o="";this.host?i=e+this.host:this.hostname&&(i=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&x.isObject(this.query)&&Object.keys(this.query).length&&(o=P.stringify(this.query));var a=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||I[t])&&!1!==i?(i="//"+(i||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):i||(i=""),n&&"#"!==n.charAt(0)&&(n="#"+n),a&&"?"!==a.charAt(0)&&(a="?"+a),t+i+(r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(a=a.replace("#","%23"))+n},w.prototype.resolve=function(e){return this.resolveObject(z(e,!1,!0)).format()},w.prototype.resolveObject=function(e){if(x.isString(e)){var t=new w;t.parse(e,!1,!0),e=t}for(var r=new w,n=Object.keys(this),i=0;i<n.length;i++){var o=n[i];r[o]=this[o]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var a=Object.keys(e),s=0;s<a.length;s++){var l=a[s];"protocol"!==l&&(r[l]=e[l])}return I[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!I[e.protocol]){for(var u=Object.keys(e),c=0;c<u.length;c++){var p=u[c];r[p]=e[p]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||M[e.protocol])r.pathname=e.pathname;else{for(var d=(e.pathname||"").split("/");d.length&&!(e.host=d.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==d[0]&&d.unshift(""),d.length<2&&d.unshift(""),r.pathname=d.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var h=r.pathname||"",f=r.search||"";r.path=h+f}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var y=r.pathname&&"/"===r.pathname.charAt(0),m=e.host||e.pathname&&"/"===e.pathname.charAt(0),v=m||y||r.host&&e.pathname,g=v,b=r.pathname&&r.pathname.split("/")||[],k=(d=e.pathname&&e.pathname.split("/")||[],r.protocol&&!I[r.protocol]);if(k&&(r.hostname="",r.port=null,r.host&&(""===b[0]?b[0]=r.host:b.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===d[0]?d[0]=e.host:d.unshift(e.host)),e.host=null),v=v&&(""===d[0]||""===b[0])),m)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,b=d;else if(d.length)b||(b=[]),b.pop(),b=b.concat(d),r.search=e.search,r.query=e.query;else if(!x.isNullOrUndefined(e.search)){if(k)r.hostname=r.host=b.shift(),(j=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=j.shift(),r.host=r.hostname=j.shift());return r.search=e.search,r.query=e.query,x.isNull(r.pathname)&&x.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!b.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var _=b.slice(-1)[0],S=(r.host||e.host||b.length>1)&&("."===_||".."===_)||""===_,T=0,E=b.length;E>=0;E--)"."===(_=b[E])?b.splice(E,1):".."===_?(b.splice(E,1),T++):T&&(b.splice(E,1),T--);if(!v&&!g)for(;T--;T)b.unshift("..");!v||""===b[0]||b[0]&&"/"===b[0].charAt(0)||b.unshift(""),S&&"/"!==b.join("/").substr(-1)&&b.push("");var j,O=""===b[0]||b[0]&&"/"===b[0].charAt(0);k&&(r.hostname=r.host=O?"":b.length?b.shift():"",(j=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=j.shift(),r.host=r.hostname=j.shift()));return(v=v||r.host&&b.length)&&!O&&b.unshift(""),b.length?r.pathname=b.join("/"):(r.pathname=null,r.path=null),x.isNull(r.pathname)&&x.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},w.prototype.parseHost=function(){var e=this.host,t=_.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};var X=function(e){function t(e,r){s(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,r));return n.message=r,n.key=e,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,Error),t}(),J=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];s(this,e),this.parent=t,this.bindings={};var i=!0,o=!1,a=void 0;try{for(var l,u=r[Symbol.iterator]();!(i=(l=u.next()).done);i=!0){var c=l.value,p=n(c,2),d=p[0],h=p[1];this.bindings[d]=h}}catch(e){o=!0,a=e}finally{try{!i&&u.return&&u.return()}finally{if(o)throw a}}}return i(e,[{key:"concat",value:function(t){return new e(this,t)}},{key:"get",value:function(e){if(this.bindings[e])return this.bindings[e];if(this.parent)return this.parent.get(e);throw new Error(e+" not found in scope.")}},{key:"has",value:function(e){return!!this.bindings[e]||!!this.parent&&this.parent.has(e)}}]),e}(),Z={kind:"null"},K={kind:"number"},H={kind:"string"},V={kind:"boolean"},$={kind:"color"},Q={kind:"object"},ee={kind:"value"},te={kind:"collator"},re={kind:"formatted"},ne={kind:"resolvedImage"};function ie(e,t){return{kind:"array",itemType:e,N:t}}function oe(e){if("array"===e.kind){var t=oe(e.itemType);return"number"==typeof e.N?"array<"+t+", "+e.N+">":"value"===e.itemType.kind?"array":"array<"+t+">"}return e.kind}var ae=[Z,K,H,V,$,re,Q,ie(ee),ne];function se(e,t){if("error"===t.kind)return null;if("array"===e.kind){if("array"===t.kind&&(0===t.N&&"value"===t.itemType.kind||!se(e.itemType,t.itemType))&&("number"!=typeof e.N||e.N===t.N))return null}else{if(e.kind===t.kind)return null;if("value"===e.kind){var r=!0,n=!1,i=void 0;try{for(var o,a=ae[Symbol.iterator]();!(r=(o=a.next()).done);r=!0){if(!se(o.value,t))return null}}catch(e){n=!0,i=e}finally{try{!r&&a.return&&a.return()}finally{if(n)throw i}}}}return"Expected "+oe(e)+" but found "+oe(t)+" instead."}function le(e,t){return t.some((function(t){return t.kind===e.kind}))}function ue(e,t){return t.some((function(t){return"null"===t?null===e:"array"===t?Array.isArray(e):"object"===t?e&&!Array.isArray(e)&&"object"===(void 0===e?"undefined":o(e)):t===(void 0===e?"undefined":o(e))}))}var ce,pe={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],rebeccapurple:[102,51,153,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function de(e){return(e=Math.round(e))<0?0:e>255?255:e}function he(e){return e<0?0:e>1?1:e}function fe(e){return"%"===e[e.length-1]?de(parseFloat(e)/100*255):de(parseInt(e))}function ye(e){return"%"===e[e.length-1]?he(parseFloat(e)/100):he(parseFloat(e))}function me(e,t,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?e+(t-e)*r*6:2*r<1?t:3*r<2?e+(t-e)*(2/3-r)*6:e}try{ce={}.parseCSSColor=function(e){var t,r=e.replace(/ /g,"").toLowerCase();if(r in pe)return pe[r].slice();if("#"===r[0])return 4===r.length?(t=parseInt(r.substr(1),16))>=0&&t<=4095?[(3840&t)>>4|(3840&t)>>8,240&t|(240&t)>>4,15&t|(15&t)<<4,1]:null:7===r.length&&(t=parseInt(r.substr(1),16))>=0&&t<=16777215?[(16711680&t)>>16,(65280&t)>>8,255&t,1]:null;var n=r.indexOf("("),i=r.indexOf(")");if(-1!==n&&i+1===r.length){var o=r.substr(0,n),a=r.substr(n+1,i-(n+1)).split(","),s=1;switch(o){case"rgba":if(4!==a.length)return null;s=ye(a.pop());case"rgb":return 3!==a.length?null:[fe(a[0]),fe(a[1]),fe(a[2]),s];case"hsla":if(4!==a.length)return null;s=ye(a.pop());case"hsl":if(3!==a.length)return null;var l=(parseFloat(a[0])%360+360)%360/360,u=ye(a[1]),c=ye(a[2]),p=c<=.5?c*(u+1):c+u-c*u,d=2*c-p;return[de(255*me(d,p,l+1/3)),de(255*me(d,p,l)),de(255*me(d,p,l-1/3)),s];default:return null}}return null}}catch(e){}var ve=function(){function e(t,r,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;s(this,e),this.r=t,this.g=r,this.b=n,this.a=i}return i(e,[{key:"toString",value:function(){var e=this.toArray(),t=n(e,4),r=t[0],i=t[1],o=t[2],a=t[3];return"rgba("+Math.round(r)+","+Math.round(i)+","+Math.round(o)+","+a+")"}},{key:"toArray",value:function(){var e=this.r,t=this.g,r=this.b,n=this.a;return 0===n?[0,0,0,0]:[255*e/n,255*t/n,255*r/n,n]}},{key:"toArray01",value:function(){var e=this.r,t=this.g,r=this.b,n=this.a;return 0===n?[0,0,0,0]:[e/n,t/n,r/n,n]}},{key:"toArray01PremultipliedAlpha",value:function(){return[this.r,this.g,this.b,this.a]}}],[{key:"parse",value:function(t){if(t){if(t instanceof e)return t;if("string"==typeof t){var r=ce(t);if(r)return new e(r[0]/255*r[3],r[1]/255*r[3],r[2]/255*r[3],r[3])}}}}]),e}();ve.black=new ve(0,0,0,1),ve.white=new ve(1,1,1,1),ve.transparent=new ve(0,0,0,0),ve.red=new ve(1,0,0,1),ve.blue=new ve(0,0,1,1);var ge=ve,be=function(){function e(t,r,n){s(this,e),this.sensitivity=t?r?"variant":"case":r?"accent":"base",this.locale=n,this.collator=new Intl.Collator(this.locale?this.locale:[],{sensitivity:this.sensitivity,usage:"search"})}return i(e,[{key:"compare",value:function(e,t){return this.collator.compare(e,t)}},{key:"resolvedLocale",value:function(){return new Intl.Collator(this.locale?this.locale:[]).resolvedOptions().locale}}]),e}(),xe=function e(t,r,n,i,o){s(this,e),this.text=t.normalize?t.normalize():t,this.image=r,this.scale=n,this.fontStack=i,this.textColor=o},we=function(){function e(t){s(this,e),this.sections=t}return i(e,[{key:"isEmpty",value:function(){return 0===this.sections.length||!this.sections.some((function(e){return 0!==e.text.length||e.image&&0!==e.image.name.length}))}},{key:"toString",value:function(){return 0===this.sections.length?"":this.sections.map((function(e){return e.text})).join("")}},{key:"serialize",value:function(){var e=["format"],t=!0,r=!1,n=void 0;try{for(var i,o=this.sections[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){var a=i.value;if(a.image)e.push(["image",a.image.name]);else{e.push(a.text);var s={};a.fontStack&&(s["text-font"]=["literal",a.fontStack.split(",")]),a.scale&&(s["font-scale"]=a.scale),a.textColor&&(s["text-color"]=["rgba"].concat(a.textColor.toArray())),e.push(s)}}}catch(e){r=!0,n=e}finally{try{!t&&o.return&&o.return()}finally{if(r)throw n}}return e}}],[{key:"fromString",value:function(t){return new e([new xe(t,null,null,null,null)])}},{key:"factory",value:function(t){return t instanceof e?t:e.fromString(t)}}]),e}(),ke=function(){function e(t){s(this,e),this.name=t.name,this.available=t.available}return i(e,[{key:"toString",value:function(){return this.name}},{key:"serialize",value:function(){return["image",this.name]}}],[{key:"fromString",value:function(t){return t?new e({name:t,available:!1}):null}}]),e}();function _e(e,t,r,n){return"number"==typeof e&&e>=0&&e<=255&&"number"==typeof t&&t>=0&&t<=255&&"number"==typeof r&&r>=0&&r<=255?void 0===n||"number"==typeof n&&n>=0&&n<=1?null:"Invalid rgba value ["+[e,t,r,n].join(", ")+"]: 'a' must be between 0 and 1.":"Invalid rgba value ["+("number"==typeof n?[e,t,r,n]:[e,t,r]).join(", ")+"]: 'r', 'g', and 'b' must be between 0 and 255."}function Se(e){if(null===e)return!0;if("string"==typeof e)return!0;if("boolean"==typeof e)return!0;if("number"==typeof e)return!0;if(e instanceof ge)return!0;if(e instanceof be)return!0;if(e instanceof we)return!0;if(e instanceof ke)return!0;if(Array.isArray(e)){var t=!0,r=!1,n=void 0;try{for(var i,a=e[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){if(!Se(i.value))return!1}}catch(e){r=!0,n=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw n}}return!0}if("object"===(void 0===e?"undefined":o(e))){for(var s in e)if(!Se(e[s]))return!1;return!0}return!1}function Te(e){if(null===e)return Z;if("string"==typeof e)return H;if("boolean"==typeof e)return V;if("number"==typeof e)return K;if(e instanceof ge)return $;if(e instanceof be)return te;if(e instanceof we)return re;if(e instanceof ke)return ne;if(Array.isArray(e)){var t=e.length,r=void 0,n=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var l=Te(a.value);if(r){if(r===l)continue;r=ee;break}r=l}}catch(e){i=!0,o=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw o}}return ie(r||ee,t)}return Q}function Ee(e){var t=void 0===e?"undefined":o(e);return null===e?"":"string"===t||"number"===t||"boolean"===t?String(e):e instanceof ge||e instanceof we||e instanceof ke?e.toString():JSON.stringify(e)}var je=function(){function e(t,r){s(this,e),this.type=t,this.value=r}return i(e,[{key:"evaluate",value:function(){return this.value}},{key:"eachChild",value:function(){}},{key:"outputDefined",value:function(){return!0}},{key:"serialize",value:function(){return"array"===this.type.kind||"object"===this.type.kind?["literal",this.value]:this.value instanceof ge?["rgba"].concat(this.value.toArray()):this.value instanceof we?this.value.serialize():this.value}}],[{key:"parse",value:function(t,r){if(2!==t.length)return r.error("'literal' expression requires exactly one argument, but found "+(t.length-1)+" instead.");if(!Se(t[1]))return r.error("invalid value");var n=t[1],i=Te(n),o=r.expectedType;return"array"!==i.kind||0!==i.N||!o||"array"!==o.kind||"number"==typeof o.N&&0!==o.N||(i=o),new e(i,n)}}]),e}(),Oe=function(){function e(t){s(this,e),this.name="ExpressionEvaluationError",this.message=t}return i(e,[{key:"toJSON",value:function(){return this.message}}]),e}(),Ae={string:H,number:K,boolean:V,object:Q},Re=function(){function e(t,r){s(this,e),this.type=t,this.args=r}return i(e,[{key:"evaluate",value:function(e){for(var t=0;t<this.args.length;t++){var r=this.args[t].evaluate(e);if(!se(this.type,Te(r)))return r;if(t===this.args.length-1)throw new Oe("Expected value to be of type "+oe(this.type)+", but found "+oe(Te(r))+" instead.")}return null}},{key:"eachChild",value:function(e){this.args.forEach(e)}},{key:"outputDefined",value:function(){return this.args.every((function(e){return e.outputDefined()}))}},{key:"serialize",value:function(){var e=this.type,t=[e.kind];if("array"===e.kind){var r=e.itemType;if("string"===r.kind||"number"===r.kind||"boolean"===r.kind){t.push(r.kind);var n=e.N;("number"==typeof n||this.args.length>1)&&t.push(n)}}return t.concat(this.args.map((function(e){return e.serialize()})))}}],[{key:"parse",value:function(t,r){if(t.length<2)return r.error("Expected at least one argument.");var n=1,i=void 0,o=t[0];if("array"===o){var a=void 0;if(t.length>2){var s=t[1];if("string"!=typeof s||!(s in Ae)||"object"===s)return r.error('The item type argument of "array" must be one of string, number, boolean',1);a=Ae[s],n++}else a=ee;var l=void 0;if(t.length>3){if(null!==t[2]&&("number"!=typeof t[2]||t[2]<0||t[2]!==Math.floor(t[2])))return r.error('The length argument to "array" must be a positive integer literal',2);l=t[2],n++}i=ie(a,l)}else i=Ae[o];for(var u=[];n<t.length;n++){var c=r.parse(t[n],n,ee);if(!c)return null;u.push(c)}return new e(i,u)}}]),e}(),Ce=function(){function e(t){s(this,e),this.type=re,this.sections=t}return i(e,[{key:"evaluate",value:function(e){return new we(this.sections.map((function(t){var r=t.content.evaluate(e);return Te(r)===ne?new xe("",r,null,null,null):new xe(Ee(r),null,t.scale?t.scale.evaluate(e):null,t.font?t.font.evaluate(e).join(","):null,t.textColor?t.textColor.evaluate(e):null)})))}},{key:"eachChild",value:function(e){var t=!0,r=!1,n=void 0;try{for(var i,o=this.sections[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){var a=i.value;e(a.content),a.scale&&e(a.scale),a.font&&e(a.font),a.textColor&&e(a.textColor)}}catch(e){r=!0,n=e}finally{try{!t&&o.return&&o.return()}finally{if(r)throw n}}}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){var e=["format"],t=!0,r=!1,n=void 0;try{for(var i,o=this.sections[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){var a=i.value;e.push(a.content.serialize());var s={};a.scale&&(s["font-scale"]=a.scale.serialize()),a.font&&(s["text-font"]=a.font.serialize()),a.textColor&&(s["text-color"]=a.textColor.serialize()),e.push(s)}}catch(e){r=!0,n=e}finally{try{!t&&o.return&&o.return()}finally{if(r)throw n}}return e}}],[{key:"parse",value:function(t,r){if(t.length<2)return r.error("Expected at least one argument.");var n=t[1];if(!Array.isArray(n)&&"object"===(void 0===n?"undefined":o(n)))return r.error("First argument must be an image or text section.");for(var i=[],a=!1,s=1;s<=t.length-1;++s){var l=t[s];if(a&&"object"===(void 0===l?"undefined":o(l))&&!Array.isArray(l)){a=!1;var u=null;if(l["font-scale"]&&!(u=r.parse(l["font-scale"],1,K)))return null;var c=null;if(l["text-font"]&&!(c=r.parse(l["text-font"],1,ie(H))))return null;var p=null;if(l["text-color"]&&!(p=r.parse(l["text-color"],1,$)))return null;var d=i[i.length-1];d.scale=u,d.font=c,d.textColor=p}else{var h=r.parse(t[s],1,ee);if(!h)return null;var f=h.type.kind;if("string"!==f&&"value"!==f&&"null"!==f&&"resolvedImage"!==f)return r.error("Formatted text type must be 'string', 'value', 'image' or 'null'.");a=!0,i.push({content:h,scale:null,font:null,textColor:null})}}return new e(i)}}]),e}(),Me=function(){function e(t){s(this,e),this.type=ne,this.input=t}return i(e,[{key:"evaluate",value:function(e){var t=this.input.evaluate(e),r=ke.fromString(t);return r&&e.availableImages&&(r.available=e.availableImages.indexOf(t)>-1),r}},{key:"eachChild",value:function(e){e(this.input)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){return["image",this.input.serialize()]}}],[{key:"parse",value:function(t,r){if(2!==t.length)return r.error("Expected two arguments.");var n=r.parse(t[1],1,H);return n?new e(n):r.error("No image name provided.")}}]),e}(),Ie={"to-boolean":V,"to-color":$,"to-number":K,"to-string":H},Pe=function(){function e(t,r){s(this,e),this.type=t,this.args=r}return i(e,[{key:"evaluate",value:function(e){if("boolean"===this.type.kind)return Boolean(this.args[0].evaluate(e));if("color"===this.type.kind){var t=void 0,r=void 0,n=!0,i=!1,o=void 0;try{for(var a,s=this.args[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){if(t=a.value.evaluate(e),r=null,t instanceof ge)return t;if("string"==typeof t){var l=e.parseColor(t);if(l)return l}else if(Array.isArray(t)&&!(r=t.length<3||t.length>4?"Invalid rbga value "+JSON.stringify(t)+": expected an array containing either three or four numeric values.":_e(t[0],t[1],t[2],t[3])))return new ge(t[0]/255,t[1]/255,t[2]/255,t[3])}}catch(e){i=!0,o=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw o}}throw new Oe(r||"Could not parse color from value '"+("string"==typeof t?t:String(JSON.stringify(t)))+"'")}if("number"===this.type.kind){var u=null,c=!0,p=!1,d=void 0;try{for(var h,f=this.args[Symbol.iterator]();!(c=(h=f.next()).done);c=!0){if(null===(u=h.value.evaluate(e)))return 0;var y=Number(u);if(!isNaN(y))return y}}catch(e){p=!0,d=e}finally{try{!c&&f.return&&f.return()}finally{if(p)throw d}}throw new Oe("Could not convert "+JSON.stringify(u)+" to number.")}return"formatted"===this.type.kind?we.fromString(Ee(this.args[0].evaluate(e))):"resolvedImage"===this.type.kind?ke.fromString(Ee(this.args[0].evaluate(e))):Ee(this.args[0].evaluate(e))}},{key:"eachChild",value:function(e){this.args.forEach(e)}},{key:"outputDefined",value:function(){return this.args.every((function(e){return e.outputDefined()}))}},{key:"serialize",value:function(){if("formatted"===this.type.kind)return new Ce([{content:this.args[0],scale:null,font:null,textColor:null}]).serialize();if("resolvedImage"===this.type.kind)return new Me(this.args[0]).serialize();var e=["to-"+this.type.kind];return this.eachChild((function(t){e.push(t.serialize())})),e}}],[{key:"parse",value:function(t,r){if(t.length<2)return r.error("Expected at least one argument.");var n=t[0];if(("to-boolean"===n||"to-string"===n)&&2!==t.length)return r.error("Expected one argument.");for(var i=Ie[n],o=[],a=1;a<t.length;a++){var s=r.parse(t[a],a,ee);if(!s)return null;o.push(s)}return new e(i,o)}}]),e}(),ze=["Unknown","Point","LineString","Polygon"],Le=function(){function e(){s(this,e),this.globals=null,this.feature=null,this.featureState=null,this.formattedSection=null,this._parseColorCache={},this.availableImages=null,this.canonical=null,this.featureTileCoord=null,this.featureDistanceData=null}return i(e,[{key:"id",value:function(){return this.feature&&void 0!==this.feature.id?this.feature.id:null}},{key:"geometryType",value:function(){return this.feature?"number"==typeof this.feature.type?ze[this.feature.type]:this.feature.type:null}},{key:"geometry",value:function(){return this.feature&&"geometry"in this.feature?this.feature.geometry:null}},{key:"canonicalID",value:function(){return this.canonical}},{key:"properties",value:function(){return this.feature&&this.feature.properties||{}}},{key:"distanceFromCenter",value:function(){if(this.featureTileCoord&&this.featureDistanceData){var e=this.featureDistanceData.center,t=this.featureDistanceData.scale,r=this.featureTileCoord,n=r.x,i=r.y,o=n*t-e[0],a=i*t-e[1];return this.featureDistanceData.bearing[0]*o+this.featureDistanceData.bearing[1]*a}return 0}},{key:"parseColor",value:function(e){var t=this._parseColorCache[e];return t||(t=this._parseColorCache[e]=ge.parse(e)),t}}]),e}();var Ne=function(){function e(t,r,n,i){s(this,e),this.name=t,this.type=r,this._evaluate=n,this.args=i}return i(e,[{key:"evaluate",value:function(e){return this._evaluate(e,this.args)}},{key:"eachChild",value:function(e){this.args.forEach(e)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){return[this.name].concat(this.args.map((function(e){return e.serialize()})))}}],[{key:"parse",value:function(t,r){var i=t[0],o=e.definitions[i];if(!o)return r.error('Unknown expression "'+i+'". If you wanted a literal array, use ["literal", [...]].',0);var s=Array.isArray(o)?o[0]:o.type,l=Array.isArray(o)?[[o[1],o[2]]]:o.overloads,u=l.filter((function(e){var r=n(e,1)[0];return!Array.isArray(r)||r.length===t.length-1})),c=null,p=!0,d=!1,h=void 0;try{for(var f,y=u[Symbol.iterator]();!(p=(f=y.next()).done);p=!0){var m=f.value,v=n(m,2),g=v[0],b=v[1];c=new ot(r.registry,r.path,null,r.scope);for(var x=[],w=!1,k=1;k<t.length;k++){var _=t[k],S=Array.isArray(g)?g[k-1]:g.type,T=c.parse(_,1+x.length,S);if(!T){w=!0;break}x.push(T)}if(!w)if(Array.isArray(g)&&g.length!==x.length)c.error("Expected "+g.length+" arguments, but found "+x.length+" instead.");else{for(var E=0;E<x.length;E++){var j=Array.isArray(g)?g[E]:g.type,O=x[E];c.concat(E+1).checkSubtype(j,O.type)}if(0===c.errors.length)return new e(i,s,b,x)}}}catch(e){d=!0,h=e}finally{try{!p&&y.return&&y.return()}finally{if(d)throw h}}if(1===u.length){var A;(A=r.errors).push.apply(A,a(c.errors))}else{for(var R=(u.length?u:l).map((function(e){var t,r=n(e,1)[0];return t=r,Array.isArray(t)?"("+t.map(oe).join(", ")+")":"("+oe(t.type)+"...)"})).join(" | "),C=[],M=1;M<t.length;M++){var I=r.parse(t[M],1+C.length);if(!I)return null;C.push(oe(I.type))}r.error("Expected arguments of type "+R+", but found ("+C.join(", ")+") instead.")}return null}},{key:"register",value:function(t,r){for(var n in e.definitions=r,r)t[n]=e}}]),e}(),Fe=function(){function e(t,r,n){s(this,e),this.type=te,this.locale=n,this.caseSensitive=t,this.diacriticSensitive=r}return i(e,[{key:"evaluate",value:function(e){return new be(this.caseSensitive.evaluate(e),this.diacriticSensitive.evaluate(e),this.locale?this.locale.evaluate(e):null)}},{key:"eachChild",value:function(e){e(this.caseSensitive),e(this.diacriticSensitive),this.locale&&e(this.locale)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){var e={};return e["case-sensitive"]=this.caseSensitive.serialize(),e["diacritic-sensitive"]=this.diacriticSensitive.serialize(),this.locale&&(e.locale=this.locale.serialize()),["collator",e]}}],[{key:"parse",value:function(t,r){if(2!==t.length)return r.error("Expected one argument.");var n=t[1];if("object"!==(void 0===n?"undefined":o(n))||Array.isArray(n))return r.error("Collator options argument must be an object.");var i=r.parse(void 0!==n["case-sensitive"]&&n["case-sensitive"],1,V);if(!i)return null;var a=r.parse(void 0!==n["diacritic-sensitive"]&&n["diacritic-sensitive"],1,V);if(!a)return null;var s=null;return n.locale&&!(s=r.parse(n.locale,1,H))?null:new e(i,a,s)}}]),e}();function De(e,t){e[0]=Math.min(e[0],t[0]),e[1]=Math.min(e[1],t[1]),e[2]=Math.max(e[2],t[0]),e[3]=Math.max(e[3],t[1])}function Ue(e,t){return!(e[0]<=t[0])&&(!(e[2]>=t[2])&&(!(e[1]<=t[1])&&!(e[3]>=t[3])))}function qe(e,t){var r,n=(180+e[0])/360,i=(r=e[1],(180-180/Math.PI*Math.log(Math.tan(Math.PI/4+r*Math.PI/360)))/360),o=Math.pow(2,t.z);return[Math.round(n*o*8192),Math.round(i*o*8192)]}function Ge(e,t,r){return t[1]>e[1]!=r[1]>e[1]&&e[0]<(r[0]-t[0])*(e[1]-t[1])/(r[1]-t[1])+t[0]}function Be(e,t){for(var r,n,i,o,a,s,l,u=!1,c=0,p=t.length;c<p;c++)for(var d=t[c],h=0,f=d.length;h<f-1;h++){if(r=e,n=d[h],i=d[h+1],o=void 0,a=void 0,s=void 0,l=void 0,o=r[0]-n[0],a=r[1]-n[1],s=r[0]-i[0],l=r[1]-i[1],o*l-s*a==0&&o*s<=0&&a*l<=0)return!1;Ge(e,d[h],d[h+1])&&(u=!u)}return u}function We(e,t){for(var r=0;r<t.length;r++)if(Be(e,t[r]))return!0;return!1}function Ye(e,t,r,n){var i=e[0]-r[0],o=e[1]-r[1],a=t[0]-r[0],s=t[1]-r[1],l=n[0]-r[0],u=n[1]-r[1],c=i*u-l*o,p=a*u-l*s;return c>0&&p<0||c<0&&p>0}function Xe(e,t,r){var n,i,o,a,s,l,u,c,p=!0,d=!1,h=void 0;try{for(var f,y=r[Symbol.iterator]();!(p=(f=y.next()).done);p=!0)for(var m=f.value,v=0;v<m.length-1;++v)if(n=e,i=t,o=m[v],a=m[v+1],s=void 0,l=void 0,u=void 0,c=void 0,u=[i[0]-n[0],i[1]-n[1]],c=[a[0]-o[0],a[1]-o[1]],0!=(s=c)[0]*(l=u)[1]-s[1]*l[0]&&Ye(n,i,o,a)&&Ye(o,a,n,i))return!0}catch(e){d=!0,h=e}finally{try{!p&&y.return&&y.return()}finally{if(d)throw h}}return!1}function Je(e,t){for(var r=0;r<e.length;++r)if(!Be(e[r],t))return!1;for(var n=0;n<e.length-1;++n)if(Xe(e[n],e[n+1],t))return!1;return!0}function Ze(e,t){for(var r=0;r<t.length;r++)if(Je(e,t[r]))return!0;return!1}function Ke(e,t,r){for(var n=[],i=0;i<e.length;i++){for(var o=[],a=0;a<e[i].length;a++){var s=qe(e[i][a],r);De(t,s),o.push(s)}n.push(o)}return n}function He(e,t,r){for(var n=[],i=0;i<e.length;i++){var o=Ke(e[i],t,r);n.push(o)}return n}function Ve(e,t,r,n){if(e[0]<r[0]||e[0]>r[2]){var i=.5*n,o=e[0]-r[0]>i?-n:r[0]-e[0]>i?n:0;0===o&&(o=e[0]-r[2]>i?-n:r[2]-e[0]>i?n:0),e[0]+=o}De(t,e)}function $e(e,t,r,n){var i=8192*Math.pow(2,n.z),o=[8192*n.x,8192*n.y],a=[];if(!e)return a;var s=!0,l=!1,u=void 0;try{for(var c,p=e[Symbol.iterator]();!(s=(c=p.next()).done);s=!0){var d=c.value,h=!0,f=!1,y=void 0;try{for(var m,v=d[Symbol.iterator]();!(h=(m=v.next()).done);h=!0){var g=m.value,b=[g.x+o[0],g.y+o[1]];Ve(b,t,r,i),a.push(b)}}catch(e){f=!0,y=e}finally{try{!h&&v.return&&v.return()}finally{if(f)throw y}}}}catch(e){l=!0,u=e}finally{try{!s&&p.return&&p.return()}finally{if(l)throw u}}return a}function Qe(e,t,r,n){var i=8192*Math.pow(2,n.z),o=[8192*n.x,8192*n.y],a=[];if(!e)return a;var s,l=!0,u=!1,c=void 0;try{for(var p,d=e[Symbol.iterator]();!(l=(p=d.next()).done);l=!0){var h=p.value,f=[],y=!0,m=!1,v=void 0;try{for(var g,b=h[Symbol.iterator]();!(y=(g=b.next()).done);y=!0){var x=g.value,w=[x.x+o[0],x.y+o[1]];De(t,w),f.push(w)}}catch(e){m=!0,v=e}finally{try{!y&&b.return&&b.return()}finally{if(m)throw v}}a.push(f)}}catch(e){u=!0,c=e}finally{try{!l&&d.return&&d.return()}finally{if(u)throw c}}if(t[2]-t[0]<=i/2){(s=t)[0]=s[1]=1/0,s[2]=s[3]=-1/0;var k=!0,_=!1,S=void 0;try{for(var T,E=a[Symbol.iterator]();!(k=(T=E.next()).done);k=!0){var j=T.value,O=!0,A=!1,R=void 0;try{for(var C,M=j[Symbol.iterator]();!(O=(C=M.next()).done);O=!0){Ve(C.value,t,r,i)}}catch(e){A=!0,R=e}finally{try{!O&&M.return&&M.return()}finally{if(A)throw R}}}}catch(e){_=!0,S=e}finally{try{!k&&E.return&&E.return()}finally{if(_)throw S}}}return a}var et=function(){function e(t,r){s(this,e),this.type=V,this.geojson=t,this.geometries=r}return i(e,[{key:"evaluate",value:function(e){if(null!=e.geometry()&&null!=e.canonicalID()){if("Point"===e.geometryType())return function(e,t){var r=[1/0,1/0,-1/0,-1/0],n=[1/0,1/0,-1/0,-1/0],i=e.canonicalID();if(!i)return!1;if("Polygon"===t.type){var o=Ke(t.coordinates,n,i),a=$e(e.geometry(),r,n,i);if(!Ue(r,n))return!1;var s=!0,l=!1,u=void 0;try{for(var c,p=a[Symbol.iterator]();!(s=(c=p.next()).done);s=!0){if(!Be(c.value,o))return!1}}catch(e){l=!0,u=e}finally{try{!s&&p.return&&p.return()}finally{if(l)throw u}}}if("MultiPolygon"===t.type){var d=He(t.coordinates,n,i),h=$e(e.geometry(),r,n,i);if(!Ue(r,n))return!1;var f=!0,y=!1,m=void 0;try{for(var v,g=h[Symbol.iterator]();!(f=(v=g.next()).done);f=!0){if(!We(v.value,d))return!1}}catch(e){y=!0,m=e}finally{try{!f&&g.return&&g.return()}finally{if(y)throw m}}}return!0}(e,this.geometries);if("LineString"===e.geometryType())return function(e,t){var r=[1/0,1/0,-1/0,-1/0],n=[1/0,1/0,-1/0,-1/0],i=e.canonicalID();if(!i)return!1;if("Polygon"===t.type){var o=Ke(t.coordinates,n,i),a=Qe(e.geometry(),r,n,i);if(!Ue(r,n))return!1;var s=!0,l=!1,u=void 0;try{for(var c,p=a[Symbol.iterator]();!(s=(c=p.next()).done);s=!0){if(!Je(c.value,o))return!1}}catch(e){l=!0,u=e}finally{try{!s&&p.return&&p.return()}finally{if(l)throw u}}}if("MultiPolygon"===t.type){var d=He(t.coordinates,n,i),h=Qe(e.geometry(),r,n,i);if(!Ue(r,n))return!1;var f=!0,y=!1,m=void 0;try{for(var v,g=h[Symbol.iterator]();!(f=(v=g.next()).done);f=!0){if(!Ze(v.value,d))return!1}}catch(e){y=!0,m=e}finally{try{!f&&g.return&&g.return()}finally{if(y)throw m}}}return!0}(e,this.geometries)}return!1}},{key:"eachChild",value:function(){}},{key:"outputDefined",value:function(){return!0}},{key:"serialize",value:function(){return["within",this.geojson]}}],[{key:"parse",value:function(t,r){if(2!==t.length)return r.error("'within' expression requires exactly one argument, but found "+(t.length-1)+" instead.");if(Se(t[1])){var n=t[1];if("FeatureCollection"===n.type)for(var i=0;i<n.features.length;++i){var o=n.features[i].geometry.type;if("Polygon"===o||"MultiPolygon"===o)return new e(n,n.features[i].geometry)}else if("Feature"===n.type){var a=n.geometry.type;if("Polygon"===a||"MultiPolygon"===a)return new e(n,n.geometry)}else if("Polygon"===n.type||"MultiPolygon"===n.type)return new e(n,n)}return r.error("'within' expression requires valid geojson object that contains polygon geometry type.")}}]),e}();function tt(e){if(e instanceof Ne){if("get"===e.name&&1===e.args.length)return!1;if("feature-state"===e.name)return!1;if("has"===e.name&&1===e.args.length)return!1;if("properties"===e.name||"geometry-type"===e.name||"id"===e.name)return!1;if(/^filter-/.test(e.name))return!1}if(e instanceof et)return!1;var t=!0;return e.eachChild((function(e){t&&!tt(e)&&(t=!1)})),t}function rt(e){if(e instanceof Ne&&"feature-state"===e.name)return!1;var t=!0;return e.eachChild((function(e){t&&!rt(e)&&(t=!1)})),t}function nt(e,t){if(e instanceof Ne&&t.indexOf(e.name)>=0)return!1;var r=!0;return e.eachChild((function(e){r&&!nt(e,t)&&(r=!1)})),r}var it=function(){function e(t,r){s(this,e),this.type=r.type,this.name=t,this.boundExpression=r}return i(e,[{key:"evaluate",value:function(e){return this.boundExpression.evaluate(e)}},{key:"eachChild",value:function(){}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){return["var",this.name]}}],[{key:"parse",value:function(t,r){if(2!==t.length||"string"!=typeof t[1])return r.error("'var' expression requires exactly one string literal argument.");var n=t[1];return r.scope.has(n)?new e(n,r.scope.get(n)):r.error('Unknown variable "'+n+'". Make sure "'+n+'" has been bound in an enclosing "let" expression before using it.',1)}}]),e}(),ot=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:new J,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[];s(this,e),this.registry=t,this.path=r,this.key=r.map((function(e){return"["+e+"]"})).join(""),this.scope=i,this.errors=o,this.expectedType=n}return i(e,[{key:"parse",value:function(e,t,r,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return t?this.concat(t,r,n)._parse(e,i):this._parse(e,i)}},{key:"_parse",value:function(e,t){function r(e,t,r){return"assert"===r?new Re(t,[e]):"coerce"===r?new Pe(t,[e]):e}if(null!==e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e||(e=["literal",e]),Array.isArray(e)){if(0===e.length)return this.error('Expected an array with at least one element. If you wanted a literal array, use ["literal", []].');var n=e[0];if("string"!=typeof n)return this.error("Expression name must be a string, but found "+(void 0===n?"undefined":o(n))+' instead. If you wanted a literal array, use ["literal", [...]].',0),null;var i=this.registry[n];if(i){var a=i.parse(e,this);if(!a)return null;if(this.expectedType){var s=this.expectedType,l=a.type;if("string"!==s.kind&&"number"!==s.kind&&"boolean"!==s.kind&&"object"!==s.kind&&"array"!==s.kind||"value"!==l.kind)if("color"!==s.kind&&"formatted"!==s.kind&&"resolvedImage"!==s.kind||"value"!==l.kind&&"string"!==l.kind){if(this.checkSubtype(s,l))return null}else a=r(a,s,t.typeAnnotation||"coerce");else a=r(a,s,t.typeAnnotation||"assert")}if(!(a instanceof je)&&"resolvedImage"!==a.type.kind&&function e(t){if(t instanceof it)return e(t.boundExpression);if(t instanceof Ne&&"error"===t.name)return!1;if(t instanceof Fe)return!1;if(t instanceof et)return!1;var r=t instanceof Pe||t instanceof Re,n=!0;if(t.eachChild((function(t){n=r?n&&e(t):n&&t instanceof je})),!n)return!1;return tt(t)&&nt(t,["zoom","heatmap-density","line-progress","sky-radial-progress","accumulated","is-supported-script","pitch","distance-from-center"])}(a)){var u=new Le;try{a=new je(a.type,a.evaluate(u))}catch(e){return this.error(e.message),null}}return a}return this.error('Unknown expression "'+n+'". If you wanted a literal array, use ["literal", [...]].',0)}return void 0===e?this.error("'undefined' value invalid. Use null instead."):"object"===(void 0===e?"undefined":o(e))?this.error('Bare objects invalid. Use ["literal", {...}] instead.'):this.error("Expected an array, but found "+(void 0===e?"undefined":o(e))+" instead.")}},{key:"concat",value:function(t,r,n){var i="number"==typeof t?this.path.concat(t):this.path,o=n?this.scope.concat(n):this.scope;return new e(this.registry,i,r||null,o,this.errors)}},{key:"error",value:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=""+this.key+r.map((function(e){return"["+e+"]"})).join("");this.errors.push(new X(i,e))}},{key:"checkSubtype",value:function(e,t){var r=se(e,t);return r&&this.error(r),r}}]),e}();function at(e,t){for(var r=e.length-1,n=0,i=r,o=0,a=void 0,s=void 0;n<=i;)if(a=e[o=Math.floor((n+i)/2)],s=e[o+1],a<=t){if(o===r||t<s)return o;n=o+1}else{if(!(a>t))throw new Oe("Input is not a number.");i=o-1}return 0}var st=function(){function e(t,r,i){s(this,e),this.type=t,this.input=r,this.labels=[],this.outputs=[];var o=!0,a=!1,l=void 0;try{for(var u,c=i[Symbol.iterator]();!(o=(u=c.next()).done);o=!0){var p=u.value,d=n(p,2),h=d[0],f=d[1];this.labels.push(h),this.outputs.push(f)}}catch(e){a=!0,l=e}finally{try{!o&&c.return&&c.return()}finally{if(a)throw l}}}return i(e,[{key:"evaluate",value:function(e){var t=this.labels,r=this.outputs;if(1===t.length)return r[0].evaluate(e);var n=this.input.evaluate(e);if(n<=t[0])return r[0].evaluate(e);var i=t.length;return n>=t[i-1]?r[i-1].evaluate(e):r[at(t,n)].evaluate(e)}},{key:"eachChild",value:function(e){e(this.input);var t=!0,r=!1,n=void 0;try{for(var i,o=this.outputs[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){e(i.value)}}catch(e){r=!0,n=e}finally{try{!t&&o.return&&o.return()}finally{if(r)throw n}}}},{key:"outputDefined",value:function(){return this.outputs.every((function(e){return e.outputDefined()}))}},{key:"serialize",value:function(){for(var e=["step",this.input.serialize()],t=0;t<this.labels.length;t++)t>0&&e.push(this.labels[t]),e.push(this.outputs[t].serialize());return e}}],[{key:"parse",value:function(t,r){if(t.length-1<4)return r.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if((t.length-1)%2!=0)return r.error("Expected an even number of arguments.");var n=r.parse(t[1],1,K);if(!n)return null;var i=[],o=null;r.expectedType&&"value"!==r.expectedType.kind&&(o=r.expectedType);for(var a=1;a<t.length;a+=2){var s=1===a?-1/0:t[a],l=t[a+1],u=a,c=a+1;if("number"!=typeof s)return r.error('Input/output pairs for "step" expressions must be defined using literal numeric values (not computed expressions) for the input values.',u);if(i.length&&i[i.length-1][0]>=s)return r.error('Input/output pairs for "step" expressions must be arranged with input values in strictly ascending order.',u);var p=r.parse(l,c,o);if(!p)return null;o=o||p.type,i.push([s,p])}return new e(o,n,i)}}]),e}(),lt=ut;function ut(e,t,r,n){this.cx=3*e,this.bx=3*(r-e)-this.cx,this.ax=1-this.cx-this.bx,this.cy=3*t,this.by=3*(n-t)-this.cy,this.ay=1-this.cy-this.by,this.p1x=e,this.p1y=t,this.p2x=r,this.p2y=n}function ct(e,t,r){return e*(1-r)+t*r}ut.prototype={sampleCurveX:function(e){return((this.ax*e+this.bx)*e+this.cx)*e},sampleCurveY:function(e){return((this.ay*e+this.by)*e+this.cy)*e},sampleCurveDerivativeX:function(e){return(3*this.ax*e+2*this.bx)*e+this.cx},solveCurveX:function(e,t){if(void 0===t&&(t=1e-6),e<0)return 0;if(e>1)return 1;for(var r=e,n=0;n<8;n++){var i=this.sampleCurveX(r)-e;if(Math.abs(i)<t)return r;var o=this.sampleCurveDerivativeX(r);if(Math.abs(o)<1e-6)break;r-=i/o}var a=0,s=1;for(r=e,n=0;n<20&&(i=this.sampleCurveX(r),!(Math.abs(i-e)<t));n++)e>i?a=r:s=r,r=.5*(s-a)+a;return r},solve:function(e,t){return this.sampleCurveY(this.solveCurveX(e,t))}};var pt=Object.freeze({__proto__:null,number:ct,color:function(e,t,r){return new ge(ct(e.r,t.r,r),ct(e.g,t.g,r),ct(e.b,t.b,r),ct(e.a,t.a,r))},array:function(e,t,r){return e.map((function(e,n){return ct(e,t[n],r)}))}}),dt=6/29,ht=3*dt*dt,ft=Math.PI/180,yt=180/Math.PI;function mt(e){return e>.008856451679035631?Math.pow(e,1/3):e/ht+4/29}function vt(e){return e>dt?e*e*e:ht*(e-4/29)}function gt(e){return 255*(e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055)}function bt(e){return(e/=255)<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}function xt(e){var t=bt(e.r),r=bt(e.g),n=bt(e.b),i=mt((.4124564*t+.3575761*r+.1804375*n)/.95047),o=mt((.2126729*t+.7151522*r+.072175*n)/1);return{l:116*o-16,a:500*(i-o),b:200*(o-mt((.0193339*t+.119192*r+.9503041*n)/1.08883)),alpha:e.a}}function wt(e){var t=(e.l+16)/116,r=isNaN(e.a)?t:t+e.a/500,n=isNaN(e.b)?t:t-e.b/200;return t=1*vt(t),r=.95047*vt(r),n=1.08883*vt(n),new ge(gt(3.2404542*r-1.5371385*t-.4985314*n),gt(-.969266*r+1.8760108*t+.041556*n),gt(.0556434*r-.2040259*t+1.0572252*n),e.alpha)}function kt(e,t,r){var n=t-e;return e+r*(n>180||n<-180?n-360*Math.round(n/360):n)}var _t={forward:xt,reverse:wt,interpolate:function(e,t,r){return{l:ct(e.l,t.l,r),a:ct(e.a,t.a,r),b:ct(e.b,t.b,r),alpha:ct(e.alpha,t.alpha,r)}}},St={forward:function(e){var t=xt(e),r=t.l,n=t.a,i=t.b,o=Math.atan2(i,n)*yt;return{h:o<0?o+360:o,c:Math.sqrt(n*n+i*i),l:r,alpha:e.a}},reverse:function(e){var t=e.h*ft,r=e.c;return wt({l:e.l,a:Math.cos(t)*r,b:Math.sin(t)*r,alpha:e.alpha})},interpolate:function(e,t,r){return{h:kt(e.h,t.h,r),c:ct(e.c,t.c,r),l:ct(e.l,t.l,r),alpha:ct(e.alpha,t.alpha,r)}}},Tt=Object.freeze({__proto__:null,lab:_t,hcl:St});function Et(e,t,r,n){var i=n-r,o=e-r;return 0===i?0:1===t?o/i:(Math.pow(t,o)-1)/(Math.pow(t,i)-1)}var jt=function(){function e(t,r,i,o,a){s(this,e),this.type=t,this.operator=r,this.interpolation=i,this.input=o,this.labels=[],this.outputs=[];var l=!0,u=!1,c=void 0;try{for(var p,d=a[Symbol.iterator]();!(l=(p=d.next()).done);l=!0){var h=p.value,f=n(h,2),y=f[0],m=f[1];this.labels.push(y),this.outputs.push(m)}}catch(e){u=!0,c=e}finally{try{!l&&d.return&&d.return()}finally{if(u)throw c}}}return i(e,[{key:"evaluate",value:function(t){var r=this.labels,n=this.outputs;if(1===r.length)return n[0].evaluate(t);var i=this.input.evaluate(t);if(i<=r[0])return n[0].evaluate(t);var o=r.length;if(i>=r[o-1])return n[o-1].evaluate(t);var a=at(r,i),s=r[a],l=r[a+1],u=e.interpolationFactor(this.interpolation,i,s,l),c=n[a].evaluate(t),p=n[a+1].evaluate(t);return"interpolate"===this.operator?pt[this.type.kind.toLowerCase()](c,p,u):"interpolate-hcl"===this.operator?St.reverse(St.interpolate(St.forward(c),St.forward(p),u)):_t.reverse(_t.interpolate(_t.forward(c),_t.forward(p),u))}},{key:"eachChild",value:function(e){e(this.input);var t=!0,r=!1,n=void 0;try{for(var i,o=this.outputs[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){e(i.value)}}catch(e){r=!0,n=e}finally{try{!t&&o.return&&o.return()}finally{if(r)throw n}}}},{key:"outputDefined",value:function(){return this.outputs.every((function(e){return e.outputDefined()}))}},{key:"serialize",value:function(){var e=void 0;e="linear"===this.interpolation.name?["linear"]:"exponential"===this.interpolation.name?1===this.interpolation.base?["linear"]:["exponential",this.interpolation.base]:["cubic-bezier"].concat(this.interpolation.controlPoints);for(var t=[this.operator,e,this.input.serialize()],r=0;r<this.labels.length;r++)t.push(this.labels[r],this.outputs[r].serialize());return t}}],[{key:"interpolationFactor",value:function(e,t,r,n){var i=0;if("exponential"===e.name)i=Et(t,e.base,r,n);else if("linear"===e.name)i=Et(t,1,r,n);else if("cubic-bezier"===e.name){var o=e.controlPoints;i=new lt(o[0],o[1],o[2],o[3]).solve(Et(t,1,r,n))}return i}},{key:"parse",value:function(t,r){var n,i=(n=t,Array.isArray(n)?n:Array.from(n)),o=i[0],a=i[1],s=i[2],l=i.slice(3);if(!Array.isArray(a)||0===a.length)return r.error("Expected an interpolation type expression.",1);if("linear"===a[0])a={name:"linear"};else if("exponential"===a[0]){var u=a[1];if("number"!=typeof u)return r.error("Exponential interpolation requires a numeric base.",1,1);a={name:"exponential",base:u}}else{if("cubic-bezier"!==a[0])return r.error("Unknown interpolation type "+String(a[0]),1,0);var c=a.slice(1);if(4!==c.length||c.some((function(e){return"number"!=typeof e||e<0||e>1})))return r.error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1.",1);a={name:"cubic-bezier",controlPoints:c}}if(t.length-1<4)return r.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if((t.length-1)%2!=0)return r.error("Expected an even number of arguments.");if(!(s=r.parse(s,2,K)))return null;var p=[],d=null;"interpolate-hcl"===o||"interpolate-lab"===o?d=$:r.expectedType&&"value"!==r.expectedType.kind&&(d=r.expectedType);for(var h=0;h<l.length;h+=2){var f=l[h],y=l[h+1],m=h+3,v=h+4;if("number"!=typeof f)return r.error('Input/output pairs for "interpolate" expressions must be defined using literal numeric values (not computed expressions) for the input values.',m);if(p.length&&p[p.length-1][0]>=f)return r.error('Input/output pairs for "interpolate" expressions must be arranged with input values in strictly ascending order.',m);var g=r.parse(y,v,d);if(!g)return null;d=d||g.type,p.push([f,g])}return"number"===d.kind||"color"===d.kind||"array"===d.kind&&"number"===d.itemType.kind&&"number"==typeof d.N?new e(d,o,a,s,p):r.error("Type "+oe(d)+" is not interpolatable.")}}]),e}(),Ot=function(){function e(t,r){s(this,e),this.type=t,this.args=r}return i(e,[{key:"evaluate",value:function(e){var t=null,r=0,n=void 0,i=!0,o=!1,a=void 0;try{for(var s,l=this.args[Symbol.iterator]();!(i=(s=l.next()).done);i=!0){var u=s.value;if(r++,(t=u.evaluate(e))&&t instanceof ke&&!t.available&&(n||(n=t),t=null,r===this.args.length))return n;if(null!==t)break}}catch(e){o=!0,a=e}finally{try{!i&&l.return&&l.return()}finally{if(o)throw a}}return t}},{key:"eachChild",value:function(e){this.args.forEach(e)}},{key:"outputDefined",value:function(){return this.args.every((function(e){return e.outputDefined()}))}},{key:"serialize",value:function(){var e=["coalesce"];return this.eachChild((function(t){e.push(t.serialize())})),e}}],[{key:"parse",value:function(t,r){if(t.length<2)return r.error("Expectected at least one argument.");var n=null,i=r.expectedType;i&&"value"!==i.kind&&(n=i);var o=[],a=!0,s=!1,l=void 0;try{for(var u,c=t.slice(1)[Symbol.iterator]();!(a=(u=c.next()).done);a=!0){var p=u.value,d=r.parse(p,1+o.length,n,void 0,{typeAnnotation:"omit"});if(!d)return null;n=n||d.type,o.push(d)}}catch(e){s=!0,l=e}finally{try{!a&&c.return&&c.return()}finally{if(s)throw l}}return new e(i&&o.some((function(e){return se(i,e.type)}))?ee:n,o)}}]),e}(),At=function(){function e(t,r){s(this,e),this.type=r.type,this.bindings=[].concat(t),this.result=r}return i(e,[{key:"evaluate",value:function(e){return this.result.evaluate(e)}},{key:"eachChild",value:function(e){var t=!0,r=!1,n=void 0;try{for(var i,o=this.bindings[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){e(i.value[1])}}catch(e){r=!0,n=e}finally{try{!t&&o.return&&o.return()}finally{if(r)throw n}}e(this.result)}},{key:"outputDefined",value:function(){return this.result.outputDefined()}},{key:"serialize",value:function(){var e=["let"],t=!0,r=!1,i=void 0;try{for(var o,a=this.bindings[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var s=o.value,l=n(s,2),u=l[0],c=l[1];e.push(u,c.serialize())}}catch(e){r=!0,i=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw i}}return e.push(this.result.serialize()),e}}],[{key:"parse",value:function(t,r){if(t.length<4)return r.error("Expected at least 3 arguments, but found "+(t.length-1)+" instead.");for(var n=[],i=1;i<t.length-1;i+=2){var a=t[i];if("string"!=typeof a)return r.error("Expected string, but found "+(void 0===a?"undefined":o(a))+" instead.",i);if(/[^a-zA-Z0-9_]/.test(a))return r.error("Variable names must contain only alphanumeric characters or '_'.",i);var s=r.parse(t[i+1],i+1);if(!s)return null;n.push([a,s])}var l=r.parse(t[t.length-1],t.length-1,r.expectedType,n);return l?new e(n,l):null}}]),e}(),Rt=function(){function e(t,r,n){s(this,e),this.type=t,this.index=r,this.input=n}return i(e,[{key:"evaluate",value:function(e){var t=this.index.evaluate(e),r=this.input.evaluate(e);if(t<0)throw new Oe("Array index out of bounds: "+t+" < 0.");if(t>=r.length)throw new Oe("Array index out of bounds: "+t+" > "+(r.length-1)+".");if(t!==Math.floor(t))throw new Oe("Array index must be an integer, but found "+t+" instead.");return r[t]}},{key:"eachChild",value:function(e){e(this.index),e(this.input)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){return["at",this.index.serialize(),this.input.serialize()]}}],[{key:"parse",value:function(t,r){if(3!==t.length)return r.error("Expected 2 arguments, but found "+(t.length-1)+" instead.");var n=r.parse(t[1],1,K),i=r.parse(t[2],2,ie(r.expectedType||ee));return n&&i?new e(i.type.itemType,n,i):null}}]),e}(),Ct=function(){function e(t,r){s(this,e),this.type=V,this.needle=t,this.haystack=r}return i(e,[{key:"evaluate",value:function(e){var t=this.needle.evaluate(e),r=this.haystack.evaluate(e);if(null==r)return!1;if(!ue(t,["boolean","string","number","null"]))throw new Oe("Expected first argument to be of type boolean, string, number or null, but found "+oe(Te(t))+" instead.");if(!ue(r,["string","array"]))throw new Oe("Expected second argument to be of type array or string, but found "+oe(Te(r))+" instead.");return r.indexOf(t)>=0}},{key:"eachChild",value:function(e){e(this.needle),e(this.haystack)}},{key:"outputDefined",value:function(){return!0}},{key:"serialize",value:function(){return["in",this.needle.serialize(),this.haystack.serialize()]}}],[{key:"parse",value:function(t,r){if(3!==t.length)return r.error("Expected 2 arguments, but found "+(t.length-1)+" instead.");var n=r.parse(t[1],1,ee),i=r.parse(t[2],2,ee);return n&&i?le(n.type,[V,H,K,Z,ee])?new e(n,i):r.error("Expected first argument to be of type boolean, string, number or null, but found "+oe(n.type)+" instead"):null}}]),e}(),Mt=function(){function e(t,r,n){s(this,e),this.type=K,this.needle=t,this.haystack=r,this.fromIndex=n}return i(e,[{key:"evaluate",value:function(e){var t=this.needle.evaluate(e),r=this.haystack.evaluate(e);if(!ue(t,["boolean","string","number","null"]))throw new Oe("Expected first argument to be of type boolean, string, number or null, but found "+oe(Te(t))+" instead.");if(!ue(r,["string","array"]))throw new Oe("Expected second argument to be of type array or string, but found "+oe(Te(r))+" instead.");if(this.fromIndex){var n=this.fromIndex.evaluate(e);return r.indexOf(t,n)}return r.indexOf(t)}},{key:"eachChild",value:function(e){e(this.needle),e(this.haystack),this.fromIndex&&e(this.fromIndex)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){if(null!=this.fromIndex&&void 0!==this.fromIndex){var e=this.fromIndex.serialize();return["index-of",this.needle.serialize(),this.haystack.serialize(),e]}return["index-of",this.needle.serialize(),this.haystack.serialize()]}}],[{key:"parse",value:function(t,r){if(t.length<=2||t.length>=5)return r.error("Expected 3 or 4 arguments, but found "+(t.length-1)+" instead.");var n=r.parse(t[1],1,ee),i=r.parse(t[2],2,ee);if(!n||!i)return null;if(!le(n.type,[V,H,K,Z,ee]))return r.error("Expected first argument to be of type boolean, string, number or null, but found "+oe(n.type)+" instead");if(4===t.length){var o=r.parse(t[3],3,K);return o?new e(n,i,o):null}return new e(n,i)}}]),e}(),It=function(){function e(t,r,n,i,o,a){s(this,e),this.inputType=t,this.type=r,this.input=n,this.cases=i,this.outputs=o,this.otherwise=a}return i(e,[{key:"evaluate",value:function(e){var t=this.input.evaluate(e);return(Te(t)===this.inputType&&this.outputs[this.cases[t]]||this.otherwise).evaluate(e)}},{key:"eachChild",value:function(e){e(this.input),this.outputs.forEach(e),e(this.otherwise)}},{key:"outputDefined",value:function(){return this.outputs.every((function(e){return e.outputDefined()}))&&this.otherwise.outputDefined()}},{key:"serialize",value:function(){var e=this,t=["match",this.input.serialize()],r=Object.keys(this.cases).sort(),i=[],o={},a=!0,s=!1,l=void 0;try{for(var u,c=r[Symbol.iterator]();!(a=(u=c.next()).done);a=!0){var p=u.value;void 0===(x=o[this.cases[p]])?(o[this.cases[p]]=i.length,i.push([this.cases[p],[p]])):i[x][1].push(p)}}catch(e){s=!0,l=e}finally{try{!a&&c.return&&c.return()}finally{if(s)throw l}}var d=function(t){return"number"===e.inputType.kind?Number(t):t},h=!0,f=!1,y=void 0;try{for(var m,v=i[Symbol.iterator]();!(h=(m=v.next()).done);h=!0){var g=m.value,b=n(g,2),x=b[0],w=b[1];1===w.length?t.push(d(w[0])):t.push(w.map(d)),t.push(this.outputs[x].serialize())}}catch(e){f=!0,y=e}finally{try{!h&&v.return&&v.return()}finally{if(f)throw y}}return t.push(this.otherwise.serialize()),t}}],[{key:"parse",value:function(t,r){if(t.length<5)return r.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if(t.length%2!=1)return r.error("Expected an even number of arguments.");var n=void 0,i=void 0;r.expectedType&&"value"!==r.expectedType.kind&&(i=r.expectedType);for(var o={},a=[],s=2;s<t.length-1;s+=2){var l=t[s],u=t[s+1];Array.isArray(l)||(l=[l]);var c=r.concat(s);if(0===l.length)return c.error("Expected at least one branch label.");var p=!0,d=!1,h=void 0;try{for(var f,y=l[Symbol.iterator]();!(p=(f=y.next()).done);p=!0){var m=f.value;if("number"!=typeof m&&"string"!=typeof m)return c.error("Branch labels must be numbers or strings.");if("number"==typeof m&&Math.abs(m)>Number.MAX_SAFE_INTEGER)return c.error("Branch labels must be integers no larger than "+Number.MAX_SAFE_INTEGER+".");if("number"==typeof m&&Math.floor(m)!==m)return c.error("Numeric branch labels must be integer values.");if(n){if(c.checkSubtype(n,Te(m)))return null}else n=Te(m);if(void 0!==o[String(m)])return c.error("Branch labels must be unique.");o[String(m)]=a.length}}catch(e){d=!0,h=e}finally{try{!p&&y.return&&y.return()}finally{if(d)throw h}}var v=r.parse(u,s,i);if(!v)return null;i=i||v.type,a.push(v)}var g=r.parse(t[1],1,ee);if(!g)return null;var b=r.parse(t[t.length-1],t.length-1,i);return b?"value"!==g.type.kind&&r.concat(1).checkSubtype(n,g.type)?null:new e(n,i,g,o,a,b):null}}]),e}(),Pt=function(){function e(t,r,n){s(this,e),this.type=t,this.branches=r,this.otherwise=n}return i(e,[{key:"evaluate",value:function(e){var t=!0,r=!1,i=void 0;try{for(var o,a=this.branches[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var s=o.value,l=n(s,2),u=l[0],c=l[1];if(u.evaluate(e))return c.evaluate(e)}}catch(e){r=!0,i=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw i}}return this.otherwise.evaluate(e)}},{key:"eachChild",value:function(e){var t=!0,r=!1,i=void 0;try{for(var o,a=this.branches[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var s=o.value,l=n(s,2),u=l[0],c=l[1];e(u),e(c)}}catch(e){r=!0,i=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw i}}e(this.otherwise)}},{key:"outputDefined",value:function(){return this.branches.every((function(e){var t=n(e,2);t[0];return t[1].outputDefined()}))&&this.otherwise.outputDefined()}},{key:"serialize",value:function(){var e=["case"];return this.eachChild((function(t){e.push(t.serialize())})),e}}],[{key:"parse",value:function(t,r){if(t.length<4)return r.error("Expected at least 3 arguments, but found only "+(t.length-1)+".");if(t.length%2!=0)return r.error("Expected an odd number of arguments.");var n=void 0;r.expectedType&&"value"!==r.expectedType.kind&&(n=r.expectedType);for(var i=[],o=1;o<t.length-1;o+=2){var a=r.parse(t[o],o,V);if(!a)return null;var s=r.parse(t[o+1],o+1,n);if(!s)return null;i.push([a,s]),n=n||s.type}var l=r.parse(t[t.length-1],t.length-1,n);return l?new e(n,i,l):null}}]),e}(),zt=function(){function e(t,r,n,i){s(this,e),this.type=t,this.input=r,this.beginIndex=n,this.endIndex=i}return i(e,[{key:"evaluate",value:function(e){var t=this.input.evaluate(e),r=this.beginIndex.evaluate(e);if(!ue(t,["string","array"]))throw new Oe("Expected first argument to be of type array or string, but found "+oe(Te(t))+" instead.");if(this.endIndex){var n=this.endIndex.evaluate(e);return t.slice(r,n)}return t.slice(r)}},{key:"eachChild",value:function(e){e(this.input),e(this.beginIndex),this.endIndex&&e(this.endIndex)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){if(null!=this.endIndex&&void 0!==this.endIndex){var e=this.endIndex.serialize();return["slice",this.input.serialize(),this.beginIndex.serialize(),e]}return["slice",this.input.serialize(),this.beginIndex.serialize()]}}],[{key:"parse",value:function(t,r){if(t.length<=2||t.length>=5)return r.error("Expected 3 or 4 arguments, but found "+(t.length-1)+" instead.");var n=r.parse(t[1],1,ee),i=r.parse(t[2],2,K);if(!n||!i)return null;if(!le(n.type,[ie(ee),H,ee]))return r.error("Expected first argument to be of type array or string, but found "+oe(n.type)+" instead");if(4===t.length){var o=r.parse(t[3],3,K);return o?new e(n.type,n,i,o):null}return new e(n.type,n,i)}}]),e}();function Lt(e,t){return"=="===e||"!="===e?"boolean"===t.kind||"string"===t.kind||"number"===t.kind||"null"===t.kind||"value"===t.kind:"string"===t.kind||"number"===t.kind||"value"===t.kind}function Nt(e,t,r,n){return 0===n.compare(t,r)}function Ft(e,t,r){var n="=="!==e&&"!="!==e;return function(){function o(e,t,r){s(this,o),this.type=V,this.lhs=e,this.rhs=t,this.collator=r,this.hasUntypedArgument="value"===e.type.kind||"value"===t.type.kind}return i(o,[{key:"evaluate",value:function(i){var o=this.lhs.evaluate(i),a=this.rhs.evaluate(i);if(n&&this.hasUntypedArgument){var s=Te(o),l=Te(a);if(s.kind!==l.kind||"string"!==s.kind&&"number"!==s.kind)throw new Oe('Expected arguments for "'+e+'" to be (string, string) or (number, number), but found ('+s.kind+", "+l.kind+") instead.")}if(this.collator&&!n&&this.hasUntypedArgument){var u=Te(o),c=Te(a);if("string"!==u.kind||"string"!==c.kind)return t(i,o,a)}return this.collator?r(i,o,a,this.collator.evaluate(i)):t(i,o,a)}},{key:"eachChild",value:function(e){e(this.lhs),e(this.rhs),this.collator&&e(this.collator)}},{key:"outputDefined",value:function(){return!0}},{key:"serialize",value:function(){var t=[e];return this.eachChild((function(e){t.push(e.serialize())})),t}}],[{key:"parse",value:function(e,t){if(3!==e.length&&4!==e.length)return t.error("Expected two or three arguments.");var r=e[0],i=t.parse(e[1],1,ee);if(!i)return null;if(!Lt(r,i.type))return t.concat(1).error('"'+r+"\" comparisons are not supported for type '"+oe(i.type)+"'.");var a=t.parse(e[2],2,ee);if(!a)return null;if(!Lt(r,a.type))return t.concat(2).error('"'+r+"\" comparisons are not supported for type '"+oe(a.type)+"'.");if(i.type.kind!==a.type.kind&&"value"!==i.type.kind&&"value"!==a.type.kind)return t.error("Cannot compare types '"+oe(i.type)+"' and '"+oe(a.type)+"'.");n&&("value"===i.type.kind&&"value"!==a.type.kind?i=new Re(a.type,[i]):"value"!==i.type.kind&&"value"===a.type.kind&&(a=new Re(i.type,[a])));var s=null;if(4===e.length){if("string"!==i.type.kind&&"string"!==a.type.kind&&"value"!==i.type.kind&&"value"!==a.type.kind)return t.error("Cannot use collator to compare non-string types.");if(!(s=t.parse(e[3],3,te)))return null}return new o(i,a,s)}}]),o}()}var Dt=Ft("==",(function(e,t,r){return t===r}),Nt),Ut=Ft("!=",(function(e,t,r){return t!==r}),(function(e,t,r,n){return!Nt(0,t,r,n)})),qt=Ft("<",(function(e,t,r){return t<r}),(function(e,t,r,n){return n.compare(t,r)<0})),Gt=Ft(">",(function(e,t,r){return t>r}),(function(e,t,r,n){return n.compare(t,r)>0})),Bt=Ft("<=",(function(e,t,r){return t<=r}),(function(e,t,r,n){return n.compare(t,r)<=0})),Wt=Ft(">=",(function(e,t,r){return t>=r}),(function(e,t,r,n){return n.compare(t,r)>=0})),Yt=function(){function e(t,r,n,i,o,a){s(this,e),this.type=H,this.number=t,this.locale=r,this.currency=n,this.unit=i,this.minFractionDigits=o,this.maxFractionDigits=a}return i(e,[{key:"evaluate",value:function(e){return new Intl.NumberFormat(this.locale?this.locale.evaluate(e):[],{style:(this.currency?"currency":this.unit&&"unit")||"decimal",currency:this.currency?this.currency.evaluate(e):void 0,unit:this.unit?this.unit.evaluate(e):void 0,minimumFractionDigits:this.minFractionDigits?this.minFractionDigits.evaluate(e):void 0,maximumFractionDigits:this.maxFractionDigits?this.maxFractionDigits.evaluate(e):void 0}).format(this.number.evaluate(e))}},{key:"eachChild",value:function(e){e(this.number),this.locale&&e(this.locale),this.currency&&e(this.currency),this.unit&&e(this.unit),this.minFractionDigits&&e(this.minFractionDigits),this.maxFractionDigits&&e(this.maxFractionDigits)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){var e={};return this.locale&&(e.locale=this.locale.serialize()),this.currency&&(e.currency=this.currency.serialize()),this.unit&&(e.unit=this.unit.serialize()),this.minFractionDigits&&(e["min-fraction-digits"]=this.minFractionDigits.serialize()),this.maxFractionDigits&&(e["max-fraction-digits"]=this.maxFractionDigits.serialize()),["number-format",this.number.serialize(),e]}}],[{key:"parse",value:function(t,r){if(3!==t.length)return r.error("Expected two arguments.");var n=r.parse(t[1],1,K);if(!n)return null;var i=t[2];if("object"!==(void 0===i?"undefined":o(i))||Array.isArray(i))return r.error("NumberFormat options argument must be an object.");var a=null;if(i.locale&&!(a=r.parse(i.locale,1,H)))return null;var s=null;if(i.currency&&!(s=r.parse(i.currency,1,H)))return null;var l=null;if(i.unit&&!(l=r.parse(i.unit,1,H)))return null;var u=null;if(i["min-fraction-digits"]&&!(u=r.parse(i["min-fraction-digits"],1,K)))return null;var c=null;return i["max-fraction-digits"]&&!(c=r.parse(i["max-fraction-digits"],1,K))?null:new e(n,a,s,l,u,c)}}]),e}(),Xt=function(){function e(t){s(this,e),this.type=K,this.input=t}return i(e,[{key:"evaluate",value:function(e){var t=this.input.evaluate(e);if("string"==typeof t)return t.length;if(Array.isArray(t))return t.length;throw new Oe("Expected value to be of type string or array, but found "+oe(Te(t))+" instead.")}},{key:"eachChild",value:function(e){e(this.input)}},{key:"outputDefined",value:function(){return!1}},{key:"serialize",value:function(){var e=["length"];return this.eachChild((function(t){e.push(t.serialize())})),e}}],[{key:"parse",value:function(t,r){if(2!==t.length)return r.error("Expected 1 argument, but found "+(t.length-1)+" instead.");var n=r.parse(t[1],1);return n?"array"!==n.type.kind&&"string"!==n.type.kind&&"value"!==n.type.kind?r.error("Expected argument of type string or array, but found "+oe(n.type)+" instead."):new e(n):null}}]),e}(),Jt={"==":Dt,"!=":Ut,">":Gt,"<":qt,">=":Wt,"<=":Bt,array:Re,at:Rt,boolean:Re,case:Pt,coalesce:Ot,collator:Fe,format:Ce,image:Me,in:Ct,"index-of":Mt,interpolate:jt,"interpolate-hcl":jt,"interpolate-lab":jt,length:Xt,let:At,literal:je,match:It,number:Re,"number-format":Yt,object:Re,slice:zt,step:st,string:Re,"to-boolean":Pe,"to-color":Pe,"to-number":Pe,"to-string":Pe,var:it,within:et};function Zt(e,t){var r=n(t,4),i=r[0],o=r[1],a=r[2],s=r[3];i=i.evaluate(e),o=o.evaluate(e),a=a.evaluate(e);var l=s?s.evaluate(e):1,u=_e(i,o,a,l);if(u)throw new Oe(u);return new ge(i/255*l,o/255*l,a/255*l,l)}function Kt(e,t){return e in t}function Ht(e,t){var r=t[e];return void 0===r?null:r}function Vt(e){return{type:e}}Ne.register(Jt,{error:[{kind:"error"},[H],function(e,t){var r=n(t,1)[0];throw new Oe(r.evaluate(e))}],typeof:[H,[ee],function(e,t){return oe(Te(n(t,1)[0].evaluate(e)))}],"to-rgba":[ie(K,4),[$],function(e,t){return n(t,1)[0].evaluate(e).toArray()}],rgb:[$,[K,K,K],Zt],rgba:[$,[K,K,K,K],Zt],has:{type:V,overloads:[[[H],function(e,t){return Kt(n(t,1)[0].evaluate(e),e.properties())}],[[H,Q],function(e,t){var r=n(t,2),i=r[0],o=r[1];return Kt(i.evaluate(e),o.evaluate(e))}]]},get:{type:ee,overloads:[[[H],function(e,t){return Ht(n(t,1)[0].evaluate(e),e.properties())}],[[H,Q],function(e,t){var r=n(t,2),i=r[0],o=r[1];return Ht(i.evaluate(e),o.evaluate(e))}]]},"feature-state":[ee,[H],function(e,t){return Ht(n(t,1)[0].evaluate(e),e.featureState||{})}],properties:[Q,[],function(e){return e.properties()}],"geometry-type":[H,[],function(e){return e.geometryType()}],id:[ee,[],function(e){return e.id()}],zoom:[K,[],function(e){return e.globals.zoom}],pitch:[K,[],function(e){return e.globals.pitch||0}],"distance-from-center":[K,[],function(e){return e.distanceFromCenter()}],"heatmap-density":[K,[],function(e){return e.globals.heatmapDensity||0}],"line-progress":[K,[],function(e){return e.globals.lineProgress||0}],"sky-radial-progress":[K,[],function(e){return e.globals.skyRadialProgress||0}],accumulated:[ee,[],function(e){return void 0===e.globals.accumulated?null:e.globals.accumulated}],"+":[K,Vt(K),function(e,t){var r=0,n=!0,i=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){r+=a.value.evaluate(e)}}catch(e){i=!0,o=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw o}}return r}],"*":[K,Vt(K),function(e,t){var r=1,n=!0,i=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){r*=a.value.evaluate(e)}}catch(e){i=!0,o=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw o}}return r}],"-":{type:K,overloads:[[[K,K],function(e,t){var r=n(t,2),i=r[0],o=r[1];return i.evaluate(e)-o.evaluate(e)}],[[K],function(e,t){return-n(t,1)[0].evaluate(e)}]]},"/":[K,[K,K],function(e,t){var r=n(t,2),i=r[0],o=r[1];return i.evaluate(e)/o.evaluate(e)}],"%":[K,[K,K],function(e,t){var r=n(t,2),i=r[0],o=r[1];return i.evaluate(e)%o.evaluate(e)}],ln2:[K,[],function(){return Math.LN2}],pi:[K,[],function(){return Math.PI}],e:[K,[],function(){return Math.E}],"^":[K,[K,K],function(e,t){var r=n(t,2),i=r[0],o=r[1];return Math.pow(i.evaluate(e),o.evaluate(e))}],sqrt:[K,[K],function(e,t){var r=n(t,1)[0];return Math.sqrt(r.evaluate(e))}],log10:[K,[K],function(e,t){var r=n(t,1)[0];return Math.log(r.evaluate(e))/Math.LN10}],ln:[K,[K],function(e,t){var r=n(t,1)[0];return Math.log(r.evaluate(e))}],log2:[K,[K],function(e,t){var r=n(t,1)[0];return Math.log(r.evaluate(e))/Math.LN2}],sin:[K,[K],function(e,t){var r=n(t,1)[0];return Math.sin(r.evaluate(e))}],cos:[K,[K],function(e,t){var r=n(t,1)[0];return Math.cos(r.evaluate(e))}],tan:[K,[K],function(e,t){var r=n(t,1)[0];return Math.tan(r.evaluate(e))}],asin:[K,[K],function(e,t){var r=n(t,1)[0];return Math.asin(r.evaluate(e))}],acos:[K,[K],function(e,t){var r=n(t,1)[0];return Math.acos(r.evaluate(e))}],atan:[K,[K],function(e,t){var r=n(t,1)[0];return Math.atan(r.evaluate(e))}],min:[K,Vt(K),function(e,t){return Math.min.apply(Math,a(t.map((function(t){return t.evaluate(e)}))))}],max:[K,Vt(K),function(e,t){return Math.max.apply(Math,a(t.map((function(t){return t.evaluate(e)}))))}],abs:[K,[K],function(e,t){var r=n(t,1)[0];return Math.abs(r.evaluate(e))}],round:[K,[K],function(e,t){var r=n(t,1)[0].evaluate(e);return r<0?-Math.round(-r):Math.round(r)}],floor:[K,[K],function(e,t){var r=n(t,1)[0];return Math.floor(r.evaluate(e))}],ceil:[K,[K],function(e,t){var r=n(t,1)[0];return Math.ceil(r.evaluate(e))}],"filter-==":[V,[H,ee],function(e,t){var r=n(t,2),i=r[0],o=r[1];return e.properties()[i.value]===o.value}],"filter-id-==":[V,[ee],function(e,t){var r=n(t,1)[0];return e.id()===r.value}],"filter-type-==":[V,[H],function(e,t){var r=n(t,1)[0];return e.geometryType()===r.value}],"filter-<":[V,[H,ee],function(e,t){var r=n(t,2),i=r[0],a=r[1],s=e.properties()[i.value],l=a.value;return(void 0===s?"undefined":o(s))===(void 0===l?"undefined":o(l))&&s<l}],"filter-id-<":[V,[ee],function(e,t){var r=n(t,1)[0],i=e.id(),a=r.value;return(void 0===i?"undefined":o(i))===(void 0===a?"undefined":o(a))&&i<a}],"filter->":[V,[H,ee],function(e,t){var r=n(t,2),i=r[0],a=r[1],s=e.properties()[i.value],l=a.value;return(void 0===s?"undefined":o(s))===(void 0===l?"undefined":o(l))&&s>l}],"filter-id->":[V,[ee],function(e,t){var r=n(t,1)[0],i=e.id(),a=r.value;return(void 0===i?"undefined":o(i))===(void 0===a?"undefined":o(a))&&i>a}],"filter-<=":[V,[H,ee],function(e,t){var r=n(t,2),i=r[0],a=r[1],s=e.properties()[i.value],l=a.value;return(void 0===s?"undefined":o(s))===(void 0===l?"undefined":o(l))&&s<=l}],"filter-id-<=":[V,[ee],function(e,t){var r=n(t,1)[0],i=e.id(),a=r.value;return(void 0===i?"undefined":o(i))===(void 0===a?"undefined":o(a))&&i<=a}],"filter->=":[V,[H,ee],function(e,t){var r=n(t,2),i=r[0],a=r[1],s=e.properties()[i.value],l=a.value;return(void 0===s?"undefined":o(s))===(void 0===l?"undefined":o(l))&&s>=l}],"filter-id->=":[V,[ee],function(e,t){var r=n(t,1)[0],i=e.id(),a=r.value;return(void 0===i?"undefined":o(i))===(void 0===a?"undefined":o(a))&&i>=a}],"filter-has":[V,[ee],function(e,t){return n(t,1)[0].value in e.properties()}],"filter-has-id":[V,[],function(e){return null!==e.id()&&void 0!==e.id()}],"filter-type-in":[V,[ie(H)],function(e,t){return n(t,1)[0].value.indexOf(e.geometryType())>=0}],"filter-id-in":[V,[ie(ee)],function(e,t){return n(t,1)[0].value.indexOf(e.id())>=0}],"filter-in-small":[V,[H,ie(ee)],function(e,t){var r=n(t,2),i=r[0];return r[1].value.indexOf(e.properties()[i.value])>=0}],"filter-in-large":[V,[H,ie(ee)],function(e,t){var r=n(t,2),i=r[0],o=r[1];return function(e,t,r,n){for(;r<=n;){var i=r+n>>1;if(t[i]===e)return!0;t[i]>e?n=i-1:r=i+1}return!1}(e.properties()[i.value],o.value,0,o.value.length-1)}],all:{type:V,overloads:[[[V,V],function(e,t){var r=n(t,2),i=r[0],o=r[1];return i.evaluate(e)&&o.evaluate(e)}],[Vt(V),function(e,t){var r=!0,n=!1,i=void 0;try{for(var o,a=t[Symbol.iterator]();!(r=(o=a.next()).done);r=!0){if(!o.value.evaluate(e))return!1}}catch(e){n=!0,i=e}finally{try{!r&&a.return&&a.return()}finally{if(n)throw i}}return!0}]]},any:{type:V,overloads:[[[V,V],function(e,t){var r=n(t,2),i=r[0],o=r[1];return i.evaluate(e)||o.evaluate(e)}],[Vt(V),function(e,t){var r=!0,n=!1,i=void 0;try{for(var o,a=t[Symbol.iterator]();!(r=(o=a.next()).done);r=!0){if(o.value.evaluate(e))return!0}}catch(e){n=!0,i=e}finally{try{!r&&a.return&&a.return()}finally{if(n)throw i}}return!1}]]},"!":[V,[V],function(e,t){return!n(t,1)[0].evaluate(e)}],"is-supported-script":[V,[H],function(e,t){var r=n(t,1)[0],i=e.globals&&e.globals.isSupportedScript;return!i||i(r.evaluate(e))}],upcase:[H,[H],function(e,t){return n(t,1)[0].evaluate(e).toUpperCase()}],downcase:[H,[H],function(e,t){return n(t,1)[0].evaluate(e).toLowerCase()}],concat:[H,Vt(ee),function(e,t){return t.map((function(t){return Ee(t.evaluate(e))})).join("")}],"resolved-locale":[H,[te],function(e,t){return n(t,1)[0].evaluate(e).resolvedLocale()}]});var $t=Jt;function Qt(e){return{result:"success",value:e}}function er(e){return{result:"error",value:e}}function tr(e){return"data-driven"===e["property-type"]}function rr(e){return!!e.expression&&e.expression.parameters.indexOf("zoom")>-1}function nr(e){return!!e.expression&&e.expression.interpolated}function ir(e){return e instanceof Number?"number":e instanceof String?"string":e instanceof Boolean?"boolean":Array.isArray(e)?"array":null===e?"null":void 0===e?"undefined":o(e)}function or(e){return"object"===(void 0===e?"undefined":o(e))&&null!==e&&!Array.isArray(e)}function ar(e){return e}function sr(e,t){var r="color"===t.type,n=e.stops&&"object"===o(e.stops[0][0]),i=n||void 0!==e.property,a=n||!i,s=e.type||(nr(t)?"exponential":"interval");if(r&&((e=Y({},e)).stops&&(e.stops=e.stops.map((function(e){return[e[0],ge.parse(e[1])]}))),e.default?e.default=ge.parse(e.default):e.default=ge.parse(t.default)),e.colorSpace&&"rgb"!==e.colorSpace&&!Tt[e.colorSpace])throw new Error("Unknown color space: "+e.colorSpace);var l=void 0,u=void 0,c=void 0;if("exponential"===s)l=pr;else if("interval"===s)l=cr;else if("categorical"===s){l=ur,u=Object.create(null);var p=!0,d=!1,h=void 0;try{for(var f,y=e.stops[Symbol.iterator]();!(p=(f=y.next()).done);p=!0){var m=f.value;u[m[0]]=m[1]}}catch(e){d=!0,h=e}finally{try{!p&&y.return&&y.return()}finally{if(d)throw h}}c=o(e.stops[0][0])}else{if("identity"!==s)throw new Error('Unknown function type "'+s+'"');l=dr}if(n){for(var v={},g=[],b=0;b<e.stops.length;b++){var x=e.stops[b],w=x[0].zoom;void 0===v[w]&&(v[w]={zoom:w,type:e.type,property:e.property,default:e.default,stops:[]},g.push(w)),v[w].stops.push([x[0].value,x[1]])}var k=[],_=!0,S=!1,T=void 0;try{for(var E,j=g[Symbol.iterator]();!(_=(E=j.next()).done);_=!0){var O=E.value;k.push([v[O].zoom,sr(v[O],t)])}}catch(e){S=!0,T=e}finally{try{!_&&j.return&&j.return()}finally{if(S)throw T}}var A={name:"linear"};return{kind:"composite",interpolationType:A,interpolationFactor:jt.interpolationFactor.bind(void 0,A),zoomStops:k.map((function(e){return e[0]})),evaluate:function(r,n){var i=r.zoom;return pr({stops:k,base:e.base},t,i).evaluate(i,n)}}}if(a){var R="exponential"===s?{name:"exponential",base:void 0!==e.base?e.base:1}:null;return{kind:"camera",interpolationType:R,interpolationFactor:jt.interpolationFactor.bind(void 0,R),zoomStops:e.stops.map((function(e){return e[0]})),evaluate:function(r){var n=r.zoom;return l(e,t,n,u,c)}}}return{kind:"source",evaluate:function(r,n){var i=n&&n.properties?n.properties[e.property]:void 0;return void 0===i?lr(e.default,t.default):l(e,t,i,u,c)}}}function lr(e,t,r){return void 0!==e?e:void 0!==t?t:void 0!==r?r:void 0}function ur(e,t,r,n,i){return lr((void 0===r?"undefined":o(r))===i?n[r]:void 0,e.default,t.default)}function cr(e,t,r){if("number"!==ir(r))return lr(e.default,t.default);var n=e.stops.length;if(1===n)return e.stops[0][1];if(r<=e.stops[0][0])return e.stops[0][1];if(r>=e.stops[n-1][0])return e.stops[n-1][1];var i=at(e.stops.map((function(e){return e[0]})),r);return e.stops[i][1]}function pr(e,t,r){var n=void 0!==e.base?e.base:1;if("number"!==ir(r))return lr(e.default,t.default);var i=e.stops.length;if(1===i)return e.stops[0][1];if(r<=e.stops[0][0])return e.stops[0][1];if(r>=e.stops[i-1][0])return e.stops[i-1][1];var o=at(e.stops.map((function(e){return e[0]})),r),a=function(e,t,r,n){var i=n-r,o=e-r;return 0===i?0:1===t?o/i:(Math.pow(t,o)-1)/(Math.pow(t,i)-1)}(r,n,e.stops[o][0],e.stops[o+1][0]),s=e.stops[o][1],l=e.stops[o+1][1],u=pt[t.type]||ar;if(e.colorSpace&&"rgb"!==e.colorSpace){var c=Tt[e.colorSpace];u=function(e,t){return c.reverse(c.interpolate(c.forward(e),c.forward(t),a))}}return"function"==typeof s.evaluate?{evaluate:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=s.evaluate.apply(void 0,t),i=l.evaluate.apply(void 0,t);if(void 0!==n&&void 0!==i)return u(n,i,a)}}:u(s,l,a)}function dr(e,t,r){return"color"===t.type?r=ge.parse(r):"formatted"===t.type?r=we.fromString(r.toString()):"resolvedImage"===t.type?r=ke.fromString(r.toString()):ir(r)===t.type||"enum"===t.type&&t.values[r]||(r=void 0),lr(r,e.default,t.default)}var hr=function(){function e(t,r){var n;s(this,e),this.expression=t,this._warningHistory={},this._evaluator=new Le,this._defaultValue=r?"color"===(n=r).type&&(or(n.default)||Array.isArray(n.default))?new ge(0,0,0,0):"color"===n.type?ge.parse(n.default)||null:void 0===n.default?null:n.default:null,this._enumValues=r&&"enum"===r.type?r.values:null}return i(e,[{key:"evaluateWithoutErrorHandling",value:function(e,t,r,n,i,o,a,s){return this._evaluator.globals=e,this._evaluator.feature=t,this._evaluator.featureState=r,this._evaluator.canonical=n||null,this._evaluator.availableImages=i||null,this._evaluator.formattedSection=o,this._evaluator.featureTileCoord=a||null,this._evaluator.featureDistanceData=s||null,this.expression.evaluate(this._evaluator)}},{key:"evaluate",value:function(e,t,r,n,i,o,a,s){this._evaluator.globals=e,this._evaluator.feature=t||null,this._evaluator.featureState=r||null,this._evaluator.canonical=n||null,this._evaluator.availableImages=i||null,this._evaluator.formattedSection=o||null,this._evaluator.featureTileCoord=a||null,this._evaluator.featureDistanceData=s||null;try{var l=this.expression.evaluate(this._evaluator);if(null==l||"number"==typeof l&&l!=l)return this._defaultValue;if(this._enumValues&&!(l in this._enumValues))throw new Oe("Expected value to be one of "+Object.keys(this._enumValues).map((function(e){return JSON.stringify(e)})).join(", ")+", but found "+JSON.stringify(l)+" instead.");return l}catch(e){return this._warningHistory[e.message]||(this._warningHistory[e.message]=!0,"undefined"!=typeof console&&console.warn(e.message)),this._defaultValue}}}]),e}();function fr(e){return Array.isArray(e)&&e.length>0&&"string"==typeof e[0]&&e[0]in $t}function yr(e,t){var r=new ot($t,[],t?function(e){var t={color:$,string:H,number:K,enum:H,boolean:V,formatted:re,resolvedImage:ne};if("array"===e.type)return ie(t[e.value]||ee,e.length);return t[e.type]}(t):void 0),n=r.parse(e,void 0,void 0,void 0,t&&"string"===t.type?{typeAnnotation:"coerce"}:void 0);return n?Qt(new hr(n,t)):er(r.errors)}var mr=function(){function e(t,r){s(this,e),this.kind=t,this._styleExpression=r,this.isStateDependent="constant"!==t&&!rt(r.expression)}return i(e,[{key:"evaluateWithoutErrorHandling",value:function(e,t,r,n,i,o){return this._styleExpression.evaluateWithoutErrorHandling(e,t,r,n,i,o)}},{key:"evaluate",value:function(e,t,r,n,i,o){return this._styleExpression.evaluate(e,t,r,n,i,o)}}]),e}(),vr=function(){function e(t,r,n,i){s(this,e),this.kind=t,this.zoomStops=n,this._styleExpression=r,this.isStateDependent="camera"!==t&&!rt(r.expression),this.interpolationType=i}return i(e,[{key:"evaluateWithoutErrorHandling",value:function(e,t,r,n,i,o){return this._styleExpression.evaluateWithoutErrorHandling(e,t,r,n,i,o)}},{key:"evaluate",value:function(e,t,r,n,i,o){return this._styleExpression.evaluate(e,t,r,n,i,o)}},{key:"interpolationFactor",value:function(e,t,r){return this.interpolationType?jt.interpolationFactor(this.interpolationType,e,t,r):0}}]),e}();function gr(e,t){if("error"===(e=yr(e,t)).result)return e;var r=e.value.expression,n=tt(r);if(!n&&!tr(t))return er([new X("","data expressions not supported")]);var i=nt(r,["zoom","pitch","distance-from-center"]);if(!i&&!rr(t))return er([new X("","zoom expressions not supported")]);var o=function e(t){var r=null;if(t instanceof At)r=e(t.result);else if(t instanceof Ot){var n=!0,i=!1,o=void 0;try{for(var a,s=t.args[Symbol.iterator]();!(n=(a=s.next()).done);n=!0){var l=a.value;if(r=e(l))break}}catch(e){i=!0,o=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw o}}}else(t instanceof st||t instanceof jt)&&t.input instanceof Ne&&"zoom"===t.input.name&&(r=t);if(r instanceof X)return r;return t.eachChild((function(t){var n=e(t);n instanceof X?r=n:!r&&n?r=new X("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.'):r&&n&&r!==n&&(r=new X("",'Only one zoom-based "step" or "interpolate" subexpression may be used in an expression.'))})),r}(r);if(!o&&!i)return er([new X("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.')]);if(o instanceof X)return er([o]);if(o instanceof jt&&!nr(t))return er([new X("",'"interpolate" expressions cannot be used with this property')]);if(!o)return Qt(new mr(n?"constant":"source",e.value));var a=o instanceof jt?o.interpolation:void 0;return Qt(new vr(n?"camera":"composite",e.value,o.labels,a))}var br=function(){function e(t,r){s(this,e),this._parameters=t,this._specification=r,Y(this,sr(this._parameters,this._specification))}return i(e,null,[{key:"deserialize",value:function(t){return new e(t._parameters,t._specification)}},{key:"serialize",value:function(e){return{_parameters:e._parameters,_specification:e._specification}}}]),e}();function xr(e){return"object"===(void 0===e?"undefined":o(e))?["literal",e]:e}function wr(e,t){var r=e.stops;if(!r)return function(e,t){var r=["get",e.property];if(void 0===e.default)return"string"===t.type?["string",r]:r;if("enum"===t.type)return["match",r,Object.keys(t.values),r,e.default];var n=["color"===t.type?"to-color":t.type,r,xr(e.default)];return"array"===t.type&&n.splice(1,0,t.value,t.length||null),n}(e,t);var n=r&&"object"===o(r[0][0]),i=n||void 0!==e.property,a=n||!i;return r=r.map((function(e){return!i&&t.tokens&&"string"==typeof e[1]?[e[0],Or(e[1])]:[e[0],xr(e[1])]})),n?function(e,t,r){for(var n={},i={},o=[],a=0;a<r.length;a++){var s=r[a],l=s[0].zoom;void 0===n[l]&&(n[l]={zoom:l,type:e.type,property:e.property,default:e.default},i[l]=[],o.push(l)),i[l].push([s[0].value,s[1]])}if("exponential"===jr({},t)){var u=[kr(e),["linear"],["zoom"]],c=!0,p=!1,d=void 0;try{for(var h,f=o[Symbol.iterator]();!(c=(h=f.next()).done);c=!0){var y=h.value,m=Sr(n[y],t,i[y]);Er(u,y,m,!1)}}catch(e){p=!0,d=e}finally{try{!c&&f.return&&f.return()}finally{if(p)throw d}}return u}var v=["step",["zoom"]],g=!0,b=!1,x=void 0;try{for(var w,k=o[Symbol.iterator]();!(g=(w=k.next()).done);g=!0){var _=w.value,S=Sr(n[_],t,i[_]);Er(v,_,S,!0)}}catch(e){b=!0,x=e}finally{try{!g&&k.return&&k.return()}finally{if(b)throw x}}return Tr(v),v}(e,t,r):a?function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:["zoom"],i=jr(e,t),o=void 0,a=!1;if("interval"===i)o=["step",n],a=!0;else{if("exponential"!==i)throw new Error('Unknown zoom function type "'+i+'"');var s=void 0!==e.base?e.base:1;o=[kr(e),1===s?["linear"]:["exponential",s],n]}var l=!0,u=!1,c=void 0;try{for(var p,d=r[Symbol.iterator]();!(l=(p=d.next()).done);l=!0){var h=p.value;Er(o,h[0],h[1],a)}}catch(e){u=!0,c=e}finally{try{!l&&d.return&&d.return()}finally{if(u)throw c}}return Tr(o),o}(e,t,r):Sr(e,t,r)}function kr(e){switch(e.colorSpace){case"hcl":return"interpolate-hcl";case"lab":return"interpolate-lab";default:return"interpolate"}}function _r(e,t){var r,n,i=xr((r=e.default,n=t.default,void 0!==r?r:void 0!==n?n:void 0));return void 0===i&&"resolvedImage"===t.type?"":i}function Sr(e,t,r){var n=jr(e,t),i=["get",e.property];if("categorical"===n&&"boolean"==typeof r[0][0]){var o=["case"],a=!0,s=!1,l=void 0;try{for(var u,c=r[Symbol.iterator]();!(a=(u=c.next()).done);a=!0){var p=u.value;o.push(["==",i,p[0]],p[1])}}catch(e){s=!0,l=e}finally{try{!a&&c.return&&c.return()}finally{if(s)throw l}}return o.push(_r(e,t)),o}if("categorical"===n){var d=["match",i],h=!0,f=!1,y=void 0;try{for(var m,v=r[Symbol.iterator]();!(h=(m=v.next()).done);h=!0){var g=m.value;Er(d,g[0],g[1],!1)}}catch(e){f=!0,y=e}finally{try{!h&&v.return&&v.return()}finally{if(f)throw y}}return d.push(_r(e,t)),d}if("interval"===n){var b=["step",["number",i]],x=!0,w=!1,k=void 0;try{for(var _,S=r[Symbol.iterator]();!(x=(_=S.next()).done);x=!0){var T=_.value;Er(b,T[0],T[1],!0)}}catch(e){w=!0,k=e}finally{try{!x&&S.return&&S.return()}finally{if(w)throw k}}return Tr(b),void 0===e.default?b:["case",["==",["typeof",i],"number"],b,xr(e.default)]}if("exponential"===n){var E=void 0!==e.base?e.base:1,j=[kr(e),1===E?["linear"]:["exponential",E],["number",i]],O=!0,A=!1,R=void 0;try{for(var C,M=r[Symbol.iterator]();!(O=(C=M.next()).done);O=!0){var I=C.value;Er(j,I[0],I[1],!1)}}catch(e){A=!0,R=e}finally{try{!O&&M.return&&M.return()}finally{if(A)throw R}}return void 0===e.default?j:["case",["==",["typeof",i],"number"],j,xr(e.default)]}throw new Error("Unknown property function type "+n)}function Tr(e){"step"===e[0]&&3===e.length&&(e.push(0),e.push(e[3]))}function Er(e,t,r,n){e.length>3&&t===e[e.length-2]||(n&&2===e.length||e.push(t),e.push(r))}function jr(e,t){return e.type?e.type:t.expression.interpolated?"exponential":"interval"}function Or(e){for(var t=["concat"],r=/{([^{}]+)}/g,n=0,i=r.exec(e);null!==i;i=r.exec(e)){var o=e.slice(n,r.lastIndex-i[0].length);n=r.lastIndex,o.length>0&&t.push(o),t.push(["get",i[1]])}if(1===t.length)return e;if(n<e.length)t.push(e.slice(n));else if(2===t.length)return["to-string",t[1]];return t}function Ar(e){return e instanceof Number||e instanceof String||e instanceof Boolean?e.valueOf():e}function Rr(e){if(Array.isArray(e))return e.map(Rr);if(e instanceof Object&&!(e instanceof Number||e instanceof String||e instanceof Boolean)){var t={};for(var r in e)t[r]=Rr(e[r]);return t}return Ar(e)}function Cr(e){if(!0===e||!1===e)return!0;if(!Array.isArray(e)||0===e.length)return!1;switch(e[0]){case"has":return e.length>=2&&"$id"!==e[1]&&"$type"!==e[1];case"in":return e.length>=3&&("string"!=typeof e[1]||Array.isArray(e[2]));case"!in":case"!has":case"none":return!1;case"==":case"!=":case">":case">=":case"<":case"<=":return 3!==e.length||Array.isArray(e[1])||Array.isArray(e[2]);case"any":case"all":var t=!0,r=!1,n=void 0;try{for(var i,o=e.slice(1)[Symbol.iterator]();!(t=(i=o.next()).done);t=!0){var a=i.value;if(!Cr(a)&&"boolean"!=typeof a)return!1}}catch(e){r=!0,n=e}finally{try{!t&&o.return&&o.return()}finally{if(r)throw n}}return!0;default:return!0}}function Mr(e){if(!Ir(e))return e;var t=Rr(e);return function e(t){var r=!1,n=[];if("case"===t[0]){for(var i=1;i<t.length-1;i+=2)r=r||Ir(t[i]),n.push(t[i+1]);n.push(t[t.length-1])}else if("match"===t[0]){r=r||Ir(t[1]);for(var o=2;o<t.length-1;o+=2)n.push(t[o+1]);n.push(t[t.length-1])}else if("step"===t[0]){r=r||Ir(t[1]);for(var a=1;a<t.length-1;a+=2)n.push(t[a+1])}r&&(t.length=0,t.push.apply(t,["any"].concat(n)));for(var s=1;s<t.length;s++)e(t[s])}(t),t=function e(t){if(!Array.isArray(t))return t;var r=function(e){if(Pr.has(e[0]))for(var t=1;t<e.length;t++){if(Ir(e[t]))return!0}return e}(t);return!0===r?r:r.map((function(t){return e(t)}))}(t)}function Ir(e){if(!Array.isArray(e))return!1;if(function(e){return"pitch"===e||"distance-from-center"===e}(e[0]))return!0;for(var t=1;t<e.length;t++){if(Ir(e[t]))return!0}return!1}var Pr=new Set(["in","==","!=",">",">=","<","<=","to-boolean"]);function zr(e,t){return e<t?-1:e>t?1:0}function Lr(e){if(!Array.isArray(e))return!1;if("within"===e[0])return!0;for(var t=1;t<e.length;t++)if(Lr(e[t]))return!0;return!1}function Nr(e){if(!e)return!0;var t,r=e[0];return e.length<=1?"any"!==r:"=="===r?Fr(e[1],e[2],"=="):"!="===r?qr(Fr(e[1],e[2],"==")):"<"===r||">"===r||"<="===r||">="===r?Fr(e[1],e[2],r):"any"===r?(t=e.slice(1),["any"].concat(t.map(Nr))):"all"===r?["all"].concat(e.slice(1).map(Nr)):"none"===r?["all"].concat(e.slice(1).map(Nr).map(qr)):"in"===r?Dr(e[1],e.slice(2)):"!in"===r?qr(Dr(e[1],e.slice(2))):"has"===r?Ur(e[1]):"!has"===r?qr(Ur(e[1])):"within"!==r||e}function Fr(e,t,r){switch(e){case"$type":return["filter-type-"+r,t];case"$id":return["filter-id-"+r,t];default:return["filter-"+r,e,t]}}function Dr(e,t){if(0===t.length)return!1;switch(e){case"$type":return["filter-type-in",["literal",t]];case"$id":return["filter-id-in",["literal",t]];default:return t.length>200&&!t.some((function(e){return(void 0===e?"undefined":o(e))!==o(t[0])}))?["filter-in-large",e,["literal",t.sort(zr)]]:["filter-in-small",e,["literal",t]]}}function Ur(e){switch(e){case"$type":return!0;case"$id":return["filter-has-id"];default:return["filter-has",e]}}function qr(e){return["!",e]}function Gr(e){return function e(t,r){if(Cr(t))return t;if(!t)return!0;var i=t[0];if(t.length<=1)return"any"!==i;var s=void 0;if("=="===i||"!="===i||"<"===i||">"===i||"<="===i||">="===i){var l=n(t,3),u=l[1],c=l[2];s=function(e,t,r,n){var i=void 0;if("$type"===e)return[r,["geometry-type"],t];i="$id"===e?["id"]:["get",e];if(n&&null!==t){var a=void 0===t?"undefined":o(t);n[e]=a}if("=="===r&&"$id"!==e&&null===t)return["all",["has",e],["==",i,null]];if("!="===r&&"$id"!==e&&null===t)return["any",["!",["has",e]],["!=",i,null]];return[r,i,t]}(u,c,i,r)}else{if("any"===i){var p=t.slice(1).map((function(t){var r={},n=e(t,r),i=function(e){var t=[];for(var r in e){var n="$id"===r?["id"]:["get",r];t.push(["==",["typeof",n],e[r]])}return 0===t.length||(1===t.length?t[0]:["all"].concat(t))}(r);return!0===i?n:["case",i,n,!1]}));return["any"].concat(p)}if("all"===i){var d,h=t.slice(1).map((function(t){return e(t,r)}));return h.length>1?["all"].concat(h):(d=[]).concat.apply(d,a(h))}if("none"===i)return["!",e(["any"].concat(t.slice(1)),{})];s="in"===i?Br(t[1],t.slice(2)):"!in"===i?Br(t[1],t.slice(2),!0):"has"===i?Wr(t[1]):"!has"!==i||["!",Wr(t[1])]}return s}(e,{})}function Br(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(0===t.length)return r;var n=void 0;n="$type"===e?["geometry-type"]:"$id"===e?["id"]:["get",e];var i=!0,a=o(t[0]),s=!0,l=!1,u=void 0;try{for(var c,p=t[Symbol.iterator]();!(s=(c=p.next()).done);s=!0){var d=c.value;if((void 0===d?"undefined":o(d))!==a){i=!1;break}}}catch(e){l=!0,u=e}finally{try{!s&&p.return&&p.return()}finally{if(l)throw u}}if(i&&("string"===a||"number"===a)){var h=t.sort().filter((function(e,r){return 0===r||t[r-1]!==e}));return["match",n,h,!r,r]}return[r?"all":"any"].concat(t.map((function(e){return[r?"!=":"==",n,e]})))}function Wr(e){return"$type"===e||("$id"===e?["!=",["id"],null]:["has",e])}var Yr=["type","source","source-layer","minzoom","maxzoom","filter","layout"];function Xr(e,t){var r={};for(var n in e)"ref"!==n&&(r[n]=e[n]);return Yr.forEach((function(e){e in t&&(r[e]=t[e])})),r}function Jr(e,t){if(Array.isArray(e)){if(!Array.isArray(t)||e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(!Jr(e[r],t[r]))return!1;return!0}if("object"===(void 0===e?"undefined":o(e))&&null!==e&&null!==t){if("object"!==(void 0===t?"undefined":o(t)))return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var n in e)if(!Jr(e[n],t[n]))return!1;return!0}return e===t}var Zr="setStyle",Kr="addLayer",Hr="removeLayer",Vr="setPaintProperty",$r="setLayoutProperty",Qr="setFilter",en="addSource",tn="removeSource",rn="setGeoJSONSourceData",nn="setLayerZoomRange",on="setLayerProperty",an="setCenter",sn="setZoom",ln="setBearing",un="setPitch",cn="setSprite",pn="setGlyphs",dn="setTransition",hn="setLight",fn="setTerrain",yn="setFog",mn="setProjection";function vn(e,t,r){r.push({command:en,args:[e,t[e]]})}function gn(e,t,r){t.push({command:tn,args:[e]}),r[e]=!0}function bn(e,t,r,n){gn(e,r,n),vn(e,t,r)}function xn(e,t,r){var n=void 0;for(n in e[r])if(e[r].hasOwnProperty(n)&&"data"!==n&&!Jr(e[r][n],t[r][n]))return!1;for(n in t[r])if(t[r].hasOwnProperty(n)&&"data"!==n&&!Jr(e[r][n],t[r][n]))return!1;return!0}function wn(e,t,r,n,i,o){e=e||{},t=t||{};var a=void 0;for(a in e)e.hasOwnProperty(a)&&(Jr(e[a],t[a])||r.push({command:o,args:[n,a,t[a],i]}));for(a in t)t.hasOwnProperty(a)&&!e.hasOwnProperty(a)&&(Jr(e[a],t[a])||r.push({command:o,args:[n,a,t[a],i]}))}function kn(e){return e.id}function _n(e,t){return e[t.id]=t,e}var Sn=function e(t,r,n,i){s(this,e),this.message=(t?t+": ":"")+n,i&&(this.identifier=i),null!=r&&r.__line__&&(this.line=r.__line__)},Tn=function e(t){s(this,e),this.error=t,this.message=t.message;var r=t.message.match(/line (\d+)/);this.line=r?parseInt(r[1],10):0};function En(e){var t=e.key,r=e.value,n=e.valueSpec||{},i=e.objectElementValidators||{},o=e.style,a=e.styleSpec,s=[],l=ir(r);if("object"!==l)return[new Sn(t,r,"object expected, "+l+" found")];for(var u in r){var c=u.split(".")[0],p=n[c]||n["*"],d=void 0;i[c]?d=i[c]:n[c]?d=Fn:i["*"]?d=i["*"]:n["*"]&&(d=Fn),d?s=s.concat(d({key:(t?t+".":t)+u,value:r[u],valueSpec:p,style:o,styleSpec:a,object:r,objectKey:u},r)):s.push(new Sn(t,r[u],'unknown property "'+u+'"'))}for(var h in n)i[h]||n[h].required&&void 0===n[h].default&&void 0===r[h]&&s.push(new Sn(t,r,'missing required property "'+h+'"'));return s}function jn(e){var t=e.value,r=e.valueSpec,n=e.style,i=e.styleSpec,o=e.key,a=e.arrayElementValidator||Fn;if("array"!==ir(t))return[new Sn(o,t,"array expected, "+ir(t)+" found")];if(r.length&&t.length!==r.length)return[new Sn(o,t,"array length "+r.length+" expected, length "+t.length+" found")];if(r["min-length"]&&t.length<r["min-length"])return[new Sn(o,t,"array length at least "+r["min-length"]+" expected, length "+t.length+" found")];var s={type:r.value,values:r.values,minimum:r.minimum,maximum:r.maximum,function:void 0};i.$version<7&&(s.function=r.function),"object"===ir(r.value)&&(s=r.value);for(var l=[],u=0;u<t.length;u++)l=l.concat(a({array:t,arrayIndex:u,value:t[u],valueSpec:s,style:n,styleSpec:i,key:o+"["+u+"]"}));return l}function On(e){var t=e.key,r=e.value,n=e.valueSpec,i=ir(r);if("number"===i&&r!=r&&(i="NaN"),"number"!==i)return[new Sn(t,r,"number expected, "+i+" found")];if("minimum"in n){var o=n.minimum;if("array"===ir(n.minimum)){var a=e.arrayIndex;o=n.minimum[a]}if(r<o)return[new Sn(t,r,r+" is less than the minimum value "+o)]}if("maximum"in n){var s=n.maximum;if("array"===ir(n.maximum)){var l=e.arrayIndex;s=n.maximum[l]}if(r>s)return[new Sn(t,r,r+" is greater than the maximum value "+s)]}return[]}function An(e){var t=e.valueSpec,r=Ar(e.value.type),n=void 0,i={},o=void 0,a=void 0,s="categorical"!==r&&void 0===e.value.property,l=!s,u="array"===ir(e.value.stops)&&"array"===ir(e.value.stops[0])&&"object"===ir(e.value.stops[0][0]),c=En({key:e.key,value:e.value,valueSpec:e.styleSpec.function,style:e.style,styleSpec:e.styleSpec,objectElementValidators:{stops:function(e){if("identity"===r)return[new Sn(e.key,e.value,'identity function may not have a "stops" property')];var t=[],n=e.value;t=t.concat(jn({key:e.key,value:n,valueSpec:e.valueSpec,style:e.style,styleSpec:e.styleSpec,arrayElementValidator:p})),"array"===ir(n)&&0===n.length&&t.push(new Sn(e.key,n,"array must have at least one stop"));return t},default:function(e){return Fn({key:e.key,value:e.value,valueSpec:t,style:e.style,styleSpec:e.styleSpec})}}});return"identity"===r&&s&&c.push(new Sn(e.key,e.value,'missing required property "property"')),"identity"===r||e.value.stops||c.push(new Sn(e.key,e.value,'missing required property "stops"')),"exponential"===r&&e.valueSpec.expression&&!nr(e.valueSpec)&&c.push(new Sn(e.key,e.value,"exponential functions not supported")),e.styleSpec.$version>=8&&(l&&!tr(e.valueSpec)?c.push(new Sn(e.key,e.value,"property functions not supported")):s&&!rr(e.valueSpec)&&c.push(new Sn(e.key,e.value,"zoom functions not supported"))),"categorical"!==r&&!u||void 0!==e.value.property||c.push(new Sn(e.key,e.value,'"property" property is required')),c;function p(e){var r=[],n=e.value,s=e.key;if("array"!==ir(n))return[new Sn(s,n,"array expected, "+ir(n)+" found")];if(2!==n.length)return[new Sn(s,n,"array length 2 expected, length "+n.length+" found")];if(u){if("object"!==ir(n[0]))return[new Sn(s,n,"object expected, "+ir(n[0])+" found")];if(void 0===n[0].zoom)return[new Sn(s,n,"object stop key must have zoom")];if(void 0===n[0].value)return[new Sn(s,n,"object stop key must have value")];var l=Ar(n[0].zoom);if("number"!=typeof l)return[new Sn(s,n[0].zoom,"stop zoom values must be numbers")];if(a&&a>l)return[new Sn(s,n[0].zoom,"stop zoom values must appear in ascending order")];l!==a&&(a=l,o=void 0,i={}),r=r.concat(En({key:s+"[0]",value:n[0],valueSpec:{zoom:{}},style:e.style,styleSpec:e.styleSpec,objectElementValidators:{zoom:On,value:d}}))}else r=r.concat(d({key:s+"[0]",value:n[0],valueSpec:{},style:e.style,styleSpec:e.styleSpec},n));return fr(Rr(n[1]))?r.concat([new Sn(s+"[1]",n[1],"expressions are not allowed in function stops.")]):r.concat(Fn({key:s+"[1]",value:n[1],valueSpec:t,style:e.style,styleSpec:e.styleSpec}))}function d(e,a){var s=ir(e.value),l=Ar(e.value),u=null!==e.value?e.value:a;if(n){if(s!==n)return[new Sn(e.key,u,s+" stop domain type must match previous stop domain type "+n)]}else n=s;if("number"!==s&&"string"!==s&&"boolean"!==s&&"number"!=typeof l&&"string"!=typeof l&&"boolean"!=typeof l)return[new Sn(e.key,u,"stop domain value must be a number, string, or boolean")];if("number"!==s&&"categorical"!==r){var c="number expected, "+s+" found";return tr(t)&&void 0===r&&(c+='\nIf you intended to use a categorical function, specify `"type": "categorical"`.'),[new Sn(e.key,u,c)]}return"categorical"!==r||"number"!==s||"number"==typeof l&&isFinite(l)&&Math.floor(l)===l?"categorical"!==r&&"number"===s&&"number"==typeof l&&"number"==typeof o&&void 0!==o&&l<o?[new Sn(e.key,u,"stop domain values must appear in ascending order")]:(o=l,"categorical"===r&&l in i?[new Sn(e.key,u,"stop domain values must be unique")]:(i[l]=!0,[])):[new Sn(e.key,u,"integer expected, found "+String(l))]}}function Rn(e){var t=("property"===e.expressionContext?gr:yr)(Rr(e.value),e.valueSpec);if("error"===t.result)return t.value.map((function(t){return new Sn(""+e.key+t.key,e.value,t.message)}));var r=t.value.expression||t.value._styleExpression.expression;if("property"===e.expressionContext&&"text-font"===e.propertyKey&&!r.outputDefined())return[new Sn(e.key,e.value,'Invalid data expression for "'+e.propertyKey+'". Output values must be contained as literals within the expression.')];if("property"===e.expressionContext&&"layout"===e.propertyType&&!rt(r))return[new Sn(e.key,e.value,'"feature-state" data expressions are not supported with layout properties.')];if("filter"===e.expressionContext)return function e(t,r){var n=new Set(["zoom","feature-state","pitch","distance-from-center"]);if(r.valueSpec&&r.valueSpec.expression){var i=!0,o=!1,s=void 0;try{for(var l,u=r.valueSpec.expression.parameters[Symbol.iterator]();!(i=(l=u.next()).done);i=!0){var c=l.value;n.delete(c)}}catch(e){o=!0,s=e}finally{try{!i&&u.return&&u.return()}finally{if(o)throw s}}}if(0===n.size)return[];var p=[];if(t instanceof Ne&&n.has(t.name))return[new Sn(r.key,r.value,'["'+t.name+'"] expression is not supported in a filter for a '+r.object.type+" layer with id: "+r.object.id)];return t.eachChild((function(t){p.push.apply(p,a(e(t,r)))})),p}(r,e);if(e.expressionContext&&0===e.expressionContext.indexOf("cluster")){if(!nt(r,["zoom","feature-state"]))return[new Sn(e.key,e.value,'"zoom" and "feature-state" expressions are not supported with cluster properties.')];if("cluster-initial"===e.expressionContext&&!tt(r))return[new Sn(e.key,e.value,"Feature data expressions are not supported with initial expression part of cluster properties.")]}return[]}function Cn(e){var t=e.key,r=e.value,n=e.valueSpec,i=[];return Array.isArray(n.values)?-1===n.values.indexOf(Ar(r))&&i.push(new Sn(t,r,"expected one of ["+n.values.join(", ")+"], "+JSON.stringify(r)+" found")):-1===Object.keys(n.values).indexOf(Ar(r))&&i.push(new Sn(t,r,"expected one of ["+Object.keys(n.values).join(", ")+"], "+JSON.stringify(r)+" found")),i}function Mn(e){if(Cr(Rr(e.value))){var t=e.layerType||"fill";return Rn(Y({},e,{expressionContext:"filter",valueSpec:e.styleSpec["filter_"+t]}))}return function e(t){var r=t.value,n=t.key;if("array"!==ir(r))return[new Sn(n,r,"array expected, "+ir(r)+" found")];var i=t.styleSpec,o=void 0,a=[];if(r.length<1)return[new Sn(n,r,"filter array must have at least 1 element")];switch(a=a.concat(Cn({key:n+"[0]",value:r[0],valueSpec:i.filter_operator,style:t.style,styleSpec:t.styleSpec})),Ar(r[0])){case"<":case"<=":case">":case">=":r.length>=2&&"$type"===Ar(r[1])&&a.push(new Sn(n,r,'"$type" cannot be use with operator "'+r[0]+'"'));case"==":case"!=":3!==r.length&&a.push(new Sn(n,r,'filter array for operator "'+r[0]+'" must have 3 elements'));case"in":case"!in":r.length>=2&&"string"!==(o=ir(r[1]))&&a.push(new Sn(n+"[1]",r[1],"string expected, "+o+" found"));for(var s=2;s<r.length;s++)o=ir(r[s]),"$type"===Ar(r[1])?a=a.concat(Cn({key:n+"["+s+"]",value:r[s],valueSpec:i.geometry_type,style:t.style,styleSpec:t.styleSpec})):"string"!==o&&"number"!==o&&"boolean"!==o&&a.push(new Sn(n+"["+s+"]",r[s],"string, number, or boolean expected, "+o+" found"));break;case"any":case"all":case"none":for(var l=1;l<r.length;l++)a=a.concat(e({key:n+"["+l+"]",value:r[l],style:t.style,styleSpec:t.styleSpec}));break;case"has":case"!has":o=ir(r[1]),2!==r.length?a.push(new Sn(n,r,'filter array for "'+r[0]+'" operator must have 2 elements')):"string"!==o&&a.push(new Sn(n+"[1]",r[1],"string expected, "+o+" found"));break;case"within":o=ir(r[1]),2!==r.length?a.push(new Sn(n,r,'filter array for "'+r[0]+'" operator must have 2 elements')):"object"!==o&&a.push(new Sn(n+"[1]",r[1],"object expected, "+o+" found"))}return a}(e)}function In(e,t){var r=e.key,n=e.style,i=e.styleSpec,o=e.value,a=e.objectKey,s=i[t+"_"+e.layerType];if(!s)return[];var l=a.match(/^(.*)-transition$/);if("paint"===t&&l&&s[l[1]]&&s[l[1]].transition)return Fn({key:r,value:o,valueSpec:i.transition,style:n,styleSpec:i});var u=e.valueSpec||s[a];if(!u)return[new Sn(r,o,'unknown property "'+a+'"')];var c=void 0;if("string"===ir(o)&&tr(u)&&!u.tokens&&(c=/^{([^}]+)}$/.exec(o)))return[new Sn(r,o,'"'+a+'" does not support interpolation syntax\nUse an identity property function instead: `{ "type": "identity", "property": '+JSON.stringify(c[1])+" }`.")];var p=[];return"symbol"===e.layerType&&("text-field"===a&&n&&!n.glyphs&&p.push(new Sn(r,o,'use of "text-field" requires a style "glyphs" property')),"text-font"===a&&or(Rr(o))&&"identity"===Ar(o.type)&&p.push(new Sn(r,o,'"text-font" does not support identity functions'))),p.concat(Fn({key:e.key,value:o,valueSpec:u,style:n,styleSpec:i,expressionContext:"property",propertyType:t,propertyKey:a}))}function Pn(e){var t=e.value,r=e.key,n=ir(t);return"string"!==n?[new Sn(r,t,"string expected, "+n+" found")]:[]}var zn={promoteId:function(e){var t=e.key,r=e.value;if("string"===ir(r))return Pn({key:t,value:r});var n=[];for(var i in r)n.push.apply(n,a(Pn({key:t+"."+i,value:r[i]})));return n}};function Ln(e){return e.source.reduce((function(t,r){var n=e[r];return"enum"===n.type.type&&(t=t.concat(Object.keys(n.type.values))),t}),[])}var Nn={"*":function(){return[]},array:jn,boolean:function(e){var t=e.value,r=e.key,n=ir(t);return"boolean"!==n?[new Sn(r,t,"boolean expected, "+n+" found")]:[]},number:On,color:function(e){var t=e.key,r=e.value,n=ir(r);return"string"!==n?[new Sn(t,r,"color expected, "+n+" found")]:null===ce(r)?[new Sn(t,r,'color expected, "'+r+'" found')]:[]},enum:Cn,filter:Mn,function:An,layer:function(e){var t=[],r=e.value,n=e.key,i=e.style,o=e.styleSpec;r.type||r.ref||t.push(new Sn(n,r,'either "type" or "ref" is required'));var a=Ar(r.type),s=Ar(r.ref);if(r.id)for(var l=Ar(r.id),u=0;u<e.arrayIndex;u++){var c=i.layers[u];Ar(c.id)===l&&t.push(new Sn(n,r.id,'duplicate layer id "'+r.id+'", previously used at line '+c.id.__line__))}if("ref"in r){["type","source","source-layer","filter","layout"].forEach((function(e){e in r&&t.push(new Sn(n,r[e],'"'+e+'" is prohibited for ref layers'))}));var p=void 0;i.layers.forEach((function(e){Ar(e.id)===s&&(p=e)})),p?p.ref?t.push(new Sn(n,r.ref,"ref cannot reference another ref layer")):a=Ar(p.type):"string"==typeof s&&t.push(new Sn(n,r.ref,'ref layer "'+s+'" not found'))}else if("background"!==a&&"sky"!==a)if(r.source){var d=i.sources&&i.sources[r.source],h=d&&Ar(d.type);d?"vector"===h&&"raster"===a?t.push(new Sn(n,r.source,'layer "'+r.id+'" requires a raster source')):"raster"===h&&"raster"!==a?t.push(new Sn(n,r.source,'layer "'+r.id+'" requires a vector source')):"vector"!==h||r["source-layer"]?"raster-dem"===h&&"hillshade"!==a?t.push(new Sn(n,r.source,"raster-dem source can only be used with layer type 'hillshade'.")):"line"!==a||!r.paint||!r.paint["line-gradient"]&&!r.paint["line-trim-offset"]||"geojson"===h&&d.lineMetrics||t.push(new Sn(n,r,'layer "'+r.id+'" specifies a line-gradient, which requires a GeoJSON source with `lineMetrics` enabled.')):t.push(new Sn(n,r,'layer "'+r.id+'" must specify a "source-layer"')):t.push(new Sn(n,r.source,'source "'+r.source+'" not found'))}else t.push(new Sn(n,r,'missing required property "source"'));return t=t.concat(En({key:n,value:r,valueSpec:o.layer,style:e.style,styleSpec:e.styleSpec,objectElementValidators:{"*":function(){return[]},type:function(){return Fn({key:n+".type",value:r.type,valueSpec:o.layer.type,style:e.style,styleSpec:e.styleSpec,object:r,objectKey:"type"})},filter:function(e){return Mn(Y({layerType:a},e))},layout:function(e){return En({layer:r,key:e.key,value:e.value,valueSpec:{},style:e.style,styleSpec:e.styleSpec,objectElementValidators:{"*":function(e){return function(e){return In(e,"layout")}(Y({layerType:a},e))}}})},paint:function(e){return En({layer:r,key:e.key,value:e.value,valueSpec:{},style:e.style,styleSpec:e.styleSpec,objectElementValidators:{"*":function(e){return function(e){return In(e,"paint")}(Y({layerType:a},e))}}})}}}))},object:En,source:function(e){var t=e.value,r=e.key,i=e.styleSpec,o=e.style;if(!t.type)return[new Sn(r,t,'"type" is required')];var s=Ar(t.type),l=void 0;switch(s){case"vector":case"raster":case"raster-dem":return l=En({key:r,value:t,valueSpec:i["source_"+s.replace("-","_")],style:e.style,styleSpec:i,objectElementValidators:zn});case"geojson":if(l=En({key:r,value:t,valueSpec:i.source_geojson,style:o,styleSpec:i,objectElementValidators:zn}),t.cluster)for(var u in t.clusterProperties){var c,p,d=n(t.clusterProperties[u],2),h=d[0],f=d[1],y="string"==typeof h?[h,["accumulated"],["get",u]]:h;(c=l).push.apply(c,a(Rn({key:r+"."+u+".map",value:f,expressionContext:"cluster-map"}))),(p=l).push.apply(p,a(Rn({key:r+"."+u+".reduce",value:y,expressionContext:"cluster-reduce"})))}return l;case"video":return En({key:r,value:t,valueSpec:i.source_video,style:o,styleSpec:i});case"image":return En({key:r,value:t,valueSpec:i.source_image,style:o,styleSpec:i});case"canvas":return[new Sn(r,null,"Please use runtime APIs to add canvas sources, rather than including them in stylesheets.","source.canvas")];default:return Cn({key:r+".type",value:t.type,valueSpec:{values:Ln(i)},style:o,styleSpec:i})}},light:function(e){var t=e.value,r=e.styleSpec,n=r.light,i=e.style,o=[],a=ir(t);if(void 0===t)return o;if("object"!==a)return o=o.concat([new Sn("light",t,"object expected, "+a+" found")]);for(var s in t){var l=s.match(/^(.*)-transition$/);o=l&&n[l[1]]&&n[l[1]].transition?o.concat(Fn({key:s,value:t[s],valueSpec:r.transition,style:i,styleSpec:r})):n[s]?o.concat(Fn({key:s,value:t[s],valueSpec:n[s],style:i,styleSpec:r})):o.concat([new Sn(s,t[s],'unknown property "'+s+'"')])}return o},terrain:function(e){var t=e.value,r=e.key,n=e.style,i=e.styleSpec,o=i.terrain,a=[],s=ir(t);if(void 0===t)return a;if("object"!==s)return a=a.concat([new Sn("terrain",t,"object expected, "+s+" found")]);for(var l in t){var u=l.match(/^(.*)-transition$/);a=u&&o[u[1]]&&o[u[1]].transition?a.concat(Fn({key:l,value:t[l],valueSpec:i.transition,style:n,styleSpec:i})):o[l]?a.concat(Fn({key:l,value:t[l],valueSpec:o[l],style:n,styleSpec:i})):a.concat([new Sn(l,t[l],'unknown property "'+l+'"')])}if(t.source){var c=n.sources&&n.sources[t.source],p=c&&Ar(c.type);c?"raster-dem"!==p&&a.push(new Sn(r,t.source,"terrain cannot be used with a source of type "+String(p)+', it only be used with a "raster-dem" source type')):a.push(new Sn(r,t.source,'source "'+t.source+'" not found'))}else a.push(new Sn(r,t,'terrain is missing required property "source"'));return a},fog:function(e){var t=e.value,r=e.style,n=e.styleSpec,i=n.fog,o=[],a=ir(t);if(void 0===t)return o;if("object"!==a)return o=o.concat([new Sn("fog",t,"object expected, "+a+" found")]);for(var s in t){var l=s.match(/^(.*)-transition$/);o=l&&i[l[1]]&&i[l[1]].transition?o.concat(Fn({key:s,value:t[s],valueSpec:n.transition,style:r,styleSpec:n})):i[s]?o.concat(Fn({key:s,value:t[s],valueSpec:i[s],style:r,styleSpec:n})):o.concat([new Sn(s,t[s],'unknown property "'+s+'"')])}return o},string:Pn,formatted:function(e){return 0===Pn(e).length?[]:Rn(e)},resolvedImage:function(e){return 0===Pn(e).length?[]:Rn(e)},projection:function(e){var t=e.value,r=e.styleSpec,n=r.projection,i=e.style,o=[],a=ir(t);if("object"===a)for(var s in t)o=o.concat(Fn({key:s,value:t[s],valueSpec:n[s],style:i,styleSpec:r}));else"string"!==a&&(o=o.concat([new Sn("projection",t,"object or string expected, "+a+" found")]));return o}};function Fn(e){var t=e.value,r=e.valueSpec,n=e.styleSpec;return r.expression&&or(Ar(t))?An(e):r.expression&&fr(Rr(t))?Rn(e):r.type&&Nn[r.type]?Nn[r.type](e):En(Y({},e,{valueSpec:r.type?n[r.type]:r}))}function Dn(e){var t=e.value,r=e.key,n=Pn(e);return n.length||(-1===t.indexOf("{fontstack}")&&n.push(new Sn(r,t,'"glyphs" url must include a "{fontstack}" token')),-1===t.indexOf("{range}")&&n.push(new Sn(r,t,'"glyphs" url must include a "{range}" token'))),n}function Un(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,r=Fn({key:"",value:e,valueSpec:t.$root,styleSpec:t,style:e,objectElementValidators:{glyphs:Dn,"*":function(){return[]}}});return qn(r)}function qn(e){return e.slice().sort((function(e,t){return e.line&&t.line?e.line-t.line:0}))}function Gn(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Bn={};!function(e){var t=function(){var e=function(e,t,r,n){for(r=r||{},n=e.length;n--;r[e[n]]=t);return r},t=[1,12],r=[1,13],n=[1,9],i=[1,10],o=[1,11],a=[1,14],s=[1,15],l=[14,18,22,24],u=[18,22],c=[22,24],p={trace:function(){},yy:{},symbols_:{error:2,JSONString:3,STRING:4,JSONNumber:5,NUMBER:6,JSONNullLiteral:7,NULL:8,JSONBooleanLiteral:9,TRUE:10,FALSE:11,JSONText:12,JSONValue:13,EOF:14,JSONObject:15,JSONArray:16,"{":17,"}":18,JSONMemberList:19,JSONMember:20,":":21,",":22,"[":23,"]":24,JSONElementList:25,$accept:0,$end:1},terminals_:{2:"error",4:"STRING",6:"NUMBER",8:"NULL",10:"TRUE",11:"FALSE",14:"EOF",17:"{",18:"}",21:":",22:",",23:"[",24:"]"},productions_:[0,[3,1],[5,1],[7,1],[9,1],[9,1],[12,2],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[15,2],[15,3],[20,3],[19,1],[19,3],[16,2],[16,3],[25,1],[25,3]],performAction:function(e,t,r,n,i,o,a){var s=o.length-1;switch(i){case 1:this.$=new String(e.replace(/\\(\\|")/g,"$1").replace(/\\n/g,"\n").replace(/\\r/g,"\r").replace(/\\t/g,"\t").replace(/\\v/g,"\v").replace(/\\f/g,"\f").replace(/\\b/g,"\b")),this.$.__line__=this._$.first_line;break;case 2:this.$=new Number(e),this.$.__line__=this._$.first_line;break;case 3:this.$=null;break;case 4:this.$=new Boolean(!0),this.$.__line__=this._$.first_line;break;case 5:this.$=new Boolean(!1),this.$.__line__=this._$.first_line;break;case 6:return this.$=o[s-1];case 13:this.$={},Object.defineProperty(this.$,"__line__",{value:this._$.first_line,enumerable:!1});break;case 14:case 19:this.$=o[s-1],Object.defineProperty(this.$,"__line__",{value:this._$.first_line,enumerable:!1});break;case 15:this.$=[o[s-2],o[s]];break;case 16:this.$={},this.$[o[s][0]]=o[s][1];break;case 17:this.$=o[s-2],o[s-2][o[s][0]]=o[s][1];break;case 18:this.$=[],Object.defineProperty(this.$,"__line__",{value:this._$.first_line,enumerable:!1});break;case 20:this.$=[o[s]];break;case 21:this.$=o[s-2],o[s-2].push(o[s])}},table:[{3:5,4:t,5:6,6:r,7:3,8:n,9:4,10:i,11:o,12:1,13:2,15:7,16:8,17:a,23:s},{1:[3]},{14:[1,16]},e(l,[2,7]),e(l,[2,8]),e(l,[2,9]),e(l,[2,10]),e(l,[2,11]),e(l,[2,12]),e(l,[2,3]),e(l,[2,4]),e(l,[2,5]),e([14,18,21,22,24],[2,1]),e(l,[2,2]),{3:20,4:t,18:[1,17],19:18,20:19},{3:5,4:t,5:6,6:r,7:3,8:n,9:4,10:i,11:o,13:23,15:7,16:8,17:a,23:s,24:[1,21],25:22},{1:[2,6]},e(l,[2,13]),{18:[1,24],22:[1,25]},e(u,[2,16]),{21:[1,26]},e(l,[2,18]),{22:[1,28],24:[1,27]},e(c,[2,20]),e(l,[2,14]),{3:20,4:t,20:29},{3:5,4:t,5:6,6:r,7:3,8:n,9:4,10:i,11:o,13:30,15:7,16:8,17:a,23:s},e(l,[2,19]),{3:5,4:t,5:6,6:r,7:3,8:n,9:4,10:i,11:o,13:31,15:7,16:8,17:a,23:s},e(u,[2,17]),e(u,[2,15]),e(c,[2,21])],defaultActions:{16:[2,6]},parseError:function(e,t){if(!t.recoverable)throw new Error(e);this.trace(e)},parse:function(e){var t=this,r=[0],n=[null],i=[],o=this.table,a="",s=0,l=0,u=2,c=1,p=i.slice.call(arguments,1),d=Object.create(this.lexer),h={yy:{}};for(var f in this.yy)Object.prototype.hasOwnProperty.call(this.yy,f)&&(h.yy[f]=this.yy[f]);d.setInput(e,h.yy),h.yy.lexer=d,h.yy.parser=this,void 0===d.yylloc&&(d.yylloc={});var y=d.yylloc;i.push(y);var m=d.options&&d.options.ranges;function v(){var e;return"number"!=typeof(e=d.lex()||c)&&(e=t.symbols_[e]||e),e}"function"==typeof h.yy.parseError?this.parseError=h.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var g,b,x,w,k,_,S,T,E={};;){if(b=r[r.length-1],this.defaultActions[b]?x=this.defaultActions[b]:(null==g&&(g=v()),x=o[b]&&o[b][g]),void 0===x||!x.length||!x[0]){var j="";for(k in T=[],o[b])this.terminals_[k]&&k>u&&T.push("'"+this.terminals_[k]+"'");j=d.showPosition?"Parse error on line "+(s+1)+":\n"+d.showPosition()+"\nExpecting "+T.join(", ")+", got '"+(this.terminals_[g]||g)+"'":"Parse error on line "+(s+1)+": Unexpected "+(g==c?"end of input":"'"+(this.terminals_[g]||g)+"'"),this.parseError(j,{text:d.match,token:this.terminals_[g]||g,line:d.yylineno,loc:y,expected:T})}if(x[0]instanceof Array&&x.length>1)throw new Error("Parse Error: multiple actions possible at state: "+b+", token: "+g);switch(x[0]){case 1:r.push(g),n.push(d.yytext),i.push(d.yylloc),r.push(x[1]),g=null,l=d.yyleng,a=d.yytext,s=d.yylineno,y=d.yylloc;break;case 2:if(_=this.productions_[x[1]][1],E.$=n[n.length-_],E._$={first_line:i[i.length-(_||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(_||1)].first_column,last_column:i[i.length-1].last_column},m&&(E._$.range=[i[i.length-(_||1)].range[0],i[i.length-1].range[1]]),void 0!==(w=this.performAction.apply(E,[a,l,s,h.yy,x[1],n,i].concat(p))))return w;_&&(r=r.slice(0,-1*_*2),n=n.slice(0,-1*_),i=i.slice(0,-1*_)),r.push(this.productions_[x[1]][0]),n.push(E.$),i.push(E._$),S=o[r[r.length-2]][r[r.length-1]],r.push(S);break;case 3:return!0}}return!0}},d={EOF:1,parseError:function(e,t){if(!this.yy.parser)throw new Error(e);this.yy.parser.parseError(e,t)},setInput:function(e,t){return this.yy=t||this.yy||{},this._input=e,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e,e.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e},unput:function(e){var t=e.length,r=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),r.length-1&&(this.yylineno-=r.length-1);var i=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:r?(r.length===n.length?this.yylloc.first_column:0)+n[n.length-r.length].length-r[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[i[0],i[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(e){this.unput(this.match.slice(e))},pastInput:function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var e=this.pastInput(),t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"},test_match:function(e,t){var r,n,i;if(this.options.backtrack_lexer&&(i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(i.yylloc.range=this.yylloc.range.slice(0))),(n=e[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],r=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),r)return r;if(this._backtrack){for(var o in i)this[o]=i[o];return!1}return!1},next:function(){if(this.done)return this.EOF;var e,t,r,n;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var i=this._currentRules(),o=0;o<i.length;o++)if((r=this._input.match(this.rules[i[o]]))&&(!t||r[0].length>t[0].length)){if(t=r,n=o,this.options.backtrack_lexer){if(!1!==(e=this.test_match(r,i[o])))return e;if(this._backtrack){t=!1;continue}return!1}if(!this.options.flex)break}return t?!1!==(e=this.test_match(t,i[n]))&&e:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var e=this.next();return e||this.lex()},begin:function(e){this.conditionStack.push(e)},popState:function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(e){return(e=this.conditionStack.length-1-Math.abs(e||0))>=0?this.conditionStack[e]:"INITIAL"},pushState:function(e){this.begin(e)},stateStackSize:function(){return this.conditionStack.length},options:{},performAction:function(e,t,r,n){switch(r){case 0:break;case 1:return 6;case 2:return t.yytext=t.yytext.substr(1,t.yyleng-2),4;case 3:return 17;case 4:return 18;case 5:return 23;case 6:return 24;case 7:return 22;case 8:return 21;case 9:return 10;case 10:return 11;case 11:return 8;case 12:return 14;case 13:return"INVALID"}},rules:[/^(?:\s+)/,/^(?:(-?([0-9]|[1-9][0-9]+))(\.[0-9]+)?([eE][-+]?[0-9]+)?\b)/,/^(?:"(?:\\[\\"bfnrt/]|\\u[a-fA-F0-9]{4}|[^\\\0-\x09\x0a-\x1f"])*")/,/^(?:\{)/,/^(?:\})/,/^(?:\[)/,/^(?:\])/,/^(?:,)/,/^(?::)/,/^(?:true\b)/,/^(?:false\b)/,/^(?:null\b)/,/^(?:$)/,/^(?:.)/],conditions:{INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,12,13],inclusive:!0}}};function h(){this.yy={}}return p.lexer=d,h.prototype=p,p.Parser=h,new h}();void 0!==Gn&&(e.parser=t,e.Parser=t.Parser,e.parse=function(){return t.parse.apply(t,arguments)})}(Bn);var Wn=Bn;function Yn(e){if(e instanceof String||"string"==typeof e||e instanceof r)try{return Wn.parse(e.toString())}catch(e){throw new Tn(e)}return e}function Xn(e,t){return!e||"string"!==ir(e)||!!e.match(t)}function Jn(e,t,r){var n=new Set(t),i=[];return Object.keys(e).forEach((function(t){if(!n.has(t)){var o=r?r+"."+t:null;i.push(new Sn(o,e[t],'Unsupported property "'+t+'"'))}})),i}var Zn=new Set(["vector","raster","raster-dem"]);function Kn(e){var t=[],r=0;return Object.keys(e).forEach((function(n,i){var o=function(e,t){var r=[];return r.push.apply(r,a(Jn(e,["type","url","tileSize"],"source"))),Zn.has(String(e.type))||r.push(new Sn("sources["+t+"].type",e.type,"Expected one of ["+Array.from(Zn).join(", ")+"]")),e.url&&Xn(e.url,/^mapbox:\/\/([^/]*)$/)||r.push(new Sn("sources["+t+"].url",e.url,"Expected a valid Mapbox tileset url")),r}(e[n],i);o.length||(r+=function(e){return e.url?e.url.split(",").length:0}(e[n])),t.push.apply(t,a(o))})),r>15&&t.push(new Sn("sources",null,"Styles must contain 15 or fewer sources")),t}function Hn(e,t){var r=[],n=Jn(e,[].concat(a(t),["owner","id","cacheControl","draft","created","modified","visibility","protected"]));r.push.apply(r,a(n)),(e.version>8||e.version<8)&&r.push(new Sn("version",e.version,"Style version must be 8"));Xn(e.glyphs,/^mapbox:\/\/fonts\/([^/]*)\/{fontstack}\/{range}.pbf$/)||r.push(new Sn("glyphs",e.glyphs,"Styles must reference glyphs hosted by Mapbox"));Xn(e.sprite,/^mapbox:\/\/sprites\/([^/]*)\/([^/]*)\/?([^/]*)?$/)||r.push(new Sn("sprite",e.sprite,"Styles must reference sprites hosted by Mapbox"));return Xn(e.visibility,/^(public|private)$/)||r.push(new Sn("visibility",e.visibility,"Style visibility must be public or private")),void 0!==e.protected&&"boolean"!==ir(e.protected)&&r.push(new Sn("protected",e.protected,"Style protection must be true or false")),r}var Vn={StyleExpression:hr,isExpression:fr,isExpressionFilter:Cr,createExpression:yr,createPropertyExpression:gr,normalizePropertyExpression:function(e,t){if(or(e))return new br(e,t);if(fr(e)){var r=gr(e,t);if("error"===r.result)throw new Error(r.value.map((function(e){return e.key+": "+e.message})).join(", "));return r.value}var n=e;return"string"==typeof e&&"color"===t.type&&(n=ge.parse(e)),{kind:"constant",evaluate:function(){return n}}},ZoomConstantExpression:mr,ZoomDependentExpression:vr,StylePropertyFunction:br},$n={convertFunction:wr,createFunction:sr,isFunction:or},Qn={eachSource:N,eachLayer:F,eachProperty:D};t.Color=ge,t.ParsingError=Tn,t.ValidationError=Sn,t.composite=function(e){var t=[],r=[],n=[];for(var i in e.sources){var o=e.sources[i];if("vector"===o.type){var a=/^mapbox:\/\/(.*)/.exec(o.url);a&&(t.push(i),r.push(a[1]))}}if(t.length<2)return e;t.forEach((function(t){delete e.sources[t]}));var s=r.join(",");return e.sources[s]={type:"vector",url:"mapbox://"+s},e.layers.forEach((function(e){if(t.indexOf(e.source)>=0&&(e.source=s,"source-layer"in e)){if(n.indexOf(e["source-layer"])>=0)throw new Error("Conflicting source layer names");n.push(e["source-layer"])}})),e},t.convertFilter=Gr,t.derefLayers=function(e){e=e.slice();for(var t=Object.create(null),r=0;r<e.length;r++)t[e[r].id]=e[r];for(var n=0;n<e.length;n++)"ref"in e[n]&&(e[n]=Xr(e[n],t[e[n].ref]));return e},t.diff=function(e,t){if(!e)return[{command:Zr,args:[t]}];var r=[];try{if(!Jr(e.version,t.version))return[{command:Zr,args:[t]}];Jr(e.center,t.center)||r.push({command:an,args:[t.center]}),Jr(e.zoom,t.zoom)||r.push({command:sn,args:[t.zoom]}),Jr(e.bearing,t.bearing)||r.push({command:ln,args:[t.bearing]}),Jr(e.pitch,t.pitch)||r.push({command:un,args:[t.pitch]}),Jr(e.sprite,t.sprite)||r.push({command:cn,args:[t.sprite]}),Jr(e.glyphs,t.glyphs)||r.push({command:pn,args:[t.glyphs]}),Jr(e.transition,t.transition)||r.push({command:dn,args:[t.transition]}),Jr(e.light,t.light)||r.push({command:hn,args:[t.light]}),Jr(e.fog,t.fog)||r.push({command:yn,args:[t.fog]}),Jr(e.projection,t.projection)||r.push({command:mn,args:[t.projection]});var n={},i=[];!function(e,t,r,n){e=e||{},t=t||{};var i=void 0;for(i in e)e.hasOwnProperty(i)&&(t.hasOwnProperty(i)||gn(i,r,n));for(i in t)t.hasOwnProperty(i)&&(e.hasOwnProperty(i)?Jr(e[i],t[i])||("geojson"===e[i].type&&"geojson"===t[i].type&&xn(e,t,i)?r.push({command:rn,args:[i,t[i].data]}):bn(i,t,r,n)):vn(i,t,r))}(e.sources,t.sources,i,n);var o=[];e.layers&&e.layers.forEach((function(e){e.source&&n[e.source]?r.push({command:Hr,args:[e.id]}):o.push(e)}));var a=e.terrain;a&&n[a.source]&&(r.push({command:fn,args:[void 0]}),a=void 0),r=r.concat(i),Jr(a,t.terrain)||r.push({command:fn,args:[t.terrain]}),function(e,t,r){t=t||[];var n=(e=e||[]).map(kn),i=t.map(kn),o=e.reduce(_n,{}),a=t.reduce(_n,{}),s=n.slice(),l=Object.create(null),u=void 0,c=void 0,p=void 0,d=void 0,h=void 0,f=void 0,y=void 0;for(u=0,c=0;u<n.length;u++)p=n[u],a.hasOwnProperty(p)?c++:(r.push({command:Hr,args:[p]}),s.splice(s.indexOf(p,c),1));for(u=0,c=0;u<i.length;u++)p=i[i.length-1-u],s[s.length-1-u]!==p&&(o.hasOwnProperty(p)?(r.push({command:Hr,args:[p]}),s.splice(s.lastIndexOf(p,s.length-c),1)):c++,f=s[s.length-u],r.push({command:Kr,args:[a[p],f]}),s.splice(s.length-u,0,p),l[p]=!0);for(u=0;u<i.length;u++)if(d=o[p=i[u]],h=a[p],!l[p]&&!Jr(d,h))if(Jr(d.source,h.source)&&Jr(d["source-layer"],h["source-layer"])&&Jr(d.type,h.type)){for(y in wn(d.layout,h.layout,r,p,null,$r),wn(d.paint,h.paint,r,p,null,Vr),Jr(d.filter,h.filter)||r.push({command:Qr,args:[p,h.filter]}),Jr(d.minzoom,h.minzoom)&&Jr(d.maxzoom,h.maxzoom)||r.push({command:nn,args:[p,h.minzoom,h.maxzoom]}),d)d.hasOwnProperty(y)&&"layout"!==y&&"paint"!==y&&"filter"!==y&&"metadata"!==y&&"minzoom"!==y&&"maxzoom"!==y&&(0===y.indexOf("paint.")?wn(d[y],h[y],r,p,y.slice(6),Vr):Jr(d[y],h[y])||r.push({command:on,args:[p,y,h[y]]}));for(y in h)h.hasOwnProperty(y)&&!d.hasOwnProperty(y)&&"layout"!==y&&"paint"!==y&&"filter"!==y&&"metadata"!==y&&"minzoom"!==y&&"maxzoom"!==y&&(0===y.indexOf("paint.")?wn(d[y],h[y],r,p,y.slice(6),Vr):Jr(d[y],h[y])||r.push({command:on,args:[p,y,h[y]]}))}else r.push({command:Hr,args:[p]}),f=s[s.lastIndexOf(p)+1],r.push({command:Kr,args:[h,f]})}(o,t.layers,r)}catch(e){console.warn("Unable to compute style diff:",e),r=[{command:Zr,args:[t]}]}return r},t.expression=Vn,t.featureFilter=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"fill";if(null==e)return{filter:function(){return!0},needGeometry:!1,needFeature:!1};Cr(e)||(e=Nr(e));var r=e,n=!0;try{n=Mr(r)}catch(e){console.warn("Failed to extract static filter. Filter will continue working, but at higher memory usage and slower framerate.\nThis is most likely a bug, please report this via https://github.com/mapbox/mapbox-gl-js/issues/new?assignees=&labels=&template=Bug_report.md\nand paste the contents of this message in the report.\nThank you!\nFilter Expression:\n"+JSON.stringify(r,null,2)+"\n        ")}var i=l["filter_"+t],o=yr(n,i),a=null;if("error"===o.result)throw new Error(o.value.map((function(e){return e.key+": "+e.message})).join(", "));a=function(e,t,r){return o.value.evaluate(e,t,{},r)};var s=null,u=null;if(n!==r){var c=yr(r,i);if("error"===c.result)throw new Error(c.value.map((function(e){return e.key+": "+e.message})).join(", "));s=function(e,t,r,n,i){return c.value.evaluate(e,t,{},r,void 0,void 0,n,i)},u=!tt(c.value.expression)}a=a;var p=Lr(n);return{filter:a,dynamicFilter:s||void 0,needGeometry:p,needFeature:!!u}},t.format=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(e=p(e,l.$root)).layers&&(e.layers=e.layers.map((function(e){return p(e,l.layer)}))),c(e,{indent:t})},t.function=$n,t.latest=l,t.migrate=function(e){var t=!1;if(7===e.version&&(e=W(e),t=!0),8===e.version&&(t=function(e){var t=[];return F(e,(function(e){e.filter&&(e.filter=Gr(e.filter))})),D(e,{paint:!0,layout:!0},(function(e){var r=e.path,n=e.value,i=e.reference,a=e.set;fr(n)||("object"!==(void 0===n?"undefined":o(n))||Array.isArray(n)?i.tokens&&"string"==typeof n&&a(Or(n)):(a(wr(n,i)),t.push(r.join("."))))})),e}(e),t=!0),!t)throw new Error("cannot migrate from",e.version);return e},t.v8=l,t.validate=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,r=e;try{r=Yn(r)}catch(e){return[e]}return Un(r,t)},t.validateMapboxApiSupported=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,r=e;try{r=Yn(r)}catch(e){return[e]}var n=Un(r,t).concat(Hn(r,Object.keys(l.$root)));return r.sources&&(n=n.concat(Kn(r.sources))),n},t.visit=Qn}).call(this,r(18),r(29).Buffer)},function(e,t,r){"use strict";var n,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"===("undefined"==typeof window?"undefined":i(window))&&(n=window)}e.exports=n},function(e,t,r){"use strict";var n={thin:100,hairline:100,"ultra-light":200,"extra-light":200,light:300,book:300,regular:400,normal:400,plain:400,roman:400,standard:400,medium:500,"semi-bold":600,"demi-bold":600,bold:700,"extra-bold":800,"ultra-bold":800,heavy:900,black:900,"heavy-black":900,fat:900,poster:900,"ultra-black":950,"extra-black":950},i=/(italic|oblique)$/i,o={};e.exports=function(e,t,r){var a=o[e];if(!a){Array.isArray(e)||(e=[e]);for(var s,l,u=400,c="normal",p=[],d=0,h=e.length;d<h;++d){var f=e[d].split(" "),y=f[f.length-1].toLowerCase();for(var m in"normal"==y||"italic"==y||"oblique"==y?(c=l?c:y,l=!0,f.pop(),y=f[f.length-1].toLowerCase()):i.test(y)&&(y=y.replace(i,""),c=l?c:f[f.length-1].replace(y,""),l=!0),n){var v=f.length>1?f[f.length-2].toLowerCase():"";if(y==m||y==m.replace("-","")||v+"-"+y==m){u=s?u:n[m],f.pop(),v&&m.startsWith(v)&&f.pop();break}}s||"number"!=typeof y||(u=y,s=!0);var g=f.join(" ").replace("Klokantech Noto Sans","Noto Sans");-1!==g.indexOf(" ")&&(g='"'+g+'"'),p.push(g)}a=o[e]=[c,u,p]}return a[0]+" "+a[1]+" "+t+"px"+(r?"/"+r:"")+" "+a[2]}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.getValue=_,t.default=function(e,t,r,f,y,m,v,g){if(!f){f=[];for(var w=78271.51696402048;f.length<21;w/=2)f.push(w)}if("string"==typeof t&&(t=JSON.parse(t)),8!=t.version)throw new Error("glStyle version 8 required.");if(m&&!v){var T=new Image;T.crossOrigin="anonymous",T.onload=function(){v=T,[T.width,T.height],e.changed()},T.src=m}var R=document.createElement("CANVAS").getContext("2d"),M={};function I(e,t,r){var n=r+","+t+","+e,i=M[n];if(!i){R.font=t;for(var o=R.measureText("M").width*r,a=[],s="",l=[],u=0,c=(a=/[\u4E00-\u9FA5]+/.test(e)?function(e,t,r){if(r.measureText(e).width>t){var n=[],i=[];return e.split("").forEach((function(e){var o=i.join("")+e;r.measureText(o).width>t?(n.push(i.join("")),i=[e]):i.push(e)})),n.push(i.join("")),n}return[e]}(e,o,R):e.split(" ")).length;u<c;++u){var p=a[u];R.measureText(s+p).width<=o?s+=(s?" ":"")+p:(s&&l.push(s),s=p)}s&&l.push(s),M[n]=i=l.join("\n")}return i}for(var P=(0,p.derefLayers)(t.layers),z={},L=[],N=void 0,F=0,D=P.length;F<D;++F){var U=P[F],q=U.id;if("string"==typeof r&&U.source==r||-1!==r.indexOf(q)){var G=U["source-layer"];N||(N=U.source);var B=z[G];B||(B=z[G]=[]),B.push({layer:U,index:F}),L.push(q)}delete k[q],delete j[q]}var W={},Y=[],X={},J=function(e,t){var r=e.getProperties();e.styleIds=e.styleIds||{};var p=z[r.layer];if(p){var m=f.indexOf(t);-1==m&&(m=Math.round((0,h.getZoomForResolution)(t,f)));for(var g=b[e.getGeometry().getType()],w={properties:r,type:g},k=-1,T=0,j=p.length;T<j;++T){var R=p[T],M=R.layer,P=M.id,L=M.layout||x,N=M.paint||x;if(!("none"===L.visibility||"minzoom"in M&&m<M.minzoom||"maxzoom"in M&&m>=M.maxzoom)){e.styleIds[m]=e.styleIds[m]||[];var F=M.filter,D=void 0,U=void 0;if(!F||O(P,F,w,m)){var q=void 0,G=void 0,B=void 0,J=void 0,Z=void 0,K=void 0,H=R.index;if(3==g&&"fill"==M.type)if(G=_(M,"paint","fill-opacity",m,w),"fill-pattern"in N){var V=_(M,"paint","fill-pattern",m,w);if(V&&(D="string"==typeof V?C(V,r):V.toString(),v&&y&&y[D])){++k,-1===e.styleIds[m].indexOf(P)&&e.styleIds[m].push(P),(K=Y[k])&&K.getFill()&&!K.getStroke()&&!K.getText()||(K=Y[k]=new i.default({fill:new o.default})),B=K.getFill(),K.setZIndex(H);var $=D+"."+G,Q=X[$];if(!Q){var ee=y[D],te=document.createElement("canvas");te.width=ee.width,te.height=ee.height;var re=te.getContext("2d");re.globalAlpha=G,re.drawImage(v,ee.x,ee.y,ee.width,ee.height,0,0,ee.width,ee.height),Q=re.createPattern(te,"repeat"),X[$]=Q}B.setColor(Q)}}else"fill-color"in N&&((q=A(_(M,"paint","fill-color",m,w),G))&&(++k,-1===e.styleIds[m].indexOf(P)&&e.styleIds[m].push(P),(K=Y[k])&&K.getFill()&&!K.getStroke()&&!K.getText()||(K=Y[k]=new i.default({fill:new o.default})),(B=K.getFill()).setColor(q),K.setZIndex(H)),"fill-outline-color"in N?Z=A(_(M,"paint","fill-outline-color",m,r),G):"fill-antialias"in N&&(Z=q),Z&&(++k,-1===e.styleIds[m].indexOf(P)&&e.styleIds[m].push(P),(K=Y[k])&&K.getStroke()&&!K.getFill()&&!K.getText()||(K=Y[k]=new i.default({stroke:new a.default})),(J=K.getStroke()).setColor(Z),J.setWidth(1),K.setZIndex(H)));1!=g&&"line"==M.type&&function(){q=!("line-pattern"in N)&&"line-color"in N?A(_(M,"paint","line-color",m,w),_(M,"paint","line-opacity",m,w)):void 0;var t=_(M,"paint","line-width",m,w);q&&t>0&&(++k,-1===e.styleIds[m].indexOf(P)&&e.styleIds[m].push(P),(K=Y[k])&&K.getStroke()&&!K.getFill()&&!K.getText()||(K=Y[k]=new i.default({stroke:new a.default})),(J=K.getStroke()).setLineCap(_(M,"layout","line-cap",m,w)),J.setLineJoin(_(M,"layout","line-join",m,w)),J.setMiterLimit(_(M,"layout","line-miter-limit",m,w)),J.setColor(q),J.setWidth(t),J.setLineDash(N["line-dasharray"]?_(M,"paint","line-dasharray",m,w).map((function(e){return e*t})):null),K.setZIndex(H))}();var ne=!1,ie=null,oe=void 0;if((1==g||2==g)&&"icon-image"in L){var ae=_(M,"layout","icon-image",m,w);if(ae){D="string"==typeof ae?C(ae,r):ae.toString();var se=void 0;if(v&&y&&y[D]){if(2==g){var le=e.getGeometry();if(le.getFlatMidpoint){var ue=le.getExtent();Math.sqrt(Math.max(Math.pow((ue[2]-ue[0])/t,2),Math.pow((ue[3]-ue[1])/t,2)))>150&&(se=new c.default(le.getFlatMidpoint()))}}if(2!==g||se){++k,-1===e.styleIds[m].indexOf(P)&&e.styleIds[m].push(P),(K=Y[k])&&K.getImage()&&!K.getFill()&&!K.getStroke()||(K=Y[k]=new i.default),K.setGeometry(se);var ce=_(M,"layout","icon-size",m,w),pe=void 0!==N["icon-color"]?_(M,"paint","icon-color",m,w):null,de=_(M,"paint","icon-translate",m,w),he=_(M,"paint","icon-translate-anchor",m,w),fe=_(M,"layout","icon-anchor",m,w),ye=_(M,"layout","icon-offset",m,w),me=S(fe),ve=me.anchorOffset,ge=me.iconAnchor,be=void 0===ge?fe:ge,xe=D+"."+ce+"."+de+"."+he+"."+be+"."+ye+"."+ve;if(null!==pe&&(xe+="."+pe),!(U=W[xe])){var we=y[D],ke=document.createElement("canvas");ke.width=we.width,ke.height=we.height;var _e=ke.getContext("2d");_e.drawImage(v,we.x,we.y,we.width,we.height,0,0,we.width,we.height);var Se=_e.getImageData(0,0,ke.width,ke.height);if(null!==pe&&we.sdf){q=A(pe,1);for(var Te=0,Ee=Se.data.length;Te<Ee;Te+=4)Se.data[Te]=255*pe.r,Se.data[Te+1]=255*pe.g,Se.data[Te+2]=255*pe.b}_e.putImageData(Se,0,0);var je=[de[0]/we.width,de[1]/we.height];U=W[xe]=new s.default({img:ke,anchorOrigin:be,anchor:[ye[0]+ve[0]+je[0],ye[1]+ve[1]-je[1]],imgSize:[ke.width,ke.height],scale:ce/we.pixelRatio})}var Oe=_(M,"layout","icon-rotate",m,w);Oe.indexOf&&0===Oe.indexOf("{")&&(Oe=360-C(Oe,r)),U.setRotation((0,h.deg2rad)(Oe)),U.setOpacity(_(M,"paint","icon-opacity",m,w)),K.setImage(U),ie=K.getText(),K.setText(void 0),K.setZIndex(99999-H),ne=!0,oe=!1}else oe=!0}}}if(1==g&&"circle-radius"in N){++k,-1===e.styleIds[m].indexOf(P)&&e.styleIds[m].push(P),(K=Y[k])&&K.getImage()&&!K.getFill()&&!K.getStroke()||(K=Y[k]=new i.default);var Ae=_(M,"paint","circle-radius",m,w),Re=_(M,"paint","circle-stroke-color",m,w),Ce=_(M,"paint","circle-color",m,w),Me=_(M,"paint","circle-opacity",m,w),Ie=_(M,"paint","circle-stroke-width",m,w),Pe=_(M,"paint","circle-stroke-opacity",m,w);(U=W[Ae+"."+Re+"."+Ce+"."+Me+"."+Ie+"."+Pe])||(U=new u.default({radius:Ae,stroke:0===Ie?void 0:new a.default({width:Ie,color:A(Re,Pe)}),fill:new o.default({color:A(Ce,Me)})})),K.setImage(U),ie=K.getText(),K.setText(void 0),K.setGeometry(void 0),K.setZIndex(99999-H),ne=!0}var ze=void 0;if("text-field"in L){var Le=_(M,"layout","text-field",m,w);ze="object"===(void 0===Le?"undefined":n(Le))&&Le.sections&&1===Le.sections.length?Le.toString():C(Le,r)}if(ze&&!oe){ne||(++k,-1===e.styleIds[m].indexOf(P)&&e.styleIds[m].push(P),(K=Y[k])&&K.getText()&&!K.getFill()&&!K.getStroke()||(K=Y[k]=new i.default),K.setImage(void 0),K.setGeometry(void 0)),K.getText()||K.setText(ie||new l.default),ie=K.getText();var Ne=_(M,"layout","text-size",m,w),Fe=_(M,"layout","text-line-height",m,w),De=(0,d.default)(E(_(M,"layout","text-font",m,w)),Ne),Ue=L["text-transform"];"uppercase"==Ue?ze=ze.toUpperCase():"lowercase"==Ue&&(ze=ze.toLowerCase());var qe=2==g?ze:I(ze,De,_(M,"layout","text-max-width",m,w));ie.setText(qe),ie.setFont(De),ie.setRotation((0,h.deg2rad)(_(M,"layout","text-rotate",m,w)));var Ge=_(M,"layout","text-anchor",m,w),Be=ne||1==g?"point":_(M,"layout","symbol-placement",m,w);ie.setPlacement(Be);var We=_(M,"paint","text-halo-width",m,w),Ye=_(M,"layout","text-offset",m,w),Xe=_(M,"paint","text-translate",m,w),Je=0,Ze=0,Ke="center";-1!==Ge.indexOf("left")?(Ke="left",Ze=We):-1!==Ge.indexOf("right")&&(Ke="right",Ze=-We),"point"==Be?ie.setTextAlign(Ke):(ie.setMaxAngle((0,h.deg2rad)(_(M,"layout","text-max-angle",m,w))*ze.length/qe.length),ie.setTextAlign());var He="middle";0==Ge.indexOf("bottom")?(He="bottom",Je=-We-.5*(Fe-1)*Ne):0==Ge.indexOf("top")&&(He="top",Je=We+.5*(Fe-1)*Ne),ie.setTextBaseline(He),ie.setOffsetX(Ye[0]*Ne+Ze+Xe[0]),ie.setOffsetY(Ye[1]*Ne+Je+Xe[1]),G=_(M,"paint","text-opacity",m,w);var Ve=new o.default;Ve.setColor(A(_(M,"paint","text-color",m,w),G)||"rgba(0,0,0,0)"),ie.setFill(Ve);var $e=A(_(M,"paint","text-halo-color",m,w),G);if($e){var Qe=new a.default;Qe.setColor($e),Qe.setWidth(_(M,"paint","text-halo-width",m,w)),ie.setStroke(Qe)}else ie.setStroke(void 0);K.setZIndex(99999-H)}}}}return k>-1?(Y.length=k+1,Y):void 0}};return e.setStyle(J),e.set("mapbox-source",N),e.set("mapbox-layers",L),J};var i=f(r(33)),o=f(r(34)),a=f(r(35)),s=f(r(36)),l=f(r(37)),u=f(r(38)),c=f(r(39)),p=r(17),d=f(r(19)),h=r(40);function f(e){return e&&e.__esModule?e:{default:e}}var y=p.function.isFunction,m=p.function.convertFunction,v=p.expression.isExpression,g=p.expression.createPropertyExpression,b={Point:1,MultiPoint:1,LineString:2,MultiLineString:2,Polygon:3,MultiPolygon:3},x={},w={zoom:0},k={};function _(e,t,r,n,i){var o=e.id;k[o]||(k[o]={});var a=k[o];if(!a[r]){var s=(e[t]||x)[r],l=p.latest[t+"_"+e.type][r];void 0===s&&(s=l.default);var u=v(s);if(!u&&y(s)&&(s=m(s,l),u=!0),u){var c=function(e,t){var r=g(e,t);if("error"===r.result)throw new Error(r.value.map((function(e){return e.key+": "+e.message})).join(", "));return r.value}(s,l);a[r]=c.evaluate.bind(c)}else"color"==l.type&&(s=p.Color.parse(s)),a[r]=function(){return s}}return w.zoom=n,a[r](w,i)}function S(e){var t=[.5,.5];return["top-left","top-right","bottom-left","bottom-right"].indexOf(e)>-1&&(t=[0,0]),"left"===e&&(e="top-left",t=[0,.5]),"right"===e&&(e="top-left",t=[1,.5]),"bottom"===e&&(e="top-left",t=[.5,1]),"top"===e&&(e="top-left",t=[.5,0]),{anchorOffset:t,iconAnchor:e}}var T={};function E(e,t){if(T[e])return T[e];if(t){for(var r=0,n=e.length;r<n;++r){var i=e[r];if(-1!=t.indexOf(i)){T[e]=i;break}}T[e]||(T[e]=e[e.length-1])}else T[e]=e[0];return T[e]}var j={};function O(e,t,r,n){return e in j||(j[e]=(0,p.featureFilter)(t).filter),w.zoom=n,j[e](w,r)}function A(e,t){if(e){if(0===e.a||0===t)return;var r=e.a;return t=void 0===t?1:t,"rgba("+Math.round(255*e.r/r)+","+Math.round(255*e.g/r)+","+Math.round(255*e.b/r)+","+r*t+")"}return e}var R=/^([^]*)\{(.*)\}([^]*)$/;function C(e,t){var r=void 0;do{if(r=e.match(R)){var n=t[r[2]]||"";e=r[1]+n+r[3]}}while(r);return e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.ERROR_THRESHOLD=.5,t.ENABLE_RASTER_REPROJECTION=!0},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=l(r(2)),o=r(58),a=l(r(12)),s=l(r(3));function l(e){return e&&e.__esModule?e:{default:e}}var u=function(e,t,r){a.default.call(this);var n=r||{};this.tileCoord=e,this.state=t,this.interimTile=null,this.key="",this.transition_=void 0===n.transition?250:n.transition,this.transitionStarts_={}};(0,n.inherits)(u,a.default),u.prototype.changed=function(){this.dispatchEvent(s.default.CHANGE)},u.prototype.getKey=function(){return this.key+"/"+this.tileCoord},u.prototype.getInterimTile=function(){if(!this.interimTile)return this;var e=this.interimTile;do{if(e.getState()==i.default.LOADED)return e;e=e.interimTile}while(e);return this},u.prototype.refreshInterimChain=function(){if(this.interimTile){var e=this.interimTile,t=this;do{if(e.getState()==i.default.LOADED){e.interimTile=null;break}e.getState()==i.default.LOADING?t=e:e.getState()==i.default.IDLE?t.interimTile=e.interimTile:t=e,e=t.interimTile}while(e)}},u.prototype.getTileCoord=function(){return this.tileCoord},u.prototype.getState=function(){return this.state},u.prototype.setState=function(e){this.state=e,this.changed()},u.prototype.load=function(){},u.prototype.getAlpha=function(e,t){if(!this.transition_)return 1;var r=this.transitionStarts_[e];if(r){if(-1===r)return 1}else r=t,this.transitionStarts_[e]=r;var n=t-r+1e3/60;return n>=this.transition_?1:(0,o.easeIn)(n/this.transition_)},u.prototype.inTransition=function(e){return!!this.transition_&&-1!==this.transitionStarts_[e]},u.prototype.endTransition=function(e){this.transition_&&(this.transitionStarts_[e]=-1)},t.default=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createCanvasContext2D=function(e,t){var r=document.createElement("CANVAS");e&&(r.width=e);t&&(r.height=t);return r.getContext("2d")},t.outerWidth=function(e){var t=e.offsetWidth,r=getComputedStyle(e);return t+=parseInt(r.marginLeft,10)+parseInt(r.marginRight,10)},t.outerHeight=function(e){var t=e.offsetHeight,r=getComputedStyle(e);return t+=parseInt(r.marginTop,10)+parseInt(r.marginBottom,10)},t.replaceNode=function(e,t){var r=t.parentNode;r&&r.replaceChild(e,t)},t.removeNode=function(e){return e&&e.parentNode?e.parentNode.removeChild(e):null},t.removeChildren=function(e){for(;e.lastChild;)e.removeChild(e.lastChild)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,i=r(0),o=r(60),a=(n=o)&&n.__esModule?n:{default:n},s=r(4);var l=function(e){a.default.call(this,e)};(0,i.inherits)(l,a.default),l.prototype.expireCache=function(e){for(;this.canExpireCache();){var t=this.peekLast(),r=t.tileCoord[0].toString();if(r in e&&e[r].contains(t.tileCoord))break;this.pop().dispose()}},l.prototype.pruneExceptNewestZ=function(){if(0!==this.getCount()){var e=this.peekFirstKey(),t=(0,s.fromKey)(e)[0];this.forEach((function(e){e.tileCoord[0]!==t&&(this.remove((0,s.getKey)(e.tileCoord)),e.dispose())}),this)}},t.default=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={BOTTOM_LEFT:"bottom-left",BOTTOM_RIGHT:"bottom-right",TOP_LEFT:"top-left",TOP_RIGHT:"top-right"}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.DEFAULT_MAX_ZOOM=42,t.DEFAULT_TILE_SIZE=256},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,i=r(28),o=r(20),a=(n=o)&&n.__esModule?n:{default:n};var s={apply:i.apply,applyBackground:i.applyBackground,applyStyle:i.applyStyle,stylefunction:a.default};window&&(window.olms=s),t.default=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.applyStyle=A,t.applyBackground=function(e,t){t.layers.some((function(t){if("background"==t.type)return R(e,t),!0}))},t.apply=function(e,t){var r,n,i,o;r=n=i=o="",e instanceof d.default||(e=new d.default({target:e}));if("string"==typeof t){var a=t.match(E);a&&(n=a[1],r=a.length>2?a[2]:""),fetch(t,{credentials:"same-origin"}).then((function(e){return e.json()})).then((function(a){var s=document.createElement("A");s.href=t,o=s.pathname.split("/").slice(0,-1).join("/")+"/",i=t.substr(0,t.indexOf(o)),M(a,e,n,i,o,r)})).catch((function(e){throw console.error(e),new Error("Could not load "+t)}))}else setTimeout((function(){M(t,e)}),0);return e},t.getLayer=function(e,t){for(var r=e.getLayers().getArray(),n=0,i=r.length;n<i;++n)if(-1!==r[n].get("mapbox-layers").indexOf(t))return r[n]},t.getSource=function(e,t){for(var r=e.getLayers().getArray(),n=0,i=r.length;n<i;++n){var o=r[n].getSource();if(-1!==r[n].get("mapbox-source").indexOf(t))return o}};var i,o=r(17),a=_(r(19)),s=r(20),l=_(s),u=_(r(41)),c=r(42),p=r(43),d=_(r(44)),h=_(r(45)),f=_(r(46)),y=r(47),m=_(r(48)),v=_(r(49)),g=_(r(50)),b=_(r(51)),x=_(r(52)),w=_(r(53)),k=_(r(54));function _(e){return e&&e.__esModule?e:{default:e}}var S=["Open Sans Regular","Arial Regular"];function T(e){"layout"in e&&"text-field"in e.layout&&function e(t){var r,n;if(Array.isArray(t)){var o=u.default.getNames(),s=t.map((function(e){return(0,a.default)(e,1).split(" 1px ")[1].replace(/"/g,"")}));for(r=0,n=s.length;r<n;++r){var l=s[r],c=t[r];if(-1!==o.indexOf(l)){if(i||(i=[]),-1==i.indexOf(c)){i.push(c);var p="https://fonts.googleapis.com/css?family="+l.replace(/ /g,"+");if(!document.querySelector('link[href="'+p+'"]')){var d=document.createElement("link");d.href=p,d.rel="stylesheet",document.getElementsByTagName("head")[0].appendChild(d)}}break}}}else{var h=t.stops;if(h)for(r=0,n=h.length;r<n;++r)e(h[r][1])}}(e.layout["text-font"]||S)}var E=/^(.*)(\?.*)$/;function j(e,t){return t&&0!=e.indexOf("http")&&(e=t+e),e}function O(e,t,r){var n=(e=j(e,t)).match(E);return n?n[1]+r+(n.length>2?n[2]:""):e+r}function A(e,t,r,o,a){return new Promise((function(s,u){var c,p,d;if("object"!=(void 0===t?"undefined":n(t))&&(t=JSON.parse(t)),8!=t.version&&u(new Error("glStyle version 8 required.")),t.sprite){var h=.5==(window.devicePixelRatio>=1.5?.5:1)?"@2x":"",f=O(t.sprite,o,h+".json");fetch(f,{credentials:"same-origin"}).then((function(e){return 200===e.status?e.json():""!==h?(h="",f=O(t.sprite,o,".json"),fetch(f,{credentials:"same-origin"}).then((function(e){return e.json()}))):void 0})).then((function(e){if(void 0===e)throw"No sprites found.";c=e,p=O(t.sprite,o,h+".png"),y()})).catch((function(e){console.error(e),u(new Error("Sprites cannot be loaded from "+f))}))}function y(){d||t.sprite&&!c||i&&!(i.length>0)?d&&e.setStyle(d):(d=(0,l.default)(e,t,r,a,c,p,i),s())}if(e instanceof g.default||e instanceof v.default)try{for(var m=t.layers,b=0,x=m.length;b<x;++b)("string"==typeof r&&m[b].source==r||r.indexOf(m[b].id)>=0)&&T(m[b]);y()}catch(e){setTimeout((function(){u(e)}),0)}}))}function R(e,t){var r={type:t.type};function n(){var n=e.getTargetElement();if(n){var i=t.layout||{},a=t.paint||{};r.paint=a,r.id="olms-bg-"+a["background-opacity"]+a["background-color"];var l=e.getView().getZoom();if(void 0!==a["background-color"]){var u=(0,s.getValue)(r,"paint","background-color",l,{});n.style.background=o.Color.parse(u).toString()}void 0!==a["background-opacity"]&&(n.style.opacity=(0,s.getValue)(r,"paint","background-opacity",l,{})),"none"==i.visibility&&(n.style.backgroundColor="",n.style.opacity="")}}e.getTargetElement()&&n(),e.on(["change:resolution","change:target"],n)}function C(e,t){var r;return e.some((function(e){if(e.id==t)return r=e.source,!0})),r}function M(e,t,r,n,i,o){var a=t.getView();"center"in e&&!a.getCenter()&&a.setCenter((0,c.fromLonLat)(e.center)),"zoom"in e&&void 0===a.getZoom()&&a.setZoom(e.zoom),a.getCenter()&&void 0!==a.getZoom()||a.fit(a.getProjection().getExtent(),{nearest:!0,size:t.getSize()}),e.sprite&&(0==e.sprite.indexOf("mapbox://")?e.sprite=r+"/sprite"+o:0!=e.sprite.indexOf("http")&&(e.sprite=(n?n+i:"")+e.sprite+o));var s,l,u,d,_,S,T,E,O=e.layers,M=new h.default,I=[];function P(r){if(I.length>0){t.addLayer(r);var n=function(){A(r,e,I,i).then((function(){r.setVisible(!0)}),(function(e){console.error(e)}))};r.getSource()?n():r.once("change:source",n)}}for(var z=0,L=O.length;z<L;++z)if("background"==(s=O[z]).type)R(t,s);else{if((d=s.source||C(O,s.ref))!=u){P(_),I=[],l=e.sources[d],T=l.url;var N=l.tiles;if(T&&0==T.indexOf("mapbox://")&&(S=T.replace("mapbox://",""),N=["a","b","c","d"].map((function(e){return"https://"+e+".tiles.mapbox.com/v4/"+S+"/{z}/{x}/{y}."+("vector"==l.type?"vector.pbf":"png")+o}))),"vector"==l.type)_=N?(E=void 0,E=(0,p.createXYZ)({tileSize:512,maxZoom:"maxzoom"in l?l.maxzoom:22,minZoom:l.minzoom}),new g.default({declutter:!0,maxResolution:E.getMinZoom()>0?E.getResolution(E.getMinZoom()):void 0,source:new w.default({attributions:l.attribution,format:new f.default,tileGrid:E,urls:N}),visible:!1,zIndex:z})):function(){var e=new g.default({declutter:!0,visible:!1,zIndex:z}),t=new b.default({url:T}),r=t.on("change",(function(){if("ready"==t.getState()){for(var n=t.getTileJSON(),i=Array.isArray(n.tiles)?n.tiles:[n.tiles],o=0,a=i.length;o<a;++o){var s=i[o];0!=s.indexOf("http")&&(i[o]=l.url+s)}var u=t.getTileGrid();e.setSource(new w.default({attributions:t.getAttributions()||n.attribution,format:new f.default,tileGrid:(0,p.createXYZ)({minZoom:u.getMinZoom(),maxZoom:u.getMaxZoom(),tileSize:512}),urls:i})),u.getMinZoom()>0&&e.setMaxResolution(u.getResolution(u.getMinZoom())),(0,y.unByKey)(r)}}));return e}();else if("raster"==l.type){var F;(F=l.tiles?new k.default({attributions:l.attribution,minZoom:l.minzoom,maxZoom:"maxzoom"in l?l.maxzoom:22,tileSize:l.tileSize||512,url:T,urls:l.tiles,crossOrigin:"anonymous"}):new b.default({url:T,crossOrigin:"anonymous"})).setTileLoadFunction((function(e,t){if(-1!=t.indexOf("{bbox-epsg-3857}")){var r=F.getTileGrid().getTileCoordExtent(e.getTileCoord());t=t.replace("{bbox-epsg-3857}",r.toString())}e.getImage().src=t})),_=new m.default({source:F,visible:!s.layout||"none"!==s.layout.visibility})}else if("geojson"==l.type){var D,U,q=l.data;"string"==typeof q?U=j(q,i):D=M.readFeatures(q,{featureProjection:"EPSG:3857"}),_=new v.default({source:new x.default({attributions:l.attribution,features:D,format:M,url:U}),visible:!1,zIndex:z})}u=d}I.push(s.id)}P(_),t.set("mapbox-style",e)}},function(e,t,r){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var n=r(30),i=r(31),o=r(32);function a(){return l.TYPED_ARRAY_SUPPORT?**********:**********}function s(e,t){if(a()<t)throw new RangeError("Invalid typed array length");return l.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=l.prototype:(null===e&&(e=new l(t)),e.length=t),e}function l(e,t,r){if(!(l.TYPED_ARRAY_SUPPORT||this instanceof l))return new l(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return p(this,e)}return u(this,e,t,r)}function u(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);l.TYPED_ARRAY_SUPPORT?(e=t).__proto__=l.prototype:e=d(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!l.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|f(t,r),i=(e=s(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(l.isBuffer(t)){var r=0|h(t.length);return 0===(e=s(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?s(e,0):d(e,t);if("Buffer"===t.type&&o(t.data))return d(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function c(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function p(e,t){if(c(t),e=s(e,t<0?0:0|h(t)),!l.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function d(e,t){var r=t.length<0?0:0|h(t.length);e=s(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function h(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function f(e,t){if(l.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return U(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return q(e).length;default:if(n)return U(e).length;t=(""+t).toLowerCase(),n=!0}}function y(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return A(this,t,r);case"utf8":case"utf-8":return E(this,t,r);case"ascii":return j(this,t,r);case"latin1":case"binary":return O(this,t,r);case"base64":return T(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function v(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=l.from(t,n)),l.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,i);if("number"==typeof t)return t&=255,l.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):g(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var o,a=1,s=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;a=2,s/=2,l/=2,r/=2}function u(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var c=-1;for(o=r;o<s;o++)if(u(e,o)===u(t,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===l)return c*a}else-1!==c&&(o-=o-c),c=-1}else for(r+l>s&&(r=s-l),o=r;o>=0;o--){for(var p=!0,d=0;d<l;d++)if(u(e,o+d)!==u(t,d)){p=!1;break}if(p)return o}return-1}function b(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var a=0;a<n;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[r+a]=s}return a}function x(e,t,r,n){return G(U(t,e.length-r),e,r,n)}function w(e,t,r,n){return G(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function k(e,t,r,n){return w(e,t,r,n)}function _(e,t,r,n){return G(q(t),e,r,n)}function S(e,t,r,n){return G(function(e,t){for(var r,n,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)r=e.charCodeAt(a),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function T(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function E(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,s,l,u=e[i],c=null,p=u>239?4:u>223?3:u>191?2:1;if(i+p<=r)switch(p){case 1:u<128&&(c=u);break;case 2:128==(192&(o=e[i+1]))&&(l=(31&u)<<6|63&o)>127&&(c=l);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(l=(15&u)<<12|(63&o)<<6|63&a)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(l=(15&u)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&l<1114112&&(c=l)}null===c?(c=65533,p=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=p}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}t.Buffer=l,t.SlowBuffer=function(e){+e!=e&&(e=0);return l.alloc(+e)},t.INSPECT_MAX_BYTES=50,l.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=a(),l.poolSize=8192,l._augment=function(e){return e.__proto__=l.prototype,e},l.from=function(e,t,r){return u(null,e,t,r)},l.TYPED_ARRAY_SUPPORT&&(l.prototype.__proto__=Uint8Array.prototype,l.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&l[Symbol.species]===l&&Object.defineProperty(l,Symbol.species,{value:null,configurable:!0})),l.alloc=function(e,t,r){return function(e,t,r,n){return c(t),t<=0?s(e,t):void 0!==r?"string"==typeof n?s(e,t).fill(r,n):s(e,t).fill(r):s(e,t)}(null,e,t,r)},l.allocUnsafe=function(e){return p(null,e)},l.allocUnsafeSlow=function(e){return p(null,e)},l.isBuffer=function(e){return!(null==e||!e._isBuffer)},l.compare=function(e,t){if(!l.isBuffer(e)||!l.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},l.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(e,t){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return l.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=l.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(!l.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},l.byteLength=f,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},l.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},l.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},l.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?E(this,0,e):y.apply(this,arguments)},l.prototype.equals=function(e){if(!l.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===l.compare(this,e)},l.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},l.prototype.compare=function(e,t,r,n,i){if(!l.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(t>>>=0),s=Math.min(o,a),u=this.slice(n,i),c=e.slice(t,r),p=0;p<s;++p)if(u[p]!==c[p]){o=u[p],a=c[p];break}return o<a?-1:a<o?1:0},l.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},l.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},l.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},l.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return b(this,e,t,r);case"utf8":case"utf-8":return x(this,e,t,r);case"ascii":return w(this,e,t,r);case"latin1":case"binary":return k(this,e,t,r);case"base64":return _(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function j(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function O(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function A(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=D(e[o]);return i}function R(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function C(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function M(e,t,r,n,i,o){if(!l.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function I(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function P(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function z(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function L(e,t,r,n,o){return o||z(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function N(e,t,r,n,o){return o||z(e,0,r,8),i.write(e,t,r,n,52,8),r+8}l.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),l.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=l.prototype;else{var i=t-e;r=new l(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},l.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},l.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},l.prototype.readUInt8=function(e,t){return t||C(e,1,this.length),this[e]},l.prototype.readUInt16LE=function(e,t){return t||C(e,2,this.length),this[e]|this[e+1]<<8},l.prototype.readUInt16BE=function(e,t){return t||C(e,2,this.length),this[e]<<8|this[e+1]},l.prototype.readUInt32LE=function(e,t){return t||C(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},l.prototype.readUInt32BE=function(e,t){return t||C(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},l.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},l.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},l.prototype.readInt8=function(e,t){return t||C(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},l.prototype.readInt16LE=function(e,t){t||C(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},l.prototype.readInt16BE=function(e,t){t||C(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},l.prototype.readInt32LE=function(e,t){return t||C(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},l.prototype.readInt32BE=function(e,t){return t||C(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},l.prototype.readFloatLE=function(e,t){return t||C(e,4,this.length),i.read(this,e,!0,23,4)},l.prototype.readFloatBE=function(e,t){return t||C(e,4,this.length),i.read(this,e,!1,23,4)},l.prototype.readDoubleLE=function(e,t){return t||C(e,8,this.length),i.read(this,e,!0,52,8)},l.prototype.readDoubleBE=function(e,t){return t||C(e,8,this.length),i.read(this,e,!1,52,8)},l.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||M(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},l.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||M(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},l.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,1,255,0),l.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},l.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,2,65535,0),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):I(this,e,t,!0),t+2},l.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,2,65535,0),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):I(this,e,t,!1),t+2},l.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,4,4294967295,0),l.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):P(this,e,t,!0),t+4},l.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,4,4294967295,0),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):P(this,e,t,!1),t+4},l.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);M(this,e,t,r,i-1,-i)}var o=0,a=1,s=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},l.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);M(this,e,t,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},l.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,1,127,-128),l.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},l.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,2,32767,-32768),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):I(this,e,t,!0),t+2},l.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,2,32767,-32768),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):I(this,e,t,!1),t+2},l.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,4,**********,-2147483648),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):P(this,e,t,!0),t+4},l.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||M(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):P(this,e,t,!1),t+4},l.prototype.writeFloatLE=function(e,t,r){return L(this,e,t,!0,r)},l.prototype.writeFloatBE=function(e,t,r){return L(this,e,t,!1,r)},l.prototype.writeDoubleLE=function(e,t,r){return N(this,e,t,!0,r)},l.prototype.writeDoubleBE=function(e,t,r){return N(this,e,t,!1,r)},l.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!l.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},l.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!l.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var a=l.isBuffer(e)?e:U(new l(e,n).toString()),s=a.length;for(o=0;o<r-t;++o)this[o+t]=a[o%s]}return this};var F=/[^+\/0-9A-Za-z-_]/g;function D(e){return e<16?"0"+e.toString(16):e.toString(16)}function U(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function q(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(F,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function G(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(this,r(18))},function(e,t,r){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){var t,r,n=u(e),a=n[0],s=n[1],l=new o(function(e,t,r){return 3*(t+r)/4-r}(0,a,s)),c=0,p=s>0?a-4:a;for(r=0;r<p;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],l[c++]=t>>16&255,l[c++]=t>>8&255,l[c++]=255&t;2===s&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,l[c++]=255&t);1===s&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,l[c++]=t>>8&255,l[c++]=255&t);return l},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],a=0,s=r-i;a<s;a+=16383)o.push(c(e,a,a+16383>s?s:a+16383));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,l=a.length;s<l;++s)n[s]=a[s],i[a.charCodeAt(s)]=s;function u(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function c(e,t,r){for(var i,o,a=[],s=t;s<r;s+=3)i=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),a.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(e,t,r){"use strict";
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */t.read=function(e,t,r,n,i){var o,a,s=8*i-n-1,l=(1<<s)-1,u=l>>1,c=-7,p=r?i-1:0,d=r?-1:1,h=e[t+p];for(p+=d,o=h&(1<<-c)-1,h>>=-c,c+=s;c>0;o=256*o+e[t+p],p+=d,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=n;c>0;a=256*a+e[t+p],p+=d,c-=8);if(0===o)o=1-u;else{if(o===l)return a?NaN:1/0*(h?-1:1);a+=Math.pow(2,n),o-=u}return(h?-1:1)*a*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var a,s,l,u=8*o-i-1,c=(1<<u)-1,p=c>>1,d=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:o-1,f=n?1:-1,y=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-a))<1&&(a--,l*=2),(t+=a+p>=1?d/l:d*Math.pow(2,1-p))*l>=2&&(a++,l/=2),a+p>=c?(s=0,a=c):a+p>=1?(s=(t*l-1)*Math.pow(2,i),a+=p):(s=t*Math.pow(2,p-1)*Math.pow(2,i),a=0));i>=8;e[r+h]=255&s,h+=f,s/=256,i-=8);for(a=a<<i|s,u+=i;u>0;e[r+h]=255&a,h+=f,a/=256,u-=8);e[r+h-f]|=128*y}},function(e,t,r){"use strict";var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(t,r){t.exports=e},function(e,r){e.exports=t},function(e,t){e.exports=r},function(e,t){e.exports=n},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t){e.exports=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.deg2rad=function(e){return e*Math.PI/180},t.getZoomForResolution=function(e,t){for(var r=0,n=t.length;r<n;++r){if(t[r]<e&&r+1<n){var i=t[r]/t[r+1];return r+Math.log(t[r]/e)/Math.log(i)}}return n-1}},function(e,t,r){"use strict";(function(){var t;e.exports={icon:"icons/google.svg",name:"google",title:"Google Fonts",link:"google.com/fonts",getNames:function(){return t},getLink:function(e){return"https://fonts.google.com/specimen/"+e.replace(/( )/g,"+")},normalizeName:function(e){return e}},t=["ABeeZee","Abel","Abril Fatface","Aclonica","Acme","Actor","Adamina","Advent Pro","Aguafina Script","Akronim","Aladin","Aldrich","Alef","Alegreya","Alegreya SC","Alegreya Sans","Alegreya Sans SC","Alex Brush","Alfa Slab One","Alice","Alike","Alike Angular","Allan","Allerta","Allerta Stencil","Allura","Almendra","Almendra Display","Almendra SC","Amarante","Amaranth","Amatic SC","Amethysta","Amiri","Amita","Anaheim","Andada","Andika","Angkor","Annie Use Your Telescope","Anonymous Pro","Antic","Antic Didone","Antic Slab","Anton","Arapey","Arbutus","Arbutus Slab","Architects Daughter","Archivo Black","Archivo Narrow","Arimo","Arizonia","Armata","Artifika","Arvo","Arya","Asap","Asar","Asset","Astloch","Asul","Atomic Age","Aubrey","Audiowide","Autour One","Average","Average Sans","Averia Gruesa Libre","Averia Libre","Averia Sans Libre","Averia Serif Libre","Bad Script","Balthazar","Bangers","Basic","Battambang","Baumans","Bayon","Belgrano","Belleza","BenchNine","Bentham","Berkshire Swash","Bevan","Bigelow Rules","Bigshot One","Bilbo","Bilbo Swash Caps","Biryani","Bitter","Black Ops One","Bokor","Bonbon","Boogaloo","Bowlby One","Bowlby One SC","Brawler","Bree Serif","Bubblegum Sans","Bubbler One","Buda","Buenard","Butcherman","Butterfly Kids","Cabin","Cabin Condensed","Cabin Sketch","Caesar Dressing","Cagliostro","Calligraffitti","Cambay","Cambo","Candal","Cantarell","Cantata One","Cantora One","Capriola","Cardo","Carme","Carrois Gothic","Carrois Gothic SC","Carter One","Catamaran","Caudex","Caveat","Caveat Brush","Cedarville Cursive","Ceviche One","Changa One","Chango","Chau Philomene One","Chela One","Chelsea Market","Chenla","Cherry Cream Soda","Cherry Swash","Chewy","Chicle","Chivo","Chonburi","Cinzel","Cinzel Decorative","Clicker Script","Coda","Coda Caption","Codystar","Combo","Comfortaa","Coming Soon","Concert One","Condiment","Content","Contrail One","Convergence","Cookie","Copse","Corben","Courgette","Cousine","Coustard","Covered By Your Grace","Crafty Girls","Creepster","Crete Round","Crimson Text","Croissant One","Crushed","Cuprum","Cutive","Cutive Mono","Damion","Dancing Script","Dangrek","Dawning of a New Day","Days One","Dekko","Delius","Delius Swash Caps","Delius Unicase","Della Respira","Denk One","Devonshire","Dhurjati","Didact Gothic","Diplomata","Diplomata SC","Domine","Donegal One","Doppio One","Dorsa","Dosis","Dr Sugiyama","Droid Sans","Droid Sans Mono","Droid Serif","Duru Sans","Dynalight","EB Garamond","Eagle Lake","Eater","Economica","Eczar","Ek Mukta","Electrolize","Elsie","Elsie Swash Caps","Emblema One","Emilys Candy","Engagement","Englebert","Enriqueta","Erica One","Esteban","Euphoria Script","Ewert","Exo","Exo 2","Expletus Sans","Fanwood Text","Fascinate","Fascinate Inline","Faster One","Fasthand","Fauna One","Federant","Federo","Felipa","Fenix","Finger Paint","Fira Mono","Fira Sans","Fjalla One","Fjord One","Flamenco","Flavors","Fondamento","Fontdiner Swanky","Forum","Francois One","Freckle Face","Fredericka the Great","Fredoka One","Freehand","Fresca","Frijole","Fruktur","Fugaz One","GFS Didot","GFS Neohellenic","Gabriela","Gafata","Galdeano","Galindo","Gentium Basic","Gentium Book Basic","Geo","Geostar","Geostar Fill","Germania One","Gidugu","Gilda Display","Give You Glory","Glass Antiqua","Glegoo","Gloria Hallelujah","Goblin One","Gochi Hand","Gorditas","Goudy Bookletter 1911","Graduate","Grand Hotel","Gravitas One","Great Vibes","Griffy","Gruppo","Gudea","Gurajada","Habibi","Halant","Hammersmith One","Hanalei","Hanalei Fill","Handlee","Hanuman","Happy Monkey","Headland One","Henny Penny","Herr Von Muellerhoff","Hind","Hind Siliguri","Hind Vadodara","Holtwood One SC","Homemade Apple","Homenaje","IM Fell DW Pica","IM Fell DW Pica SC","IM Fell Double Pica","IM Fell Double Pica SC","IM Fell English","IM Fell English SC","IM Fell French Canon","IM Fell French Canon SC","IM Fell Great Primer","IM Fell Great Primer SC","Iceberg","Iceland","Imprima","Inconsolata","Inder","Indie Flower","Inika","Inknut Antiqua","Irish Grover","Istok Web","Italiana","Italianno","Itim","Jacques Francois","Jacques Francois Shadow","Jaldi","Jim Nightshade","Jockey One","Jolly Lodger","Josefin Sans","Josefin Slab","Joti One","Judson","Julee","Julius Sans One","Junge","Jura","Just Another Hand","Just Me Again Down Here","Kadwa","Kalam","Kameron","Kantumruy","Karla","Karma","Kaushan Script","Kavoon","Kdam Thmor","Keania One","Kelly Slab","Kenia","Khand","Khmer","Khula","Kite One","Knewave","Kotta One","Koulen","Kranky","Kreon","Kristi","Krona One","Kurale","La Belle Aurore","Laila","Lakki Reddy","Lancelot","Lateef","Lato","League Script","Leckerli One","Ledger","Lekton","Lemon","Libre Baskerville","Life Savers","Lilita One","Lily Script One","Limelight","Linden Hill","Lobster","Lobster Two","Londrina Outline","Londrina Shadow","Londrina Sketch","Londrina Solid","Lora","Love Ya Like A Sister","Loved by the King","Lovers Quarrel","Luckiest Guy","Lusitana","Lustria","Macondo","Macondo Swash Caps","Magra","Maiden Orange","Mako","Mallanna","Mandali","Marcellus","Marcellus SC","Marck Script","Margarine","Marko One","Marmelad","Martel","Martel Sans","Marvel","Mate","Mate SC","Maven Pro","McLaren","Meddon","MedievalSharp","Medula One","Megrim","Meie Script","Merienda","Merienda One","Merriweather","Merriweather Sans","Metal","Metal Mania","Metamorphous","Metrophobic","Michroma","Milonga","Miltonian","Miltonian Tattoo","Miniver","Miss Fajardose","Modak","Modern Antiqua","Molengo","Molle","Monda","Monofett","Monoton","Monsieur La Doulaise","Montaga","Montez","Montserrat","Montserrat Alternates","Montserrat Subrayada","Moul","Moulpali","Mountains of Christmas","Mouse Memoirs","Mr Bedfort","Mr Dafoe","Mr De Haviland","Mrs Saint Delafield","Mrs Sheppards","Muli","Mystery Quest","NTR","Neucha","Neuton","New Rocker","News Cycle","Niconne","Nixie One","Nobile","Nokora","Norican","Nosifer","Nothing You Could Do","Noticia Text","Noto Sans","Noto Serif","Nova Cut","Nova Flat","Nova Mono","Nova Oval","Nova Round","Nova Script","Nova Slim","Nova Square","Numans","Nunito","Odor Mean Chey","Offside","Old Standard TT","Oldenburg","Oleo Script","Oleo Script Swash Caps","Open Sans","Open Sans Condensed","Oranienbaum","Orbitron","Oregano","Orienta","Original Surfer","Oswald","Over the Rainbow","Overlock","Overlock SC","Ovo","Oxygen","Oxygen Mono","PT Mono","PT Sans","PT Sans Caption","PT Sans Narrow","PT Serif","PT Serif Caption","Pacifico","Palanquin","Palanquin Dark","Paprika","Parisienne","Passero One","Passion One","Pathway Gothic One","Patrick Hand","Patrick Hand SC","Patua One","Paytone One","Peddana","Peralta","Permanent Marker","Petit Formal Script","Petrona","Philosopher","Piedra","Pinyon Script","Pirata One","Plaster","Play","Playball","Playfair Display","Playfair Display SC","Podkova","Poiret One","Poller One","Poly","Pompiere","Pontano Sans","Poppins","Port Lligat Sans","Port Lligat Slab","Pragati Narrow","Prata","Preahvihear","Press Start 2P","Princess Sofia","Prociono","Prosto One","Puritan","Purple Purse","Quando","Quantico","Quattrocento","Quattrocento Sans","Questrial","Quicksand","Quintessential","Qwigley","Racing Sans One","Radley","Rajdhani","Raleway","Raleway Dots","Ramabhadra","Ramaraja","Rambla","Rammetto One","Ranchers","Rancho","Ranga","Rationale","Ravi Prakash","Redressed","Reenie Beanie","Revalia","Rhodium Libre","Ribeye","Ribeye Marrow","Righteous","Risque","Roboto","Roboto Condensed","Roboto Mono","Roboto Slab","Rochester","Rock Salt","Rokkitt","Romanesco","Ropa Sans","Rosario","Rosarivo","Rouge Script","Rozha One","Rubik","Rubik Mono One","Rubik One","Ruda","Rufina","Ruge Boogie","Ruluko","Rum Raisin","Ruslan Display","Russo One","Ruthie","Rye","Sacramento","Sahitya","Sail","Salsa","Sanchez","Sancreek","Sansita One","Sarala","Sarina","Sarpanch","Satisfy","Scada","Scheherazade","Schoolbell","Seaweed Script","Sevillana","Seymour One","Shadows Into Light","Shadows Into Light Two","Shanti","Share","Share Tech","Share Tech Mono","Shojumaru","Short Stack","Siemreap","Sigmar One","Signika","Signika Negative","Simonetta","Sintony","Sirin Stencil","Six Caps","Skranji","Slabo 13px","Slabo 27px","Slackey","Smokum","Smythe","Sniglet","Snippet","Snowburst One","Sofadi One","Sofia","Sonsie One","Sorts Mill Goudy","Source Code Pro","Source Sans Pro","Source Serif Pro","Special Elite","Spicy Rice","Spinnaker","Spirax","Squada One","Sree Krushnadevaraya","Stalemate","Stalinist One","Stardos Stencil","Stint Ultra Condensed","Stint Ultra Expanded","Stoke","Strait","Sue Ellen Francisco","Sumana","Sunshiney","Supermercado One","Sura","Suranna","Suravaram","Suwannaphum","Swanky and Moo Moo","Syncopate","Tangerine","Taprom","Tauri","Teko","Telex","Tenali Ramakrishna","Tenor Sans","Text Me One","The Girl Next Door","Tienne","Tillana","Timmana","Tinos","Titan One","Titillium Web","Trade Winds","Trocchi","Trochut","Trykker","Tulpen One","Ubuntu","Ubuntu Condensed","Ubuntu Mono","Ultra","Uncial Antiqua","Underdog","Unica One","UnifrakturCook","UnifrakturMaguntia","Unkempt","Unlock","Unna","VT323","Vampiro One","Varela","Varela Round","Vast Shadow","Vesper Libre","Vibur","Vidaloka","Viga","Voces","Volkhov","Vollkorn","Voltaire","Waiting for the Sunrise","Wallpoet","Walter Turncoat","Warnes","Wellfleet","Wendy One","Wire One","Work Sans","Yanone Kaffeesatz","Yantramanav","Yellowtail","Yeseva One","Yesteryear","Zeyada"]}).call(void 0)},function(e,t){e.exports=s},function(e,t){e.exports=l},function(e,t){e.exports=u},function(e,t){e.exports=c},function(e,t){e.exports=p},function(e,t){e.exports=d},function(e,t){e.exports=h},function(e,t){e.exports=f},function(e,t){e.exports=y},function(e,t){e.exports=m},function(e,t){e.exports=v},function(e,t){e.exports=g},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,i=r(0),o=r(56),a=(n=o)&&n.__esModule?n:{default:n},s=r(16);var l=function(e){var t=e||{},r=void 0!==t.projection?t.projection:"EPSG:3857",n=void 0!==t.tileGrid?t.tileGrid:(0,s.createXYZ)({extent:(0,s.extentFromProjection)(r),maxZoom:t.maxZoom,minZoom:t.minZoom,tileSize:t.tileSize});a.default.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,opaque:t.opaque,projection:r,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileGrid:n,tileLoadFunction:t.tileLoadFunction,tilePixelRatio:t.tilePixelRatio,tileUrlFunction:t.tileUrlFunction,url:t.url,urls:t.urls,wrapX:void 0===t.wrapX||t.wrapX,transition:t.transition})};(0,i.inherits)(l,a.default),t.default=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getContext=function(e,t){for(var r=n.length,i=0;i<r;++i)try{var o=e.getContext(n[i],t);if(o)return o}catch(e){}return null};t.ONE=1,t.SRC_ALPHA=770,t.COLOR_ATTACHMENT0=36064,t.COLOR_BUFFER_BIT=16384,t.TRIANGLES=4,t.TRIANGLE_STRIP=5,t.ONE_MINUS_SRC_ALPHA=771,t.ARRAY_BUFFER=34962,t.ELEMENT_ARRAY_BUFFER=34963,t.STREAM_DRAW=35040,t.STATIC_DRAW=35044,t.DYNAMIC_DRAW=35048,t.CULL_FACE=2884,t.BLEND=3042,t.STENCIL_TEST=2960,t.DEPTH_TEST=2929,t.SCISSOR_TEST=3089,t.UNSIGNED_BYTE=5121,t.UNSIGNED_SHORT=5123,t.UNSIGNED_INT=5125,t.FLOAT=5126,t.RGBA=6408,t.FRAGMENT_SHADER=35632,t.VERTEX_SHADER=35633,t.LINK_STATUS=35714,t.LINEAR=9729,t.TEXTURE_MAG_FILTER=10240,t.TEXTURE_MIN_FILTER=10241,t.TEXTURE_WRAP_S=10242,t.TEXTURE_WRAP_T=10243,t.TEXTURE_2D=3553,t.TEXTURE0=33984,t.CLAMP_TO_EDGE=33071,t.COMPILE_STATUS=35713,t.FRAMEBUFFER=36160;var n=["experimental-webgl","webgl","webkit-3d","moz-webgl"]},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(21),i=r(0),o=y(r(57)),a=y(r(24)),s=y(r(2)),l=r(7),u=y(r(3)),c=r(5),p=y(r(69)),d=y(r(72)),h=r(4),f=r(16);function y(e){return e&&e.__esModule?e:{default:e}}var m=function(e){d.default.call(this,{attributions:e.attributions,cacheSize:e.cacheSize,extent:e.extent,opaque:e.opaque,projection:e.projection,state:e.state,tileGrid:e.tileGrid,tileLoadFunction:e.tileLoadFunction?e.tileLoadFunction:v,tilePixelRatio:e.tilePixelRatio,tileUrlFunction:e.tileUrlFunction,url:e.url,urls:e.urls,wrapX:e.wrapX,transition:e.transition}),this.crossOrigin=void 0!==e.crossOrigin?e.crossOrigin:null,this.tileClass=void 0!==e.tileClass?e.tileClass:o.default,this.tileCacheForProjection={},this.tileGridForProjection={},this.reprojectionErrorThreshold_=e.reprojectionErrorThreshold,this.renderReprojectionEdges_=!1};function v(e,t){e.getImage().src=t}(0,i.inherits)(m,d.default),m.prototype.canExpireCache=function(){if(!n.ENABLE_RASTER_REPROJECTION)return d.default.prototype.canExpireCache.call(this);if(this.tileCache.canExpireCache())return!0;for(var e in this.tileCacheForProjection)if(this.tileCacheForProjection[e].canExpireCache())return!0;return!1},m.prototype.expireCache=function(e,t){if(n.ENABLE_RASTER_REPROJECTION){var r=this.getTileCacheForProjection(e);for(var i in this.tileCache.expireCache(this.tileCache==r?t:{}),this.tileCacheForProjection){var o=this.tileCacheForProjection[i];o.expireCache(o==r?t:{})}}else d.default.prototype.expireCache.call(this,e,t)},m.prototype.getGutter=function(e){return n.ENABLE_RASTER_REPROJECTION&&this.getProjection()&&e&&!(0,c.equivalent)(this.getProjection(),e)?0:this.getGutterInternal()},m.prototype.getGutterInternal=function(){return 0},m.prototype.getOpaque=function(e){return!(n.ENABLE_RASTER_REPROJECTION&&this.getProjection()&&e&&!(0,c.equivalent)(this.getProjection(),e))&&d.default.prototype.getOpaque.call(this,e)},m.prototype.getTileGridForProjection=function(e){if(!n.ENABLE_RASTER_REPROJECTION)return d.default.prototype.getTileGridForProjection.call(this,e);var t=this.getProjection();if(!this.tileGrid||t&&!(0,c.equivalent)(t,e)){var r=(0,i.getUid)(e).toString();return r in this.tileGridForProjection||(this.tileGridForProjection[r]=(0,f.getForProjection)(e)),this.tileGridForProjection[r]}return this.tileGrid},m.prototype.getTileCacheForProjection=function(e){if(!n.ENABLE_RASTER_REPROJECTION)return d.default.prototype.getTileCacheForProjection.call(this,e);var t=this.getProjection();if(!t||(0,c.equivalent)(t,e))return this.tileCache;var r=(0,i.getUid)(e).toString();return r in this.tileCacheForProjection||(this.tileCacheForProjection[r]=new a.default(this.tileCache.highWaterMark)),this.tileCacheForProjection[r]},m.prototype.createTile_=function(e,t,r,n,i,o){var a=[e,t,r],c=this.getTileCoordForTileUrlFunction(a,i),p=c?this.tileUrlFunction(c,n,i):void 0,d=new this.tileClass(a,void 0!==p?s.default.IDLE:s.default.EMPTY,void 0!==p?p:"",this.crossOrigin,this.tileLoadFunction,this.tileOptions);return d.key=o,(0,l.listen)(d,u.default.CHANGE,this.handleTileChange,this),d},m.prototype.getTile=function(e,t,r,i,o){var a=this.getProjection();if(n.ENABLE_RASTER_REPROJECTION&&a&&o&&!(0,c.equivalent)(a,o)){var s=this.getTileCacheForProjection(o),l=[e,t,r],u=void 0,d=(0,h.getKey)(l);s.containsKey(d)&&(u=s.get(d));var f=this.getKey();if(u&&u.key==f)return u;var y=this.getTileGridForProjection(a),m=this.getTileGridForProjection(o),v=this.getTileCoordForTileUrlFunction(l,o),g=new p.default(a,y,o,m,l,v,this.getTilePixelRatio(i),this.getGutterInternal(),function(e,t,r,n){return this.getTileInternal(e,t,r,n,a)}.bind(this),this.reprojectionErrorThreshold_,this.renderReprojectionEdges_);return g.key=f,u?(g.interimTile=u,g.refreshInterimChain(),s.replace(d,g)):s.set(d,g),g}return this.getTileInternal(e,t,r,i,a||o)},m.prototype.getTileInternal=function(e,t,r,n,i){var o=null,a=(0,h.getKeyZXY)(e,t,r),l=this.getKey();if(this.tileCache.containsKey(a)){if((o=this.tileCache.get(a)).key!=l){var u=o;o=this.createTile_(e,t,r,n,i,l),u.getState()==s.default.IDLE?o.interimTile=u.interimTile:o.interimTile=u,o.refreshInterimChain(),this.tileCache.replace(a,o)}}else o=this.createTile_(e,t,r,n,i,l),this.tileCache.set(a,o);return o},m.prototype.setRenderReprojectionEdges=function(e){if(n.ENABLE_RASTER_REPROJECTION&&this.renderReprojectionEdges_!=e){for(var t in this.renderReprojectionEdges_=e,this.tileCacheForProjection)this.tileCacheForProjection[t].clear();this.changed()}},m.prototype.setTileGridForProjection=function(e,t){if(n.ENABLE_RASTER_REPROJECTION){var r=(0,c.get)(e);if(r){var o=(0,i.getUid)(r).toString();o in this.tileGridForProjection||(this.tileGridForProjection[o]=t)}}},t.default=m},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=u(r(22)),o=u(r(2)),a=r(23),s=r(7),l=u(r(3));function u(e){return e&&e.__esModule?e:{default:e}}var c=function(e,t,r,n,o,a){i.default.call(this,e,t,a),this.crossOrigin_=n,this.src_=r,this.image_=new Image,null!==n&&(this.image_.crossOrigin=n),this.imageListenerKeys_=null,this.tileLoadFunction_=o};function p(){var e=(0,a.createCanvasContext2D)(1,1);return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),e.canvas}(0,n.inherits)(c,i.default),c.prototype.disposeInternal=function(){this.state==o.default.LOADING&&(this.unlistenImage_(),this.image_=p()),this.interimTile&&this.interimTile.dispose(),this.state=o.default.ABORT,this.changed(),i.default.prototype.disposeInternal.call(this)},c.prototype.getImage=function(){return this.image_},c.prototype.getKey=function(){return this.src_},c.prototype.handleImageError_=function(){this.state=o.default.ERROR,this.unlistenImage_(),this.image_=p(),this.changed()},c.prototype.handleImageLoad_=function(){this.image_.naturalWidth&&this.image_.naturalHeight?this.state=o.default.LOADED:this.state=o.default.EMPTY,this.unlistenImage_(),this.changed()},c.prototype.load=function(){this.state==o.default.ERROR&&(this.state=o.default.IDLE,this.image_=new Image,null!==this.crossOrigin_&&(this.image_.crossOrigin=this.crossOrigin_)),this.state==o.default.IDLE&&(this.state=o.default.LOADING,this.changed(),this.imageListenerKeys_=[(0,s.listenOnce)(this.image_,l.default.ERROR,this.handleImageError_,this),(0,s.listenOnce)(this.image_,l.default.LOAD,this.handleImageLoad_,this)],this.tileLoadFunction_(this,this.src_))},c.prototype.unlistenImage_=function(){this.imageListenerKeys_.forEach(s.unlistenByKey),this.imageListenerKeys_=null},t.default=c},function(e,t,r){"use strict";function n(e){return Math.pow(e,3)}function i(e){return 3*e*e-2*e*e*e}Object.defineProperty(t,"__esModule",{value:!0}),t.easeIn=n,t.easeOut=function(e){return 1-n(1-e)},t.inAndOut=i,t.linear=function(e){return e},t.upAndDown=function(e){return e<.5?i(2*e):1-i(2*(e-.5))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(10),i=function(){};i.prototype.disposed_=!1,i.prototype.dispose=function(){this.disposed_||(this.disposed_=!0,this.disposeInternal())},i.prototype.disposeInternal=n.UNDEFINED,t.default=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=r(8),o=s(r(12)),a=s(r(3));function s(e){return e&&e.__esModule?e:{default:e}}var l=function(e){o.default.call(this),this.highWaterMark=void 0!==e?e:2048,this.count_=0,this.entries_={},this.oldest_=null,this.newest_=null};(0,n.inherits)(l,o.default),l.prototype.canExpireCache=function(){return this.getCount()>this.highWaterMark},l.prototype.clear=function(){this.count_=0,this.entries_={},this.oldest_=null,this.newest_=null,this.dispatchEvent(a.default.CLEAR)},l.prototype.containsKey=function(e){return this.entries_.hasOwnProperty(e)},l.prototype.forEach=function(e,t){for(var r=this.oldest_;r;)e.call(t,r.value_,r.key_,this),r=r.newer},l.prototype.get=function(e){var t=this.entries_[e];return(0,i.assert)(void 0!==t,15),t===this.newest_||(t===this.oldest_?(this.oldest_=this.oldest_.newer,this.oldest_.older=null):(t.newer.older=t.older,t.older.newer=t.newer),t.newer=null,t.older=this.newest_,this.newest_.newer=t,this.newest_=t),t.value_},l.prototype.remove=function(e){var t=this.entries_[e];return(0,i.assert)(void 0!==t,15),t===this.newest_?(this.newest_=t.older,this.newest_&&(this.newest_.newer=null)):t===this.oldest_?(this.oldest_=t.newer,this.oldest_&&(this.oldest_.older=null)):(t.newer.older=t.older,t.older.newer=t.newer),delete this.entries_[e],--this.count_,t.value_},l.prototype.getCount=function(){return this.count_},l.prototype.getKeys=function(){var e=new Array(this.count_),t=0,r=void 0;for(r=this.newest_;r;r=r.older)e[t++]=r.key_;return e},l.prototype.getValues=function(){var e=new Array(this.count_),t=0,r=void 0;for(r=this.newest_;r;r=r.older)e[t++]=r.value_;return e},l.prototype.peekLast=function(){return this.oldest_.value_},l.prototype.peekLastKey=function(){return this.oldest_.key_},l.prototype.peekFirstKey=function(){return this.newest_.key_},l.prototype.pop=function(){var e=this.oldest_;return delete this.entries_[e.key_],e.newer&&(e.newer.older=null),this.oldest_=e.newer,this.oldest_||(this.newest_=null),--this.count_,e.value_},l.prototype.replace=function(e,t){this.get(e),this.entries_[e].value_=t},l.prototype.set=function(e,t){(0,i.assert)(!(e in this.entries_),16);var r={key_:e,newer:null,older:this.newest_,value_:t};this.newest_?this.newest_.newer=r:this.oldest_=r,this.newest_=r,this.entries_[e]=r,++this.count_},l.prototype.setSize=function(e){this.highWaterMark=e},l.prototype.prune=function(){for(;this.canExpireCache();)this.pop()},t.default=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=function(e){var t=n.VERSION?n.VERSION.split("-")[0]:"latest";this.message="Assertion failed. See https://openlayers.org/en/"+t+"/doc/errors/#"+e+" for details.",this.code=e,this.name="AssertionError"};(0,n.inherits)(i,Error),t.default=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_RADIUS=void 0,t.getDistance=l,t.getLength=function e(t,r){var n=r||{},i=n.radius||s,o=n.projection||"EPSG:3857",l=t.getType();l!==a.default.GEOMETRY_COLLECTION&&(t=t.clone().transform(o,"EPSG:4326"));var c=0,p=void 0,d=void 0,h=void 0,f=void 0,y=void 0,m=void 0;switch(l){case a.default.POINT:case a.default.MULTI_POINT:break;case a.default.LINE_STRING:case a.default.LINEAR_RING:p=t.getCoordinates(),c=u(p,i);break;case a.default.MULTI_LINE_STRING:case a.default.POLYGON:for(p=t.getCoordinates(),h=0,f=p.length;h<f;++h)c+=u(p[h],i);break;case a.default.MULTI_POLYGON:for(p=t.getCoordinates(),h=0,f=p.length;h<f;++h)for(d=p[h],y=0,m=d.length;y<m;++y)c+=u(d[y],i);break;case a.default.GEOMETRY_COLLECTION:var v=t.getGeometries();for(h=0,f=v.length;h<f;++h)c+=e(v[h],r);break;default:throw new Error("Unsupported geometry type: "+l)}return c},t.getArea=function e(t,r){var n=r||{},i=n.radius||s,o=n.projection||"EPSG:3857",l=t.getType();l!==a.default.GEOMETRY_COLLECTION&&(t=t.clone().transform(o,"EPSG:4326"));var u=0,p=void 0,d=void 0,h=void 0,f=void 0,y=void 0,m=void 0;switch(l){case a.default.POINT:case a.default.MULTI_POINT:case a.default.LINE_STRING:case a.default.MULTI_LINE_STRING:case a.default.LINEAR_RING:break;case a.default.POLYGON:for(p=t.getCoordinates(),u=Math.abs(c(p[0],i)),h=1,f=p.length;h<f;++h)u-=Math.abs(c(p[h],i));break;case a.default.MULTI_POLYGON:for(p=t.getCoordinates(),h=0,f=p.length;h<f;++h)for(d=p[h],u+=Math.abs(c(d[0],i)),y=1,m=d.length;y<m;++y)u-=Math.abs(c(d[y],i));break;case a.default.GEOMETRY_COLLECTION:var v=t.getGeometries();for(h=0,f=v.length;h<f;++h)u+=e(v[h],r);break;default:throw new Error("Unsupported geometry type: "+l)}return u},t.offset=function(e,t,r,n){var o=n||s,a=(0,i.toRadians)(e[1]),l=(0,i.toRadians)(e[0]),u=t/o,c=Math.asin(Math.sin(a)*Math.cos(u)+Math.cos(a)*Math.sin(u)*Math.cos(r)),p=l+Math.atan2(Math.sin(r)*Math.sin(u)*Math.cos(a),Math.cos(u)-Math.sin(a)*Math.sin(c));return[(0,i.toDegrees)(p),(0,i.toDegrees)(c)]};var n,i=r(1),o=r(63),a=(n=o)&&n.__esModule?n:{default:n};
/**
 * @license
 * Latitude/longitude spherical geodesy formulae taken from
 * http://www.movable-type.co.uk/scripts/latlong.html
 * Licensed under CC-BY-3.0.
 */
var s=t.DEFAULT_RADIUS=6371008.8;function l(e,t,r){var n=r||s,o=(0,i.toRadians)(e[1]),a=(0,i.toRadians)(t[1]),l=(a-o)/2,u=(0,i.toRadians)(t[0]-e[0])/2,c=Math.sin(l)*Math.sin(l)+Math.sin(u)*Math.sin(u)*Math.cos(o)*Math.cos(a);return 2*n*Math.atan2(Math.sqrt(c),Math.sqrt(1-c))}function u(e,t){for(var r=0,n=0,i=e.length;n<i-1;++n)r+=l(e[n],e[n+1],t);return r}function c(e,t){for(var r=0,n=e.length,o=e[n-1][0],a=e[n-1][1],s=0;s<n;s++){var l=e[s][0],u=e[s][1];r+=(0,i.toRadians)(l-o)*(2+Math.sin((0,i.toRadians)(a))+Math.sin((0,i.toRadians)(u))),o=l,a=u}return r*t*t/2}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={POINT:"Point",LINE_STRING:"LineString",LINEAR_RING:"LinearRing",POLYGON:"Polygon",MULTI_POINT:"MultiPoint",MULTI_LINE_STRING:"MultiLineString",MULTI_POLYGON:"MultiPolygon",GEOMETRY_COLLECTION:"GeometryCollection",CIRCLE:"Circle"}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={UNKNOWN:0,INTERSECTING:1,ABOVE:2,RIGHT:4,BELOW:8,LEFT:16}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PROJECTIONS=t.WORLD_EXTENT=t.EXTENT=t.HALF_SIZE=t.RADIUS=void 0,t.fromEPSG4326=function(e,t,r){var n=e.length,i=r>1?r:2,o=t;void 0===o&&(o=i>2?e.slice():new Array(n));for(var a=u,s=0;s<n;s+=i){o[s]=a*e[s]/180;var c=l*Math.log(Math.tan(Math.PI*(e[s+1]+90)/360));c>a?c=a:c<-a&&(c=-a),o[s+1]=c}return o},t.toEPSG4326=function(e,t,r){var n=e.length,i=r>1?r:2,o=t;void 0===o&&(o=i>2?e.slice():new Array(n));for(var a=0;a<n;a+=i)o[a]=180*e[a]/u,o[a+1]=360*Math.atan(Math.exp(e[a+1]/l))/Math.PI-90;return o};var n=r(0),i=r(1),o=s(r(14)),a=s(r(9));function s(e){return e&&e.__esModule?e:{default:e}}var l=t.RADIUS=6378137,u=t.HALF_SIZE=Math.PI*l,c=t.EXTENT=[-u,-u,u,u],p=t.WORLD_EXTENT=[-180,-85,180,85];function d(e){o.default.call(this,{code:e,units:a.default.METERS,extent:c,global:!0,worldExtent:p,getPointResolution:function(e,t){return e/(0,i.cosh)(t[1]/l)}})}(0,n.inherits)(d,o.default);t.PROJECTIONS=[new d("EPSG:3857"),new d("EPSG:102100"),new d("EPSG:102113"),new d("EPSG:900913"),new d("urn:ogc:def:crs:EPSG:6.18:3:3857"),new d("urn:ogc:def:crs:EPSG::3857"),new d("http://www.opengis.net/gml/srs/epsg.xml#3857")]},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PROJECTIONS=t.METERS_PER_UNIT=t.EXTENT=t.RADIUS=void 0;var n=r(0),i=a(r(14)),o=a(r(9));function a(e){return e&&e.__esModule?e:{default:e}}var s=t.RADIUS=6378137,l=t.EXTENT=[-180,-90,180,90],u=t.METERS_PER_UNIT=Math.PI*s/180;function c(e,t){i.default.call(this,{code:e,units:o.default.DEGREES,extent:l,axisOrientation:t,global:!0,metersPerUnit:u,worldExtent:l})}(0,n.inherits)(c,i.default);t.PROJECTIONS=[new c("CRS:84"),new c("EPSG:4326","neu"),new c("urn:ogc:def:crs:EPSG::4326","neu"),new c("urn:ogc:def:crs:EPSG:6.6:4326","neu"),new c("urn:ogc:def:crs:OGC:1.3:CRS84"),new c("urn:ogc:def:crs:OGC:2:84"),new c("http://www.opengis.net/gml/srs/epsg.xml#4326","neu"),new c("urn:x-ogc:def:crs:EPSG:4326","neu")]},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clear=function(){n={}},t.get=function(e){return n[e]||null},t.add=function(e,t){n[e]=t};var n={}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clear=function(){i={}},t.add=function(e,t,r){var n=e.getCode(),o=t.getCode();n in i||(i[n]={});i[n][o]=r},t.remove=function(e,t){var r=e.getCode(),o=t.getCode(),a=i[r][o];delete i[r][o],(0,n.isEmpty)(i[r])&&delete i[r];return a},t.get=function(e,t){var r=void 0;e in i&&t in i[e]&&(r=i[e][t]);return r};var n=r(11),i={}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(21),i=r(0),o=h(r(22)),a=h(r(2)),s=r(7),l=h(r(3)),u=r(6),c=r(1),p=r(70),d=h(r(71));function h(e){return e&&e.__esModule?e:{default:e}}var f=function(e,t,r,i,s,l,h,f,y,m,v){o.default.call(this,s,a.default.IDLE),this.renderEdges_=void 0!==v&&v,this.pixelRatio_=h,this.gutter_=f,this.canvas_=null,this.sourceTileGrid_=t,this.targetTileGrid_=i,this.wrappedTileCoord_=l||s,this.sourceTiles_=[],this.sourcesListenerKeys_=null,this.sourceZ_=0;var g=i.getTileCoordExtent(this.wrappedTileCoord_),b=this.targetTileGrid_.getExtent(),x=this.sourceTileGrid_.getExtent(),w=b?(0,u.getIntersection)(g,b):g;if(0!==(0,u.getArea)(w)){var k=e.getExtent();k&&(x=x?(0,u.getIntersection)(x,k):k);var _=i.getResolution(this.wrappedTileCoord_[0]),S=(0,u.getCenter)(w),T=(0,p.calculateSourceResolution)(e,r,S,_);if(!isFinite(T)||T<=0)this.state=a.default.EMPTY;else{var E=void 0!==m?m:n.ERROR_THRESHOLD;if(this.triangulation_=new d.default(e,r,w,x,T*E),0!==this.triangulation_.getTriangles().length){this.sourceZ_=t.getZForResolution(T);var j=this.triangulation_.calculateSourceExtent();if(x&&(e.canWrapX()?(j[1]=(0,c.clamp)(j[1],x[1],x[3]),j[3]=(0,c.clamp)(j[3],x[1],x[3])):j=(0,u.getIntersection)(j,x)),(0,u.getArea)(j)){for(var O=t.getTileRangeForExtentAndZ(j,this.sourceZ_),A=O.minX;A<=O.maxX;A++)for(var R=O.minY;R<=O.maxY;R++){var C=y(this.sourceZ_,A,R,h);C&&this.sourceTiles_.push(C)}0===this.sourceTiles_.length&&(this.state=a.default.EMPTY)}else this.state=a.default.EMPTY}else this.state=a.default.EMPTY}}else this.state=a.default.EMPTY};(0,i.inherits)(f,o.default),f.prototype.disposeInternal=function(){this.state==a.default.LOADING&&this.unlistenSources_(),o.default.prototype.disposeInternal.call(this)},f.prototype.getImage=function(){return this.canvas_},f.prototype.reproject_=function(){var e=[];if(this.sourceTiles_.forEach(function(t,r,n){t&&t.getState()==a.default.LOADED&&e.push({extent:this.sourceTileGrid_.getTileCoordExtent(t.tileCoord),image:t.getImage()})}.bind(this)),this.sourceTiles_.length=0,0===e.length)this.state=a.default.ERROR;else{var t=this.wrappedTileCoord_[0],r=this.targetTileGrid_.getTileSize(t),n="number"==typeof r?r:r[0],i="number"==typeof r?r:r[1],o=this.targetTileGrid_.getResolution(t),s=this.sourceTileGrid_.getResolution(this.sourceZ_),l=this.targetTileGrid_.getTileCoordExtent(this.wrappedTileCoord_);this.canvas_=(0,p.render)(n,i,this.pixelRatio_,s,this.sourceTileGrid_.getExtent(),o,l,this.triangulation_,e,this.gutter_,this.renderEdges_),this.state=a.default.LOADED}this.changed()},f.prototype.load=function(){if(this.state==a.default.IDLE){this.state=a.default.LOADING,this.changed();var e=0;this.sourcesListenerKeys_=[],this.sourceTiles_.forEach(function(t,r,n){var i=t.getState();if(i==a.default.IDLE||i==a.default.LOADING){e++;var o=(0,s.listen)(t,l.default.CHANGE,(function(r){var n=t.getState();n!=a.default.LOADED&&n!=a.default.ERROR&&n!=a.default.EMPTY||((0,s.unlistenByKey)(o),0===--e&&(this.unlistenSources_(),this.reproject_()))}),this);this.sourcesListenerKeys_.push(o)}}.bind(this)),this.sourceTiles_.forEach((function(e,t,r){e.getState()==a.default.IDLE&&e.load()})),0===e&&setTimeout(this.reproject_.bind(this),0)}},f.prototype.unlistenSources_=function(){this.sourcesListenerKeys_.forEach(s.unlistenByKey),this.sourcesListenerKeys_=null},t.default=f},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.calculateSourceResolution=function(e,t,r,n){var o=(0,a.transform)(r,t,e),s=(0,a.getPointResolution)(t,n,r),l=t.getMetersPerUnit();void 0!==l&&(s*=l);var u=e.getMetersPerUnit();void 0!==u&&(s/=u);var c=e.getExtent();if(!c||(0,i.containsCoordinate)(c,o)){var p=(0,a.getPointResolution)(e,s,o)/s;isFinite(p)&&p>0&&(s/=p)}return s},t.render=function(e,t,r,a,l,u,c,p,d,h,f){var y=(0,n.createCanvasContext2D)(Math.round(r*e),Math.round(r*t));if(0===d.length)return y.canvas;y.scale(r,r);var m=(0,i.createEmpty)();d.forEach((function(e,t,r){(0,i.extend)(m,e.extent)}));var v=(0,i.getWidth)(m),g=(0,i.getHeight)(m),b=(0,n.createCanvasContext2D)(Math.round(r*v/a),Math.round(r*g/a)),x=r/a;d.forEach((function(e,t,r){var n=e.extent[0]-m[0],o=-(e.extent[3]-m[3]),a=(0,i.getWidth)(e.extent),s=(0,i.getHeight)(e.extent);b.drawImage(e.image,h,h,e.image.width-2*h,e.image.height-2*h,n*x,o*x,a*x,s*x)}));var w=(0,i.getTopLeft)(c);p.getTriangles().forEach((function(e,t,n){var i=e.source,l=e.target,c=i[0][0],p=i[0][1],d=i[1][0],h=i[1][1],f=i[2][0],v=i[2][1],g=(l[0][0]-w[0])/u,x=-(l[0][1]-w[1])/u,k=(l[1][0]-w[0])/u,_=-(l[1][1]-w[1])/u,S=(l[2][0]-w[0])/u,T=-(l[2][1]-w[1])/u,E=c,j=p;c=0,p=0;var O=[[d-=E,h-=j,0,0,k-g],[f-=E,v-=j,0,0,S-g],[0,0,d,h,_-x],[0,0,f,v,T-x]],A=(0,o.solveLinearSystem)(O);if(A){y.save(),y.beginPath();var R=(g+k+S)/3,C=(x+_+T)/3,M=s(R,C,g,x),I=s(R,C,k,_),P=s(R,C,S,T);y.moveTo(I[0],I[1]),y.lineTo(M[0],M[1]),y.lineTo(P[0],P[1]),y.clip(),y.transform(A[0],A[2],A[1],A[3],g,x),y.translate(m[0]-E,m[3]-j),y.scale(a/r,-a/r),y.drawImage(b.canvas,0,0),y.restore()}})),f&&(y.save(),y.strokeStyle="black",y.lineWidth=1,p.getTriangles().forEach((function(e,t,r){var n=e.target,i=(n[0][0]-w[0])/u,o=-(n[0][1]-w[1])/u,a=(n[1][0]-w[0])/u,s=-(n[1][1]-w[1])/u,l=(n[2][0]-w[0])/u,c=-(n[2][1]-w[1])/u;y.beginPath(),y.moveTo(a,s),y.lineTo(i,o),y.lineTo(l,c),y.closePath(),y.stroke()})),y.restore());return y.canvas};var n=r(23),i=r(6),o=r(1),a=r(5);function s(e,t,r,n){var i=r-e,o=n-t,a=Math.sqrt(i*i+o*o);return[Math.round(r+i/a),Math.round(n+o/a)]}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(6),i=r(1),o=r(5),a=function(e,t,r,i,a){this.sourceProj_=e,this.targetProj_=t;var s={},l=(0,o.getTransform)(this.targetProj_,this.sourceProj_);this.transformInv_=function(e){var t=e[0]+"/"+e[1];return s[t]||(s[t]=l(e)),s[t]},this.maxSourceExtent_=i,this.errorThresholdSquared_=a*a,this.triangles_=[],this.wrapsXInSource_=!1,this.canWrapXInSource_=this.sourceProj_.canWrapX()&&!!i&&!!this.sourceProj_.getExtent()&&(0,n.getWidth)(i)==(0,n.getWidth)(this.sourceProj_.getExtent()),this.sourceWorldWidth_=this.sourceProj_.getExtent()?(0,n.getWidth)(this.sourceProj_.getExtent()):null,this.targetWorldWidth_=this.targetProj_.getExtent()?(0,n.getWidth)(this.targetProj_.getExtent()):null;var u=(0,n.getTopLeft)(r),c=(0,n.getTopRight)(r),p=(0,n.getBottomRight)(r),d=(0,n.getBottomLeft)(r),h=this.transformInv_(u),f=this.transformInv_(c),y=this.transformInv_(p),m=this.transformInv_(d);if(this.addQuad_(u,c,p,d,h,f,y,m,10),this.wrapsXInSource_){var v=1/0;this.triangles_.forEach((function(e,t,r){v=Math.min(v,e.source[0][0],e.source[1][0],e.source[2][0])})),this.triangles_.forEach(function(e){if(Math.max(e.source[0][0],e.source[1][0],e.source[2][0])-v>this.sourceWorldWidth_/2){var t=[[e.source[0][0],e.source[0][1]],[e.source[1][0],e.source[1][1]],[e.source[2][0],e.source[2][1]]];t[0][0]-v>this.sourceWorldWidth_/2&&(t[0][0]-=this.sourceWorldWidth_),t[1][0]-v>this.sourceWorldWidth_/2&&(t[1][0]-=this.sourceWorldWidth_),t[2][0]-v>this.sourceWorldWidth_/2&&(t[2][0]-=this.sourceWorldWidth_);var r=Math.min(t[0][0],t[1][0],t[2][0]);Math.max(t[0][0],t[1][0],t[2][0])-r<this.sourceWorldWidth_/2&&(e.source=t)}}.bind(this))}s={}};a.prototype.addTriangle_=function(e,t,r,n,i,o){this.triangles_.push({source:[n,i,o],target:[e,t,r]})},a.prototype.addQuad_=function(e,t,r,o,a,s,l,u,c){var p=(0,n.boundingExtent)([a,s,l,u]),d=this.sourceWorldWidth_?(0,n.getWidth)(p)/this.sourceWorldWidth_:null,h=this.sourceWorldWidth_,f=this.sourceProj_.canWrapX()&&d>.5&&d<1,y=!1;if(c>0){if(this.targetProj_.isGlobal()&&this.targetWorldWidth_){var m=(0,n.boundingExtent)([e,t,r,o]);y|=(0,n.getWidth)(m)/this.targetWorldWidth_>.25}!f&&this.sourceProj_.isGlobal()&&d&&(y|=d>.25)}if(y||!this.maxSourceExtent_||(0,n.intersects)(p,this.maxSourceExtent_)){if(!(y||isFinite(a[0])&&isFinite(a[1])&&isFinite(s[0])&&isFinite(s[1])&&isFinite(l[0])&&isFinite(l[1])&&isFinite(u[0])&&isFinite(u[1]))){if(!(c>0))return;y=!0}if(c>0){if(!y){var v=[(e[0]+r[0])/2,(e[1]+r[1])/2],g=this.transformInv_(v),b=void 0;if(f)b=((0,i.modulo)(a[0],h)+(0,i.modulo)(l[0],h))/2-(0,i.modulo)(g[0],h);else b=(a[0]+l[0])/2-g[0];var x=(a[1]+l[1])/2-g[1];y=b*b+x*x>this.errorThresholdSquared_}if(y){if(Math.abs(e[0]-r[0])<=Math.abs(e[1]-r[1])){var w=[(t[0]+r[0])/2,(t[1]+r[1])/2],k=this.transformInv_(w),_=[(o[0]+e[0])/2,(o[1]+e[1])/2],S=this.transformInv_(_);this.addQuad_(e,t,w,_,a,s,k,S,c-1),this.addQuad_(_,w,r,o,S,k,l,u,c-1)}else{var T=[(e[0]+t[0])/2,(e[1]+t[1])/2],E=this.transformInv_(T),j=[(r[0]+o[0])/2,(r[1]+o[1])/2],O=this.transformInv_(j);this.addQuad_(e,T,j,o,a,E,O,u,c-1),this.addQuad_(T,t,r,j,E,s,l,O,c-1)}return}}if(f){if(!this.canWrapXInSource_)return;this.wrapsXInSource_=!0}this.addTriangle_(e,r,o,a,l,u),this.addTriangle_(e,t,r,a,s,l)}},a.prototype.calculateSourceExtent=function(){var e=(0,n.createEmpty)();return this.triangles_.forEach((function(t,r,i){var o=t.source;(0,n.extendCoordinate)(e,o[0]),(0,n.extendCoordinate)(e,o[1]),(0,n.extendCoordinate)(e,o[2])})),e},a.prototype.getTriangles=function(){return this.triangles_},t.default=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=c(r(2)),o=r(73),a=r(74),s=c(a),l=c(r(83)),u=r(4);function c(e){return e&&e.__esModule?e:{default:e}}var p=function(e){s.default.call(this,{attributions:e.attributions,cacheSize:e.cacheSize,extent:e.extent,opaque:e.opaque,projection:e.projection,state:e.state,tileGrid:e.tileGrid,tilePixelRatio:e.tilePixelRatio,wrapX:e.wrapX,transition:e.transition}),this.tileLoadFunction=e.tileLoadFunction,this.tileUrlFunction=this.fixedTileUrlFunction?this.fixedTileUrlFunction.bind(this):o.nullTileUrlFunction,this.urls=null,e.urls?this.setUrls(e.urls):e.url&&this.setUrl(e.url),e.tileUrlFunction&&this.setTileUrlFunction(e.tileUrlFunction),this.tileLoadingKeys_={}};(0,n.inherits)(p,s.default),p.prototype.fixedTileUrlFunction,p.prototype.getTileLoadFunction=function(){return this.tileLoadFunction},p.prototype.getTileUrlFunction=function(){return this.tileUrlFunction},p.prototype.getUrls=function(){return this.urls},p.prototype.handleTileChange=function(e){var t=e.target,r=(0,n.getUid)(t),o=t.getState(),s=void 0;o==i.default.LOADING?(this.tileLoadingKeys_[r]=!0,s=l.default.TILELOADSTART):r in this.tileLoadingKeys_&&(delete this.tileLoadingKeys_[r],s=o==i.default.ERROR?l.default.TILELOADERROR:o==i.default.LOADED||o==i.default.ABORT?l.default.TILELOADEND:void 0),null!=s&&this.dispatchEvent(new a.TileSourceEvent(s,t))},p.prototype.setTileLoadFunction=function(e){this.tileCache.clear(),this.tileLoadFunction=e,this.changed()},p.prototype.setTileUrlFunction=function(e,t){this.tileUrlFunction=e,this.tileCache.pruneExceptNewestZ(),void 0!==t?this.setKey(t):this.changed()},p.prototype.setUrl=function(e){var t=this.urls=(0,o.expandUrl)(e);this.setTileUrlFunction(this.fixedTileUrlFunction?this.fixedTileUrlFunction.bind(this):(0,o.createFromTemplates)(t,this.tileGrid),e)},p.prototype.setUrls=function(e){this.urls=e;var t=e.join("\n");this.setTileUrlFunction(this.fixedTileUrlFunction?this.fixedTileUrlFunction.bind(this):(0,o.createFromTemplates)(e,this.tileGrid),t)},p.prototype.useTile=function(e,t,r){var n=(0,u.getKeyZXY)(e,t,r);this.tileCache.containsKey(n)&&this.tileCache.get(n)},t.default=p},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createFromTemplate=a,t.createFromTemplates=function(e,t){for(var r=e.length,n=new Array(r),i=0;i<r;++i)n[i]=a(e[i],t);return s(n)},t.createFromTileUrlFunctions=s,t.nullTileUrlFunction=function(e,t,r){return},t.expandUrl=function(e){var t=[],r=/\{([a-z])-([a-z])\}/.exec(e);if(r){var n=r[1].charCodeAt(0),i=r[2].charCodeAt(0),o=void 0;for(o=n;o<=i;++o)t.push(e.replace(r[0],String.fromCharCode(o)));return t}if(r=r=/\{(\d+)-(\d+)\}/.exec(e)){for(var a=parseInt(r[2],10),s=parseInt(r[1],10);s<=a;s++)t.push(e.replace(r[0],s.toString()));return t}return t.push(e),t};var n=r(8),i=r(1),o=r(4);function a(e,t){var r=/\{z\}/g,i=/\{x\}/g,o=/\{y\}/g,a=/\{-y\}/g;return function(s,l,u){return s?e.replace(r,s[0].toString()).replace(i,s[1].toString()).replace(o,(function(){return(-s[2]-1).toString()})).replace(a,(function(){var e=s[0],r=t.getFullTileRange(e);return(0,n.assert)(r,55),(r.getHeight()+s[2]).toString()})):void 0}}function s(e){return 1===e.length?e[0]:function(t,r,n){if(t){var a=(0,o.hash)(t),s=(0,i.modulo)(a,e.length);return e[s](t,r,n)}}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TileSourceEvent=void 0;var n=r(0),i=r(10),o=h(r(24)),a=h(r(2)),s=h(r(13)),l=r(5),u=r(15),c=h(r(75)),p=r(4),d=r(16);function h(e){return e&&e.__esModule?e:{default:e}}var f=function(e){c.default.call(this,{attributions:e.attributions,extent:e.extent,projection:e.projection,state:e.state,wrapX:e.wrapX}),this.opaque_=void 0!==e.opaque&&e.opaque,this.tilePixelRatio_=void 0!==e.tilePixelRatio?e.tilePixelRatio:1,this.tileGrid=void 0!==e.tileGrid?e.tileGrid:null,this.tileCache=new o.default(e.cacheSize),this.tmpSize=[0,0],this.key_="",this.tileOptions={transition:e.transition}};(0,n.inherits)(f,c.default),f.prototype.canExpireCache=function(){return this.tileCache.canExpireCache()},f.prototype.expireCache=function(e,t){var r=this.getTileCacheForProjection(e);r&&r.expireCache(t)},f.prototype.forEachLoadedTile=function(e,t,r,n){var i=this.getTileCacheForProjection(e);if(!i)return!1;for(var o=!0,s=void 0,l=void 0,u=void 0,c=r.minX;c<=r.maxX;++c)for(var d=r.minY;d<=r.maxY;++d)l=(0,p.getKeyZXY)(t,c,d),u=!1,i.containsKey(l)&&(u=(s=i.get(l)).getState()===a.default.LOADED)&&(u=!1!==n(s)),u||(o=!1);return o},f.prototype.getGutter=function(e){return 0},f.prototype.getKey=function(){return this.key_},f.prototype.setKey=function(e){this.key_!==e&&(this.key_=e,this.changed())},f.prototype.getOpaque=function(e){return this.opaque_},f.prototype.getResolutions=function(){return this.tileGrid.getResolutions()},f.prototype.getTile=function(e,t,r,n,i){},f.prototype.getTileGrid=function(){return this.tileGrid},f.prototype.getTileGridForProjection=function(e){return this.tileGrid?this.tileGrid:(0,d.getForProjection)(e)},f.prototype.getTileCacheForProjection=function(e){var t=this.getProjection();return t&&!(0,l.equivalent)(t,e)?null:this.tileCache},f.prototype.getTilePixelRatio=function(e){return this.tilePixelRatio_},f.prototype.getTilePixelSize=function(e,t,r){var n=this.getTileGridForProjection(r),i=this.getTilePixelRatio(t),o=(0,u.toSize)(n.getTileSize(e),this.tmpSize);return 1==i?o:(0,u.scale)(o,i,this.tmpSize)},f.prototype.getTileCoordForTileUrlFunction=function(e,t){var r=void 0!==t?t:this.getProjection(),n=this.getTileGridForProjection(r);return this.getWrapX()&&r.isGlobal()&&(e=(0,d.wrapX)(n,e,r)),(0,p.withinExtentAndZ)(e,n)?e:null},f.prototype.refresh=function(){this.tileCache.clear(),this.changed()},f.prototype.useTile=i.UNDEFINED;var y=t.TileSourceEvent=function(e,t){s.default.call(this,e),this.tile=t};(0,n.inherits)(y,s.default),t.default=f},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=r(10),o=l(r(76)),a=r(5),s=l(r(79));function l(e){return e&&e.__esModule?e:{default:e}}var u=function(e){o.default.call(this),this.projection_=(0,a.get)(e.projection),this.attributions_=this.adaptAttributions_(e.attributions),this.state_=void 0!==e.state?e.state:s.default.READY,this.wrapX_=void 0!==e.wrapX&&e.wrapX};(0,n.inherits)(u,o.default),u.prototype.adaptAttributions_=function(e){return e?Array.isArray(e)?function(t){return e}:"function"==typeof e?e:function(t){return[e]}:null},u.prototype.forEachFeatureAtCoordinate=i.UNDEFINED,u.prototype.getAttributions=function(){return this.attributions_},u.prototype.getProjection=function(){return this.projection_},u.prototype.getResolutions=function(){},u.prototype.getState=function(){return this.state_},u.prototype.getWrapX=function(){return this.wrapX_},u.prototype.refresh=function(){this.changed()},u.prototype.setAttributions=function(e){this.attributions_=this.adaptAttributions_(e),this.changed()},u.prototype.setState=function(e){this.state_=e,this.changed()},t.default=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getChangeEventType=d;var n=r(0),i=l(r(77)),o=l(r(78)),a=l(r(13)),s=r(11);function l(e){return e&&e.__esModule?e:{default:e}}var u=function(e,t,r){a.default.call(this,e),this.key=t,this.oldValue=r};(0,n.inherits)(u,a.default);var c=function(e){o.default.call(this),(0,n.getUid)(this),this.values_={},void 0!==e&&this.setProperties(e)};(0,n.inherits)(c,o.default);var p={};function d(e){return p.hasOwnProperty(e)?p[e]:p[e]="change:"+e}c.prototype.get=function(e){var t=void 0;return this.values_.hasOwnProperty(e)&&(t=this.values_[e]),t},c.prototype.getKeys=function(){return Object.keys(this.values_)},c.prototype.getProperties=function(){return(0,s.assign)({},this.values_)},c.prototype.notify=function(e,t){var r=void 0;r=d(e),this.dispatchEvent(new u(r,e,t)),r=i.default.PROPERTYCHANGE,this.dispatchEvent(new u(r,e,t))},c.prototype.set=function(e,t,r){if(r)this.values_[e]=t;else{var n=this.values_[e];this.values_[e]=t,n!==t&&this.notify(e,n)}},c.prototype.setProperties=function(e,t){for(var r in e)this.set(r,e[r],t)},c.prototype.unset=function(e,t){if(e in this.values_){var r=this.values_[e];delete this.values_[e],t||this.notify(e,r)}},t.default=c},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={PROPERTYCHANGE:"propertychange"}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unByKey=function(e){if(Array.isArray(e))for(var t=0,r=e.length;t<r;++t)(0,i.unlistenByKey)(e[t]);else(0,i.unlistenByKey)(e)};var n=r(0),i=r(7),o=s(r(12)),a=s(r(3));function s(e){return e&&e.__esModule?e:{default:e}}var l=function(){o.default.call(this),this.revision_=0};(0,n.inherits)(l,o.default),l.prototype.changed=function(){++this.revision_,this.dispatchEvent(a.default.CHANGE)},l.prototype.dispatchEvent,l.prototype.getRevision=function(){return this.revision_},l.prototype.on=function(e,t){if(Array.isArray(e)){for(var r=e.length,n=new Array(r),o=0;o<r;++o)n[o]=(0,i.listen)(this,e[o],t);return n}return(0,i.listen)(this,e,t)},l.prototype.once=function(e,t){if(Array.isArray(e)){for(var r=e.length,n=new Array(r),o=0;o<r;++o)n[o]=(0,i.listenOnce)(this,e[o],t);return n}return(0,i.listenOnce)(this,e,t)},l.prototype.un=function(e,t){if(Array.isArray(e))for(var r=0,n=e.length;r<n;++r)(0,i.unlisten)(this,e[r],t);else(0,i.unlisten)(this,e,t)},t.default=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={UNDEFINED:"undefined",LOADING:"loading",READY:"ready",ERROR:"error"}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,i=r(26),o=r(8),a=r(81),s=(n=a)&&n.__esModule?n:{default:n},l=r(82),u=r(6),c=r(1),p=r(15),d=r(4);var h=function(e){this.minZoom=void 0!==e.minZoom?e.minZoom:0,this.resolutions_=e.resolutions,(0,o.assert)((0,l.isSorted)(this.resolutions_,(function(e,t){return t-e}),!0),17);var t=void 0;if(!e.origins)for(var r=0,n=this.resolutions_.length-1;r<n;++r)if(t){if(this.resolutions_[r]/this.resolutions_[r+1]!==t){t=void 0;break}}else t=this.resolutions_[r]/this.resolutions_[r+1];this.zoomFactor_=t,this.maxZoom=this.resolutions_.length-1,this.origin_=void 0!==e.origin?e.origin:null,this.origins_=null,void 0!==e.origins&&(this.origins_=e.origins,(0,o.assert)(this.origins_.length==this.resolutions_.length,20));var a=e.extent;void 0===a||this.origin_||this.origins_||(this.origin_=(0,u.getTopLeft)(a)),(0,o.assert)(!this.origin_&&this.origins_||this.origin_&&!this.origins_,18),this.tileSizes_=null,void 0!==e.tileSizes&&(this.tileSizes_=e.tileSizes,(0,o.assert)(this.tileSizes_.length==this.resolutions_.length,19)),this.tileSize_=void 0!==e.tileSize?e.tileSize:this.tileSizes_?null:i.DEFAULT_TILE_SIZE,(0,o.assert)(!this.tileSize_&&this.tileSizes_||this.tileSize_&&!this.tileSizes_,22),this.extent_=void 0!==a?a:null,this.fullTileRanges_=null,this.tmpSize_=[0,0],void 0!==e.sizes?this.fullTileRanges_=e.sizes.map((function(e,t){return new s.default(Math.min(0,e[0]),Math.max(e[0]-1,-1),Math.min(0,e[1]),Math.max(e[1]-1,-1))}),this):a&&this.calculateTileRanges_(a)},f=[0,0,0];h.prototype.forEachTileCoord=function(e,t,r){for(var n=this.getTileRangeForExtentAndZ(e,t),i=n.minX,o=n.maxX;i<=o;++i)for(var a=n.minY,s=n.maxY;a<=s;++a)r([t,i,a])},h.prototype.forEachTileCoordParentTileRange=function(e,t,r,n,i){var o=void 0,s=void 0,l=void 0,u=null,c=e[0]-1;for(2===this.zoomFactor_?(s=e[1],l=e[2]):u=this.getTileCoordExtent(e,i);c>=this.minZoom;){if(2===this.zoomFactor_?(s=Math.floor(s/2),l=Math.floor(l/2),o=(0,a.createOrUpdate)(s,s,l,l,n)):o=this.getTileRangeForExtentAndZ(u,c,n),t.call(r,c,o))return!0;--c}return!1},h.prototype.getExtent=function(){return this.extent_},h.prototype.getMaxZoom=function(){return this.maxZoom},h.prototype.getMinZoom=function(){return this.minZoom},h.prototype.getOrigin=function(e){return this.origin_?this.origin_:this.origins_[e]},h.prototype.getResolution=function(e){return this.resolutions_[e]},h.prototype.getResolutions=function(){return this.resolutions_},h.prototype.getTileCoordChildTileRange=function(e,t,r){if(e[0]<this.maxZoom){if(2===this.zoomFactor_){var n=2*e[1],i=2*e[2];return(0,a.createOrUpdate)(n,n+1,i,i+1,t)}var o=this.getTileCoordExtent(e,r);return this.getTileRangeForExtentAndZ(o,e[0]+1,t)}return null},h.prototype.getTileRangeExtent=function(e,t,r){var n=this.getOrigin(e),i=this.getResolution(e),o=(0,p.toSize)(this.getTileSize(e),this.tmpSize_),a=n[0]+t.minX*o[0]*i,s=n[0]+(t.maxX+1)*o[0]*i,l=n[1]+t.minY*o[1]*i,c=n[1]+(t.maxY+1)*o[1]*i;return(0,u.createOrUpdate)(a,l,s,c,r)},h.prototype.getTileRangeForExtentAndZ=function(e,t,r){var n=f;this.getTileCoordForXYAndZ_(e[0],e[1],t,!1,n);var i=n[1],o=n[2];return this.getTileCoordForXYAndZ_(e[2],e[3],t,!0,n),(0,a.createOrUpdate)(i,n[1],o,n[2],r)},h.prototype.getTileCoordCenter=function(e){var t=this.getOrigin(e[0]),r=this.getResolution(e[0]),n=(0,p.toSize)(this.getTileSize(e[0]),this.tmpSize_);return[t[0]+(e[1]+.5)*n[0]*r,t[1]+(e[2]+.5)*n[1]*r]},h.prototype.getTileCoordExtent=function(e,t){var r=this.getOrigin(e[0]),n=this.getResolution(e[0]),i=(0,p.toSize)(this.getTileSize(e[0]),this.tmpSize_),o=r[0]+e[1]*i[0]*n,a=r[1]+e[2]*i[1]*n,s=o+i[0]*n,l=a+i[1]*n;return(0,u.createOrUpdate)(o,a,s,l,t)},h.prototype.getTileCoordForCoordAndResolution=function(e,t,r){return this.getTileCoordForXYAndResolution_(e[0],e[1],t,!1,r)},h.prototype.getTileCoordForXYAndResolution_=function(e,t,r,n,i){var o=this.getZForResolution(r),a=r/this.getResolution(o),s=this.getOrigin(o),l=(0,p.toSize)(this.getTileSize(o),this.tmpSize_),u=n?.5:0,c=n?0:.5,h=Math.floor((e-s[0])/r+u),f=Math.floor((t-s[1])/r+c),y=a*h/l[0],m=a*f/l[1];return n?(y=Math.ceil(y)-1,m=Math.ceil(m)-1):(y=Math.floor(y),m=Math.floor(m)),(0,d.createOrUpdate)(o,y,m,i)},h.prototype.getTileCoordForXYAndZ_=function(e,t,r,n,i){var o=this.getOrigin(r),a=this.getResolution(r),s=(0,p.toSize)(this.getTileSize(r),this.tmpSize_),l=n?.5:0,u=n?0:.5,c=Math.floor((e-o[0])/a+l),h=Math.floor((t-o[1])/a+u),f=c/s[0],y=h/s[1];return n?(f=Math.ceil(f)-1,y=Math.ceil(y)-1):(f=Math.floor(f),y=Math.floor(y)),(0,d.createOrUpdate)(r,f,y,i)},h.prototype.getTileCoordForCoordAndZ=function(e,t,r){return this.getTileCoordForXYAndZ_(e[0],e[1],t,!1,r)},h.prototype.getTileCoordResolution=function(e){return this.resolutions_[e[0]]},h.prototype.getTileSize=function(e){return this.tileSize_?this.tileSize_:this.tileSizes_[e]},h.prototype.getFullTileRange=function(e){return this.fullTileRanges_?this.fullTileRanges_[e]:null},h.prototype.getZForResolution=function(e,t){var r=(0,l.linearFindNearest)(this.resolutions_,e,t||0);return(0,c.clamp)(r,this.minZoom,this.maxZoom)},h.prototype.calculateTileRanges_=function(e){for(var t=this.resolutions_.length,r=new Array(t),n=this.minZoom;n<t;++n)r[n]=this.getTileRangeForExtentAndZ(e,n);this.fullTileRanges_=r},t.default=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createOrUpdate=function(e,t,r,i,o){return void 0!==o?(o.minX=e,o.maxX=t,o.minY=r,o.maxY=i,o):new n(e,t,r,i)};var n=function(e,t,r,n){this.minX=e,this.maxX=t,this.minY=r,this.maxY=n};n.prototype.contains=function(e){return this.containsXY(e[1],e[2])},n.prototype.containsTileRange=function(e){return this.minX<=e.minX&&e.maxX<=this.maxX&&this.minY<=e.minY&&e.maxY<=this.maxY},n.prototype.containsXY=function(e,t){return this.minX<=e&&e<=this.maxX&&this.minY<=t&&t<=this.maxY},n.prototype.equals=function(e){return this.minX==e.minX&&this.minY==e.minY&&this.maxX==e.maxX&&this.maxY==e.maxY},n.prototype.extend=function(e){e.minX<this.minX&&(this.minX=e.minX),e.maxX>this.maxX&&(this.maxX=e.maxX),e.minY<this.minY&&(this.minY=e.minY),e.maxY>this.maxY&&(this.maxY=e.maxY)},n.prototype.getHeight=function(){return this.maxY-this.minY+1},n.prototype.getSize=function(){return[this.getWidth(),this.getHeight()]},n.prototype.getWidth=function(){return this.maxX-this.minX+1},n.prototype.intersects=function(e){return this.minX<=e.maxX&&this.maxX>=e.minX&&this.minY<=e.maxY&&this.maxY>=e.minY},t.default=n},function(e,t,r){"use strict";function n(e,t){return e>t?1:e<t?-1:0}Object.defineProperty(t,"__esModule",{value:!0}),t.binarySearch=function(e,t,r){var i=void 0,o=void 0,a=r||n,s=0,l=e.length,u=!1;for(;s<l;)(o=+a(e[i=s+(l-s>>1)],t))<0?s=i+1:(l=i,u=!o);return u?s:~s},t.numberSafeCompareFunction=n,t.includes=function(e,t){return e.indexOf(t)>=0},t.linearFindNearest=function(e,t,r){var n=e.length;if(e[0]<=t)return 0;if(t<=e[n-1])return n-1;var i=void 0;if(r>0){for(i=1;i<n;++i)if(e[i]<t)return i-1}else if(r<0){for(i=1;i<n;++i)if(e[i]<=t)return i}else for(i=1;i<n;++i){if(e[i]==t)return i;if(e[i]<t)return e[i-1]-t<t-e[i]?i-1:i}return n-1},t.reverseSubArray=function(e,t,r){for(;t<r;){var n=e[t];e[t]=e[r],e[r]=n,++t,--r}},t.extend=function(e,t){for(var r=Array.isArray(t)?t:[t],n=r.length,i=0;i<n;i++)e[e.length]=r[i]},t.remove=function(e,t){var r=e.indexOf(t),n=r>-1;n&&e.splice(r,1);return n},t.find=function(e,t){for(var r=e.length>>>0,n=void 0,i=0;i<r;i++)if(n=e[i],t(n,i,e))return n;return null},t.equals=function(e,t){var r=e.length;if(r!==t.length)return!1;for(var n=0;n<r;n++)if(e[n]!==t[n])return!1;return!0},t.stableSort=function(e,t){var r=e.length,n=Array(e.length),i=void 0;for(i=0;i<r;i++)n[i]={index:i,value:e[i]};for(n.sort((function(e,r){return t(e.value,r.value)||e.index-r.index})),i=0;i<e.length;i++)e[i]=n[i].value},t.findIndex=function(e,t){var r=void 0;return e.every((function(n,i){return r=i,!t(n,i,e)}))?-1:r},t.isSorted=function(e,t,r){var i=t||n;return e.every((function(t,n){if(0===n)return!0;var o=i(e[n-1],t);return!(o>0||r&&0===o)}))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={TILELOADSTART:"tileloadstart",TILELOADEND:"tileloadend",TILELOADERROR:"tileloaderror"}}]).default}));