/*!
* 	  Copyright (c) 2016 @thinkinggis
* 	  Copyright© 2000-2017 SuperMap Software Co. Ltd
*     echartsLayer.
*     github: https://github.com/SuperMap/echartsLayer
*     license: MIT
*/
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("echarts")):"function"==typeof define&&define.amd?define(["echarts"],e):"object"==typeof exports?exports.EchartsLayer=e(require("echarts")):t.EchartsLayer=e(t.echarts)}(window,(function(t){return function(t){var e={};function n(o){if(e[o])return e[o].exports;var r=e[o]={i:o,l:!1,exports:{}};return t[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,o){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(o,r,function(e){return t[e]}.bind(null,r));return o},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=1)}([function(e,n){e.exports=t},function(t,e,n){n(2);var o=n(6);t.exports=o},function(t,e,n){var o;void 0===(o=function(t){return n(0).registerCoordinateSystem("GLMap",n(3)),n(4),n(5),n(0).registerAction({type:"GLMapRoam",event:"GLMapRoam",update:"updateLayout"},(function(t,e){})),{version:"1.0.0"}}.call(e,n,e,t))||(t.exports=o)},function(t,e,n){var o;void 0===(o=function(t){var e=n(0);function o(t,e){this._GLMap=t,this.dimensions=["lng","lat"],this._mapOffset=[0,0],this._api=e}return o.prototype.dimensions=["lng","lat"],o.prototype.setMapOffset=function(t){this._mapOffset=t},o.prototype.getBMap=function(){return this._GLMap},o.prototype.fixLat=function(t){return t>=90?89.99999999999999:t<=-90?-89.99999999999999:t},o.prototype.dataToPoint=function(t){t[1]=this.fixLat(t[1]);var e=this._GLMap.project(t),n=this._mapOffset;return[e.x-n[0],e.y-n[1]]},o.prototype.pointToData=function(t){var e=this._mapOffset;return[(t=this._bmap.project([t[0]+e[0],t[1]+e[1]])).lng,t.lat]},o.prototype.getViewRect=function(){var t=this._api;return new e.graphic.BoundingRect(0,0,t.getWidth(),t.getHeight())},o.prototype.getRoamTransform=function(){return e.matrix.create()},o.prototype.prepareCustoms=function(t){var n=e.util,o=this.getViewRect();return{coordSys:{type:"GLMap",x:o.x,y:o.y,width:o.width,height:o.height},api:{coord:n.bind(this.dataToPoint,this),size:n.bind((function(t,e){return e=e||[0,0],n.map([0,1],(function(n){var o=e[n],r=t[n]/2,i=[],a=[];return i[n]=o-r,a[n]=o+r,i[1-n]=a[1-n]=e[1-n],Math.abs(this.dataToPoint(i)[n]-this.dataToPoint(a)[n])}),this)}),this)}}},o.dimensions=o.prototype.dimensions,o.create=function(t,n){var r;t.eachComponent("GLMap",(function(t){n.getZr().painter.getViewportRoot();var i=e.glMap;(r=new o(i,n)).setMapOffset(t.__mapOffset||[0,0]),t.coordinateSystem=r})),t.eachSeries((function(t){"GLMap"===t.get("coordinateSystem")&&(t.coordinateSystem=r)}))},o}.call(e,n,e,t))||(t.exports=o)},function(t,e,n){var o;void 0===(o=function(t){return n(0).extendComponentModel({type:"GLMap",getBMap:function(){return this.__GLMap},defaultOption:{roam:!1}})}.call(e,n,e,t))||(t.exports=o)},function(t,e,n){var o;void 0===(o=function(t){return n(0).extendComponentView({type:"GLMap",render:function(t,e,o){var r=!0,i=n(0).glMap,a=o.getZr().painter.getViewportRoot(),s=t.coordinateSystem,p=function(e,n){if(!r){var i=document.getElementsByClassName("maplibregl-map")[0],p=[-parseInt(i.style.left,10)||0,-parseInt(i.style.top,10)||0];a.style.left=p[0]+"px",a.style.top=p[1]+"px",s.setMapOffset(p),t.__mapOffset=p,o.dispatchAction({type:"GLMapRoam"})}};function c(){r||o.dispatchAction({type:"GLMapRoam"})}i.off("move",this._oldMoveHandler),i.off("zoomend",this._oldZoomEndHandler),i.on("move",p),i.on("zoomend",c),this._oldMoveHandler=p,this._oldZoomEndHandler=c;t.get("roam");r=!1}})}.call(e,n,e,t))||(t.exports=o)},function(t,e,n){function o(t){var e=n(0);const o=t.getCanvasContainer();this._container=document.createElement("div"),this._container.style.width=t.getCanvas().style.width,this._container.style.height=t.getCanvas().style.height,this._container.setAttribute("id","echarts"),this._container.setAttribute("class","echartMap"),this._map=t,o.appendChild(this._container),this.chart=e.init(this._container),e.glMap=t,this.resize()}o.prototype.remove=function(){var t=this;this._map._listeners.move.forEach((function(e){"moveHandler"===e.name&&t._map.off("move",e)})),this._map._listeners.move.forEach((function(e){"zoomEndHandler"===e.name&&t._map.off("zoomend",e)})),this.chart.clear(),this._container.parentNode&&this._container.parentNode.removeChild(this._container),this._map=void 0},o.prototype.resize=function(){const t=this;window.onresize=function(){t._container.style.width=t._map.getCanvas().style.width,t._container.style.height=t._map.getCanvas().style.height,t.chart.resize()}},t.exports=o}])}));