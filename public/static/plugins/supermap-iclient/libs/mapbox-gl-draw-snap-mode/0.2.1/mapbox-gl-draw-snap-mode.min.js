/**
 * Skipped minification because the original files appears to be already minified.
 * Original file: /npm/mapbox-gl-draw-snap-mode@0.2.1/dist/mapbox-gl-draw-snap-mode.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.mapboxGlDrawSnapMode=e():t.mapboxGlDrawSnapMode=e()}(this,(()=>(()=>{var t={188:function(t,e,r){t.exports=function(){"use strict";var t=function(t,e){var r={drag:[],click:[],mousemove:[],mousedown:[],mouseup:[],mouseout:[],keydown:[],keyup:[],touchstart:[],touchmove:[],touchend:[],tap:[]},n={on:function(t,e,n){if(void 0===r[t])throw new Error("Invalid event type: "+t);r[t].push({selector:e,fn:n})},render:function(t){e.store.featureChanged(t)}},o=function(t,o){for(var i=r[t],a=i.length;a--;){var s=i[a];if(s.selector(o)){s.fn.call(n,o)||e.store.render(),e.ui.updateMapClasses();break}}};return t.start.call(n),{render:t.render,stop:function(){t.stop&&t.stop()},trash:function(){t.trash&&(t.trash(),e.store.render())},combineFeatures:function(){t.combineFeatures&&t.combineFeatures()},uncombineFeatures:function(){t.uncombineFeatures&&t.uncombineFeatures()},drag:function(t){o("drag",t)},click:function(t){o("click",t)},mousemove:function(t){o("mousemove",t)},mousedown:function(t){o("mousedown",t)},mouseup:function(t){o("mouseup",t)},mouseout:function(t){o("mouseout",t)},keydown:function(t){o("keydown",t)},keyup:function(t){o("keyup",t)},touchstart:function(t){o("touchstart",t)},touchmove:function(t){o("touchmove",t)},touchend:function(t){o("touchend",t)},tap:function(t){o("tap",t)}}};function e(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function n(t){if(t.__esModule)return t;var e=t.default;if("function"==typeof e){var r=function t(){if(this instanceof t){var r=[null];return r.push.apply(r,arguments),new(Function.bind.apply(e,r))}return e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var n=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(r,e,n.get?n:{enumerable:!0,get:function(){return t[e]}})})),r}var o={},i={RADIUS:6378137,FLATTENING:1/298.257223563,POLAR_RADIUS:6356752.3142};function a(t){var e=0;if(t&&t.length>0){e+=Math.abs(s(t[0]));for(var r=1;r<t.length;r++)e-=Math.abs(s(t[r]))}return e}function s(t){var e,r,n,o,a,s,c=0,l=t.length;if(l>2){for(s=0;s<l;s++)s===l-2?(n=l-2,o=l-1,a=0):s===l-1?(n=l-1,o=0,a=1):(n=s,o=s+1,a=s+2),e=t[n],r=t[o],c+=(u(t[a][0])-u(e[0]))*Math.sin(u(r[1]));c=c*i.RADIUS*i.RADIUS/2}return c}function u(t){return t*Math.PI/180}o.geometry=function t(e){var r,n=0;switch(e.type){case"Polygon":return a(e.coordinates);case"MultiPolygon":for(r=0;r<e.coordinates.length;r++)n+=a(e.coordinates[r]);return n;case"Point":case"MultiPoint":case"LineString":case"MultiLineString":return 0;case"GeometryCollection":for(r=0;r<e.geometries.length;r++)n+=t(e.geometries[r]);return n}},o.ring=s;var c={CONTROL_BASE:"mapboxgl-ctrl",CONTROL_PREFIX:"mapboxgl-ctrl-",CONTROL_BUTTON:"mapbox-gl-draw_ctrl-draw-btn",CONTROL_BUTTON_LINE:"mapbox-gl-draw_line",CONTROL_BUTTON_POLYGON:"mapbox-gl-draw_polygon",CONTROL_BUTTON_POINT:"mapbox-gl-draw_point",CONTROL_BUTTON_TRASH:"mapbox-gl-draw_trash",CONTROL_BUTTON_COMBINE_FEATURES:"mapbox-gl-draw_combine",CONTROL_BUTTON_UNCOMBINE_FEATURES:"mapbox-gl-draw_uncombine",CONTROL_GROUP:"mapboxgl-ctrl-group",ATTRIBUTION:"mapboxgl-ctrl-attrib",ACTIVE_BUTTON:"active",BOX_SELECT:"mapbox-gl-draw_boxselect"},l={HOT:"mapbox-gl-draw-hot",COLD:"mapbox-gl-draw-cold"},p={ADD:"add",MOVE:"move",DRAG:"drag",POINTER:"pointer",NONE:"none"},f={POLYGON:"polygon",LINE:"line_string",POINT:"point"},d={FEATURE:"Feature",POLYGON:"Polygon",LINE_STRING:"LineString",POINT:"Point",FEATURE_COLLECTION:"FeatureCollection",MULTI_PREFIX:"Multi",MULTI_POINT:"MultiPoint",MULTI_LINE_STRING:"MultiLineString",MULTI_POLYGON:"MultiPolygon"},h={DRAW_LINE_STRING:"draw_line_string",DRAW_POLYGON:"draw_polygon",DRAW_POINT:"draw_point",SIMPLE_SELECT:"simple_select",DIRECT_SELECT:"direct_select",STATIC:"static"},g={CREATE:"draw.create",DELETE:"draw.delete",UPDATE:"draw.update",SELECTION_CHANGE:"draw.selectionchange",MODE_CHANGE:"draw.modechange",ACTIONABLE:"draw.actionable",RENDER:"draw.render",COMBINE_FEATURES:"draw.combine",UNCOMBINE_FEATURES:"draw.uncombine"},y={MOVE:"move",CHANGE_COORDINATES:"change_coordinates"},m={FEATURE:"feature",MIDPOINT:"midpoint",VERTEX:"vertex"},v={ACTIVE:"true",INACTIVE:"false"},b=["scrollZoom","boxZoom","dragRotate","dragPan","keyboard","doubleClickZoom","touchZoomRotate"],_=-85,E=Object.freeze({__proto__:null,classes:c,sources:l,cursors:p,types:f,geojsonTypes:d,modes:h,events:g,updateActions:y,meta:m,activeStates:v,interactions:b,LAT_MIN:-90,LAT_RENDERED_MIN:_,LAT_MAX:90,LAT_RENDERED_MAX:85,LNG_MIN:-270,LNG_MAX:270}),O={Point:0,LineString:1,MultiLineString:1,Polygon:2};function x(t,e){var r=O[t.geometry.type]-O[e.geometry.type];return 0===r&&t.geometry.type===d.POLYGON?t.area-e.area:r}function S(t){return t.map((function(t){return t.geometry.type===d.POLYGON&&(t.area=o.geometry({type:d.FEATURE,property:{},geometry:t.geometry})),t})).sort(x).map((function(t){return delete t.area,t}))}function w(t,e){return void 0===e&&(e=0),[[t.point.x-e,t.point.y-e],[t.point.x+e,t.point.y+e]]}function C(t){if(this._items={},this._nums={},this._length=t?t.length:0,t)for(var e=0,r=t.length;e<r;e++)this.add(t[e]),void 0!==t[e]&&("string"==typeof t[e]?this._items[t[e]]=e:this._nums[t[e]]=e)}C.prototype.add=function(t){return this.has(t)||(this._length++,"string"==typeof t?this._items[t]=this._length:this._nums[t]=this._length),this},C.prototype.delete=function(t){return!1===this.has(t)||(this._length--,delete this._items[t],delete this._nums[t]),this},C.prototype.has=function(t){return!("string"!=typeof t&&"number"!=typeof t||void 0===this._items[t]&&void 0===this._nums[t])},C.prototype.values=function(){var t=this,e=[];return Object.keys(this._items).forEach((function(r){e.push({k:r,v:t._items[r]})})),Object.keys(this._nums).forEach((function(r){e.push({k:JSON.parse(r),v:t._nums[r]})})),e.sort((function(t,e){return t.v-e.v})).map((function(t){return t.k}))},C.prototype.clear=function(){return this._length=0,this._items={},this._nums={},this};var M=[m.FEATURE,m.MIDPOINT,m.VERTEX],I={click:function(t,e,r){return T(t,e,r,r.options.clickBuffer)},touch:function(t,e,r){return T(t,e,r,r.options.touchBuffer)}};function T(t,e,r,n){if(null===r.map)return[];var o=t?w(t,n):e,i={};r.options.styles&&(i.layers=r.options.styles.map((function(t){return t.id})));var a=r.map.queryRenderedFeatures(o,i).filter((function(t){return-1!==M.indexOf(t.properties.meta)})),s=new C,u=[];return a.forEach((function(t){var e=t.properties.id;s.has(e)||(s.add(e),u.push(t))})),S(u)}function P(t,e){var r=I.click(t,null,e),n={mouse:p.NONE};return r[0]&&(n.mouse=r[0].properties.active===v.ACTIVE?p.MOVE:p.POINTER,n.feature=r[0].properties.meta),-1!==e.events.currentModeName().indexOf("draw")&&(n.mouse=p.ADD),e.ui.queueMapClasses(n),e.ui.updateMapClasses(),r[0]}function L(t,e){var r=t.x-e.x,n=t.y-e.y;return Math.sqrt(r*r+n*n)}function A(t,e,r){void 0===r&&(r={});var n=null!=r.fineTolerance?r.fineTolerance:4,o=null!=r.grossTolerance?r.grossTolerance:12,i=null!=r.interval?r.interval:500;t.point=t.point||e.point,t.time=t.time||e.time;var a=L(t.point,e.point);return a<n||a<o&&e.time-t.time<i}function F(t,e,r){void 0===r&&(r={});var n=null!=r.tolerance?r.tolerance:25,o=null!=r.interval?r.interval:250;return t.point=t.point||e.point,t.time=t.time||e.time,L(t.point,e.point)<n&&e.time-t.time<o}var N={exports:{}},j=N.exports=function(t,e){if(e||(e=16),void 0===t&&(t=128),t<=0)return"0";for(var r=Math.log(Math.pow(2,t))/Math.log(e),n=2;r===1/0;n*=2)r=Math.log(Math.pow(2,t/n))/Math.log(e)*n;var o=r-Math.floor(r),i="";for(n=0;n<Math.floor(r);n++)i=Math.floor(Math.random()*e).toString(e)+i;if(o){var a=Math.pow(e,o);i=Math.floor(Math.random()*a).toString(e)+i}var s=parseInt(i,e);return s!==1/0&&s>=Math.pow(2,t)?j(t,e):i};j.rack=function(t,e,r){var n=function(n){var i=0;do{if(i++>10){if(!r)throw new Error("too many ID collisions, use more bits");t+=r}var a=j(t,e)}while(Object.hasOwnProperty.call(o,a));return o[a]=n,a},o=n.hats={};return n.get=function(t){return n.hats[t]},n.set=function(t,e){return n.hats[t]=e,n},n.bits=t||128,n.base=e||16,n};var R=e(N.exports),k=function(t,e){this.ctx=t,this.properties=e.properties||{},this.coordinates=e.geometry.coordinates,this.id=e.id||R(),this.type=e.geometry.type};k.prototype.changed=function(){this.ctx.store.featureChanged(this.id)},k.prototype.incomingCoords=function(t){this.setCoordinates(t)},k.prototype.setCoordinates=function(t){this.coordinates=t,this.changed()},k.prototype.getCoordinates=function(){return JSON.parse(JSON.stringify(this.coordinates))},k.prototype.setProperty=function(t,e){this.properties[t]=e},k.prototype.toGeoJSON=function(){return JSON.parse(JSON.stringify({id:this.id,type:d.FEATURE,properties:this.properties,geometry:{coordinates:this.getCoordinates(),type:this.type}}))},k.prototype.internal=function(t){var e={id:this.id,meta:m.FEATURE,"meta:type":this.type,active:v.INACTIVE,mode:t};if(this.ctx.options.userProperties)for(var r in this.properties)e["user_"+r]=this.properties[r];return{type:d.FEATURE,properties:e,geometry:{coordinates:this.getCoordinates(),type:this.type}}};var D=function(t,e){k.call(this,t,e)};(D.prototype=Object.create(k.prototype)).isValid=function(){return"number"==typeof this.coordinates[0]&&"number"==typeof this.coordinates[1]},D.prototype.updateCoordinate=function(t,e,r){this.coordinates=3===arguments.length?[e,r]:[t,e],this.changed()},D.prototype.getCoordinate=function(){return this.getCoordinates()};var U=function(t,e){k.call(this,t,e)};(U.prototype=Object.create(k.prototype)).isValid=function(){return this.coordinates.length>1},U.prototype.addCoordinate=function(t,e,r){this.changed();var n=parseInt(t,10);this.coordinates.splice(n,0,[e,r])},U.prototype.getCoordinate=function(t){var e=parseInt(t,10);return JSON.parse(JSON.stringify(this.coordinates[e]))},U.prototype.removeCoordinate=function(t){this.changed(),this.coordinates.splice(parseInt(t,10),1)},U.prototype.updateCoordinate=function(t,e,r){var n=parseInt(t,10);this.coordinates[n]=[e,r],this.changed()};var G=function(t,e){k.call(this,t,e),this.coordinates=this.coordinates.map((function(t){return t.slice(0,-1)}))};(G.prototype=Object.create(k.prototype)).isValid=function(){return 0!==this.coordinates.length&&this.coordinates.every((function(t){return t.length>2}))},G.prototype.incomingCoords=function(t){this.coordinates=t.map((function(t){return t.slice(0,-1)})),this.changed()},G.prototype.setCoordinates=function(t){this.coordinates=t,this.changed()},G.prototype.addCoordinate=function(t,e,r){this.changed();var n=t.split(".").map((function(t){return parseInt(t,10)}));this.coordinates[n[0]].splice(n[1],0,[e,r])},G.prototype.removeCoordinate=function(t){this.changed();var e=t.split(".").map((function(t){return parseInt(t,10)})),r=this.coordinates[e[0]];r&&(r.splice(e[1],1),r.length<3&&this.coordinates.splice(e[0],1))},G.prototype.getCoordinate=function(t){var e=t.split(".").map((function(t){return parseInt(t,10)})),r=this.coordinates[e[0]];return JSON.parse(JSON.stringify(r[e[1]]))},G.prototype.getCoordinates=function(){return this.coordinates.map((function(t){return t.concat([t[0]])}))},G.prototype.updateCoordinate=function(t,e,r){this.changed();var n=t.split("."),o=parseInt(n[0],10),i=parseInt(n[1],10);void 0===this.coordinates[o]&&(this.coordinates[o]=[]),this.coordinates[o][i]=[e,r]};var B={MultiPoint:D,MultiLineString:U,MultiPolygon:G},V=function(t,e,r,n,o){var i=r.split("."),a=parseInt(i[0],10),s=i[1]?i.slice(1).join("."):null;return t[a][e](s,n,o)},Y=function(t,e){if(k.call(this,t,e),delete this.coordinates,this.model=B[e.geometry.type],void 0===this.model)throw new TypeError(e.geometry.type+" is not a valid type");this.features=this._coordinatesToFeatures(e.geometry.coordinates)};function X(t){this.map=t.map,this.drawConfig=JSON.parse(JSON.stringify(t.options||{})),this._ctx=t}(Y.prototype=Object.create(k.prototype))._coordinatesToFeatures=function(t){var e=this,r=this.model.bind(this);return t.map((function(t){return new r(e.ctx,{id:R(),type:d.FEATURE,properties:{},geometry:{coordinates:t,type:e.type.replace("Multi","")}})}))},Y.prototype.isValid=function(){return this.features.every((function(t){return t.isValid()}))},Y.prototype.setCoordinates=function(t){this.features=this._coordinatesToFeatures(t),this.changed()},Y.prototype.getCoordinate=function(t){return V(this.features,"getCoordinate",t)},Y.prototype.getCoordinates=function(){return JSON.parse(JSON.stringify(this.features.map((function(t){return t.type===d.POLYGON?t.getCoordinates():t.coordinates}))))},Y.prototype.updateCoordinate=function(t,e,r){V(this.features,"updateCoordinate",t,e,r),this.changed()},Y.prototype.addCoordinate=function(t,e,r){V(this.features,"addCoordinate",t,e,r),this.changed()},Y.prototype.removeCoordinate=function(t){V(this.features,"removeCoordinate",t),this.changed()},Y.prototype.getFeatures=function(){return this.features},X.prototype.setSelected=function(t){return this._ctx.store.setSelected(t)},X.prototype.setSelectedCoordinates=function(t){var e=this;this._ctx.store.setSelectedCoordinates(t),t.reduce((function(t,r){return void 0===t[r.feature_id]&&(t[r.feature_id]=!0,e._ctx.store.get(r.feature_id).changed()),t}),{})},X.prototype.getSelected=function(){return this._ctx.store.getSelected()},X.prototype.getSelectedIds=function(){return this._ctx.store.getSelectedIds()},X.prototype.isSelected=function(t){return this._ctx.store.isSelected(t)},X.prototype.getFeature=function(t){return this._ctx.store.get(t)},X.prototype.select=function(t){return this._ctx.store.select(t)},X.prototype.deselect=function(t){return this._ctx.store.deselect(t)},X.prototype.deleteFeature=function(t,e){return void 0===e&&(e={}),this._ctx.store.delete(t,e)},X.prototype.addFeature=function(t){return this._ctx.store.add(t)},X.prototype.clearSelectedFeatures=function(){return this._ctx.store.clearSelected()},X.prototype.clearSelectedCoordinates=function(){return this._ctx.store.clearSelectedCoordinates()},X.prototype.setActionableState=function(t){void 0===t&&(t={});var e={trash:t.trash||!1,combineFeatures:t.combineFeatures||!1,uncombineFeatures:t.uncombineFeatures||!1};return this._ctx.events.actionable(e)},X.prototype.changeMode=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r={}),this._ctx.events.changeMode(t,e,r)},X.prototype.updateUIClasses=function(t){return this._ctx.ui.queueMapClasses(t)},X.prototype.activateUIButton=function(t){return this._ctx.ui.setActiveButton(t)},X.prototype.featuresAt=function(t,e,r){if(void 0===r&&(r="click"),"click"!==r&&"touch"!==r)throw new Error("invalid buffer type");return I[r](t,e,this._ctx)},X.prototype.newFeature=function(t){var e=t.geometry.type;return e===d.POINT?new D(this._ctx,t):e===d.LINE_STRING?new U(this._ctx,t):e===d.POLYGON?new G(this._ctx,t):new Y(this._ctx,t)},X.prototype.isInstanceOf=function(t,e){if(t===d.POINT)return e instanceof D;if(t===d.LINE_STRING)return e instanceof U;if(t===d.POLYGON)return e instanceof G;if("MultiFeature"===t)return e instanceof Y;throw new Error("Unknown feature class: "+t)},X.prototype.doRender=function(t){return this._ctx.store.featureChanged(t)},X.prototype.onSetup=function(){},X.prototype.onDrag=function(){},X.prototype.onClick=function(){},X.prototype.onMouseMove=function(){},X.prototype.onMouseDown=function(){},X.prototype.onMouseUp=function(){},X.prototype.onMouseOut=function(){},X.prototype.onKeyUp=function(){},X.prototype.onKeyDown=function(){},X.prototype.onTouchStart=function(){},X.prototype.onTouchMove=function(){},X.prototype.onTouchEnd=function(){},X.prototype.onTap=function(){},X.prototype.onStop=function(){},X.prototype.onTrash=function(){},X.prototype.onCombineFeature=function(){},X.prototype.onUncombineFeature=function(){},X.prototype.toDisplayFeatures=function(){throw new Error("You must overwrite toDisplayFeatures")};var J={drag:"onDrag",click:"onClick",mousemove:"onMouseMove",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseout:"onMouseOut",keyup:"onKeyUp",keydown:"onKeyDown",touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd",tap:"onTap"},z=Object.keys(J);function q(t){var e=Object.keys(t);return function(r,n){void 0===n&&(n={});var o={},i=e.reduce((function(e,r){return e[r]=t[r],e}),new X(r));return{start:function(){var e=this;o=i.onSetup(n),z.forEach((function(r){var n,a=J[r],s=function(){return!1};t[a]&&(s=function(){return!0}),e.on(r,s,(n=a,function(t){return i[n](o,t)}))}))},stop:function(){i.onStop(o)},trash:function(){i.onTrash(o)},combineFeatures:function(){i.onCombineFeatures(o)},uncombineFeatures:function(){i.onUncombineFeatures(o)},render:function(t,e){i.toDisplayFeatures(o,t,e)}}}}function Z(t){return[].concat(t).filter((function(t){return void 0!==t}))}function H(){var t=this;if(!t.ctx.map||void 0===t.ctx.map.getSource(l.HOT))return u();var e=t.ctx.events.currentModeName();t.ctx.ui.queueMapClasses({mode:e});var r=[],n=[];t.isDirty?n=t.getAllIds():(r=t.getChangedIds().filter((function(e){return void 0!==t.get(e)})),n=t.sources.hot.filter((function(e){return e.properties.id&&-1===r.indexOf(e.properties.id)&&void 0!==t.get(e.properties.id)})).map((function(t){return t.properties.id}))),t.sources.hot=[];var o=t.sources.cold.length;t.sources.cold=t.isDirty?[]:t.sources.cold.filter((function(t){var e=t.properties.id||t.properties.parent;return-1===r.indexOf(e)}));var i=o!==t.sources.cold.length||n.length>0;function a(r,n){var o=t.get(r).internal(e);t.ctx.events.currentModeRender(o,(function(e){t.sources[n].push(e)}))}if(r.forEach((function(t){return a(t,"hot")})),n.forEach((function(t){return a(t,"cold")})),i&&t.ctx.map.getSource(l.COLD).setData({type:d.FEATURE_COLLECTION,features:t.sources.cold}),t.ctx.map.getSource(l.HOT).setData({type:d.FEATURE_COLLECTION,features:t.sources.hot}),t._emitSelectionChange&&(t.ctx.map.fire(g.SELECTION_CHANGE,{features:t.getSelected().map((function(t){return t.toGeoJSON()})),points:t.getSelectedCoordinates().map((function(t){return{type:d.FEATURE,properties:{},geometry:{type:d.POINT,coordinates:t.coordinates}}}))}),t._emitSelectionChange=!1),t._deletedFeaturesToEmit.length){var s=t._deletedFeaturesToEmit.map((function(t){return t.toGeoJSON()}));t._deletedFeaturesToEmit=[],t.ctx.map.fire(g.DELETE,{features:s})}function u(){t.isDirty=!1,t.clearChangedIds()}u(),t.ctx.map.fire(g.RENDER,{})}function $(t){var e,r=this;this._features={},this._featureIds=new C,this._selectedFeatureIds=new C,this._selectedCoordinates=[],this._changedFeatureIds=new C,this._deletedFeaturesToEmit=[],this._emitSelectionChange=!1,this._mapInitialConfig={},this.ctx=t,this.sources={hot:[],cold:[]},this.render=function(){e||(e=requestAnimationFrame((function(){e=null,H.call(r)})))},this.isDirty=!1}function W(t,e){var r=t._selectedCoordinates.filter((function(e){return t._selectedFeatureIds.has(e.feature_id)}));t._selectedCoordinates.length===r.length||e.silent||(t._emitSelectionChange=!0),t._selectedCoordinates=r}$.prototype.createRenderBatch=function(){var t=this,e=this.render,r=0;return this.render=function(){r++},function(){t.render=e,r>0&&t.render()}},$.prototype.setDirty=function(){return this.isDirty=!0,this},$.prototype.featureChanged=function(t){return this._changedFeatureIds.add(t),this},$.prototype.getChangedIds=function(){return this._changedFeatureIds.values()},$.prototype.clearChangedIds=function(){return this._changedFeatureIds.clear(),this},$.prototype.getAllIds=function(){return this._featureIds.values()},$.prototype.add=function(t){return this.featureChanged(t.id),this._features[t.id]=t,this._featureIds.add(t.id),this},$.prototype.delete=function(t,e){var r=this;return void 0===e&&(e={}),Z(t).forEach((function(t){r._featureIds.has(t)&&(r._featureIds.delete(t),r._selectedFeatureIds.delete(t),e.silent||-1===r._deletedFeaturesToEmit.indexOf(r._features[t])&&r._deletedFeaturesToEmit.push(r._features[t]),delete r._features[t],r.isDirty=!0)})),W(this,e),this},$.prototype.get=function(t){return this._features[t]},$.prototype.getAll=function(){var t=this;return Object.keys(this._features).map((function(e){return t._features[e]}))},$.prototype.select=function(t,e){var r=this;return void 0===e&&(e={}),Z(t).forEach((function(t){r._selectedFeatureIds.has(t)||(r._selectedFeatureIds.add(t),r._changedFeatureIds.add(t),e.silent||(r._emitSelectionChange=!0))})),this},$.prototype.deselect=function(t,e){var r=this;return void 0===e&&(e={}),Z(t).forEach((function(t){r._selectedFeatureIds.has(t)&&(r._selectedFeatureIds.delete(t),r._changedFeatureIds.add(t),e.silent||(r._emitSelectionChange=!0))})),W(this,e),this},$.prototype.clearSelected=function(t){return void 0===t&&(t={}),this.deselect(this._selectedFeatureIds.values(),{silent:t.silent}),this},$.prototype.setSelected=function(t,e){var r=this;return void 0===e&&(e={}),t=Z(t),this.deselect(this._selectedFeatureIds.values().filter((function(e){return-1===t.indexOf(e)})),{silent:e.silent}),this.select(t.filter((function(t){return!r._selectedFeatureIds.has(t)})),{silent:e.silent}),this},$.prototype.setSelectedCoordinates=function(t){return this._selectedCoordinates=t,this._emitSelectionChange=!0,this},$.prototype.clearSelectedCoordinates=function(){return this._selectedCoordinates=[],this._emitSelectionChange=!0,this},$.prototype.getSelectedIds=function(){return this._selectedFeatureIds.values()},$.prototype.getSelected=function(){var t=this;return this._selectedFeatureIds.values().map((function(e){return t.get(e)}))},$.prototype.getSelectedCoordinates=function(){var t=this;return this._selectedCoordinates.map((function(e){return{coordinates:t.get(e.feature_id).getCoordinate(e.coord_path)}}))},$.prototype.isSelected=function(t){return this._selectedFeatureIds.has(t)},$.prototype.setFeatureProperty=function(t,e,r){this.get(t).setProperty(e,r),this.featureChanged(t)},$.prototype.storeMapConfig=function(){var t=this;b.forEach((function(e){t.ctx.map[e]&&(t._mapInitialConfig[e]=t.ctx.map[e].isEnabled())}))},$.prototype.restoreMapConfig=function(){var t=this;Object.keys(this._mapInitialConfig).forEach((function(e){t._mapInitialConfig[e]?t.ctx.map[e].enable():t.ctx.map[e].disable()}))},$.prototype.getInitialConfigValue=function(t){return void 0===this._mapInitialConfig[t]||this._mapInitialConfig[t]};var K=function(){for(var t=arguments,e={},r=0;r<arguments.length;r++){var n=t[r];for(var o in n)Q.call(n,o)&&(e[o]=n[o])}return e},Q=Object.prototype.hasOwnProperty,tt=e(K),et=["mode","feature","mouse"];function rt(e){var r=null,n=null,o={onRemove:function(){return e.map.off("load",o.connect),clearInterval(n),o.removeLayers(),e.store.restoreMapConfig(),e.ui.removeButtons(),e.events.removeEventListeners(),e.ui.clearMapClasses(),e.boxZoomInitial&&e.map.boxZoom.enable(),e.map=null,e.container=null,e.store=null,r&&r.parentNode&&r.parentNode.removeChild(r),r=null,this},connect:function(){e.map.off("load",o.connect),clearInterval(n),o.addLayers(),e.store.storeMapConfig(),e.events.addEventListeners()},onAdd:function(i){var a=i.fire;return i.fire=function(t,e){var r=arguments;return 1===a.length&&1!==arguments.length&&(r=[tt({},{type:t},e)]),a.apply(i,r)},e.map=i,e.events=function(e){var r=Object.keys(e.options.modes).reduce((function(t,r){return t[r]=q(e.options.modes[r]),t}),{}),n={},o={},i={},a=null,s=null;i.drag=function(t,r){r({point:t.point,time:(new Date).getTime()})?(e.ui.queueMapClasses({mouse:p.DRAG}),s.drag(t)):t.originalEvent.stopPropagation()},i.mousedrag=function(t){i.drag(t,(function(t){return!A(n,t)}))},i.touchdrag=function(t){i.drag(t,(function(t){return!F(o,t)}))},i.mousemove=function(t){if(1===(void 0!==t.originalEvent.buttons?t.originalEvent.buttons:t.originalEvent.which))return i.mousedrag(t);var r=P(t,e);t.featureTarget=r,s.mousemove(t)},i.mousedown=function(t){n={time:(new Date).getTime(),point:t.point};var r=P(t,e);t.featureTarget=r,s.mousedown(t)},i.mouseup=function(t){var r=P(t,e);t.featureTarget=r,A(n,{point:t.point,time:(new Date).getTime()})?s.click(t):s.mouseup(t)},i.mouseout=function(t){s.mouseout(t)},i.touchstart=function(t){if(t.originalEvent.preventDefault(),e.options.touchEnabled){o={time:(new Date).getTime(),point:t.point};var r=I.touch(t,null,e)[0];t.featureTarget=r,s.touchstart(t)}},i.touchmove=function(t){if(t.originalEvent.preventDefault(),e.options.touchEnabled)return s.touchmove(t),i.touchdrag(t)},i.touchend=function(t){if(t.originalEvent.preventDefault(),e.options.touchEnabled){var r=I.touch(t,null,e)[0];t.featureTarget=r,F(o,{time:(new Date).getTime(),point:t.point})?s.tap(t):s.touchend(t)}};var u=function(t){return!(8===t||46===t||t>=48&&t<=57)};function c(n,o,i){void 0===i&&(i={}),s.stop();var u=r[n];if(void 0===u)throw new Error(n+" is not valid");a=n;var c=u(e,o);s=t(c,e),i.silent||e.map.fire(g.MODE_CHANGE,{mode:n}),e.store.setDirty(),e.store.render()}i.keydown=function(t){(t.srcElement||t.target).classList.contains("mapboxgl-canvas")&&(8!==t.keyCode&&46!==t.keyCode||!e.options.controls.trash?u(t.keyCode)?s.keydown(t):49===t.keyCode&&e.options.controls.point?c(h.DRAW_POINT):50===t.keyCode&&e.options.controls.line_string?c(h.DRAW_LINE_STRING):51===t.keyCode&&e.options.controls.polygon&&c(h.DRAW_POLYGON):(t.preventDefault(),s.trash()))},i.keyup=function(t){u(t.keyCode)&&s.keyup(t)},i.zoomend=function(){e.store.changeZoom()},i.data=function(t){if("style"===t.dataType){var r=e.setup,n=e.map,o=e.options,i=e.store;o.styles.some((function(t){return n.getLayer(t.id)}))||(r.addLayers(),i.setDirty(),i.render())}};var l={trash:!1,combineFeatures:!1,uncombineFeatures:!1};return{start:function(){a=e.options.defaultMode,s=t(r[a](e),e)},changeMode:c,actionable:function(t){var r=!1;Object.keys(t).forEach((function(e){if(void 0===l[e])throw new Error("Invalid action type");l[e]!==t[e]&&(r=!0),l[e]=t[e]})),r&&e.map.fire(g.ACTIONABLE,{actions:l})},currentModeName:function(){return a},currentModeRender:function(t,e){return s.render(t,e)},fire:function(t,e){i[t]&&i[t](e)},addEventListeners:function(){e.map.on("mousemove",i.mousemove),e.map.on("mousedown",i.mousedown),e.map.on("mouseup",i.mouseup),e.map.on("data",i.data),e.map.on("touchmove",i.touchmove),e.map.on("touchstart",i.touchstart),e.map.on("touchend",i.touchend),e.container.addEventListener("mouseout",i.mouseout),e.options.keybindings&&(e.container.addEventListener("keydown",i.keydown),e.container.addEventListener("keyup",i.keyup))},removeEventListeners:function(){e.map.off("mousemove",i.mousemove),e.map.off("mousedown",i.mousedown),e.map.off("mouseup",i.mouseup),e.map.off("data",i.data),e.map.off("touchmove",i.touchmove),e.map.off("touchstart",i.touchstart),e.map.off("touchend",i.touchend),e.container.removeEventListener("mouseout",i.mouseout),e.options.keybindings&&(e.container.removeEventListener("keydown",i.keydown),e.container.removeEventListener("keyup",i.keyup))},trash:function(t){s.trash(t)},combineFeatures:function(){s.combineFeatures()},uncombineFeatures:function(){s.uncombineFeatures()},getMode:function(){return a}}}(e),e.ui=function(t){var e={},r=null,n={mode:null,feature:null,mouse:null},o={mode:null,feature:null,mouse:null};function i(t){o=tt(o,t)}function a(){var e,r;if(t.container){var i=[],a=[];et.forEach((function(t){o[t]!==n[t]&&(i.push(t+"-"+n[t]),null!==o[t]&&a.push(t+"-"+o[t]))})),i.length>0&&(e=t.container.classList).remove.apply(e,i),a.length>0&&(r=t.container.classList).add.apply(r,a),n=tt(n,o)}}function s(t,e){void 0===e&&(e={});var n=document.createElement("button");return n.className=c.CONTROL_BUTTON+" "+e.className,n.setAttribute("title",e.title),e.container.appendChild(n),n.addEventListener("click",(function(n){if(n.preventDefault(),n.stopPropagation(),n.target===r)return u(),void e.onDeactivate();l(t),e.onActivate()}),!0),n}function u(){r&&(r.classList.remove(c.ACTIVE_BUTTON),r=null)}function l(t){u();var n=e[t];n&&n&&"trash"!==t&&(n.classList.add(c.ACTIVE_BUTTON),r=n)}return{setActiveButton:l,queueMapClasses:i,updateMapClasses:a,clearMapClasses:function(){i({mode:null,feature:null,mouse:null}),a()},addButtons:function(){var r=t.options.controls,n=document.createElement("div");return n.className=c.CONTROL_GROUP+" "+c.CONTROL_BASE,r?(r[f.LINE]&&(e[f.LINE]=s(f.LINE,{container:n,className:c.CONTROL_BUTTON_LINE,title:"LineString tool "+(t.options.keybindings?"(l)":""),onActivate:function(){return t.events.changeMode(h.DRAW_LINE_STRING)},onDeactivate:function(){return t.events.trash()}})),r[f.POLYGON]&&(e[f.POLYGON]=s(f.POLYGON,{container:n,className:c.CONTROL_BUTTON_POLYGON,title:"Polygon tool "+(t.options.keybindings?"(p)":""),onActivate:function(){return t.events.changeMode(h.DRAW_POLYGON)},onDeactivate:function(){return t.events.trash()}})),r[f.POINT]&&(e[f.POINT]=s(f.POINT,{container:n,className:c.CONTROL_BUTTON_POINT,title:"Marker tool "+(t.options.keybindings?"(m)":""),onActivate:function(){return t.events.changeMode(h.DRAW_POINT)},onDeactivate:function(){return t.events.trash()}})),r.trash&&(e.trash=s("trash",{container:n,className:c.CONTROL_BUTTON_TRASH,title:"Delete",onActivate:function(){t.events.trash()}})),r.combine_features&&(e.combine_features=s("combineFeatures",{container:n,className:c.CONTROL_BUTTON_COMBINE_FEATURES,title:"Combine",onActivate:function(){t.events.combineFeatures()}})),r.uncombine_features&&(e.uncombine_features=s("uncombineFeatures",{container:n,className:c.CONTROL_BUTTON_UNCOMBINE_FEATURES,title:"Uncombine",onActivate:function(){t.events.uncombineFeatures()}})),n):n},removeButtons:function(){Object.keys(e).forEach((function(t){var r=e[t];r.parentNode&&r.parentNode.removeChild(r),delete e[t]}))}}}(e),e.container=i.getContainer(),e.store=new $(e),r=e.ui.addButtons(),e.options.boxSelect&&(e.boxZoomInitial=i.boxZoom.isEnabled(),i.boxZoom.disable(),i.dragPan.disable(),i.dragPan.enable()),i.loaded()?o.connect():(i.on("load",o.connect),n=setInterval((function(){i.loaded()&&o.connect()}),16)),e.events.start(),r},addLayers:function(){e.map.addSource(l.COLD,{data:{type:d.FEATURE_COLLECTION,features:[]},type:"geojson"}),e.map.addSource(l.HOT,{data:{type:d.FEATURE_COLLECTION,features:[]},type:"geojson"}),e.options.styles.forEach((function(t){e.map.addLayer(t)})),e.store.setDirty(!0),e.store.render()},removeLayers:function(){e.options.styles.forEach((function(t){e.map.getLayer(t.id)&&e.map.removeLayer(t.id)})),e.map.getSource(l.COLD)&&e.map.removeSource(l.COLD),e.map.getSource(l.HOT)&&e.map.removeSource(l.HOT)}};return e.setup=o,o}var nt=[{id:"gl-draw-polygon-fill-inactive",type:"fill",filter:["all",["==","active","false"],["==","$type","Polygon"],["!=","mode","static"]],paint:{"fill-color":"#3bb2d0","fill-outline-color":"#3bb2d0","fill-opacity":.1}},{id:"gl-draw-polygon-fill-active",type:"fill",filter:["all",["==","active","true"],["==","$type","Polygon"]],paint:{"fill-color":"#fbb03b","fill-outline-color":"#fbb03b","fill-opacity":.1}},{id:"gl-draw-polygon-midpoint",type:"circle",filter:["all",["==","$type","Point"],["==","meta","midpoint"]],paint:{"circle-radius":3,"circle-color":"#fbb03b"}},{id:"gl-draw-polygon-stroke-inactive",type:"line",filter:["all",["==","active","false"],["==","$type","Polygon"],["!=","mode","static"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#3bb2d0","line-width":2}},{id:"gl-draw-polygon-stroke-active",type:"line",filter:["all",["==","active","true"],["==","$type","Polygon"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#fbb03b","line-dasharray":[.2,2],"line-width":2}},{id:"gl-draw-line-inactive",type:"line",filter:["all",["==","active","false"],["==","$type","LineString"],["!=","mode","static"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#3bb2d0","line-width":2}},{id:"gl-draw-line-active",type:"line",filter:["all",["==","$type","LineString"],["==","active","true"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#fbb03b","line-dasharray":[.2,2],"line-width":2}},{id:"gl-draw-polygon-and-line-vertex-stroke-inactive",type:"circle",filter:["all",["==","meta","vertex"],["==","$type","Point"],["!=","mode","static"]],paint:{"circle-radius":5,"circle-color":"#fff"}},{id:"gl-draw-polygon-and-line-vertex-inactive",type:"circle",filter:["all",["==","meta","vertex"],["==","$type","Point"],["!=","mode","static"]],paint:{"circle-radius":3,"circle-color":"#fbb03b"}},{id:"gl-draw-point-point-stroke-inactive",type:"circle",filter:["all",["==","active","false"],["==","$type","Point"],["==","meta","feature"],["!=","mode","static"]],paint:{"circle-radius":5,"circle-opacity":1,"circle-color":"#fff"}},{id:"gl-draw-point-inactive",type:"circle",filter:["all",["==","active","false"],["==","$type","Point"],["==","meta","feature"],["!=","mode","static"]],paint:{"circle-radius":3,"circle-color":"#3bb2d0"}},{id:"gl-draw-point-stroke-active",type:"circle",filter:["all",["==","$type","Point"],["==","active","true"],["!=","meta","midpoint"]],paint:{"circle-radius":7,"circle-color":"#fff"}},{id:"gl-draw-point-active",type:"circle",filter:["all",["==","$type","Point"],["!=","meta","midpoint"],["==","active","true"]],paint:{"circle-radius":5,"circle-color":"#fbb03b"}},{id:"gl-draw-polygon-fill-static",type:"fill",filter:["all",["==","mode","static"],["==","$type","Polygon"]],paint:{"fill-color":"#404040","fill-outline-color":"#404040","fill-opacity":.1}},{id:"gl-draw-polygon-stroke-static",type:"line",filter:["all",["==","mode","static"],["==","$type","Polygon"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#404040","line-width":2}},{id:"gl-draw-line-static",type:"line",filter:["all",["==","mode","static"],["==","$type","LineString"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#404040","line-width":2}},{id:"gl-draw-point-static",type:"circle",filter:["all",["==","mode","static"],["==","$type","Point"]],paint:{"circle-radius":5,"circle-color":"#404040"}}];function ot(t){return function(e){var r=e.featureTarget;return!!r&&!!r.properties&&r.properties.meta===t}}function it(t){return!!t.originalEvent&&!!t.originalEvent.shiftKey&&0===t.originalEvent.button}function at(t){return!!t.featureTarget&&!!t.featureTarget.properties&&t.featureTarget.properties.active===v.ACTIVE&&t.featureTarget.properties.meta===m.FEATURE}function st(t){return!!t.featureTarget&&!!t.featureTarget.properties&&t.featureTarget.properties.active===v.INACTIVE&&t.featureTarget.properties.meta===m.FEATURE}function ut(t){return void 0===t.featureTarget}function ct(t){return!!t.featureTarget&&!!t.featureTarget.properties&&t.featureTarget.properties.meta===m.FEATURE}function lt(t){var e=t.featureTarget;return!!e&&!!e.properties&&e.properties.meta===m.VERTEX}function pt(t){return!!t.originalEvent&&!0===t.originalEvent.shiftKey}function ft(t){return 27===t.keyCode}function dt(t){return 13===t.keyCode}var ht=Object.freeze({__proto__:null,isOfMetaType:ot,isShiftMousedown:it,isActiveFeature:at,isInactiveFeature:st,noTarget:ut,isFeature:ct,isVertex:lt,isShiftDown:pt,isEscapeKey:ft,isEnterKey:dt,isTrue:function(){return!0}}),gt=yt;function yt(t,e){this.x=t,this.y=e}yt.prototype={clone:function(){return new yt(this.x,this.y)},add:function(t){return this.clone()._add(t)},sub:function(t){return this.clone()._sub(t)},multByPoint:function(t){return this.clone()._multByPoint(t)},divByPoint:function(t){return this.clone()._divByPoint(t)},mult:function(t){return this.clone()._mult(t)},div:function(t){return this.clone()._div(t)},rotate:function(t){return this.clone()._rotate(t)},rotateAround:function(t,e){return this.clone()._rotateAround(t,e)},matMult:function(t){return this.clone()._matMult(t)},unit:function(){return this.clone()._unit()},perp:function(){return this.clone()._perp()},round:function(){return this.clone()._round()},mag:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},equals:function(t){return this.x===t.x&&this.y===t.y},dist:function(t){return Math.sqrt(this.distSqr(t))},distSqr:function(t){var e=t.x-this.x,r=t.y-this.y;return e*e+r*r},angle:function(){return Math.atan2(this.y,this.x)},angleTo:function(t){return Math.atan2(this.y-t.y,this.x-t.x)},angleWith:function(t){return this.angleWithSep(t.x,t.y)},angleWithSep:function(t,e){return Math.atan2(this.x*e-this.y*t,this.x*t+this.y*e)},_matMult:function(t){var e=t[0]*this.x+t[1]*this.y,r=t[2]*this.x+t[3]*this.y;return this.x=e,this.y=r,this},_add:function(t){return this.x+=t.x,this.y+=t.y,this},_sub:function(t){return this.x-=t.x,this.y-=t.y,this},_mult:function(t){return this.x*=t,this.y*=t,this},_div:function(t){return this.x/=t,this.y/=t,this},_multByPoint:function(t){return this.x*=t.x,this.y*=t.y,this},_divByPoint:function(t){return this.x/=t.x,this.y/=t.y,this},_unit:function(){return this._div(this.mag()),this},_perp:function(){var t=this.y;return this.y=this.x,this.x=-t,this},_rotate:function(t){var e=Math.cos(t),r=Math.sin(t),n=e*this.x-r*this.y,o=r*this.x+e*this.y;return this.x=n,this.y=o,this},_rotateAround:function(t,e){var r=Math.cos(t),n=Math.sin(t),o=e.x+r*(this.x-e.x)-n*(this.y-e.y),i=e.y+n*(this.x-e.x)+r*(this.y-e.y);return this.x=o,this.y=i,this},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}},yt.convert=function(t){return t instanceof yt?t:Array.isArray(t)?new yt(t[0],t[1]):t};var mt=e(gt);function vt(t,e){var r=e.getBoundingClientRect();return new mt(t.clientX-r.left-(e.clientLeft||0),t.clientY-r.top-(e.clientTop||0))}function bt(t,e,r,n){return{type:d.FEATURE,properties:{meta:m.VERTEX,parent:t,coord_path:r,active:n?v.ACTIVE:v.INACTIVE},geometry:{type:d.POINT,coordinates:e}}}function _t(t,e,r){var n=e.geometry.coordinates,o=r.geometry.coordinates;if(n[1]>85||n[1]<_||o[1]>85||o[1]<_)return null;var i={lng:(n[0]+o[0])/2,lat:(n[1]+o[1])/2};return{type:d.FEATURE,properties:{meta:m.MIDPOINT,parent:t,lng:i.lng,lat:i.lat,coord_path:r.properties.coord_path},geometry:{type:d.POINT,coordinates:[i.lng,i.lat]}}}function Et(t,e,r){void 0===e&&(e={}),void 0===r&&(r=null);var n,o=t.geometry,i=o.type,a=o.coordinates,s=t.properties&&t.properties.id,u=[];function c(t,r){var n="",o=null;t.forEach((function(t,i){var a=null!=r?r+"."+i:String(i),c=bt(s,t,a,l(a));if(e.midpoints&&o){var p=_t(s,o,c);p&&u.push(p)}o=c;var f=JSON.stringify(t);n!==f&&u.push(c),0===i&&(n=f)}))}function l(t){return!!e.selectedPaths&&-1!==e.selectedPaths.indexOf(t)}return i===d.POINT?u.push(bt(s,a,r,l(r))):i===d.POLYGON?a.forEach((function(t,e){c(t,null!==r?r+"."+e:String(e))})):i===d.LINE_STRING?c(a,r):0===i.indexOf(d.MULTI_PREFIX)&&(n=i.replace(d.MULTI_PREFIX,""),a.forEach((function(r,o){var i={type:d.FEATURE,properties:t.properties,geometry:{type:n,coordinates:r}};u=u.concat(Et(i,e,o))}))),u}var Ot={enable:function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t._ctx&&t._ctx.store&&t._ctx.store.getInitialConfigValue&&t._ctx.store.getInitialConfigValue("doubleClickZoom")&&t.map.doubleClickZoom.enable()}),0)},disable:function(t){setTimeout((function(){t.map&&t.map.doubleClickZoom&&t.map.doubleClickZoom.disable()}),0)}},xt={exports:{}},St=function(t){if(!t||!t.type)return null;var e=wt[t.type];return e?"geometry"===e?{type:"FeatureCollection",features:[{type:"Feature",properties:{},geometry:t}]}:"feature"===e?{type:"FeatureCollection",features:[t]}:"featurecollection"===e?t:void 0:null},wt={Point:"geometry",MultiPoint:"geometry",LineString:"geometry",MultiLineString:"geometry",Polygon:"geometry",MultiPolygon:"geometry",GeometryCollection:"geometry",Feature:"feature",FeatureCollection:"featurecollection"},Ct=e(St),Mt=Object.freeze({__proto__:null,default:function t(e){switch(e&&e.type||null){case"FeatureCollection":return e.features=e.features.reduce((function(e,r){return e.concat(t(r))}),[]),e;case"Feature":return e.geometry?t(e.geometry).map((function(t){var r={type:"Feature",properties:JSON.parse(JSON.stringify(e.properties)),geometry:t};return void 0!==e.id&&(r.id=e.id),r})):[e];case"MultiPoint":return e.coordinates.map((function(t){return{type:"Point",coordinates:t}}));case"MultiPolygon":return e.coordinates.map((function(t){return{type:"Polygon",coordinates:t}}));case"MultiLineString":return e.coordinates.map((function(t){return{type:"LineString",coordinates:t}}));case"GeometryCollection":return e.geometries.map(t).reduce((function(t,e){return t.concat(e)}),[]);case"Point":case"Polygon":case"LineString":return[e]}}}),It=St,Tt=n(Mt),Pt=function(t){return function t(e){return Array.isArray(e)&&e.length&&"number"==typeof e[0]?[e]:e.reduce((function(e,r){return Array.isArray(r)&&Array.isArray(r[0])?e.concat(t(r)):(e.push(r),e)}),[])}(t)};Tt instanceof Function||(Tt=Tt.default);var Lt={exports:{}},At=Lt.exports=function(t){return new Ft(t)};function Ft(t){this.value=t}function Nt(t,e,r){var n=[],o=[],i=!0;return function t(a){var s=r?jt(a):a,u={},c=!0,l={node:s,node_:a,path:[].concat(n),parent:o[o.length-1],parents:o,key:n.slice(-1)[0],isRoot:0===n.length,level:n.length,circular:null,update:function(t,e){l.isRoot||(l.parent.node[l.key]=t),l.node=t,e&&(c=!1)},delete:function(t){delete l.parent.node[l.key],t&&(c=!1)},remove:function(t){Dt(l.parent.node)?l.parent.node.splice(l.key,1):delete l.parent.node[l.key],t&&(c=!1)},keys:null,before:function(t){u.before=t},after:function(t){u.after=t},pre:function(t){u.pre=t},post:function(t){u.post=t},stop:function(){i=!1},block:function(){c=!1}};if(!i)return l;function p(){if("object"==typeof l.node&&null!==l.node){l.keys&&l.node_===l.node||(l.keys=Rt(l.node)),l.isLeaf=0==l.keys.length;for(var t=0;t<o.length;t++)if(o[t].node_===a){l.circular=o[t];break}}else l.isLeaf=!0,l.keys=null;l.notLeaf=!l.isLeaf,l.notRoot=!l.isRoot}p();var f=e.call(l,l.node);return void 0!==f&&l.update&&l.update(f),u.before&&u.before.call(l,l.node),c?("object"!=typeof l.node||null===l.node||l.circular||(o.push(l),p(),Ut(l.keys,(function(e,o){n.push(e),u.pre&&u.pre.call(l,l.node[e],e);var i=t(l.node[e]);r&&Gt.call(l.node,e)&&(l.node[e]=i.node),i.isLast=o==l.keys.length-1,i.isFirst=0==o,u.post&&u.post.call(l,i),n.pop()})),o.pop()),u.after&&u.after.call(l,l.node),l):l}(t).node}function jt(t){if("object"==typeof t&&null!==t){var e;if(Dt(t))e=[];else if("[object Date]"===kt(t))e=new Date(t.getTime?t.getTime():t);else if(function(t){return"[object RegExp]"===kt(t)}(t))e=new RegExp(t);else if(function(t){return"[object Error]"===kt(t)}(t))e={message:t.message};else if(function(t){return"[object Boolean]"===kt(t)}(t))e=new Boolean(t);else if(function(t){return"[object Number]"===kt(t)}(t))e=new Number(t);else if(function(t){return"[object String]"===kt(t)}(t))e=new String(t);else if(Object.create&&Object.getPrototypeOf)e=Object.create(Object.getPrototypeOf(t));else if(t.constructor===Object)e={};else{var r=t.constructor&&t.constructor.prototype||t.__proto__||{},n=function(){};n.prototype=r,e=new n}return Ut(Rt(t),(function(r){e[r]=t[r]})),e}return t}Ft.prototype.get=function(t){for(var e=this.value,r=0;r<t.length;r++){var n=t[r];if(!e||!Gt.call(e,n)){e=void 0;break}e=e[n]}return e},Ft.prototype.has=function(t){for(var e=this.value,r=0;r<t.length;r++){var n=t[r];if(!e||!Gt.call(e,n))return!1;e=e[n]}return!0},Ft.prototype.set=function(t,e){for(var r=this.value,n=0;n<t.length-1;n++){var o=t[n];Gt.call(r,o)||(r[o]={}),r=r[o]}return r[t[n]]=e,e},Ft.prototype.map=function(t){return Nt(this.value,t,!0)},Ft.prototype.forEach=function(t){return this.value=Nt(this.value,t,!1),this.value},Ft.prototype.reduce=function(t,e){var r=1===arguments.length,n=r?this.value:e;return this.forEach((function(e){this.isRoot&&r||(n=t.call(this,n,e))})),n},Ft.prototype.paths=function(){var t=[];return this.forEach((function(e){t.push(this.path)})),t},Ft.prototype.nodes=function(){var t=[];return this.forEach((function(e){t.push(this.node)})),t},Ft.prototype.clone=function(){var t=[],e=[];return function r(n){for(var o=0;o<t.length;o++)if(t[o]===n)return e[o];if("object"==typeof n&&null!==n){var i=jt(n);return t.push(n),e.push(i),Ut(Rt(n),(function(t){i[t]=r(n[t])})),t.pop(),e.pop(),i}return n}(this.value)};var Rt=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};function kt(t){return Object.prototype.toString.call(t)}var Dt=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},Ut=function(t,e){if(t.forEach)return t.forEach(e);for(var r=0;r<t.length;r++)e(t[r],r,t)};Ut(Rt(Ft.prototype),(function(t){At[t]=function(e){var r=[].slice.call(arguments,1),n=new Ft(e);return n[t].apply(n,r)}}));var Gt=Object.hasOwnProperty||function(t,e){return e in t},Bt=Lt.exports,Vt=Yt;function Yt(t){if(!(this instanceof Yt))return new Yt(t);this._bbox=t||[1/0,1/0,-1/0,-1/0],this._valid=!!t}Yt.prototype.include=function(t){return this._valid=!0,this._bbox[0]=Math.min(this._bbox[0],t[0]),this._bbox[1]=Math.min(this._bbox[1],t[1]),this._bbox[2]=Math.max(this._bbox[2],t[0]),this._bbox[3]=Math.max(this._bbox[3],t[1]),this},Yt.prototype.equals=function(t){var e;return e=t instanceof Yt?t.bbox():t,this._bbox[0]==e[0]&&this._bbox[1]==e[1]&&this._bbox[2]==e[2]&&this._bbox[3]==e[3]},Yt.prototype.center=function(t){return this._valid?[(this._bbox[0]+this._bbox[2])/2,(this._bbox[1]+this._bbox[3])/2]:null},Yt.prototype.union=function(t){var e;return this._valid=!0,e=t instanceof Yt?t.bbox():t,this._bbox[0]=Math.min(this._bbox[0],e[0]),this._bbox[1]=Math.min(this._bbox[1],e[1]),this._bbox[2]=Math.max(this._bbox[2],e[2]),this._bbox[3]=Math.max(this._bbox[3],e[3]),this},Yt.prototype.bbox=function(){return this._valid?this._bbox:null},Yt.prototype.contains=function(t){if(!t)return this._fastContains();if(!this._valid)return null;var e=t[0],r=t[1];return this._bbox[0]<=e&&this._bbox[1]<=r&&this._bbox[2]>=e&&this._bbox[3]>=r},Yt.prototype.intersect=function(t){return this._valid?(e=t instanceof Yt?t.bbox():t,!(this._bbox[0]>e[2]||this._bbox[2]<e[0]||this._bbox[3]<e[1]||this._bbox[1]>e[3])):null;var e},Yt.prototype._fastContains=function(){if(!this._valid)return new Function("return null;");var t="return "+this._bbox[0]+"<= ll[0] &&"+this._bbox[1]+"<= ll[1] &&"+this._bbox[2]+">= ll[0] &&"+this._bbox[3]+">= ll[1]";return new Function("ll",t)},Yt.prototype.polygon=function(){return this._valid?{type:"Polygon",coordinates:[[[this._bbox[0],this._bbox[1]],[this._bbox[2],this._bbox[1]],[this._bbox[2],this._bbox[3]],[this._bbox[0],this._bbox[3]],[this._bbox[0],this._bbox[1]]]]}:null};var Xt=function(t){if(!t)return[];var e=Tt(It(t)),r=[];return e.features.forEach((function(t){t.geometry&&(r=r.concat(Pt(t.geometry.coordinates)))})),r},Jt=Bt,zt=Vt,qt={features:["FeatureCollection"],coordinates:["Point","MultiPoint","LineString","MultiLineString","Polygon","MultiPolygon"],geometry:["Feature"],geometries:["GeometryCollection"]},Zt=Object.keys(qt);function Ht(t){for(var e=zt(),r=Xt(t),n=0;n<r.length;n++)e.include(r[n]);return e}xt.exports=function(t){return Ht(t).bbox()},xt.exports.polygon=function(t){return Ht(t).polygon()},xt.exports.bboxify=function(t){return Jt(t).map((function(t){t&&Zt.some((function(e){return!!t[e]&&-1!==qt[e].indexOf(t.type)}))&&(t.bbox=Ht(t).bbox(),this.update(t))}))};var $t=e(xt.exports),Wt=-90;function Kt(t,e){var r=Wt,n=90,o=Wt,i=90,a=270,s=-270;t.forEach((function(t){var e=$t(t),u=e[1],c=e[3],l=e[0],p=e[2];u>r&&(r=u),c<n&&(n=c),c>o&&(o=c),u<i&&(i=u),l<a&&(a=l),p>s&&(s=p)}));var u=e;return r+u.lat>85&&(u.lat=85-r),o+u.lat>90&&(u.lat=90-o),n+u.lat<-85&&(u.lat=-85-n),i+u.lat<Wt&&(u.lat=Wt-i),a+u.lng<=-270&&(u.lng+=360*Math.ceil(Math.abs(u.lng)/360)),s+u.lng>=270&&(u.lng-=360*Math.ceil(Math.abs(u.lng)/360)),u}function Qt(t,e){var r=Kt(t.map((function(t){return t.toGeoJSON()})),e);t.forEach((function(t){var e,n=t.getCoordinates(),o=function(t){var e={lng:t[0]+r.lng,lat:t[1]+r.lat};return[e.lng,e.lat]},i=function(t){return t.map((function(t){return o(t)}))};t.type===d.POINT?e=o(n):t.type===d.LINE_STRING||t.type===d.MULTI_POINT?e=n.map(o):t.type===d.POLYGON||t.type===d.MULTI_LINE_STRING?e=n.map(i):t.type===d.MULTI_POLYGON&&(e=n.map((function(t){return t.map((function(t){return i(t)}))}))),t.incomingCoords(e)}))}var te={onSetup:function(t){var e=this,r={dragMoveLocation:null,boxSelectStartLocation:null,boxSelectElement:void 0,boxSelecting:!1,canBoxSelect:!1,dragMoving:!1,canDragMove:!1,initiallySelectedFeatureIds:t.featureIds||[]};return this.setSelected(r.initiallySelectedFeatureIds.filter((function(t){return void 0!==e.getFeature(t)}))),this.fireActionable(),this.setActionableState({combineFeatures:!0,uncombineFeatures:!0,trash:!0}),r},fireUpdate:function(){this.map.fire(g.UPDATE,{action:y.MOVE,features:this.getSelected().map((function(t){return t.toGeoJSON()}))})},fireActionable:function(){var t=this,e=this.getSelected(),r=e.filter((function(e){return t.isInstanceOf("MultiFeature",e)})),n=!1;if(e.length>1){n=!0;var o=e[0].type.replace("Multi","");e.forEach((function(t){t.type.replace("Multi","")!==o&&(n=!1)}))}var i=r.length>0,a=e.length>0;this.setActionableState({combineFeatures:n,uncombineFeatures:i,trash:a})},getUniqueIds:function(t){return t.length?t.map((function(t){return t.properties.id})).filter((function(t){return void 0!==t})).reduce((function(t,e){return t.add(e),t}),new C).values():[]},stopExtendedInteractions:function(t){t.boxSelectElement&&(t.boxSelectElement.parentNode&&t.boxSelectElement.parentNode.removeChild(t.boxSelectElement),t.boxSelectElement=null),this.map.dragPan.enable(),t.boxSelecting=!1,t.canBoxSelect=!1,t.dragMoving=!1,t.canDragMove=!1},onStop:function(){Ot.enable(this)},onMouseMove:function(t,e){return ct(e)&&t.dragMoving&&this.fireUpdate(),this.stopExtendedInteractions(t),!0},onMouseOut:function(t){return!t.dragMoving||this.fireUpdate()}};te.onTap=te.onClick=function(t,e){return ut(e)?this.clickAnywhere(t,e):ot(m.VERTEX)(e)?this.clickOnVertex(t,e):ct(e)?this.clickOnFeature(t,e):void 0},te.clickAnywhere=function(t){var e=this,r=this.getSelectedIds();r.length&&(this.clearSelectedFeatures(),r.forEach((function(t){return e.doRender(t)}))),Ot.enable(this),this.stopExtendedInteractions(t)},te.clickOnVertex=function(t,e){this.changeMode(h.DIRECT_SELECT,{featureId:e.featureTarget.properties.parent,coordPath:e.featureTarget.properties.coord_path,startPos:e.lngLat}),this.updateUIClasses({mouse:p.MOVE})},te.startOnActiveFeature=function(t,e){this.stopExtendedInteractions(t),this.map.dragPan.disable(),this.doRender(e.featureTarget.properties.id),t.canDragMove=!0,t.dragMoveLocation=e.lngLat},te.clickOnFeature=function(t,e){var r=this;Ot.disable(this),this.stopExtendedInteractions(t);var n=pt(e),o=this.getSelectedIds(),i=e.featureTarget.properties.id,a=this.isSelected(i);if(!n&&a&&this.getFeature(i).type!==d.POINT)return this.changeMode(h.DIRECT_SELECT,{featureId:i});a&&n?(this.deselect(i),this.updateUIClasses({mouse:p.POINTER}),1===o.length&&Ot.enable(this)):!a&&n?(this.select(i),this.updateUIClasses({mouse:p.MOVE})):a||n||(o.forEach((function(t){return r.doRender(t)})),this.setSelected(i),this.updateUIClasses({mouse:p.MOVE})),this.doRender(i)},te.onMouseDown=function(t,e){return at(e)?this.startOnActiveFeature(t,e):this.drawConfig.boxSelect&&it(e)?this.startBoxSelect(t,e):void 0},te.startBoxSelect=function(t,e){this.stopExtendedInteractions(t),this.map.dragPan.disable(),t.boxSelectStartLocation=vt(e.originalEvent,this.map.getContainer()),t.canBoxSelect=!0},te.onTouchStart=function(t,e){if(at(e))return this.startOnActiveFeature(t,e)},te.onDrag=function(t,e){return t.canDragMove?this.dragMove(t,e):this.drawConfig.boxSelect&&t.canBoxSelect?this.whileBoxSelect(t,e):void 0},te.whileBoxSelect=function(t,e){t.boxSelecting=!0,this.updateUIClasses({mouse:p.ADD}),t.boxSelectElement||(t.boxSelectElement=document.createElement("div"),t.boxSelectElement.classList.add(c.BOX_SELECT),this.map.getContainer().appendChild(t.boxSelectElement));var r=vt(e.originalEvent,this.map.getContainer()),n=Math.min(t.boxSelectStartLocation.x,r.x),o=Math.max(t.boxSelectStartLocation.x,r.x),i=Math.min(t.boxSelectStartLocation.y,r.y),a=Math.max(t.boxSelectStartLocation.y,r.y),s="translate("+n+"px, "+i+"px)";t.boxSelectElement.style.transform=s,t.boxSelectElement.style.WebkitTransform=s,t.boxSelectElement.style.width=o-n+"px",t.boxSelectElement.style.height=a-i+"px"},te.dragMove=function(t,e){t.dragMoving=!0,e.originalEvent.stopPropagation();var r={lng:e.lngLat.lng-t.dragMoveLocation.lng,lat:e.lngLat.lat-t.dragMoveLocation.lat};Qt(this.getSelected(),r),t.dragMoveLocation=e.lngLat},te.onTouchEnd=te.onMouseUp=function(t,e){var r=this;if(t.dragMoving)this.fireUpdate();else if(t.boxSelecting){var n=[t.boxSelectStartLocation,vt(e.originalEvent,this.map.getContainer())],o=this.featuresAt(null,n,"click"),i=this.getUniqueIds(o).filter((function(t){return!r.isSelected(t)}));i.length&&(this.select(i),i.forEach((function(t){return r.doRender(t)})),this.updateUIClasses({mouse:p.MOVE}))}this.stopExtendedInteractions(t)},te.toDisplayFeatures=function(t,e,r){e.properties.active=this.isSelected(e.properties.id)?v.ACTIVE:v.INACTIVE,r(e),this.fireActionable(),e.properties.active===v.ACTIVE&&e.geometry.type!==d.POINT&&Et(e).forEach(r)},te.onTrash=function(){this.deleteFeature(this.getSelectedIds()),this.fireActionable()},te.onCombineFeatures=function(){var t=this.getSelected();if(!(0===t.length||t.length<2)){for(var e=[],r=[],n=t[0].type.replace("Multi",""),o=0;o<t.length;o++){var i=t[o];if(i.type.replace("Multi","")!==n)return;i.type.includes("Multi")?i.getCoordinates().forEach((function(t){e.push(t)})):e.push(i.getCoordinates()),r.push(i.toGeoJSON())}if(r.length>1){var a=this.newFeature({type:d.FEATURE,properties:r[0].properties,geometry:{type:"Multi"+n,coordinates:e}});this.addFeature(a),this.deleteFeature(this.getSelectedIds(),{silent:!0}),this.setSelected([a.id]),this.map.fire(g.COMBINE_FEATURES,{createdFeatures:[a.toGeoJSON()],deletedFeatures:r})}this.fireActionable()}},te.onUncombineFeatures=function(){var t=this,e=this.getSelected();if(0!==e.length){for(var r=[],n=[],o=function(o){var i=e[o];t.isInstanceOf("MultiFeature",i)&&(i.getFeatures().forEach((function(e){t.addFeature(e),e.properties=i.properties,r.push(e.toGeoJSON()),t.select([e.id])})),t.deleteFeature(i.id,{silent:!0}),n.push(i.toGeoJSON()))},i=0;i<e.length;i++)o(i);r.length>1&&this.map.fire(g.UNCOMBINE_FEATURES,{createdFeatures:r,deletedFeatures:n}),this.fireActionable()}};var ee=ot(m.VERTEX),re=ot(m.MIDPOINT),ne={fireUpdate:function(){this.map.fire(g.UPDATE,{action:y.CHANGE_COORDINATES,features:this.getSelected().map((function(t){return t.toGeoJSON()}))})},fireActionable:function(t){this.setActionableState({combineFeatures:!1,uncombineFeatures:!1,trash:t.selectedCoordPaths.length>0})},startDragging:function(t,e){this.map.dragPan.disable(),t.canDragMove=!0,t.dragMoveLocation=e.lngLat},stopDragging:function(t){this.map.dragPan.enable(),t.dragMoving=!1,t.canDragMove=!1,t.dragMoveLocation=null},onVertex:function(t,e){this.startDragging(t,e);var r=e.featureTarget.properties,n=t.selectedCoordPaths.indexOf(r.coord_path);pt(e)||-1!==n?pt(e)&&-1===n&&t.selectedCoordPaths.push(r.coord_path):t.selectedCoordPaths=[r.coord_path];var o=this.pathsToCoordinates(t.featureId,t.selectedCoordPaths);this.setSelectedCoordinates(o)},onMidpoint:function(t,e){this.startDragging(t,e);var r=e.featureTarget.properties;t.feature.addCoordinate(r.coord_path,r.lng,r.lat),this.fireUpdate(),t.selectedCoordPaths=[r.coord_path]},pathsToCoordinates:function(t,e){return e.map((function(e){return{feature_id:t,coord_path:e}}))},onFeature:function(t,e){0===t.selectedCoordPaths.length?this.startDragging(t,e):this.stopDragging(t)},dragFeature:function(t,e,r){Qt(this.getSelected(),r),t.dragMoveLocation=e.lngLat},dragVertex:function(t,e,r){for(var n=t.selectedCoordPaths.map((function(e){return t.feature.getCoordinate(e)})),o=Kt(n.map((function(t){return{type:d.FEATURE,properties:{},geometry:{type:d.POINT,coordinates:t}}})),r),i=0;i<n.length;i++){var a=n[i];t.feature.updateCoordinate(t.selectedCoordPaths[i],a[0]+o.lng,a[1]+o.lat)}},clickNoTarget:function(){this.changeMode(h.SIMPLE_SELECT)},clickInactive:function(){this.changeMode(h.SIMPLE_SELECT)},clickActiveFeature:function(t){t.selectedCoordPaths=[],this.clearSelectedCoordinates(),t.feature.changed()},onSetup:function(t){var e=t.featureId,r=this.getFeature(e);if(!r)throw new Error("You must provide a featureId to enter direct_select mode");if(r.type===d.POINT)throw new TypeError("direct_select mode doesn't handle point features");var n={featureId:e,feature:r,dragMoveLocation:t.startPos||null,dragMoving:!1,canDragMove:!1,selectedCoordPaths:t.coordPath?[t.coordPath]:[]};return this.setSelectedCoordinates(this.pathsToCoordinates(e,n.selectedCoordPaths)),this.setSelected(e),Ot.disable(this),this.setActionableState({trash:!0}),n},onStop:function(){Ot.enable(this),this.clearSelectedCoordinates()},toDisplayFeatures:function(t,e,r){t.featureId===e.properties.id?(e.properties.active=v.ACTIVE,r(e),Et(e,{map:this.map,midpoints:!0,selectedPaths:t.selectedCoordPaths}).forEach(r)):(e.properties.active=v.INACTIVE,r(e)),this.fireActionable(t)},onTrash:function(t){t.selectedCoordPaths.sort((function(t,e){return e.localeCompare(t,"en",{numeric:!0})})).forEach((function(e){return t.feature.removeCoordinate(e)})),this.fireUpdate(),t.selectedCoordPaths=[],this.clearSelectedCoordinates(),this.fireActionable(t),!1===t.feature.isValid()&&(this.deleteFeature([t.featureId]),this.changeMode(h.SIMPLE_SELECT,{}))},onMouseMove:function(t,e){var r=at(e),n=ee(e),o=re(e),i=0===t.selectedCoordPaths.length;return r&&i||n&&!i?this.updateUIClasses({mouse:p.MOVE}):this.updateUIClasses({mouse:p.NONE}),(n||r||o)&&t.dragMoving&&this.fireUpdate(),this.stopDragging(t),!0},onMouseOut:function(t){return t.dragMoving&&this.fireUpdate(),!0}};ne.onTouchStart=ne.onMouseDown=function(t,e){return ee(e)?this.onVertex(t,e):at(e)?this.onFeature(t,e):re(e)?this.onMidpoint(t,e):void 0},ne.onDrag=function(t,e){if(!0===t.canDragMove){t.dragMoving=!0,e.originalEvent.stopPropagation();var r={lng:e.lngLat.lng-t.dragMoveLocation.lng,lat:e.lngLat.lat-t.dragMoveLocation.lat};t.selectedCoordPaths.length>0?this.dragVertex(t,e,r):this.dragFeature(t,e,r),t.dragMoveLocation=e.lngLat}},ne.onClick=function(t,e){return ut(e)?this.clickNoTarget(t,e):at(e)?this.clickActiveFeature(t,e):st(e)?this.clickInactive(t,e):void this.stopDragging(t)},ne.onTap=function(t,e){return ut(e)?this.clickNoTarget(t,e):at(e)?this.clickActiveFeature(t,e):st(e)?this.clickInactive(t,e):void 0},ne.onTouchEnd=ne.onMouseUp=function(t){t.dragMoving&&this.fireUpdate(),this.stopDragging(t)};var oe={};function ie(t,e){return!!t.lngLat&&t.lngLat.lng===e[0]&&t.lngLat.lat===e[1]}oe.onSetup=function(){var t=this.newFeature({type:d.FEATURE,properties:{},geometry:{type:d.POINT,coordinates:[]}});return this.addFeature(t),this.clearSelectedFeatures(),this.updateUIClasses({mouse:p.ADD}),this.activateUIButton(f.POINT),this.setActionableState({trash:!0}),{point:t}},oe.stopDrawingAndRemove=function(t){this.deleteFeature([t.point.id],{silent:!0}),this.changeMode(h.SIMPLE_SELECT)},oe.onTap=oe.onClick=function(t,e){this.updateUIClasses({mouse:p.MOVE}),t.point.updateCoordinate("",e.lngLat.lng,e.lngLat.lat),this.map.fire(g.CREATE,{features:[t.point.toGeoJSON()]}),this.changeMode(h.SIMPLE_SELECT,{featureIds:[t.point.id]})},oe.onStop=function(t){this.activateUIButton(),t.point.getCoordinate().length||this.deleteFeature([t.point.id],{silent:!0})},oe.toDisplayFeatures=function(t,e,r){var n=e.properties.id===t.point.id;if(e.properties.active=n?v.ACTIVE:v.INACTIVE,!n)return r(e)},oe.onTrash=oe.stopDrawingAndRemove,oe.onKeyUp=function(t,e){if(ft(e)||dt(e))return this.stopDrawingAndRemove(t,e)};var ae={onSetup:function(){var t=this.newFeature({type:d.FEATURE,properties:{},geometry:{type:d.POLYGON,coordinates:[[]]}});return this.addFeature(t),this.clearSelectedFeatures(),Ot.disable(this),this.updateUIClasses({mouse:p.ADD}),this.activateUIButton(f.POLYGON),this.setActionableState({trash:!0}),{polygon:t,currentVertexPosition:0}},clickAnywhere:function(t,e){if(t.currentVertexPosition>0&&ie(e,t.polygon.coordinates[0][t.currentVertexPosition-1]))return this.changeMode(h.SIMPLE_SELECT,{featureIds:[t.polygon.id]});this.updateUIClasses({mouse:p.ADD}),t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),t.currentVertexPosition++,t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat)},clickOnVertex:function(t){return this.changeMode(h.SIMPLE_SELECT,{featureIds:[t.polygon.id]})},onMouseMove:function(t,e){t.polygon.updateCoordinate("0."+t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),lt(e)&&this.updateUIClasses({mouse:p.POINTER})}};ae.onTap=ae.onClick=function(t,e){return lt(e)?this.clickOnVertex(t,e):this.clickAnywhere(t,e)},ae.onKeyUp=function(t,e){ft(e)?(this.deleteFeature([t.polygon.id],{silent:!0}),this.changeMode(h.SIMPLE_SELECT)):dt(e)&&this.changeMode(h.SIMPLE_SELECT,{featureIds:[t.polygon.id]})},ae.onStop=function(t){this.updateUIClasses({mouse:p.NONE}),Ot.enable(this),this.activateUIButton(),void 0!==this.getFeature(t.polygon.id)&&(t.polygon.removeCoordinate("0."+t.currentVertexPosition),t.polygon.isValid()?this.map.fire(g.CREATE,{features:[t.polygon.toGeoJSON()]}):(this.deleteFeature([t.polygon.id],{silent:!0}),this.changeMode(h.SIMPLE_SELECT,{},{silent:!0})))},ae.toDisplayFeatures=function(t,e,r){var n=e.properties.id===t.polygon.id;if(e.properties.active=n?v.ACTIVE:v.INACTIVE,!n)return r(e);if(0!==e.geometry.coordinates.length){var o=e.geometry.coordinates[0].length;if(!(o<3)){if(e.properties.meta=m.FEATURE,r(bt(t.polygon.id,e.geometry.coordinates[0][0],"0.0",!1)),o>3){var i=e.geometry.coordinates[0].length-3;r(bt(t.polygon.id,e.geometry.coordinates[0][i],"0."+i,!1))}if(o<=4){var a=[[e.geometry.coordinates[0][0][0],e.geometry.coordinates[0][0][1]],[e.geometry.coordinates[0][1][0],e.geometry.coordinates[0][1][1]]];if(r({type:d.FEATURE,properties:e.properties,geometry:{coordinates:a,type:d.LINE_STRING}}),3===o)return}return r(e)}}},ae.onTrash=function(t){this.deleteFeature([t.polygon.id],{silent:!0}),this.changeMode(h.SIMPLE_SELECT)};var se={onSetup:function(t){var e,r,n=(t=t||{}).featureId,o="forward";if(n){if(!(e=this.getFeature(n)))throw new Error("Could not find a feature with the provided featureId");var i=t.from;if(i&&"Feature"===i.type&&i.geometry&&"Point"===i.geometry.type&&(i=i.geometry),i&&"Point"===i.type&&i.coordinates&&2===i.coordinates.length&&(i=i.coordinates),!i||!Array.isArray(i))throw new Error("Please use the `from` property to indicate which point to continue the line from");var a=e.coordinates.length-1;if(e.coordinates[a][0]===i[0]&&e.coordinates[a][1]===i[1])r=a+1,e.addCoordinate.apply(e,[r].concat(e.coordinates[a]));else{if(e.coordinates[0][0]!==i[0]||e.coordinates[0][1]!==i[1])throw new Error("`from` should match the point at either the start or the end of the provided LineString");o="backwards",r=0,e.addCoordinate.apply(e,[r].concat(e.coordinates[0]))}}else e=this.newFeature({type:d.FEATURE,properties:{},geometry:{type:d.LINE_STRING,coordinates:[]}}),r=0,this.addFeature(e);return this.clearSelectedFeatures(),Ot.disable(this),this.updateUIClasses({mouse:p.ADD}),this.activateUIButton(f.LINE),this.setActionableState({trash:!0}),{line:e,currentVertexPosition:r,direction:o}},clickAnywhere:function(t,e){if(t.currentVertexPosition>0&&ie(e,t.line.coordinates[t.currentVertexPosition-1])||"backwards"===t.direction&&ie(e,t.line.coordinates[t.currentVertexPosition+1]))return this.changeMode(h.SIMPLE_SELECT,{featureIds:[t.line.id]});this.updateUIClasses({mouse:p.ADD}),t.line.updateCoordinate(t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),"forward"===t.direction?(t.currentVertexPosition++,t.line.updateCoordinate(t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat)):t.line.addCoordinate(0,e.lngLat.lng,e.lngLat.lat)},clickOnVertex:function(t){return this.changeMode(h.SIMPLE_SELECT,{featureIds:[t.line.id]})},onMouseMove:function(t,e){t.line.updateCoordinate(t.currentVertexPosition,e.lngLat.lng,e.lngLat.lat),lt(e)&&this.updateUIClasses({mouse:p.POINTER})}};se.onTap=se.onClick=function(t,e){if(lt(e))return this.clickOnVertex(t,e);this.clickAnywhere(t,e)},se.onKeyUp=function(t,e){dt(e)?this.changeMode(h.SIMPLE_SELECT,{featureIds:[t.line.id]}):ft(e)&&(this.deleteFeature([t.line.id],{silent:!0}),this.changeMode(h.SIMPLE_SELECT))},se.onStop=function(t){Ot.enable(this),this.activateUIButton(),void 0!==this.getFeature(t.line.id)&&(t.line.removeCoordinate(""+t.currentVertexPosition),t.line.isValid()?this.map.fire(g.CREATE,{features:[t.line.toGeoJSON()]}):(this.deleteFeature([t.line.id],{silent:!0}),this.changeMode(h.SIMPLE_SELECT,{},{silent:!0})))},se.onTrash=function(t){this.deleteFeature([t.line.id],{silent:!0}),this.changeMode(h.SIMPLE_SELECT)},se.toDisplayFeatures=function(t,e,r){var n=e.properties.id===t.line.id;if(e.properties.active=n?v.ACTIVE:v.INACTIVE,!n)return r(e);e.geometry.coordinates.length<2||(e.properties.meta=m.FEATURE,r(bt(t.line.id,e.geometry.coordinates["forward"===t.direction?e.geometry.coordinates.length-2:1],""+("forward"===t.direction?e.geometry.coordinates.length-2:1),!1)),r(e))};var ue={simple_select:te,direct_select:ne,draw_point:oe,draw_polygon:ae,draw_line_string:se},ce={defaultMode:h.SIMPLE_SELECT,keybindings:!0,touchEnabled:!0,clickBuffer:2,touchBuffer:25,boxSelect:!0,displayControlsDefault:!0,styles:nt,modes:ue,controls:{},userProperties:!1},le={point:!0,line_string:!0,polygon:!0,trash:!0,combine_features:!0,uncombine_features:!0},pe={point:!1,line_string:!1,polygon:!1,trash:!1,combine_features:!1,uncombine_features:!1};function fe(t,e){return t.map((function(t){return t.source?t:tt(t,{id:t.id+"."+e,source:"hot"===e?l.HOT:l.COLD})}))}var de={exports:{}};!function(t,e){var n="__lodash_hash_undefined__",o=9007199254740991,i="[object Arguments]",a="[object Array]",s="[object Boolean]",u="[object Date]",c="[object Error]",l="[object Function]",p="[object Map]",f="[object Number]",d="[object Object]",h="[object Promise]",g="[object RegExp]",y="[object Set]",m="[object String]",v="[object Symbol]",b="[object WeakMap]",_="[object ArrayBuffer]",E="[object DataView]",O=/^\[object .+?Constructor\]$/,x=/^(?:0|[1-9]\d*)$/,S={};S["[object Float32Array]"]=S["[object Float64Array]"]=S["[object Int8Array]"]=S["[object Int16Array]"]=S["[object Int32Array]"]=S["[object Uint8Array]"]=S["[object Uint8ClampedArray]"]=S["[object Uint16Array]"]=S["[object Uint32Array]"]=!0,S[i]=S[a]=S[_]=S[s]=S[E]=S[u]=S[c]=S[l]=S[p]=S[f]=S[d]=S[g]=S[y]=S[m]=S[b]=!1;var w="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,C="object"==typeof self&&self&&self.Object===Object&&self,M=w||C||Function("return this")(),I=e&&!e.nodeType&&e,T=I&&t&&!t.nodeType&&t,P=T&&T.exports===I,L=P&&w.process,A=function(){try{return L&&L.binding&&L.binding("util")}catch(t){}}(),F=A&&A.isTypedArray;function N(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}function j(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}function R(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}var k,D,U,G=Array.prototype,B=Function.prototype,V=Object.prototype,Y=M["__core-js_shared__"],X=B.toString,J=V.hasOwnProperty,z=(k=/[^.]+$/.exec(Y&&Y.keys&&Y.keys.IE_PROTO||""))?"Symbol(src)_1."+k:"",q=V.toString,Z=RegExp("^"+X.call(J).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),H=P?M.Buffer:void 0,$=M.Symbol,W=M.Uint8Array,K=V.propertyIsEnumerable,Q=G.splice,tt=$?$.toStringTag:void 0,et=Object.getOwnPropertySymbols,rt=H?H.isBuffer:void 0,nt=(D=Object.keys,U=Object,function(t){return D(U(t))}),ot=At(M,"DataView"),it=At(M,"Map"),at=At(M,"Promise"),st=At(M,"Set"),ut=At(M,"WeakMap"),ct=At(Object,"create"),lt=Rt(ot),pt=Rt(it),ft=Rt(at),dt=Rt(st),ht=Rt(ut),gt=$?$.prototype:void 0,yt=gt?gt.valueOf:void 0;function mt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function vt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function bt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function _t(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new bt;++e<r;)this.add(t[e])}function Et(t){var e=this.__data__=new vt(t);this.size=e.size}function Ot(t,e){var r=Ut(t),n=!r&&Dt(t),o=!r&&!n&&Gt(t),i=!r&&!n&&!o&&Jt(t),a=r||n||o||i,s=a?function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}(t.length,String):[],u=s.length;for(var c in t)!e&&!J.call(t,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||jt(c,u))||s.push(c);return s}function xt(t,e){for(var r=t.length;r--;)if(kt(t[r][0],e))return r;return-1}function St(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":tt&&tt in Object(t)?function(t){var e=J.call(t,tt),r=t[tt];try{t[tt]=void 0;var n=!0}catch(t){}var o=q.call(t);return n&&(e?t[tt]=r:delete t[tt]),o}(t):function(t){return q.call(t)}(t)}function wt(t){return Xt(t)&&St(t)==i}function Ct(t,e,r,n,o){return t===e||(null==t||null==e||!Xt(t)&&!Xt(e)?t!=t&&e!=e:function(t,e,r,n,o,l){var h=Ut(t),b=Ut(e),O=h?a:Nt(t),x=b?a:Nt(e),S=(O=O==i?d:O)==d,w=(x=x==i?d:x)==d,C=O==x;if(C&&Gt(t)){if(!Gt(e))return!1;h=!0,S=!1}if(C&&!S)return l||(l=new Et),h||Jt(t)?Tt(t,e,r,n,o,l):function(t,e,r,n,o,i,a){switch(r){case E:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case _:return!(t.byteLength!=e.byteLength||!i(new W(t),new W(e)));case s:case u:case f:return kt(+t,+e);case c:return t.name==e.name&&t.message==e.message;case g:case m:return t==e+"";case p:var l=j;case y:var d=1&n;if(l||(l=R),t.size!=e.size&&!d)return!1;var h=a.get(t);if(h)return h==e;n|=2,a.set(t,e);var b=Tt(l(t),l(e),n,o,i,a);return a.delete(t),b;case v:if(yt)return yt.call(t)==yt.call(e)}return!1}(t,e,O,r,n,o,l);if(!(1&r)){var M=S&&J.call(t,"__wrapped__"),I=w&&J.call(e,"__wrapped__");if(M||I){var T=M?t.value():t,P=I?e.value():e;return l||(l=new Et),o(T,P,r,n,l)}}return!!C&&(l||(l=new Et),function(t,e,r,n,o,i){var a=1&r,s=Pt(t),u=s.length;if(u!=Pt(e).length&&!a)return!1;for(var c=u;c--;){var l=s[c];if(!(a?l in e:J.call(e,l)))return!1}var p=i.get(t);if(p&&i.get(e))return p==e;var f=!0;i.set(t,e),i.set(e,t);for(var d=a;++c<u;){var h=t[l=s[c]],g=e[l];if(n)var y=a?n(g,h,l,e,t,i):n(h,g,l,t,e,i);if(!(void 0===y?h===g||o(h,g,r,n,i):y)){f=!1;break}d||(d="constructor"==l)}if(f&&!d){var m=t.constructor,v=e.constructor;m==v||!("constructor"in t)||!("constructor"in e)||"function"==typeof m&&m instanceof m&&"function"==typeof v&&v instanceof v||(f=!1)}return i.delete(t),i.delete(e),f}(t,e,r,n,o,l))}(t,e,r,n,Ct,o))}function Mt(t){return!(!Yt(t)||function(t){return!!z&&z in t}(t))&&(Bt(t)?Z:O).test(Rt(t))}function It(t){if(r=(e=t)&&e.constructor,e!==("function"==typeof r&&r.prototype||V))return nt(t);var e,r,n=[];for(var o in Object(t))J.call(t,o)&&"constructor"!=o&&n.push(o);return n}function Tt(t,e,r,n,o,i){var a=1&r,s=t.length,u=e.length;if(s!=u&&!(a&&u>s))return!1;var c=i.get(t);if(c&&i.get(e))return c==e;var l=-1,p=!0,f=2&r?new _t:void 0;for(i.set(t,e),i.set(e,t);++l<s;){var d=t[l],h=e[l];if(n)var g=a?n(h,d,l,e,t,i):n(d,h,l,t,e,i);if(void 0!==g){if(g)continue;p=!1;break}if(f){if(!N(e,(function(t,e){if(a=e,!f.has(a)&&(d===t||o(d,t,r,n,i)))return f.push(e);var a}))){p=!1;break}}else if(d!==h&&!o(d,h,r,n,i)){p=!1;break}}return i.delete(t),i.delete(e),p}function Pt(t){return function(t,e,r){var n=e(t);return Ut(t)?n:function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}(n,r(t))}(t,zt,Ft)}function Lt(t,e){var r,n,o=t.__data__;return("string"==(n=typeof(r=e))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof e?"string":"hash"]:o.map}function At(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return Mt(r)?r:void 0}mt.prototype.clear=function(){this.__data__=ct?ct(null):{},this.size=0},mt.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},mt.prototype.get=function(t){var e=this.__data__;if(ct){var r=e[t];return r===n?void 0:r}return J.call(e,t)?e[t]:void 0},mt.prototype.has=function(t){var e=this.__data__;return ct?void 0!==e[t]:J.call(e,t)},mt.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=ct&&void 0===e?n:e,this},vt.prototype.clear=function(){this.__data__=[],this.size=0},vt.prototype.delete=function(t){var e=this.__data__,r=xt(e,t);return!(r<0||(r==e.length-1?e.pop():Q.call(e,r,1),--this.size,0))},vt.prototype.get=function(t){var e=this.__data__,r=xt(e,t);return r<0?void 0:e[r][1]},vt.prototype.has=function(t){return xt(this.__data__,t)>-1},vt.prototype.set=function(t,e){var r=this.__data__,n=xt(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this},bt.prototype.clear=function(){this.size=0,this.__data__={hash:new mt,map:new(it||vt),string:new mt}},bt.prototype.delete=function(t){var e=Lt(this,t).delete(t);return this.size-=e?1:0,e},bt.prototype.get=function(t){return Lt(this,t).get(t)},bt.prototype.has=function(t){return Lt(this,t).has(t)},bt.prototype.set=function(t,e){var r=Lt(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this},_t.prototype.add=_t.prototype.push=function(t){return this.__data__.set(t,n),this},_t.prototype.has=function(t){return this.__data__.has(t)},Et.prototype.clear=function(){this.__data__=new vt,this.size=0},Et.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},Et.prototype.get=function(t){return this.__data__.get(t)},Et.prototype.has=function(t){return this.__data__.has(t)},Et.prototype.set=function(t,e){var r=this.__data__;if(r instanceof vt){var n=r.__data__;if(!it||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new bt(n)}return r.set(t,e),this.size=r.size,this};var Ft=et?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}(et(t),(function(e){return K.call(t,e)})))}:function(){return[]},Nt=St;function jt(t,e){return!!(e=null==e?o:e)&&("number"==typeof t||x.test(t))&&t>-1&&t%1==0&&t<e}function Rt(t){if(null!=t){try{return X.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function kt(t,e){return t===e||t!=t&&e!=e}(ot&&Nt(new ot(new ArrayBuffer(1)))!=E||it&&Nt(new it)!=p||at&&Nt(at.resolve())!=h||st&&Nt(new st)!=y||ut&&Nt(new ut)!=b)&&(Nt=function(t){var e=St(t),r=e==d?t.constructor:void 0,n=r?Rt(r):"";if(n)switch(n){case lt:return E;case pt:return p;case ft:return h;case dt:return y;case ht:return b}return e});var Dt=wt(function(){return arguments}())?wt:function(t){return Xt(t)&&J.call(t,"callee")&&!K.call(t,"callee")},Ut=Array.isArray,Gt=rt||function(){return!1};function Bt(t){if(!Yt(t))return!1;var e=St(t);return e==l||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}function Vt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=o}function Yt(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Xt(t){return null!=t&&"object"==typeof t}var Jt=F?function(t){return function(e){return t(e)}}(F):function(t){return Xt(t)&&Vt(t.length)&&!!S[St(t)]};function zt(t){return null!=(e=t)&&Vt(e.length)&&!Bt(e)?Ot(t):It(t);var e}t.exports=function(t,e){return Ct(t,e)}}(de,de.exports);var he=e(de.exports);function ge(t,e){return t.length===e.length&&JSON.stringify(t.map((function(t){return t})).sort())===JSON.stringify(e.map((function(t){return t})).sort())}var ye={Polygon:G,LineString:U,Point:D,MultiPolygon:Y,MultiLineString:Y,MultiPoint:Y},me=Object.freeze({__proto__:null,CommonSelectors:ht,constrainFeatureMovement:Kt,createMidPoint:_t,createSupplementaryPoints:Et,createVertex:bt,doubleClickZoom:Ot,euclideanDistance:L,featuresAt:I,getFeatureAtAndSetCursors:P,isClick:A,isEventAtCoordinates:ie,isTap:F,mapEventToBoundingBox:w,ModeHandler:t,moveFeatures:Qt,sortFeatures:S,stringSetsAreEqual:ge,StringSet:C,theme:nt,toDenseArray:Z}),ve=function(t,e){var r={options:t=function(t){void 0===t&&(t={});var e=tt(t);return t.controls||(e.controls={}),!1===t.displayControlsDefault?e.controls=tt(pe,t.controls):e.controls=tt(le,t.controls),(e=tt(ce,e)).styles=fe(e.styles,"cold").concat(fe(e.styles,"hot")),e}(t)};e=function(t,e){return e.modes=h,e.getFeatureIdsAt=function(e){return I.click({point:e},null,t).map((function(t){return t.properties.id}))},e.getSelectedIds=function(){return t.store.getSelectedIds()},e.getSelected=function(){return{type:d.FEATURE_COLLECTION,features:t.store.getSelectedIds().map((function(e){return t.store.get(e)})).map((function(t){return t.toGeoJSON()}))}},e.getSelectedPoints=function(){return{type:d.FEATURE_COLLECTION,features:t.store.getSelectedCoordinates().map((function(t){return{type:d.FEATURE,properties:{},geometry:{type:d.POINT,coordinates:t.coordinates}}}))}},e.set=function(r){if(void 0===r.type||r.type!==d.FEATURE_COLLECTION||!Array.isArray(r.features))throw new Error("Invalid FeatureCollection");var n=t.store.createRenderBatch(),o=t.store.getAllIds().slice(),i=e.add(r),a=new C(i);return(o=o.filter((function(t){return!a.has(t)}))).length&&e.delete(o),n(),i},e.add=function(e){var r=JSON.parse(JSON.stringify(Ct(e))).features.map((function(e){if(e.id=e.id||R(),null===e.geometry)throw new Error("Invalid geometry: null");if(void 0===t.store.get(e.id)||t.store.get(e.id).type!==e.geometry.type){var r=ye[e.geometry.type];if(void 0===r)throw new Error("Invalid geometry type: "+e.geometry.type+".");var n=new r(t,e);t.store.add(n)}else{var o=t.store.get(e.id);o.properties=e.properties,he(o.getCoordinates(),e.geometry.coordinates)||o.incomingCoords(e.geometry.coordinates)}return e.id}));return t.store.render(),r},e.get=function(e){var r=t.store.get(e);if(r)return r.toGeoJSON()},e.getAll=function(){return{type:d.FEATURE_COLLECTION,features:t.store.getAll().map((function(t){return t.toGeoJSON()}))}},e.delete=function(r){return t.store.delete(r,{silent:!0}),e.getMode()!==h.DIRECT_SELECT||t.store.getSelectedIds().length?t.store.render():t.events.changeMode(h.SIMPLE_SELECT,void 0,{silent:!0}),e},e.deleteAll=function(){return t.store.delete(t.store.getAllIds(),{silent:!0}),e.getMode()===h.DIRECT_SELECT?t.events.changeMode(h.SIMPLE_SELECT,void 0,{silent:!0}):t.store.render(),e},e.changeMode=function(r,n){return void 0===n&&(n={}),r===h.SIMPLE_SELECT&&e.getMode()===h.SIMPLE_SELECT?(ge(n.featureIds||[],t.store.getSelectedIds())||(t.store.setSelected(n.featureIds,{silent:!0}),t.store.render()),e):(r===h.DIRECT_SELECT&&e.getMode()===h.DIRECT_SELECT&&n.featureId===t.store.getSelectedIds()[0]||t.events.changeMode(r,n,{silent:!0}),e)},e.getMode=function(){return t.events.getMode()},e.trash=function(){return t.events.trash({silent:!0}),e},e.combineFeatures=function(){return t.events.combineFeatures({silent:!0}),e},e.uncombineFeatures=function(){return t.events.uncombineFeatures({silent:!0}),e},e.setFeatureProperty=function(r,n,o){return t.store.setFeatureProperty(r,n,o),e},e}(r,e),r.api=e;var n=rt(r);return e.onAdd=n.onAdd,e.onRemove=n.onRemove,e.types=f,e.options=t,e};function be(t){ve(t,this)}return be.modes=ue,be.constants=E,be.lib=me,be}()},383:(t,e,r)=>{"use strict";var n=r(421);function o(t){var e=[1/0,1/0,-1/0,-1/0];return n.coordEach(t,(function(t){e[0]>t[0]&&(e[0]=t[0]),e[1]>t[1]&&(e[1]=t[1]),e[2]<t[0]&&(e[2]=t[0]),e[3]<t[1]&&(e[3]=t[1])})),e}o.default=o,e.A=o},967:(t,e)=>{"use strict";function r(t,e,r){void 0===r&&(r={});var n={type:"Feature"};return(0===r.id||r.id)&&(n.id=r.id),r.bbox&&(n.bbox=r.bbox),n.properties=e||{},n.geometry=t,n}function n(t,e,n){if(void 0===n&&(n={}),!t)throw new Error("coordinates is required");if(!Array.isArray(t))throw new Error("coordinates must be an Array");if(t.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!d(t[0])||!d(t[1]))throw new Error("coordinates must contain numbers");return r({type:"Point",coordinates:t},e,n)}function o(t,e,n){void 0===n&&(n={});for(var o=0,i=t;o<i.length;o++){var a=i[o];if(a.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var s=0;s<a[a.length-1].length;s++)if(a[a.length-1][s]!==a[0][s])throw new Error("First and last Position are not equivalent.")}return r({type:"Polygon",coordinates:t},e,n)}function i(t,e,n){if(void 0===n&&(n={}),t.length<2)throw new Error("coordinates must be an array of two or more positions");return r({type:"LineString",coordinates:t},e,n)}function a(t,e){void 0===e&&(e={});var r={type:"FeatureCollection"};return e.id&&(r.id=e.id),e.bbox&&(r.bbox=e.bbox),r.features=t,r}function s(t,e,n){return void 0===n&&(n={}),r({type:"MultiLineString",coordinates:t},e,n)}function u(t,e,n){return void 0===n&&(n={}),r({type:"MultiPoint",coordinates:t},e,n)}function c(t,e,n){return void 0===n&&(n={}),r({type:"MultiPolygon",coordinates:t},e,n)}function l(t,r){void 0===r&&(r="kilometers");var n=e.factors[r];if(!n)throw new Error(r+" units is invalid");return t*n}function p(t,r){void 0===r&&(r="kilometers");var n=e.factors[r];if(!n)throw new Error(r+" units is invalid");return t/n}function f(t){return 180*(t%(2*Math.PI))/Math.PI}function d(t){return!isNaN(t)&&null!==t&&!Array.isArray(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.earthRadius=6371008.8,e.factors={centimeters:100*e.earthRadius,centimetres:100*e.earthRadius,degrees:e.earthRadius/111325,feet:3.28084*e.earthRadius,inches:39.37*e.earthRadius,kilometers:e.earthRadius/1e3,kilometres:e.earthRadius/1e3,meters:e.earthRadius,metres:e.earthRadius,miles:e.earthRadius/1609.344,millimeters:1e3*e.earthRadius,millimetres:1e3*e.earthRadius,nauticalmiles:e.earthRadius/1852,radians:1,yards:1.0936*e.earthRadius},e.unitsFactors={centimeters:100,centimetres:100,degrees:1/111325,feet:3.28084,inches:39.37,kilometers:.001,kilometres:.001,meters:1,metres:1,miles:1/1609.344,millimeters:1e3,millimetres:1e3,nauticalmiles:1/1852,radians:1/e.earthRadius,yards:1.0936133},e.areaFactors={acres:247105e-9,centimeters:1e4,centimetres:1e4,feet:10.763910417,hectares:1e-4,inches:1550.003100006,kilometers:1e-6,kilometres:1e-6,meters:1,metres:1,miles:386e-9,millimeters:1e6,millimetres:1e6,yards:1.195990046},e.feature=r,e.geometry=function(t,e,r){switch(void 0===r&&(r={}),t){case"Point":return n(e).geometry;case"LineString":return i(e).geometry;case"Polygon":return o(e).geometry;case"MultiPoint":return u(e).geometry;case"MultiLineString":return s(e).geometry;case"MultiPolygon":return c(e).geometry;default:throw new Error(t+" is invalid")}},e.point=n,e.points=function(t,e,r){return void 0===r&&(r={}),a(t.map((function(t){return n(t,e)})),r)},e.polygon=o,e.polygons=function(t,e,r){return void 0===r&&(r={}),a(t.map((function(t){return o(t,e)})),r)},e.lineString=i,e.lineStrings=function(t,e,r){return void 0===r&&(r={}),a(t.map((function(t){return i(t,e)})),r)},e.featureCollection=a,e.multiLineString=s,e.multiPoint=u,e.multiPolygon=c,e.geometryCollection=function(t,e,n){return void 0===n&&(n={}),r({type:"GeometryCollection",geometries:t},e,n)},e.round=function(t,e){if(void 0===e&&(e=0),e&&!(e>=0))throw new Error("precision must be a positive number");var r=Math.pow(10,e||0);return Math.round(t*r)/r},e.radiansToLength=l,e.lengthToRadians=p,e.lengthToDegrees=function(t,e){return f(p(t,e))},e.bearingToAzimuth=function(t){var e=t%360;return e<0&&(e+=360),e},e.radiansToDegrees=f,e.degreesToRadians=function(t){return t%360*Math.PI/180},e.convertLength=function(t,e,r){if(void 0===e&&(e="kilometers"),void 0===r&&(r="kilometers"),!(t>=0))throw new Error("length must be a positive number");return l(p(t,e),r)},e.convertArea=function(t,r,n){if(void 0===r&&(r="meters"),void 0===n&&(n="kilometers"),!(t>=0))throw new Error("area must be a positive number");var o=e.areaFactors[r];if(!o)throw new Error("invalid original units");var i=e.areaFactors[n];if(!i)throw new Error("invalid final units");return t/o*i},e.isNumber=d,e.isObject=function(t){return!!t&&t.constructor===Object},e.validateBBox=function(t){if(!t)throw new Error("bbox is required");if(!Array.isArray(t))throw new Error("bbox must be an Array");if(4!==t.length&&6!==t.length)throw new Error("bbox must be an Array of 4 or 6 numbers");t.forEach((function(t){if(!d(t))throw new Error("bbox must only contain numbers")}))},e.validateId=function(t){if(!t)throw new Error("id is required");if(-1===["string","number"].indexOf(typeof t))throw new Error("id must be a number or a string")}},421:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(967);function o(t,e,r){if(null!==t)for(var n,i,a,s,u,c,l,p,f=0,d=0,h=t.type,g="FeatureCollection"===h,y="Feature"===h,m=g?t.features.length:1,v=0;v<m;v++){u=(p=!!(l=g?t.features[v].geometry:y?t.geometry:t)&&"GeometryCollection"===l.type)?l.geometries.length:1;for(var b=0;b<u;b++){var _=0,E=0;if(null!==(s=p?l.geometries[b]:l)){c=s.coordinates;var O=s.type;switch(f=!r||"Polygon"!==O&&"MultiPolygon"!==O?0:1,O){case null:break;case"Point":if(!1===e(c,d,v,_,E))return!1;d++,_++;break;case"LineString":case"MultiPoint":for(n=0;n<c.length;n++){if(!1===e(c[n],d,v,_,E))return!1;d++,"MultiPoint"===O&&_++}"LineString"===O&&_++;break;case"Polygon":case"MultiLineString":for(n=0;n<c.length;n++){for(i=0;i<c[n].length-f;i++){if(!1===e(c[n][i],d,v,_,E))return!1;d++}"MultiLineString"===O&&_++,"Polygon"===O&&E++}"Polygon"===O&&_++;break;case"MultiPolygon":for(n=0;n<c.length;n++){for(E=0,i=0;i<c[n].length;i++){for(a=0;a<c[n][i].length-f;a++){if(!1===e(c[n][i][a],d,v,_,E))return!1;d++}E++}_++}break;case"GeometryCollection":for(n=0;n<s.geometries.length;n++)if(!1===o(s.geometries[n],e,r))return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function i(t,e){var r;switch(t.type){case"FeatureCollection":for(r=0;r<t.features.length&&!1!==e(t.features[r].properties,r);r++);break;case"Feature":e(t.properties,0)}}function a(t,e){if("Feature"===t.type)e(t,0);else if("FeatureCollection"===t.type)for(var r=0;r<t.features.length&&!1!==e(t.features[r],r);r++);}function s(t,e){var r,n,o,i,a,s,u,c,l,p,f=0,d="FeatureCollection"===t.type,h="Feature"===t.type,g=d?t.features.length:1;for(r=0;r<g;r++){for(s=d?t.features[r].geometry:h?t.geometry:t,c=d?t.features[r].properties:h?t.properties:{},l=d?t.features[r].bbox:h?t.bbox:void 0,p=d?t.features[r].id:h?t.id:void 0,a=(u=!!s&&"GeometryCollection"===s.type)?s.geometries.length:1,o=0;o<a;o++)if(null!==(i=u?s.geometries[o]:s))switch(i.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":if(!1===e(i,f,c,l,p))return!1;break;case"GeometryCollection":for(n=0;n<i.geometries.length;n++)if(!1===e(i.geometries[n],f,c,l,p))return!1;break;default:throw new Error("Unknown Geometry Type")}else if(!1===e(null,f,c,l,p))return!1;f++}}function u(t,e){s(t,(function(t,r,o,i,a){var s,u=null===t?null:t.type;switch(u){case null:case"Point":case"LineString":case"Polygon":return!1!==e(n.feature(t,o,{bbox:i,id:a}),r,0)&&void 0}switch(u){case"MultiPoint":s="Point";break;case"MultiLineString":s="LineString";break;case"MultiPolygon":s="Polygon"}for(var c=0;c<t.coordinates.length;c++){var l={type:s,coordinates:t.coordinates[c]};if(!1===e(n.feature(l,o),r,c))return!1}}))}function c(t,e){u(t,(function(t,r,i){var a=0;if(t.geometry){var s=t.geometry.type;if("Point"!==s&&"MultiPoint"!==s){var u,c=0,l=0,p=0;return!1!==o(t,(function(o,s,f,d,h){if(void 0===u||r>c||d>l||h>p)return u=o,c=r,l=d,p=h,void(a=0);var g=n.lineString([u,o],t.properties);if(!1===e(g,r,i,h,a))return!1;a++,u=o}))&&void 0}}}))}function l(t,e){if(!t)throw new Error("geojson is required");u(t,(function(t,r,o){if(null!==t.geometry){var i=t.geometry.type,a=t.geometry.coordinates;switch(i){case"LineString":if(!1===e(t,r,o,0,0))return!1;break;case"Polygon":for(var s=0;s<a.length;s++)if(!1===e(n.lineString(a[s],t.properties),r,o,s))return!1}}}))}e.coordAll=function(t){var e=[];return o(t,(function(t){e.push(t)})),e},e.coordEach=o,e.coordReduce=function(t,e,r,n){var i=r;return o(t,(function(t,n,o,a,s){i=0===n&&void 0===r?t:e(i,t,n,o,a,s)}),n),i},e.featureEach=a,e.featureReduce=function(t,e,r){var n=r;return a(t,(function(t,o){n=0===o&&void 0===r?t:e(n,t,o)})),n},e.findPoint=function(t,e){if(e=e||{},!n.isObject(e))throw new Error("options is invalid");var r,o=e.featureIndex||0,i=e.multiFeatureIndex||0,a=e.geometryIndex||0,s=e.coordIndex||0,u=e.properties;switch(t.type){case"FeatureCollection":o<0&&(o=t.features.length+o),u=u||t.features[o].properties,r=t.features[o].geometry;break;case"Feature":u=u||t.properties,r=t.geometry;break;case"Point":case"MultiPoint":return null;case"LineString":case"Polygon":case"MultiLineString":case"MultiPolygon":r=t;break;default:throw new Error("geojson is invalid")}if(null===r)return null;var c=r.coordinates;switch(r.type){case"Point":return n.point(c,u,e);case"MultiPoint":return i<0&&(i=c.length+i),n.point(c[i],u,e);case"LineString":return s<0&&(s=c.length+s),n.point(c[s],u,e);case"Polygon":return a<0&&(a=c.length+a),s<0&&(s=c[a].length+s),n.point(c[a][s],u,e);case"MultiLineString":return i<0&&(i=c.length+i),s<0&&(s=c[i].length+s),n.point(c[i][s],u,e);case"MultiPolygon":return i<0&&(i=c.length+i),a<0&&(a=c[i].length+a),s<0&&(s=c[i][a].length-s),n.point(c[i][a][s],u,e)}throw new Error("geojson is invalid")},e.findSegment=function(t,e){if(e=e||{},!n.isObject(e))throw new Error("options is invalid");var r,o=e.featureIndex||0,i=e.multiFeatureIndex||0,a=e.geometryIndex||0,s=e.segmentIndex||0,u=e.properties;switch(t.type){case"FeatureCollection":o<0&&(o=t.features.length+o),u=u||t.features[o].properties,r=t.features[o].geometry;break;case"Feature":u=u||t.properties,r=t.geometry;break;case"Point":case"MultiPoint":return null;case"LineString":case"Polygon":case"MultiLineString":case"MultiPolygon":r=t;break;default:throw new Error("geojson is invalid")}if(null===r)return null;var c=r.coordinates;switch(r.type){case"Point":case"MultiPoint":return null;case"LineString":return s<0&&(s=c.length+s-1),n.lineString([c[s],c[s+1]],u,e);case"Polygon":return a<0&&(a=c.length+a),s<0&&(s=c[a].length+s-1),n.lineString([c[a][s],c[a][s+1]],u,e);case"MultiLineString":return i<0&&(i=c.length+i),s<0&&(s=c[i].length+s-1),n.lineString([c[i][s],c[i][s+1]],u,e);case"MultiPolygon":return i<0&&(i=c.length+i),a<0&&(a=c[i].length+a),s<0&&(s=c[i][a].length-s-1),n.lineString([c[i][a][s],c[i][a][s+1]],u,e)}throw new Error("geojson is invalid")},e.flattenEach=u,e.flattenReduce=function(t,e,r){var n=r;return u(t,(function(t,o,i){n=0===o&&0===i&&void 0===r?t:e(n,t,o,i)})),n},e.geomEach=s,e.geomReduce=function(t,e,r){var n=r;return s(t,(function(t,o,i,a,s){n=0===o&&void 0===r?t:e(n,t,o,i,a,s)})),n},e.lineEach=l,e.lineReduce=function(t,e,r){var n=r;return l(t,(function(t,o,i,a){n=0===o&&void 0===r?t:e(n,t,o,i,a)})),n},e.propEach=i,e.propReduce=function(t,e,r){var n=r;return i(t,(function(t,o){n=0===o&&void 0===r?t:e(n,t,o)})),n},e.segmentEach=c,e.segmentReduce=function(t,e,r){var n=r,o=!1;return c(t,(function(t,i,a,s,u){n=!1===o&&void 0===r?t:e(n,t,i,a,s,u),o=!0})),n}},945:(t,e,r)=>{var n=r(341),o=r(967),i=r(421),a=r(383).A,s=i.featureEach,u=(i.coordEach,o.polygon,o.featureCollection);function c(t){var e=new n(t);return e.insert=function(t){if("Feature"!==t.type)throw new Error("invalid feature");return t.bbox=t.bbox?t.bbox:a(t),n.prototype.insert.call(this,t)},e.load=function(t){var e=[];return Array.isArray(t)?t.forEach((function(t){if("Feature"!==t.type)throw new Error("invalid features");t.bbox=t.bbox?t.bbox:a(t),e.push(t)})):s(t,(function(t){if("Feature"!==t.type)throw new Error("invalid features");t.bbox=t.bbox?t.bbox:a(t),e.push(t)})),n.prototype.load.call(this,e)},e.remove=function(t,e){if("Feature"!==t.type)throw new Error("invalid feature");return t.bbox=t.bbox?t.bbox:a(t),n.prototype.remove.call(this,t,e)},e.clear=function(){return n.prototype.clear.call(this)},e.search=function(t){var e=n.prototype.search.call(this,this.toBBox(t));return u(e)},e.collides=function(t){return n.prototype.collides.call(this,this.toBBox(t))},e.all=function(){var t=n.prototype.all.call(this);return u(t)},e.toJSON=function(){return n.prototype.toJSON.call(this)},e.fromJSON=function(t){return n.prototype.fromJSON.call(this,t)},e.toBBox=function(t){var e;if(t.bbox)e=t.bbox;else if(Array.isArray(t)&&4===t.length)e=t;else if(Array.isArray(t)&&6===t.length)e=[t[0],t[1],t[3],t[4]];else if("Feature"===t.type)e=a(t);else{if("FeatureCollection"!==t.type)throw new Error("invalid geojson");e=a(t)}return{minX:e[0],minY:e[1],maxX:e[2],maxY:e[3]}},e}t.exports=c,t.exports.default=c},341:function(t){t.exports=function(){"use strict";function t(t,n,o,i,a){!function t(r,n,o,i,a){for(;i>o;){if(i-o>600){var s=i-o+1,u=n-o+1,c=Math.log(s),l=.5*Math.exp(2*c/3),p=.5*Math.sqrt(c*l*(s-l)/s)*(u-s/2<0?-1:1);t(r,n,Math.max(o,Math.floor(n-u*l/s+p)),Math.min(i,Math.floor(n+(s-u)*l/s+p)),a)}var f=r[n],d=o,h=i;for(e(r,o,n),a(r[i],f)>0&&e(r,o,i);d<h;){for(e(r,d,h),d++,h--;a(r[d],f)<0;)d++;for(;a(r[h],f)>0;)h--}0===a(r[o],f)?e(r,o,h):e(r,++h,i),h<=n&&(o=h+1),n<=h&&(i=h-1)}}(t,n,o||0,i||t.length-1,a||r)}function e(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function r(t,e){return t<e?-1:t>e?1:0}var n=function(t){void 0===t&&(t=9),this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function o(t,e,r){if(!r)return e.indexOf(t);for(var n=0;n<e.length;n++)if(r(t,e[n]))return n;return-1}function i(t,e){a(t,0,t.children.length,e,t)}function a(t,e,r,n,o){o||(o=h(null)),o.minX=1/0,o.minY=1/0,o.maxX=-1/0,o.maxY=-1/0;for(var i=e;i<r;i++){var a=t.children[i];s(o,t.leaf?n(a):a)}return o}function s(t,e){return t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY),t}function u(t,e){return t.minX-e.minX}function c(t,e){return t.minY-e.minY}function l(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function p(t){return t.maxX-t.minX+(t.maxY-t.minY)}function f(t,e){return t.minX<=e.minX&&t.minY<=e.minY&&e.maxX<=t.maxX&&e.maxY<=t.maxY}function d(t,e){return e.minX<=t.maxX&&e.minY<=t.maxY&&e.maxX>=t.minX&&e.maxY>=t.minY}function h(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function g(e,r,n,o,i){for(var a=[r,n];a.length;)if(!((n=a.pop())-(r=a.pop())<=o)){var s=r+Math.ceil((n-r)/o/2)*o;t(e,s,r,n,i),a.push(r,s,s,n)}}return n.prototype.all=function(){return this._all(this.data,[])},n.prototype.search=function(t){var e=this.data,r=[];if(!d(t,e))return r;for(var n=this.toBBox,o=[];e;){for(var i=0;i<e.children.length;i++){var a=e.children[i],s=e.leaf?n(a):a;d(t,s)&&(e.leaf?r.push(a):f(t,s)?this._all(a,r):o.push(a))}e=o.pop()}return r},n.prototype.collides=function(t){var e=this.data;if(!d(t,e))return!1;for(var r=[];e;){for(var n=0;n<e.children.length;n++){var o=e.children[n],i=e.leaf?this.toBBox(o):o;if(d(t,i)){if(e.leaf||f(t,i))return!0;r.push(o)}}e=r.pop()}return!1},n.prototype.load=function(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(var e=0;e<t.length;e++)this.insert(t[e]);return this}var r=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===r.height)this._splitRoot(this.data,r);else{if(this.data.height<r.height){var n=this.data;this.data=r,r=n}this._insert(r,this.data.height-r.height-1,!0)}else this.data=r;return this},n.prototype.insert=function(t){return t&&this._insert(t,this.data.height-1),this},n.prototype.clear=function(){return this.data=h([]),this},n.prototype.remove=function(t,e){if(!t)return this;for(var r,n,i,a=this.data,s=this.toBBox(t),u=[],c=[];a||u.length;){if(a||(a=u.pop(),n=u[u.length-1],r=c.pop(),i=!0),a.leaf){var l=o(t,a.children,e);if(-1!==l)return a.children.splice(l,1),u.push(a),this._condense(u),this}i||a.leaf||!f(a,s)?n?(r++,a=n.children[r],i=!1):a=null:(u.push(a),c.push(r),r=0,n=a,a=a.children[0])}return this},n.prototype.toBBox=function(t){return t},n.prototype.compareMinX=function(t,e){return t.minX-e.minX},n.prototype.compareMinY=function(t,e){return t.minY-e.minY},n.prototype.toJSON=function(){return this.data},n.prototype.fromJSON=function(t){return this.data=t,this},n.prototype._all=function(t,e){for(var r=[];t;)t.leaf?e.push.apply(e,t.children):r.push.apply(r,t.children),t=r.pop();return e},n.prototype._build=function(t,e,r,n){var o,a=r-e+1,s=this._maxEntries;if(a<=s)return i(o=h(t.slice(e,r+1)),this.toBBox),o;n||(n=Math.ceil(Math.log(a)/Math.log(s)),s=Math.ceil(a/Math.pow(s,n-1))),(o=h([])).leaf=!1,o.height=n;var u=Math.ceil(a/s),c=u*Math.ceil(Math.sqrt(s));g(t,e,r,c,this.compareMinX);for(var l=e;l<=r;l+=c){var p=Math.min(l+c-1,r);g(t,l,p,u,this.compareMinY);for(var f=l;f<=p;f+=u){var d=Math.min(f+u-1,p);o.children.push(this._build(t,f,d,n-1))}}return i(o,this.toBBox),o},n.prototype._chooseSubtree=function(t,e,r,n){for(;n.push(e),!e.leaf&&n.length-1!==r;){for(var o=1/0,i=1/0,a=void 0,s=0;s<e.children.length;s++){var u=e.children[s],c=l(u),p=(f=t,d=u,(Math.max(d.maxX,f.maxX)-Math.min(d.minX,f.minX))*(Math.max(d.maxY,f.maxY)-Math.min(d.minY,f.minY))-c);p<i?(i=p,o=c<o?c:o,a=u):p===i&&c<o&&(o=c,a=u)}e=a||e.children[0]}var f,d;return e},n.prototype._insert=function(t,e,r){var n=r?t:this.toBBox(t),o=[],i=this._chooseSubtree(n,this.data,e,o);for(i.children.push(t),s(i,n);e>=0&&o[e].children.length>this._maxEntries;)this._split(o,e),e--;this._adjustParentBBoxes(n,o,e)},n.prototype._split=function(t,e){var r=t[e],n=r.children.length,o=this._minEntries;this._chooseSplitAxis(r,o,n);var a=this._chooseSplitIndex(r,o,n),s=h(r.children.splice(a,r.children.length-a));s.height=r.height,s.leaf=r.leaf,i(r,this.toBBox),i(s,this.toBBox),e?t[e-1].children.push(s):this._splitRoot(r,s)},n.prototype._splitRoot=function(t,e){this.data=h([t,e]),this.data.height=t.height+1,this.data.leaf=!1,i(this.data,this.toBBox)},n.prototype._chooseSplitIndex=function(t,e,r){for(var n,o,i,s,u,c,p,f=1/0,d=1/0,h=e;h<=r-e;h++){var g=a(t,0,h,this.toBBox),y=a(t,h,r,this.toBBox),m=(o=g,i=y,s=void 0,u=void 0,c=void 0,p=void 0,s=Math.max(o.minX,i.minX),u=Math.max(o.minY,i.minY),c=Math.min(o.maxX,i.maxX),p=Math.min(o.maxY,i.maxY),Math.max(0,c-s)*Math.max(0,p-u)),v=l(g)+l(y);m<f?(f=m,n=h,d=v<d?v:d):m===f&&v<d&&(d=v,n=h)}return n||r-e},n.prototype._chooseSplitAxis=function(t,e,r){var n=t.leaf?this.compareMinX:u,o=t.leaf?this.compareMinY:c;this._allDistMargin(t,e,r,n)<this._allDistMargin(t,e,r,o)&&t.children.sort(n)},n.prototype._allDistMargin=function(t,e,r,n){t.children.sort(n);for(var o=this.toBBox,i=a(t,0,e,o),u=a(t,r-e,r,o),c=p(i)+p(u),l=e;l<r-e;l++){var f=t.children[l];s(i,t.leaf?o(f):f),c+=p(i)}for(var d=r-e-1;d>=e;d--){var h=t.children[d];s(u,t.leaf?o(h):h),c+=p(u)}return c},n.prototype._adjustParentBBoxes=function(t,e,r){for(var n=r;n>=0;n--)s(e[n],t)},n.prototype._condense=function(t){for(var e=t.length-1,r=void 0;e>=0;e--)0===t[e].children.length?e>0?(r=t[e-1].children).splice(r.indexOf(t[e]),1):this.clear():i(t[e],this.toBBox)},n}()}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n].call(i.exports,i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};return(()=>{"use strict";r.r(n),r.d(n,{SnapDirectSelect:()=>Zt,SnapLineMode:()=>Mt,SnapModeDrawStyles:()=>ee,SnapPointMode:()=>gt,SnapPolygonMode:()=>Ut,Utils:()=>t});var t={};r.r(t),r.d(t,{IDS:()=>$,addPointTovertices:()=>W,createSnapList:()=>K,getGuideFeature:()=>nt,shouldHideGuide:()=>ot,snap:()=>rt});var e=r(188),o=r.n(e),i=6371008.8,a={centimeters:637100880,centimetres:637100880,degrees:57.22891354143274,feet:20902260.511392,inches:39.37*i,kilometers:6371.0088,kilometres:6371.0088,meters:i,metres:i,miles:3958.************,millimeters:6371008800,millimetres:6371008800,nauticalmiles:i/1852,radians:1,yards:6967335.223679999};function s(t,e,r){void 0===r&&(r={});var n={type:"Feature"};return(0===r.id||r.id)&&(n.id=r.id),r.bbox&&(n.bbox=r.bbox),n.properties=e||{},n.geometry=t,n}function u(t,e,r){if(void 0===r&&(r={}),!t)throw new Error("coordinates is required");if(!Array.isArray(t))throw new Error("coordinates must be an Array");if(t.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!m(t[0])||!m(t[1]))throw new Error("coordinates must contain numbers");return s({type:"Point",coordinates:t},e,r)}function c(t,e,r){void 0===r&&(r={});for(var n=0,o=t;n<o.length;n++){var i=o[n];if(i.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var a=0;a<i[i.length-1].length;a++)if(i[i.length-1][a]!==i[0][a])throw new Error("First and last Position are not equivalent.")}return s({type:"Polygon",coordinates:t},e,r)}function l(t,e,r){if(void 0===r&&(r={}),t.length<2)throw new Error("coordinates must be an array of two or more positions");return s({type:"LineString",coordinates:t},e,r)}function p(t,e){void 0===e&&(e={});var r={type:"FeatureCollection"};return e.id&&(r.id=e.id),e.bbox&&(r.bbox=e.bbox),r.features=t,r}function f(t,e,r){return void 0===r&&(r={}),s({type:"MultiLineString",coordinates:t},e,r)}function d(t,e){void 0===e&&(e="kilometers");var r=a[e];if(!r)throw new Error(e+" units is invalid");return t*r}function h(t,e){void 0===e&&(e="kilometers");var r=a[e];if(!r)throw new Error(e+" units is invalid");return t/r}function g(t){return 180*(t%(2*Math.PI))/Math.PI}function y(t){return t%360*Math.PI/180}function m(t){return!isNaN(t)&&null!==t&&!Array.isArray(t)}function v(t){if(!t)throw new Error("coord is required");if(!Array.isArray(t)){if("Feature"===t.type&&null!==t.geometry&&"Point"===t.geometry.type)return t.geometry.coordinates;if("Point"===t.type)return t.coordinates}if(Array.isArray(t)&&t.length>=2&&!Array.isArray(t[0])&&!Array.isArray(t[1]))return t;throw new Error("coord must be GeoJSON Point or an Array of numbers")}function b(t){if(Array.isArray(t))return t;if("Feature"===t.type){if(null!==t.geometry)return t.geometry.coordinates}else if(t.coordinates)return t.coordinates;throw new Error("coords must be GeoJSON Feature, Geometry Object or an Array")}function _(t){return"Feature"===t.type?t.geometry:t}function E(t,e,r){if(void 0===r&&(r={}),!t)throw new Error("point is required");if(!e)throw new Error("polygon is required");var n=v(t),o=_(e),i=o.type,a=e.bbox,s=o.coordinates;if(a&&!1===function(t,e){return e[0]<=t[0]&&e[1]<=t[1]&&e[2]>=t[0]&&e[3]>=t[1]}(n,a))return!1;"Polygon"===i&&(s=[s]);for(var u=!1,c=0;c<s.length&&!u;c++)if(O(n,s[c][0],r.ignoreBoundary)){for(var l=!1,p=1;p<s[c].length&&!l;)O(n,s[c][p],!r.ignoreBoundary)&&(l=!0),p++;l||(u=!0)}return u}function O(t,e,r){var n=!1;e[0][0]===e[e.length-1][0]&&e[0][1]===e[e.length-1][1]&&(e=e.slice(0,e.length-1));for(var o=0,i=e.length-1;o<e.length;i=o++){var a=e[o][0],s=e[o][1],u=e[i][0],c=e[i][1];if(t[1]*(a-u)+s*(u-t[0])+c*(t[0]-a)==0&&(a-t[0])*(u-t[0])<=0&&(s-t[1])*(c-t[1])<=0)return!r;s>t[1]!=c>t[1]&&t[0]<(u-a)*(t[1]-s)/(c-s)+a&&(n=!n)}return n}function x(t,e){if("Feature"===t.type)e(t,0);else if("FeatureCollection"===t.type)for(var r=0;r<t.features.length&&!1!==e(t.features[r],r);r++);}function S(t,e){var r,n,o,i,a,s,u,c,l,p,f=0,d="FeatureCollection"===t.type,h="Feature"===t.type,g=d?t.features.length:1;for(r=0;r<g;r++){for(s=d?t.features[r].geometry:h?t.geometry:t,c=d?t.features[r].properties:h?t.properties:{},l=d?t.features[r].bbox:h?t.bbox:void 0,p=d?t.features[r].id:h?t.id:void 0,a=(u=!!s&&"GeometryCollection"===s.type)?s.geometries.length:1,o=0;o<a;o++)if(null!==(i=u?s.geometries[o]:s))switch(i.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":if(!1===e(i,f,c,l,p))return!1;break;case"GeometryCollection":for(n=0;n<i.geometries.length;n++)if(!1===e(i.geometries[n],f,c,l,p))return!1;break;default:throw new Error("Unknown Geometry Type")}else if(!1===e(null,f,c,l,p))return!1;f++}}function w(t,e){S(t,(function(t,r,n,o,i){var a,u=null===t?null:t.type;switch(u){case null:case"Point":case"LineString":case"Polygon":return!1!==e(s(t,n,{bbox:o,id:i}),r,0)&&void 0}switch(u){case"MultiPoint":a="Point";break;case"MultiLineString":a="LineString";break;case"MultiPolygon":a="Polygon"}for(var c=0;c<t.coordinates.length;c++){var l=t.coordinates[c];if(!1===e(s({type:a,coordinates:l},n),r,c))return!1}}))}const C=function(t){if(!t)throw new Error("geojson is required");var e=[];return w(t,(function(t){!function(t,e){var r=[],n=t.geometry;if(null!==n){switch(n.type){case"Polygon":r=b(n);break;case"LineString":r=[b(n)]}r.forEach((function(r){var n=function(t,e){var r=[];return t.reduce((function(t,n){var o,i,a,s,u,c,p=l([t,n],e);return p.bbox=(i=n,a=(o=t)[0],s=o[1],u=i[0],c=i[1],[a<u?a:u,s<c?s:c,a>u?a:u,s>c?s:c]),r.push(p),n})),r}(r,t.properties);n.forEach((function(t){t.id=e.length,e.push(t)}))}))}}(t,e)})),p(e)};var M=r(945);function I(t,e){var r=b(t),n=b(e);if(2!==r.length)throw new Error("<intersects> line1 must only contain 2 coordinates");if(2!==n.length)throw new Error("<intersects> line2 must only contain 2 coordinates");var o=r[0][0],i=r[0][1],a=r[1][0],s=r[1][1],c=n[0][0],l=n[0][1],p=n[1][0],f=n[1][1],d=(f-l)*(a-o)-(p-c)*(s-i),h=(p-c)*(i-l)-(f-l)*(o-c),g=(a-o)*(i-l)-(s-i)*(o-c);if(0===d)return null;var y=h/d,m=g/d;return y>=0&&y<=1&&m>=0&&m<=1?u([o+y*(a-o),i+y*(s-i)]):null}const T=function(t,e){var r={},n=[];if("LineString"===t.type&&(t=s(t)),"LineString"===e.type&&(e=s(e)),"Feature"===t.type&&"Feature"===e.type&&null!==t.geometry&&null!==e.geometry&&"LineString"===t.geometry.type&&"LineString"===e.geometry.type&&2===t.geometry.coordinates.length&&2===e.geometry.coordinates.length){var o=I(t,e);return o&&n.push(o),p(n)}var i=M();return i.load(C(e)),x(C(t),(function(t){x(i.search(t),(function(e){var o=I(t,e);if(o){var i=b(o).join(",");r[i]||(r[i]=!0,n.push(o))}}))})),p(n)};function P(t,e){void 0===e&&(e={});var r=_(t);switch(e.properties||"Feature"!==t.type||(e.properties=t.properties),r.type){case"Polygon":return function(t,e){void 0===e&&(e={});var r=_(t),n=r.coordinates,o=e.properties?e.properties:"Feature"===t.type?t.properties:{};return L(n,o)}(r,e);case"MultiPolygon":return function(t,e){void 0===e&&(e={});var r=_(t),n=r.coordinates,o=e.properties?e.properties:"Feature"===t.type?t.properties:{},i=[];return n.forEach((function(t){i.push(L(t,o))})),p(i)}(r,e);default:throw new Error("invalid poly")}}function L(t,e){return t.length>1?f(t,e):l(t[0],e)}function A(t,e){for(var r=0;r<t.coordinates.length-1;r++)if(N(t.coordinates[r],t.coordinates[r+1],e.coordinates))return!0;return!1}function F(t,e){for(var r=0,n=e.coordinates;r<n.length;r++){if(E(n[r],t))return!0}return T(e,P(t)).features.length>0}function N(t,e,r){var n=r[0]-t[0],o=r[1]-t[1],i=e[0]-t[0],a=e[1]-t[1];return 0==n*a-o*i&&(Math.abs(i)>=Math.abs(a)?i>0?t[0]<=r[0]&&r[0]<=e[0]:e[0]<=r[0]&&r[0]<=t[0]:a>0?t[1]<=r[1]&&r[1]<=e[1]:e[1]<=r[1]&&r[1]<=t[1])}const j=function(t,e){var r=!0;return w(t,(function(t){w(e,(function(e){if(!1===r)return!1;r=function(t,e){switch(t.type){case"Point":switch(e.type){case"Point":return r=t.coordinates,n=e.coordinates,!(r[0]===n[0]&&r[1]===n[1]);case"LineString":return!A(e,t);case"Polygon":return!E(t,e)}break;case"LineString":switch(e.type){case"Point":return!A(t,e);case"LineString":return!function(t,e){if(T(t,e).features.length>0)return!0;return!1}(t,e);case"Polygon":return!F(e,t)}break;case"Polygon":switch(e.type){case"Point":return!E(e,t);case"LineString":return!F(t,e);case"Polygon":return!function(t,e){for(var r=0,n=t.coordinates[0];r<n.length;r++){if(E(n[r],e))return!0}for(var o=0,i=e.coordinates[0];o<i.length;o++){if(E(i[o],t))return!0}var a=T(P(t),P(e));if(a.features.length>0)return!0;return!1}(e,t)}}var r,n;return!1}(t.geometry,e.geometry)}))})),r};const R=function(t,e,r){void 0===r&&(r={});var n=v(t),o=v(e),i=y(o[1]-n[1]),a=y(o[0]-n[0]),s=y(n[1]),u=y(o[1]),c=Math.pow(Math.sin(i/2),2)+Math.pow(Math.sin(a/2),2)*Math.cos(s)*Math.cos(u);return d(2*Math.atan2(Math.sqrt(c),Math.sqrt(1-c)),r.units)};function k(t,e,r){if(void 0===r&&(r={}),!0===r.final)return function(t,e){var r=k(e,t);return r=(r+180)%360}(t,e);var n=v(t),o=v(e),i=y(n[0]),a=y(o[0]),s=y(n[1]),u=y(o[1]),c=Math.sin(a-i)*Math.cos(u),l=Math.cos(s)*Math.sin(u)-Math.sin(s)*Math.cos(u)*Math.cos(a-i);return g(Math.atan2(c,l))}function D(t,e,r,n){void 0===n&&(n={});var o=v(t),i=y(o[0]),a=y(o[1]),s=y(r),c=h(e,n.units),l=Math.asin(Math.sin(a)*Math.cos(c)+Math.cos(a)*Math.sin(c)*Math.cos(s));return u([g(i+Math.atan2(Math.sin(s)*Math.sin(c)*Math.cos(a),Math.cos(c)-Math.sin(a)*Math.sin(l))),g(l)],n.properties)}const U=function(t,e,r){void 0===r&&(r={});var n=u([1/0,1/0],{dist:1/0}),o=0;return w(t,(function(t){for(var i=b(t),a=0;a<i.length-1;a++){var s=u(i[a]);s.properties.dist=R(e,s,r);var c=u(i[a+1]);c.properties.dist=R(e,c,r);var p=R(s,c,r),f=Math.max(s.properties.dist,c.properties.dist),d=k(s,c),h=D(e,f,d+90,r),g=D(e,f,d-90,r),y=T(l([h.geometry.coordinates,g.geometry.coordinates]),l([s.geometry.coordinates,c.geometry.coordinates])),m=null;y.features.length>0&&((m=y.features[0]).properties.dist=R(e,m,r),m.properties.location=o+R(s,m,r)),s.properties.dist<n.properties.dist&&((n=s).properties.index=a,n.properties.location=o),c.properties.dist<n.properties.dist&&((n=c).properties.index=a+1,n.properties.location=o+p),m&&m.properties.dist<n.properties.dist&&((n=m).properties.index=a),o+=p}})),n};function G(t){var e={type:"Feature"};return Object.keys(t).forEach((function(r){switch(r){case"type":case"properties":case"geometry":return;default:e[r]=t[r]}})),e.properties=B(t.properties),e.geometry=V(t.geometry),e}function B(t){var e={};return t?(Object.keys(t).forEach((function(r){var n=t[r];"object"==typeof n?null===n?e[r]=null:Array.isArray(n)?e[r]=n.map((function(t){return t})):e[r]=B(n):e[r]=n})),e):e}function V(t){var e={type:t.type};return t.bbox&&(e.bbox=t.bbox),"GeometryCollection"===t.type?(e.geometries=t.geometries.map((function(t){return V(t)})),e):(e.coordinates=Y(t.coordinates),e)}function Y(t){var e=t;return"object"!=typeof e[0]?e.slice():e.map((function(t){return Y(t)}))}const X=function(t){if(!t)throw new Error("geojson is required");switch(t.type){case"Feature":return G(t);case"FeatureCollection":return function(t){var e={type:"FeatureCollection"};return Object.keys(t).forEach((function(r){switch(r){case"type":case"features":return;default:e[r]=t[r]}})),e.features=t.features.map((function(t){return G(t)})),e}(t);case"Point":case"LineString":case"Polygon":case"MultiPoint":case"MultiLineString":case"MultiPolygon":case"GeometryCollection":return V(t);default:throw new Error("unknown GeoJSON type")}};const J=function(t,e){if(!t)throw new Error("targetPoint is required");if(!e)throw new Error("points is required");var r,n=1/0,o=0;return x(e,(function(e,r){var i=R(t,e);i<n&&(o=r,n=i)})),(r=X(e.features[o])).properties.featureIndex=o,r.properties.distanceToPoint=n,r};const z=function(t,e){return D(t,R(t,e)/2,k(t,e))};function q(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,i=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Z(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Z(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var H=o().constants.geojsonTypes,$={VERTICAL_GUIDE:"VERTICAL_GUIDE",HORIZONTAL_GUIDE:"HORIZONTAL_GUIDE"},W=function(t,e,r,n){var o=t.getCanvas(),i=o.width,a=o.height,s=t.project(r),u=s.x,c=s.y;(u>0&&u<i&&c>0&&c<a||n)&&e.push(r)},K=function(t,e,r){var n=e.getAll().features,o=[],i=function(){var e=t.getCanvas(),r=e.width,n=e.height,o=(t.unproject([0,0]).toArray(),t.unproject([r,0]).toArray());t.unproject([r,n]).toArray();return function(t,e){void 0===e&&(e={});var r=Number(t[0]),n=Number(t[1]),o=Number(t[2]),i=Number(t[3]);if(6===t.length)throw new Error("@turf/bbox-polygon does not support BBox with 6 positions");var a=[r,n];return c([[a,[o,n],[o,i],[r,i],a]],e.properties,{bbox:t,id:e.id})}([t.unproject([0,n]).toArray(),o].flat())}(),a=[],s=function e(r){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!Array.isArray(r))throw Error("Your array is not an array");Array.isArray(r[0])?r.forEach((function(t){e(t)})):2===r.length&&W(t,a,r,n)};return n.forEach((function(t){t.id!==r.id?t.id!==$.HORIZONTAL_GUIDE&&t.id!==$.VERTICAL_GUIDE&&(s(t.geometry.coordinates),j(i,t)||o.push(t)):r.type===H.POLYGON&&s(t.geometry.coordinates[0].slice(0,-2),!0)})),[o,a]};function Q(t,e){var r=t.map((function(t){return{feature:t,point:U(t,e)}}));return r.sort((function(t,e){return t.point.properties.dist-e.point.properties.dist})),{feature:r[0].feature,point:r[0].point}}var tt=function(t,e){var r={};return e.forEach((function(e,n){var o=function(t,e){var r,n=[t.lng,t.lat],o="Point"===e.geometry.type,i="Polygon"===e.geometry.type,a="MultiPolygon"===e.geometry.type,s="MultiPoint"===e.geometry.type,c=void 0,f=b(e);if(o){var d=q(f,2);return{latlng:{lng:d[0],lat:d[1]},distance:R(f,n)}}if(s){var h=J(n,p(f.map((function(t){return u(t)})))),g=h.geometry.coordinates;return{latlng:{lng:g[0],lat:g[1]},distance:h.properties.distanceToPoint}}if(c=i||a?P(e):e,i){var y=Q("LineString"===c.geometry.type?[l(c.geometry.coordinates)]:c.geometry.coordinates.map((function(t){return l(t)})),n);c=y.feature,r=y.point}else if(a){var m=Q(c.features.map((function(t){return"LineString"===t.geometry.type?[t.geometry.coordinates]:t.geometry.coordinates})).flatMap((function(t){return t})).map((function(t){return l(t)})),n);c=m.feature,r=m.point}else r=U(c,n);var v=q(r.geometry.coordinates,2),_=v[0],E=v[1],O=r.properties.index;return O+1===c.geometry.coordinates.length&&O--,{latlng:{lng:_,lat:E},segment:c.geometry.coordinates.slice(O,O+2),distance:r.properties.dist,isMarker:o}}(t,e);(void 0===r.distance||o.distance<r.distance)&&((r=o).layer=e)})),r};var et=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1.25;return!Array.isArray(t.segment)?function(t){return t.latlng}(t):function(t,e,r){var n=t.segment[0],o=t.segment[1],i=[t.latlng.lng,t.latlng.lat],a=R(n,i),s=R(o,i),u=a<s?n:o,c=a<s?a:s;if(e&&e.snapToMidPoints){var l=z(n,o).geometry.coordinates,p=R(l,i);p<a&&p<s&&(u=l,c=p)}var f=q(c<r?u:i,2);return{lng:f[0],lat:f[1]}}(t,e,r)},rt=function(t,e){var r,n,o,i,a,s,u,c,l,p,f,d,h=e.lngLat.lng,g=e.lngLat.lat;if(e.originalEvent.altKey)return t.showVerticalSnapLine=!1,t.showHorizontalSnapLine=!1,{lng:h,lat:g};if(t.snapList.length<=0)return{lng:h,lat:g};if(t.options.snap){if(r=tt({lng:h,lat:g},t.snapList),0===Object.keys(r).length)return!1;var y=r.isMarker,m=t.options.snapOptions?t.options.snapOptions.snapVertexPriorityDistance:void 0;o=y?r.latlng:et(r,t.options.snapOptions,m),n=(t.options.snapOptions&&t.options.snapOptions.snapPx||15)*(i=o.lat,a=t.map.getZoom(),s=i*(Math.PI/180),40075017*Math.cos(s)/Math.pow(2,a+8))}if(t.options.guides){var v=(l=t.vertices,p=e.lngLat,f=[],d=[],l.forEach((function(t){f.push(t[0]),d.push(t[1])})),{verticalPx:f.find((function(t){return Math.abs(t-p.lng)<.009})),horizontalPx:d.find((function(t){return Math.abs(t-p.lat)<.009}))});if(c=v.horizontalPx,u=v.verticalPx){var b={lng:u,lat:e.lngLat.lat+10},_={lng:u,lat:e.lngLat.lat-10};t.verticalGuide.updateCoordinate(0,b.lng,b.lat),t.verticalGuide.updateCoordinate(1,_.lng,_.lat)}if(c){var E={lng:e.lngLat.lng+10,lat:c},O={lng:e.lngLat.lng-10,lat:c};t.horizontalGuide.updateCoordinate(0,E.lng,E.lat),t.horizontalGuide.updateCoordinate(1,O.lng,O.lat)}t.showVerticalSnapLine=!!u,t.showHorizontalSnapLine=!!c}return r&&1e3*r.distance<n?o:u||c?(u&&(h=u),c&&(g=c),{lng:h,lat:g}):{lng:h,lat:g}},nt=function(t){return{id:t,type:H.FEATURE,properties:{isSnapGuide:"true"},geometry:{type:H.LINE_STRING,coordinates:[]}}},ot=function(t,e){return!(e.properties.id!==$.VERTICAL_GUIDE||t.options.guides&&t.showVerticalSnapLine)||!(e.properties.id!==$.HORIZONTAL_GUIDE||t.options.guides&&t.showHorizontalSnapLine)};function it(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,i=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return at(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return at(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function at(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function st(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ut(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ct=o().lib.doubleClickZoom,lt=o().modes.draw_point,pt=o().constants,ft=pt.geojsonTypes,dt=pt.cursors,ht=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?st(Object(r),!0).forEach((function(e){ut(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):st(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},lt);ht.onSetup=function(t){var e=this,r=this.newFeature({type:ft.FEATURE,properties:{},geometry:{type:ft.POINT,coordinates:[[]]}}),n=this.newFeature(nt($.VERTICAL_GUIDE)),o=this.newFeature(nt($.HORIZONTAL_GUIDE));this.addFeature(r),this.addFeature(n),this.addFeature(o);var i=this.getSelected();this.clearSelectedFeatures(),ct.disable(this);var a=it(K(this.map,this._ctx.api,r),2),s=a[0],u=a[1],c={map:this.map,point:r,vertices:u,snapList:s,selectedFeatures:i,verticalGuide:n,horizontalGuide:o};c.options=this._ctx.options;var l=function(){var t=it(K(e.map,e._ctx.api,r),2),n=t[0],o=t[1];c.vertices=o,c.snapList=n};c.moveendCallback=l;var p=function(t){c.options=t};return c.optionsChangedCallBAck=p,this.map.on("moveend",l),this.map.on("draw.snap.options_changed",p),c},ht.onClick=function(t){lt.onClick.call(this,t,{lngLat:{lng:t.snappedLng,lat:t.snappedLat}})},ht.onMouseMove=function(t,e){var r=rt(t,e),n=r.lng,o=r.lat;t.snappedLng=n,t.snappedLat=o,t.lastVertex&&t.lastVertex[0]===n&&t.lastVertex[1]===o?this.updateUIClasses({mouse:dt.POINTER}):this.updateUIClasses({mouse:dt.ADD})},ht.toDisplayFeatures=function(t,e,r){ot(t,e)||lt.toDisplayFeatures(t,e,r)},ht.onStop=function(t){this.deleteFeature($.VERTICAL_GUIDE,{silent:!0}),this.deleteFeature($.HORIZONTAL_GUIDE,{silent:!0}),this.map.off("moveend",t.moveendCallback),lt.onStop.call(this,t)};const gt=ht;function yt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,i=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return mt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function vt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function bt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var _t=o().constants,Et=_t.geojsonTypes,Ot=_t.modes,xt=_t.cursors,St=o().lib.doubleClickZoom,wt=o().modes.draw_line_string,Ct=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vt(Object(r),!0).forEach((function(e){bt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},wt);Ct.onSetup=function(t){var e=this,r=this.newFeature({type:Et.FEATURE,properties:{},geometry:{type:Et.LINE_STRING,coordinates:[[]]}}),n=this.newFeature(nt($.VERTICAL_GUIDE)),o=this.newFeature(nt($.HORIZONTAL_GUIDE));this.addFeature(r),this.addFeature(n),this.addFeature(o);var i=this.getSelected();this.clearSelectedFeatures(),St.disable(this);var a=yt(K(this.map,this._ctx.api,r),2),s=a[0],u=a[1],c={map:this.map,line:r,currentVertexPosition:0,vertices:u,snapList:s,selectedFeatures:i,verticalGuide:n,horizontalGuide:o,direction:"forward"};c.options=this._ctx.options;var l=function(){var t=yt(K(e.map,e._ctx.api,r),2),n=t[0],o=t[1];c.vertices=o,c.snapList=n};c.moveendCallback=l;var p=function(t){c.options=t};return c.optionsChangedCallBAck=p,this.map.on("moveend",l),this.map.on("draw.snap.options_changed",p),c},Ct.onClick=function(t){var e=t.snappedLng,r=t.snappedLat;if(t.currentVertexPosition>0){var n=t.line.coordinates[t.currentVertexPosition-1];if(t.lastVertex=n,n[0]===e&&n[1]===r)return this.changeMode(Ot.SIMPLE_SELECT,{featureIds:[t.line.id]})}W(t.map,t.vertices,{lng:e,lat:r}),t.line.updateCoordinate(t.currentVertexPosition,e,r),t.currentVertexPosition++,t.line.updateCoordinate(t.currentVertexPosition,e,r)},Ct.onMouseMove=function(t,e){var r=rt(t,e),n=r.lng,o=r.lat;t.line.updateCoordinate(t.currentVertexPosition,n,o),t.snappedLng=n,t.snappedLat=o,t.lastVertex&&t.lastVertex[0]===n&&t.lastVertex[1]===o?this.updateUIClasses({mouse:xt.POINTER}):this.updateUIClasses({mouse:xt.ADD})},Ct.toDisplayFeatures=function(t,e,r){ot(t,e)||wt.toDisplayFeatures(t,e,r)},Ct.onStop=function(t){this.deleteFeature($.VERTICAL_GUIDE,{silent:!0}),this.deleteFeature($.HORIZONTAL_GUIDE,{silent:!0}),this.map.off("moveend",t.moveendCallback),wt.onStop.call(this,t)};const Mt=Ct;function It(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,i=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Tt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Pt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Lt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var At=o().constants,Ft=At.geojsonTypes,Nt=At.modes,jt=At.cursors,Rt=o().lib.doubleClickZoom,kt=o().modes.draw_polygon,Dt=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pt(Object(r),!0).forEach((function(e){Lt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},kt);Dt.onSetup=function(t){var e=this,r=this.newFeature({type:Ft.FEATURE,properties:{},geometry:{type:Ft.POLYGON,coordinates:[[]]}}),n=this.newFeature(nt($.VERTICAL_GUIDE)),o=this.newFeature(nt($.HORIZONTAL_GUIDE));this.addFeature(r),this.addFeature(n),this.addFeature(o);var i=this.getSelected();this.clearSelectedFeatures(),Rt.disable(this);var a=It(K(this.map,this._ctx.api,r),2),s=a[0],u=a[1],c={map:this.map,polygon:r,currentVertexPosition:0,vertices:u,snapList:s,selectedFeatures:i,verticalGuide:n,horizontalGuide:o};c.options=Object.assign(this._ctx.options,{overlap:!0});var l=function(){var t=It(K(e.map,e._ctx.api,r),2),n=t[0],o=t[1];c.vertices=o,c.snapList=n};c.moveendCallback=l;var p=function(t){c.options=t};return c.optionsChangedCallBAck=p,this.map.on("moveend",l),this.map.on("draw.snap.options_changed",p),c},Dt.onClick=function(t){var e=t.snappedLng,r=t.snappedLat;if(t.currentVertexPosition>0){var n=t.polygon.coordinates[0][t.currentVertexPosition-1];if(t.lastVertex=n,n[0]===e&&n[1]===r)return this.changeMode(Nt.SIMPLE_SELECT,{featureIds:[t.polygon.id]})}W(t.map,t.vertices,{lng:e,lat:r}),t.polygon.updateCoordinate("0.".concat(t.currentVertexPosition),e,r),t.currentVertexPosition++,t.polygon.updateCoordinate("0.".concat(t.currentVertexPosition),e,r)},Dt.onMouseMove=function(t,e){var r=rt(t,e),n=r.lng,o=r.lat;t.polygon.updateCoordinate("0.".concat(t.currentVertexPosition),n,o),t.snappedLng=n,t.snappedLat=o,t.lastVertex&&t.lastVertex[0]===n&&t.lastVertex[1]===o?this.updateUIClasses({mouse:jt.POINTER}):this.updateUIClasses({mouse:jt.ADD})},Dt.toDisplayFeatures=function(t,e,r){ot(t,e)||kt.toDisplayFeatures(t,e,r)},Dt.onStop=function(t){this.deleteFeature($.VERTICAL_GUIDE,{silent:!0}),this.deleteFeature($.HORIZONTAL_GUIDE,{silent:!0}),this.map.off("moveend",t.moveendCallback),this.map.off("draw.snap.options_changed",t.optionsChangedCallBAck);var e=t.polygon;if(t.options.overlap)kt.onStop.call(this,t);else{var r=this._ctx.store.getAll();try{var n=e;r.forEach((function(t){if(e.id===t.id)return!1;var r,o;(r=n,o=!1,w(t,(function(t){w(r,(function(e){if(!0===o)return!0;o=!j(t.geometry,e.geometry)}))})),o)&&(n=turf.difference(n,t))})),t.polygon.coordinates=n.coordinates||n.geometry.coordinates}catch(e){return kt.onStop.call(this,t),void this.deleteFeature([t.polygon.id],{silent:!0})}var o=t.polygon.removeCoordinate;t.polygon.removeCoordinate=function(){},kt.onStop.call(this,t),t.polygon.removeCoordinate=o.bind(t.polygon)}};const Ut=Dt;function Gt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==r)return;var n,o,i=[],a=!0,s=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Bt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Vt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Yt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Xt=o().lib.doubleClickZoom,Jt=o().modes.direct_select,zt=o().constants,qt=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vt(Object(r),!0).forEach((function(e){Yt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},Jt);qt.onSetup=function(t){var e=t.featureId,r=this.getFeature(e);if(!r)throw new Error("You must provide a featureId to enter direct_select mode");if(r.type===zt.geojsonTypes.POINT)throw new TypeError("direct_select mode doesn't handle point features");var n=Gt(K(this.map,this._ctx.api,r),2),o=n[0],i=n[1],a=this.newFeature(nt($.VERTICAL_GUIDE)),s=this.newFeature(nt($.HORIZONTAL_GUIDE));this.addFeature(a),this.addFeature(s);var u={map:this.map,featureId:e,feature:r,dragMoveLocation:t.startPos||null,dragMoving:!1,canDragMove:!1,selectedCoordPaths:t.coordPath?[t.coordPath]:[],vertices:i,snapList:o,verticalGuide:a,horizontalGuide:s};u.options=this._ctx.options,this.setSelectedCoordinates(this.pathsToCoordinates(e,u.selectedCoordPaths)),this.setSelected(e),Xt.disable(this),this.setActionableState({trash:!0});var c=function(t){u.options=t};return u.optionsChangedCallBAck=c,this.map.on("draw.snap.options_changed",c),u},qt.dragVertex=function(t,e,r){var n=rt(t,e),o=n.lng,i=n.lat;t.feature.updateCoordinate(t.selectedCoordPaths[0],o,i)},qt.onStop=function(t){this.deleteFeature($.VERTICAL_GUIDE,{silent:!0}),this.deleteFeature($.HORIZONTAL_GUIDE,{silent:!0}),this.map.off("draw.snap.options_changed",t.optionsChangedCallBAck),Jt.onStop.call(this,t)};const Zt=qt;function Ht(t){return function(t){if(Array.isArray(t))return $t(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return $t(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $t(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $t(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Wt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Kt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Wt(Object(r),!0).forEach((function(e){Qt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Wt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Qt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var te=o().lib.theme.map((function(t){return"gl-draw-line-inactive"===t.id?Kt(Kt({},t),{},{filter:[].concat(Ht(t.filter),[["!=","user_isSnapGuide","true"]])}):t}));const ee=[].concat(Ht(te),[{id:"guide",type:"line",filter:["all",["==","$type","LineString"],["==","user_isSnapGuide","true"]],layout:{"line-cap":"round","line-join":"round"},paint:{"line-color":"#c00c00","line-width":1,"line-dasharray":[5,5]}}])})(),n})()));
//# sourceMappingURL=mapbox-gl-draw-snap-mode.js.map