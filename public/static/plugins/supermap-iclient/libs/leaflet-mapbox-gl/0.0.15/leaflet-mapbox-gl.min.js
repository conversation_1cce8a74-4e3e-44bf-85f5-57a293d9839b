/**
 * Minified by jsDeliv<PERSON> using Terser v5.3.5.
 * Original file: /npm/mapbox-gl-leaflet@0.0.15/leaflet-mapbox-gl.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
 !function(t,i){"function"==typeof define&&define.amd?define(["leaflet","mapbox-gl"],i):"object"==typeof exports?module.exports=i(require("leaflet"),require("mapbox-gl")):t.returnExports=i(window.L,window.mapboxgl)}(this,(function(t,i){t.MapboxGL=t.Layer.extend({options:{updateInterval:32,padding:.1,interactive:!1,pane:"tilePane"},initialize:function(n){t.setOptions(this,n),n.accessToken&&(i.accessToken=n.accessToken),this._throttledUpdate=t.Util.throttle(this._update,this.options.updateInterval,this)},onAdd:function(i){this._container||this._initContainer();var n=this.getPaneName();i.getPane(n).appendChild(this._container),this._initGL(),this._offset=this._map.containerPointToLayerPoint([0,0]),i.options.zoomAnimation&&t.DomEvent.on(i._proxy,t.DomUtil.TRANSITION_END,this._transitionEnd,this)},onRemove:function(i){this._map._proxy&&this._map.options.zoomAnimation&&t.DomEvent.off(this._map._proxy,t.DomUtil.TRANSITION_END,this._transitionEnd,this);var n=this.getPaneName();i.getPane(n).removeChild(this._container)},getEvents:function(){return{move:this._throttledUpdate,zoomanim:this._animateZoom,zoom:this._pinchZoom,zoomstart:this._zoomStart,zoomend:this._zoomEnd,resize:this._resize}},getMapboxMap:function(){return this._glMap},getCanvas:function(){return this._glMap.getCanvas()},getSize:function(){return this._map.getSize().multiplyBy(1+2*this.options.padding)},getBounds:function(){var i=this.getSize().multiplyBy(.5),n=this._map.latLngToContainerPoint(this._map.getCenter());return t.latLngBounds(this._map.containerPointToLatLng(n.subtract(i)),this._map.containerPointToLatLng(n.add(i)))},getContainer:function(){return this._container},getPaneName:function(){return this._map.getPane(this.options.pane)?this.options.pane:"tilePane"},_initContainer:function(){var i=this._container=t.DomUtil.create("div","leaflet-gl-layer"),n=this.getSize(),o=this._map.getSize().multiplyBy(this.options.padding);i.style.width=n.x+"px",i.style.height=n.y+"px";var a=this._map.containerPointToLayerPoint([0,0]).subtract(o);t.DomUtil.setPosition(i,a)},_initGL:function(){var n=this._map.getCenter(),o=t.extend({},this.options,{container:this._container,center:[n.lng,n.lat],zoom:this._map.getZoom()-1,attributionControl:!1});this._glMap?(this._glMap.setCenter(o.center),this._glMap.setZoom(o.zoom)):this._glMap=new i.Map(o),this._glMap.transform.latRange=null,this._transformGL(this._glMap),this._glMap._canvas.canvas?this._glMap._actualCanvas=this._glMap._canvas.canvas:this._glMap._actualCanvas=this._glMap._canvas;var a=this._glMap._actualCanvas;t.DomUtil.addClass(a,"leaflet-image-layer"),t.DomUtil.addClass(a,"leaflet-zoom-animated"),this.options.interactive&&t.DomUtil.addClass(a,"leaflet-interactive"),this.options.className&&t.DomUtil.addClass(a,this.options.className)},_update:function(i){if(this._offset=this._map.containerPointToLayerPoint([0,0]),!this._zooming){var n=this.getSize(),o=this._container,a=this._glMap,e=this._map.getSize().multiplyBy(this.options.padding),s=this._map.containerPointToLayerPoint([0,0]).subtract(e);t.DomUtil.setPosition(o,s),this._transformGL(a),a.transform.width!==n.x||a.transform.height!==n.y?(o.style.width=n.x+"px",o.style.height=n.y+"px",null!==a._resize&&void 0!==a._resize?a._resize():a.resize()):null!==a._update&&void 0!==a._update?a._update():a.update()}},_transformGL:function(t){var n=this._map.getCenter(),o=t.transform;o.center=i.LngLat.convert([n.lng,n.lat]),o.zoom=this._map.getZoom()-1},_pinchZoom:function(t){this._glMap.jumpTo({zoom:this._map.getZoom()-1,center:this._map.getCenter()})},_animateZoom:function(i){var n=this._map.getZoomScale(i.zoom),o=this._map.getSize().multiplyBy(this.options.padding*n),a=this.getSize()._divideBy(2),e=this._map.project(i.center,i.zoom)._subtract(a)._add(this._map._getMapPanePos().add(o))._round(),s=this._map.project(this._map.getBounds().getNorthWest(),i.zoom)._subtract(e);t.DomUtil.setTransform(this._glMap._actualCanvas,s.subtract(this._offset),n)},_zoomStart:function(t){this._zooming=!0},_zoomEnd:function(){var i=this._map.getZoomScale(this._map.getZoom());t.DomUtil.setTransform(this._glMap._actualCanvas,null,i),this._zooming=!1,this._update()},_transitionEnd:function(i){t.Util.requestAnimFrame((function(){var i=this._map.getZoom(),n=this._map.getCenter(),o=this._map.latLngToContainerPoint(this._map.getBounds().getNorthWest());t.DomUtil.setTransform(this._glMap._actualCanvas,o,1),this._glMap.once("moveend",t.Util.bind((function(){this._zoomEnd()}),this)),this._glMap.jumpTo({center:n,zoom:i-1})}),this)},_resize:function(t){this._transitionEnd(t)}}),t.mapboxGL=function(i){return new t.MapboxGL(i)}}));
 //# sourceMappingURL=/sm/4838ad43b9f6213690c3e6002cc39bef0fa4f5aeec249d29f97d5330197be407.map