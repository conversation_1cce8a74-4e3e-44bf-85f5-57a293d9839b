/*!
 * 	   Copyright© 2017 OSM Buildings, <PERSON>
 * 	   @osmbuildings, http://osmbuildings.org
 * 
 *     OSMBuildings-OL3.js
 *     github: https://github.com/kekscom/osmbuildings
 *     license: BSD 2-clause
 * 
 *     version 0.2.2b
 * 
 */
(function(Z){function L(b,a){var c=b.x-a.x,d=b.y-a.y;return c*c+d*d}function pa(b){var a=b.length;if(16>a)return!1;var c,d=Infinity,e=-Infinity,g=Infinity,f=-Infinity;for(c=0;c<a-1;c+=2)d=Math.min(d,b[c]),e=Math.max(e,b[c]),g=Math.min(g,b[c+1]),f=Math.max(f,b[c+1]);c=e-d;f-=g;e=c/f;if(.85>e||1.15<e)return!1;d={x:d+c/2,y:g+f/2};c=(c+f)/4;g=c*c;for(c=0;c<a-1;c+=2)if(f=L({x:b[c],y:b[c+1]},d),.8>f/g||1.2<f/g)return!1;return!0}function fa(b,a){var c={};b/=M;a/=M;var d;d=0>=a?90:1<=a?-90:(2*qa(ra(B*(1-
2*a)))-G)/B*180;c.latitude=d;c.longitude=360*(1===b?1:(b%1+1)%1)-180;return c}function aa(b,a){var c=N(1,H(0,.5-sa(ga(ta+G*b/180))/B/2));return{x:(a/360+.5)*M<<0,y:c*M<<0}}function O(b){for(var a=y+n,c=z+q,d=0,e=b.length-3;d<e;d+=2)if(b[d]>n&&b[d]<a&&b[d+1]>q&&b[d+1]<c)return!0;return!1}function ua(){V||(V=setInterval(function(){for(var b=D.items,a=!1,c=0,d=b.length;c<d;c++)1>b[c].scale&&(b[c].scale+=.1,1<b[c].scale&&(b[c].scale=1),a=!0);P.render();a||(clearInterval(V),V=null)},33))}function va(b){y=
b.width;z=b.height;W=y/2<<0;ba=z/2<<0;Q=W;R=z;P.setSize(y,z);ca=400}var w=Math,ra=w.exp,sa=w.log,wa=w.sin,xa=w.cos,ga=w.tan,qa=w.atan,I=w.atan2,N=w.min,H=w.max,ha=w.sqrt,ia=w.ceil,ja=w.pow,ka=ka||Array,la=la||Array,w=/iP(ad|hone|od)/g.test(navigator.userAgent),C=!!~navigator.userAgent.indexOf("Trident"),ya=!Z.requestAnimationFrame||w||C?function(b){b()}:Z.requestAnimationFrame,E=function(){function b(a,b,c){0>c&&(c+=1);1<c&&--c;return c<1/6?a+6*(b-a)*c:.5>c?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function a(a,
b){if(void 0!==a)return Math.min(b,Math.max(0,a||0))}var c={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",
darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",
fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",
lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",
mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",
salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},d=function(b,c,d,k){this.r=a(b,1);this.g=a(c,1);this.b=a(d,1);
this.a=a(k,1)||1};d.parse=function(a){if("string"===typeof a){a=a.toLowerCase();a=c[a]||a;var b;if(b=a.match(/^#?(\w{2})(\w{2})(\w{2})$/))return new d(parseInt(b[1],16)/255,parseInt(b[2],16)/255,parseInt(b[3],16)/255);if(b=a.match(/rgba?\((\d+)\D+(\d+)\D+(\d+)(\D+([\d.]+))?\)/))return new d(parseFloat(b[1])/255,parseFloat(b[2])/255,parseFloat(b[3])/255,b[4]?parseFloat(b[5]):1)}return new d};d.fromHSL=function(a,c,f,k){if(0===c)return new d(f,f,f,k);c=.5>f?f*(1+c):f+c-f*c;f=2*f-c;a/=360;return new d(b(f,
c,a+1/3),b(f,c,a),b(f,c,a-1/3),k)};d.prototype={toHSL:function(){if(void 0!==this.r&&void 0!==this.g&&void 0!==this.b){var a=Math.max(this.r,this.g,this.b),b=Math.min(this.r,this.g,this.b),c,d=(a+b)/2,h=a-b;if(h){b=.5<d?h/(2-a-b):h/(a+b);switch(a){case this.r:c=(this.g-this.b)/h+(this.g<this.b?6:0);break;case this.g:c=(this.b-this.r)/h+2;break;case this.b:c=(this.r-this.g)/h+4}c*=60}else c=b=0;return{h:c,s:b,l:d,a:this.a}}},toString:function(){if(void 0!==this.r&&void 0!==this.g&&void 0!==this.b)return 1===
this.a?"#"+(16777216+(Math.round(255*this.r)<<16)+(Math.round(255*this.g)<<8)+Math.round(255*this.b)).toString(16).slice(1,7):"rgba("+[Math.round(255*this.r),Math.round(255*this.g),Math.round(255*this.b),this.a.toFixed(2)].join()+")"},toArray:function(){if(void 0!==this.r&&void 0!==this.g&&void 0!==this.b)return[this.r,this.g,this.b]},hue:function(a){var b=this.toHSL();return d.fromHSL(b.h+a,b.s,b.l)},saturation:function(a){var b=this.toHSL();return d.fromHSL(b.h,b.s*a,b.l)},lightness:function(a){var b=
this.toHSL();return d.fromHSL(b.h,b.s,b.l*a)},red:function(a){return new d(this.r*a,this.g,this.b,this.a)},green:function(a){return new d(this.r,this.g*a,this.b,this.a)},blue:function(a){return new d(this.r,this.g,this.b*a,this.a)},alpha:function(a){return new d(this.r,this.g,this.b,this.a*a)},copy:function(){return new d(this.r,this.g,this.b,this.a)}};return d}();"object"===typeof module&&(module.exports=E);var za=function(){var b=Math,a=b.PI,c=b.sin,d=b.cos,e=b.tan,g=b.asin,f=b.atan2,k=a/180,h=
23.4397*k;return function(b,m,p){p=k*-p;m*=k;b=b.valueOf()/864E5-.5+2440588-2451545;var r=k*(357.5291+.98560028*b),n;n=k*(1.9148*c(r)+.02*c(2*r)+3E-4*c(3*r));n=r+n+102.9372*k+a;r=g(c(0)*d(h)+d(0)*c(h)*c(n));n=f(c(n)*d(h)-e(0)*c(h),d(n));b=k*(280.16+360.9856235*b)-p-n;p=g(c(m)*c(r)+d(m)*d(r)*d(b));m=f(c(b),d(b)*c(m)-e(r)*d(m));return{altitude:p,azimuth:m-a/2}}}(),Ba=function(){function b(a){a=a.toLowerCase();return"#"===a[0]?a:d[e[a]||a]||null}function a(a,b){var c,d,e,m,p=0,r,n;r=0;for(n=a.length-
3;r<n;r+=2)c=a[r],d=a[r+1],e=a[r+2],m=a[r+3],p+=c*m-e*d;if((0<p/2?"CW":"CCW")===b)return a;c=[];for(d=a.length-2;0<=d;d-=2)c.push(a[d],a[d+1]);return c}function c(b){var d,e,h=[],l;switch(b.type){case "GeometryCollection":h=[];d=0;for(e=b.geometries.length;d<e;d++)(l=c(b.geometries[d]))&&h.push.apply(h,l);return h;case "MultiPolygon":h=[];d=0;for(e=b.coordinates.length;d<e;d++)(l=c({type:"Polygon",coordinates:b.coordinates[d]}))&&h.push.apply(h,l);return h;case "Polygon":b=b.coordinates;break;default:return[]}var m,
p=[],r=[];m=b[0];d=0;for(e=m.length;d<e;d++)p.push(m[d][1],m[d][0]);p=a(p,"CW");d=0;for(e=b.length-1;d<e;d++){m=b[d+1];r[d]=[];h=0;for(l=m.length;h<l;h++)r[d].push(m[h][1],m[h][0]);r[d]=a(r[d],"CCW")}return[{outer:p,inner:r.length?r:null}]}var d={brick:"#cc7755",bronze:"#ffeecc",canvas:"#fff8f0",concrete:"#999999",copper:"#a0e0d0",glass:"#e8f8f8",gold:"#ffcc00",plants:"#009933",metal:"#aaaaaa",panel:"#fff8f0",plaster:"#999999",roof_tiles:"#f08060",silver:"#cccccc",slate:"#666666",stone:"#996666",
tar_paper:"#333333",wood:"#deb887"},e={asphalt:"tar_paper",bitumen:"tar_paper",block:"stone",bricks:"brick",glas:"glass",glassfront:"glass",grass:"plants",masonry:"stone",granite:"stone",panels:"panel",paving_stones:"stone",plastered:"plaster",rooftiles:"roof_tiles",roofingfelt:"tar_paper",sandstone:"stone",sheet:"canvas",sheets:"canvas",shingle:"tar_paper",shingles:"tar_paper",slates:"slate",steel:"metal",tar:"tar_paper",tent:"canvas",thatch:"plants",tile:"roof_tiles",tiles:"roof_tiles"};return{read:function(a){if(!a||
"FeatureCollection"!==a.type)return[];a=a.features;var d,e,h,l,m=[],p,r,n,q;d=0;for(e=a.length;d<e;d++)if(p=a[d],"Feature"===p.type&&!1!==ma(p)){h=p.properties;l={};h=h||{};l.height=h.height||(h.levels?3*h.levels:Aa);l.minHeight=h.minHeight||(h.minLevel?3*h.minLevel:0);if(r=h.material?b(h.material):h.wallColor||h.color)l.wallColor=r;if(r=h.roofMaterial?b(h.roofMaterial):h.roofColor)l.roofColor=r;switch(h.shape){case "cylinder":case "cone":case "dome":case "sphere":l.shape=h.shape;l.isRotational=!0;
break;case "pyramid":l.shape=h.shape}switch(h.roofShape){case "cone":case "dome":l.roofShape=h.roofShape;l.isRotational=!0;break;case "pyramid":l.roofShape=h.roofShape}l.roofShape&&h.roofHeight?(l.roofHeight=h.roofHeight,l.height=H(0,l.height-l.roofHeight)):l.roofHeight=0;n=l;r=c(p.geometry);h=0;for(l=r.length;h<l;h++){q=n;var w={},u=void 0;for(u in q)q.hasOwnProperty(u)&&(w[u]=q[u]);q=w;q.footprint=r[h].outer;if(q.isRotational){for(var w=q,u=q.footprint,t=180,x=-180,v=0,y=u.length;v<y;v+=2)t=N(t,
u[v+1]),x=H(x,u[v+1]);w.radius=(x-t)/2}r[h].inner&&(q.holes=r[h].inner);if(p.id||p.properties.id)q.id=p.id||p.properties.id;p.properties.relationId&&(q.relationId=p.properties.relationId);m.push(q)}}return m}}}(),B=Math.PI,G=B/2,ta=B/4,x,M,y=0,z=0,W=0,ba=0,n=0,q=0,F=E.parse("rgba(200, 190, 180)"),X=F.lightness(.8),J=F.lightness(1.2),da=""+F,Y=""+X,S=""+J,na=0,A=1,ca,Aa=5,Q,R,Ca=function(){function b(b,g){if(a[b])g&&g(a[b]);else{var f=new XMLHttpRequest;f.onreadystatechange=function(){if(4===f.readyState&&
!(!f.status||200>f.status||299<f.status)&&g&&f.responseText){var k=f.responseText;a[b]=k;c.push({url:b,size:k.length});d+=k.length;for(g(k);5242880<d;)k=c.shift(),d-=k.size,delete a[k.url]}};f.open("GET",b);f.send(null);return f}}var a={},c=[],d=0;return{loadJSON:function(a,c){return b(a,function(a){var b;try{b=JSON.parse(a)}catch(d){}c(b)})}}}(),D={loadedItems:{},items:[],getPixelFootprint:function(b){for(var a=new ka(b.length),c,d=0,e=b.length-1;d<e;d+=2)c=aa(b[d],b[d+1]),a[d]=c.x,a[d+1]=c.y;b=
a;a=b.length/2;c=new la(a);var d=0,e=a-1,g,f,k,h,l=[],m=[],p=[];for(c[d]=c[e]=1;e;){f=0;for(g=d+1;g<e;g++){k=b[2*g];var r=b[2*g+1],n=b[2*d],q=b[2*d+1],u=b[2*e],w=b[2*e+1],t=u-n,v=w-q,x=void 0;if(0!==t||0!==v)x=((k-n)*t+(r-q)*v)/(t*t+v*v),1<x?(n=u,q=w):0<x&&(n+=t*x,q+=v*x);t=k-n;v=r-q;k=t*t+v*v;k>f&&(h=g,f=k)}2<f&&(c[h]=1,l.push(d),m.push(h),l.push(h),m.push(e));d=l.pop();e=m.pop()}for(g=0;g<a;g++)c[g]&&p.push(b[2*g],b[2*g+1]);a=p;if(!(8>a.length))return a},resetItems:function(){this.items=[];this.loadedItems=
{};T.reset()},addRenderItems:function(b,a){for(var c,d,e,g=Ba.read(b),f=0,k=g.length;f<k;f++)c=g[f],e=c.id||[c.footprint[0],c.footprint[1],c.height,c.minHeight].join(),!this.loadedItems[e]&&(d=this.scale(c))&&(d.scale=a?0:1,this.items.push(d),this.loadedItems[e]=1);ua()},scale:function(b){var a={},c=6/ja(2,x-15);b.id&&(a.id=b.id);a.height=N(b.height/c,ca);a.minHeight=isNaN(b.minHeight)?0:b.minHeight/c;if(!(a.minHeight>ca)&&(a.footprint=this.getPixelFootprint(b.footprint),a.footprint)){for(var d=a.footprint,
e=Infinity,g=-Infinity,f=Infinity,k=-Infinity,h=0,l=d.length-3;h<l;h+=2)e=N(e,d[h]),g=H(g,d[h]),f=N(f,d[h+1]),k=H(k,d[h+1]);a.center={x:e+(g-e)/2<<0,y:f+(k-f)/2<<0};b.radius&&(a.radius=b.radius*na);b.shape&&(a.shape=b.shape);b.roofShape&&(a.roofShape=b.roofShape);"cone"!==a.roofShape&&"dome"!==a.roofShape||a.shape||!pa(a.footprint)||(a.shape="cylinder");if(b.holes){a.holes=[];for(var m,d=0,e=b.holes.length;d<e;d++)(m=this.getPixelFootprint(b.holes[d]))&&a.holes.push(m)}var p;b.wallColor&&(p=E.parse(b.wallColor))&&
(p=p.alpha(A),a.altColor=""+p.lightness(.8),a.wallColor=""+p);b.roofColor&&(p=E.parse(b.roofColor))&&(a.roofColor=""+p.alpha(A));b.relationId&&(a.relationId=b.relationId);a.hitColor=T.idToColor(b.relationId||b.id);a.roofHeight=isNaN(b.roofHeight)?0:b.roofHeight/c;if(!(a.height+a.roofHeight<=a.minHeight))return a}},set:function(b){this.isStatic=!0;this.resetItems();this._staticData=b;this.addRenderItems(this._staticData,!0)},load:function(b,a){this.src=b||"https://{s}.data.osmbuildings.org/0.2/{k}/tile/{z}/{x}/{y}.json".replace("{k}",
a||"anonymous");this.update()},update:function(){function b(a){f.addRenderItems(a)}this.resetItems();if(!(15>x))if(this.isStatic&&this._staticData)this.addRenderItems(this._staticData);else if(this.src){var a=16<x?256<<x-16:256>>16-x,c=n/a<<0,d=q/a<<0,e=ia((n+y)/a),a=ia((q+z)/a),g,f=this;for(g=d;g<=a;g++)for(d=c;d<=e;d++)this.loadTile(d,g,16,b)}},loadTile:function(b,a,c,d){b=this.src.replace("{s}","abcd"[(b+a)%4]).replace("{x}",b).replace("{y}",a).replace("{z}",c);return Ca.loadJSON(b,d)}},U={draw:function(b,
a,c,d,e,g,f,k){var h,l=this._extrude(b,a,d,e,g,f),m=[];if(c)for(a=0,h=c.length;a<h;a++)m[a]=this._extrude(b,c[a],d,e,g,f);b.fillStyle=k;b.beginPath();this._ring(b,l);if(c)for(a=0,h=m.length;a<h;a++)this._ring(b,m[a]);b.closePath();b.stroke();b.fill()},_extrude:function(b,a,c,d,e,g){c=450/(450-c);for(var f=450/(450-d),k={x:0,y:0},h={x:0,y:0},l,m,p=[],r=0,t=a.length-3;r<t;r+=2)k.x=a[r]-n,k.y=a[r+1]-q,h.x=a[r+2]-n,h.y=a[r+3]-q,l=u.project(k,c),m=u.project(h,c),d&&(k=u.project(k,f),h=u.project(h,f)),
(h.x-k.x)*(l.y-k.y)>(l.x-k.x)*(h.y-k.y)&&(b.fillStyle=k.x<h.x&&k.y<h.y||k.x>h.x&&k.y>h.y?g:e,b.beginPath(),this._ring(b,[h.x,h.y,k.x,k.y,l.x,l.y,m.x,m.y]),b.closePath(),b.fill()),p[r]=l.x,p[r+1]=l.y;return p},_ring:function(b,a){b.moveTo(a[0],a[1]);for(var c=2,d=a.length-1;c<d;c+=2)b.lineTo(a[c],a[c+1])},simplified:function(b,a,c){b.beginPath();this._ringAbs(b,a);if(c){a=0;for(var d=c.length;a<d;a++)this._ringAbs(b,c[a])}b.closePath();b.stroke();b.fill()},_ringAbs:function(b,a){b.moveTo(a[0]-n,a[1]-
q);for(var c=2,d=a.length-1;c<d;c+=2)b.lineTo(a[c]-n,a[c+1]-q)},shadow:function(b,a,c,d,e){for(var g=null,f={x:0,y:0},k={x:0,y:0},h,l,m=0,p=a.length-3;m<p;m+=2)f.x=a[m]-n,f.y=a[m+1]-q,k.x=a[m+2]-n,k.y=a[m+3]-q,h=v.project(f,d),l=v.project(k,d),e&&(f=v.project(f,e),k=v.project(k,e)),(k.x-f.x)*(h.y-f.y)>(h.x-f.x)*(k.y-f.y)?(1===g&&b.lineTo(f.x,f.y),g=0,m||b.moveTo(f.x,f.y),b.lineTo(k.x,k.y)):(0===g&&b.lineTo(h.x,h.y),g=1,m||b.moveTo(h.x,h.y),b.lineTo(l.x,l.y));if(c)for(m=0,p=c.length;m<p;m++)this._ringAbs(b,
c[m])},shadowMask:function(b,a,c){this._ringAbs(b,a);if(c){a=0;for(var d=c.length;a<d;a++)this._ringAbs(b,c[a])}},hitArea:function(b,a,c,d,e,g){c=null;var f={x:0,y:0},k={x:0,y:0};d=450/(450-d);var h=450/(450-e),l;b.fillStyle=g;b.beginPath();for(var m=0,p=a.length-3;m<p;m+=2)f.x=a[m]-n,f.y=a[m+1]-q,k.x=a[m+2]-n,k.y=a[m+3]-q,g=u.project(f,d),l=u.project(k,d),e&&(f=u.project(f,h),k=u.project(k,h)),(k.x-f.x)*(g.y-f.y)>(g.x-f.x)*(k.y-f.y)?(1===c&&b.lineTo(f.x,f.y),c=0,m||b.moveTo(f.x,f.y),b.lineTo(k.x,
k.y)):(0===c&&b.lineTo(g.x,g.y),c=1,m||b.moveTo(g.x,g.y),b.lineTo(l.x,l.y));b.closePath();b.fill()}},t={draw:function(b,a,c,d,e,g,f,k,h){a={x:a.x-n,y:a.y-q};var l=450/(450-e),m=450/(450-g);e=u.project(a,l);d*=l;g&&(a=u.project(a,m),c*=m);(l=this._tangents(a,c,e,d))?(g=I(l[0].y1-a.y,l[0].x1-a.x),l=I(l[1].y1-a.y,l[1].x1-a.x)):(g=1.5*B,l=1.5*B);b.fillStyle=f;b.beginPath();b.arc(e.x,e.y,d,G,g,!0);b.arc(a.x,a.y,c,g,G);b.closePath();b.fill();b.fillStyle=k;b.beginPath();b.arc(e.x,e.y,d,l,G,!0);b.arc(a.x,
a.y,c,G,l);b.closePath();b.fill();b.fillStyle=h;this._circle(b,e,d)},simplified:function(b,a,c){this._circle(b,{x:a.x-n,y:a.y-q},c)},shadow:function(b,a,c,d,e,g){a={x:a.x-n,y:a.y-q};e=v.project(a,e);var f;g&&(a=v.project(a,g));var k=this._tangents(a,c,e,d);k?(g=I(k[0].y1-a.y,k[0].x1-a.x),f=I(k[1].y1-a.y,k[1].x1-a.x),b.moveTo(k[1].x2,k[1].y2),b.arc(e.x,e.y,d,f,g),b.arc(a.x,a.y,c,g,f)):(b.moveTo(a.x+c,a.y),b.arc(a.x,a.y,c,0,2*B))},shadowMask:function(b,a,c){var d=a.x-n;a=a.y-q;b.moveTo(d+c,a);b.arc(d,
a,c,0,2*B)},hitArea:function(b,a,c,d,e,g,f){a={x:a.x-n,y:a.y-q};var k=450/(450-e),h=450/(450-g);e=u.project(a,k);d*=k;g&&(a=u.project(a,h),c*=h);g=this._tangents(a,c,e,d);b.fillStyle=f;b.beginPath();g?(f=I(g[0].y1-a.y,g[0].x1-a.x),k=I(g[1].y1-a.y,g[1].x1-a.x),b.moveTo(g[1].x2,g[1].y2),b.arc(e.x,e.y,d,k,f),b.arc(a.x,a.y,c,f,k)):(b.moveTo(a.x+c,a.y),b.arc(a.x,a.y,c,0,2*B));b.closePath();b.fill()},_circle:function(b,a,c){b.beginPath();b.arc(a.x,a.y,c,0,2*B);b.stroke();b.fill()},_tangents:function(b,
a,c,d){var e=b.x-c.x,g=b.y-c.y,f=a-d,k=e*e+g*g;if(!(k<=f*f)){var k=ha(k),e=-e/k,g=-g/k,f=f/k,k=[],h,l,m;h=ha(H(0,1-f*f));for(var p=1;-1<=p;p-=2)l=e*f-p*h*g,m=g*f+p*h*e,k.push({x1:b.x+a*l<<0,y1:b.y+a*m<<0,x2:c.x+d*l<<0,y2:c.y+d*m<<0});return k}}},K={draw:function(b,a,c,d,e,g,f){var k=450/(450-e);c=u.project({x:c.x-n,y:c.y-q},450/(450-d));d={x:0,y:0};for(var h={x:0,y:0},l=0,m=a.length-3;l<m;l+=2)d.x=a[l]-n,d.y=a[l+1]-q,h.x=a[l+2]-n,h.y=a[l+3]-q,e&&(d=u.project(d,k),h=u.project(h,k)),(h.x-d.x)*(c.y-
d.y)>(c.x-d.x)*(h.y-d.y)&&(b.fillStyle=d.x<h.x&&d.y<h.y||d.x>h.x&&d.y>h.y?f:g,b.beginPath(),this._triangle(b,d,h,c),b.closePath(),b.fill())},_triangle:function(b,a,c,d){b.moveTo(a.x,a.y);b.lineTo(c.x,c.y);b.lineTo(d.x,d.y)},_ring:function(b,a){b.moveTo(a[0]-n,a[1]-q);for(var c=2,d=a.length-1;c<d;c+=2)b.lineTo(a[c]-n,a[c+1]-q)},shadow:function(b,a,c,d,e){var g={x:0,y:0},f={x:0,y:0};c=v.project({x:c.x-n,y:c.y-q},d);d=0;for(var k=a.length-3;d<k;d+=2)g.x=a[d]-n,g.y=a[d+1]-q,f.x=a[d+2]-n,f.y=a[d+3]-q,
e&&(g=v.project(g,e),f=v.project(f,e)),(f.x-g.x)*(c.y-g.y)>(c.x-g.x)*(f.y-g.y)&&this._triangle(b,g,f,c)},shadowMask:function(b,a){this._ring(b,a)},hitArea:function(b,a,c,d,e,g){var f=450/(450-e);c=u.project({x:c.x-n,y:c.y-q},450/(450-d));d={x:0,y:0};var k={x:0,y:0};b.fillStyle=g;b.beginPath();g=0;for(var h=a.length-3;g<h;g+=2)d.x=a[g]-n,d.y=a[g+1]-q,k.x=a[g+2]-n,k.y=a[g+3]-q,e&&(d=u.project(d,f),k=u.project(k,f)),(k.x-d.x)*(c.y-d.y)>(c.x-d.x)*(k.y-d.y)&&this._triangle(b,d,k,c);b.closePath();b.fill()}},
u={project:function(b,a){return{x:(b.x-Q)*a+Q<<0,y:(b.y-R)*a+R<<0}},render:function(){var b=this.context;b.clearRect(0,0,y,z);if(!(15>x)){var a,c,d,e={x:Q+n,y:R+q},g,f,k,h,l=D.items;l.sort(function(a,b){return a.minHeight-b.minHeight||L(b.center,e)-L(a.center,e)||b.height-a.height});for(var m=0,p=l.length;m<p;m++)if(a=l[m],!ea.isSimple(a)&&(g=a.footprint,O(g))){c=1>a.scale?a.height*a.scale:a.height;d=0;a.minHeight&&(d=1>a.scale?a.minHeight*a.scale:a.minHeight);f=a.wallColor||da;k=a.altColor||Y;h=
a.roofColor||S;b.strokeStyle=k;switch(a.shape){case "cylinder":t.draw(b,a.center,a.radius,a.radius,c,d,f,k,h);break;case "cone":t.draw(b,a.center,a.radius,0,c,d,f,k);break;case "dome":t.draw(b,a.center,a.radius,a.radius/2,c,d,f,k);break;case "sphere":t.draw(b,a.center,a.radius,a.radius,c,d,f,k,h);break;case "pyramid":K.draw(b,g,a.center,c,d,f,k);break;default:U.draw(b,g,a.holes,c,d,f,k,h)}switch(a.roofShape){case "cone":t.draw(b,a.center,a.radius,0,c+a.roofHeight,c,h,""+E.parse(h).lightness(.9));
break;case "dome":t.draw(b,a.center,a.radius,a.radius/2,c+a.roofHeight,c,h,""+E.parse(h).lightness(.9));break;case "pyramid":K.draw(b,g,a.center,c+a.roofHeight,c,h,E.parse(h).lightness(.9))}}}}},ea={maxZoom:17,maxHeight:5,isSimple:function(b){return x<=this.maxZoom&&b.height+b.roofHeight<this.maxHeight},render:function(){var b=this.context;b.clearRect(0,0,y,z);if(!(15>x||x>this.maxZoom))for(var a,c,d=D.items,e=0,g=d.length;e<g;e++)if(a=d[e],!(a.height>=this.maxHeight)&&(c=a.footprint,O(c)))switch(b.strokeStyle=
a.altColor||Y,b.fillStyle=a.roofColor||S,a.shape){case "cylinder":case "cone":case "dome":case "sphere":t.simplified(b,a.center,a.radius);break;default:U.simplified(b,c,a.holes)}}},v={enabled:!0,color:"#666666",blurColor:"#000000",blurSize:15,date:new Date,direction:{x:0,y:0},project:function(b,a){return{x:b.x+this.direction.x*a,y:b.y+this.direction.y*a}},render:function(){var b=this.context,a,c,d;b.clearRect(0,0,y,z);if(!(!this.enabled||15>x||(a=fa(W+n,ba+q),a=za(this.date,a.latitude,a.longitude),
0>=a.altitude))){c=1/ga(a.altitude);d=5>c?.75:1/c*5;this.direction.x=xa(a.azimuth)*c;this.direction.y=wa(a.azimuth)*c;var e,g,f,k;a=D.items;b.canvas.style.opacity=d/(2*A);b.shadowColor=this.blurColor;b.shadowBlur=A/2*this.blurSize;b.fillStyle=this.color;b.beginPath();d=0;for(c=a.length;d<c;d++)if(e=a[d],k=e.footprint,O(k)){g=1>e.scale?e.height*e.scale:e.height;f=0;e.minHeight&&(f=1>e.scale?e.minHeight*e.scale:e.minHeight);switch(e.shape){case "cylinder":t.shadow(b,e.center,e.radius,e.radius,g,f);
break;case "cone":t.shadow(b,e.center,e.radius,0,g,f);break;case "dome":t.shadow(b,e.center,e.radius,e.radius/2,g,f);break;case "sphere":t.shadow(b,e.center,e.radius,e.radius,g,f);break;case "pyramid":K.shadow(b,k,e.center,g,f);break;default:U.shadow(b,k,e.holes,g,f)}switch(e.roofShape){case "cone":t.shadow(b,e.center,e.radius,0,g+e.roofHeight,g);break;case "dome":t.shadow(b,e.center,e.radius,e.radius/2,g+e.roofHeight,g);break;case "pyramid":K.shadow(b,k,e.center,g+e.roofHeight,g)}}b.closePath();
b.fill();b.shadowBlur=null;b.globalCompositeOperation="destination-out";b.beginPath();d=0;for(c=a.length;d<c;d++)if(e=a[d],k=e.footprint,O(k)&&!e.minHeight)switch(e.shape){case "cylinder":case "cone":case "dome":t.shadowMask(b,e.center,e.radius);break;default:U.shadowMask(b,k,e.holes)}b.fillStyle="#00ff00";b.fill();b.globalCompositeOperation="source-over"}}},T={_idMapping:[null],reset:function(){this._idMapping=[null]},render:function(){if(!this._timer){var b=this;this._timer=setTimeout(function(){b._timer=
null;b._render()},500)}},_render:function(){var b=this.context;b.clearRect(0,0,y,z);if(!(15>x)){var a,c,d,e={x:Q+n,y:R+q},g,f,k=D.items;k.sort(function(a,b){return a.minHeight-b.minHeight||L(b.center,e)-L(a.center,e)||b.height-a.height});for(var h=0,l=k.length;h<l;h++)if(a=k[h],f=a.hitColor)if(g=a.footprint,O(g)){c=a.height;d=0;a.minHeight&&(d=a.minHeight);switch(a.shape){case "cylinder":t.hitArea(b,a.center,a.radius,a.radius,c,d,f);break;case "cone":t.hitArea(b,a.center,a.radius,0,c,d,f);break;case "dome":t.hitArea(b,
a.center,a.radius,a.radius/2,c,d,f);break;case "sphere":t.hitArea(b,a.center,a.radius,a.radius,c,d,f);break;case "pyramid":K.hitArea(b,g,a.center,c,d,f);break;default:U.hitArea(b,g,a.holes,c,d,f)}switch(a.roofShape){case "cone":t.hitArea(b,a.center,a.radius,0,c+a.roofHeight,c,f);break;case "dome":t.hitArea(b,a.center,a.radius,a.radius/2,c+a.roofHeight,c,f);break;case "pyramid":K.hitArea(b,g,a.center,c+a.roofHeight,c,f)}}y&&z&&(this._imageData=this.context.getImageData(0,0,y,z).data)}},getIdFromXY:function(b,
a){var c=this._imageData;if(c){var d=4*((a|0)*y+(b|0));return this._idMapping[c[d]|c[d+1]<<8|c[d+2]<<16]}},idToColor:function(b){var a=this._idMapping.indexOf(b);-1===a&&(this._idMapping.push(b),a=this._idMapping.length-1);return"rgb("+[a&255,a>>8&255,a>>16&255].join()+")"}},V,P={container:document.createElement("DIV"),items:[],init:function(){this.container.style.pointerEvents="none";this.container.style.position="absolute";this.container.style.left=0;this.container.style.top=0;v.context=this.createContext(this.container);
ea.context=this.createContext(this.container);u.context=this.createContext(this.container);T.context=this.createContext()},render:function(b){ya(function(){b||(v.render(),ea.render(),T.render());u.render()})},createContext:function(b){var a=document.createElement("CANVAS");a.style.transform="translate3d(0, 0, 0)";a.style.imageRendering="optimizeSpeed";a.style.position="absolute";a.style.left=0;a.style.top=0;var c=a.getContext("2d");c.lineCap="round";c.lineJoin="round";c.lineWidth=1;c.imageSmoothingEnabled=
!1;this.items.push(a);b&&b.appendChild(a);return c},appendTo:function(b){b.appendChild(this.container)},remove:function(){this.container.parentNode.removeChild(this.container)},setSize:function(b,a){for(var c=0,d=this.items.length;c<d;c++)this.items[c].width=b,this.items[c].height=a},setPosition:function(b,a){this.container.style.left=b+"px";this.container.style.top=a+"px"}};P.init();w=function(b){this.map=b;this.maxExtent=[-2.003750834E7,-2.003750834E7,2.003750834E7,2.003750834E7];try{this.setMap(b),
b.addLayer(this)}catch(a){console.log(a)}};ol.inherits(w,ol.layer.Vector);C=w.prototype=ol.layer.Layer?new ol.layer.Vector({source:new ol.source.Vector({projection:ol.proj.get("EPSG:900913")})}):{};C.setOrigin=function(){var b=this.map;try{var a=b.getCoordinateFromPixel([0,0]),c=b.getView().getResolution(),d=this.maxExtent,e=(d[3]-a[1])/c<<0;n=(a[0]-d[0])/c<<0;q=e}catch(g){console.log(g)}};C.setMap=function(b){var a=this;P.appendTo(document.getElementById(b.get("target")));va({width:b.getSize()[0],
height:b.getSize()[1]});var c=this.map.getView().getProjection();b.on("click",function(a){var e=T.getIdFromXY(a.pixel[0],a.pixel[1]);e&&(a=ol.proj.transform(b.getCoordinateFromPixel([a.pixel[0],a.pixel[1]]),c,b.getView().getProjection()),oa({feature:e,lat:a[0],lon:a[1]}))});this.on("precompose",function(c){x=b.getView().getZoom();M=256<<x;c=fa(n+W,q+ba);var e=aa(c.latitude,0);na=aa(c.latitude,1).x-e.x;A=ja(.95,x-15);da=""+F.alpha(A);Y=""+X.alpha(A);S=""+J.alpha(A);a.setOrigin();D.resetItems();D.update()})};
C.style=function(b){b=b||{};var a;if(a=b.color||b.wallColor)F=E.parse(a),da=""+F.alpha(A),X=F.lightness(.8),Y=""+X.alpha(A),J=F.lightness(1.2),S=""+J.alpha(A);b.roofColor&&(J=E.parse(b.roofColor),S=""+J.alpha(A));void 0!==b.shadows&&(v.enabled=!!b.shadows);P.render();return this};C.date=function(b){v.date=b;v.render();return this};C.load=function(b){D.load(b);return this};C.set=function(b){D.set(b);return this};var ma=function(){};C.each=function(b){ma=function(a){return b(a)};return this};var oa=
function(){};C.click=function(b){oa=function(a){return b(a)};return this};w.VERSION="0.2.2b";w.ATTRIBUTION='&copy; <a href="https://osmbuildings.org">OSM Buildings</a>';Z.OSMBuildings=w})(this);
