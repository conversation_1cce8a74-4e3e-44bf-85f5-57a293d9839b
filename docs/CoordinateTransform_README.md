# 高德地图坐标转换为CGCS2000坐标系

## 概述

本项目提供了一个完整的PHP解决方案，用于将高德地图坐标系（GCJ02）转换为中国大地坐标系2000（CGCS2000）。转换过程分为两个步骤：

1. **GCJ02 → WGS84**：使用逆向算法将高德地图坐标转换为WGS84坐标
2. **WGS84 → CGCS2000**：使用七参数布尔沙模型进行坐标系转换

## 文件结构

```
app/Extends/Helpers/
├── CoordinateTransformer.php          # 主要转换类
├── Examples/
│   └── CoordinateTransformExample.php # 使用示例
app/Http/Controllers/
└── CoordinateController.php           # Laravel控制器
routes/
└── coordinate.php                      # API路由配置
tests/
└── CoordinateTransformTest.php        # 测试文件
docs/
└── CoordinateTransform_README.md      # 本文档
```

## 快速开始

### 1. 基本使用

```php
use App\Extends\Helpers\CoordinateTransformer;

// 创建转换器实例
$transformer = new CoordinateTransformer();

// 高德地图坐标（成都天府广场）
$gaodeLng = 104.072007;
$gaodeLat = 30.663480;
$height = 500.0; // 高程（米）

// 执行转换
$cgcs2000 = $transformer->gaodeToCGCS2000($gaodeLng, $gaodeLat, $height);

echo "CGCS2000坐标：\n";
echo "经度: {$cgcs2000['lng']}\n";
echo "纬度: {$cgcs2000['lat']}\n";
echo "高程: {$cgcs2000['h']}m\n";
```

### 2. 批量转换

```php
$coordinates = [
    ['lng' => 104.072007, 'lat' => 30.663480], // 成都天府广场
    ['lng' => 116.397477, 'lat' => 39.909652], // 北京天安门
    ['lng' => 121.487899, 'lat' => 31.240391], // 上海外滩
];

$results = $transformer->batchTransform($coordinates);

foreach ($results as $i => $result) {
    echo "点{$i}: {$result['lng']}, {$result['lat']}\n";
}
```

### 3. 自定义七参数

```php
// 设置自定义七参数（根据具体地区获取）
$customParams = [
    'dx' => -10.5,      // X平移(m)
    'dy' => 150.2,      // Y平移(m)
    'dz' => 170.1,      // Z平移(m)
    'rx' => 0.000015,   // X旋转(rad)
    'ry' => 0.000025,   // Y旋转(rad)
    'rz' => 0.000035,   // Z旋转(rad)
    's' => 1.000035     // 尺度因子
];

$transformer->setSevenParams($customParams);
```

## API接口使用

### 1. 路由注册

在 `routes/api.php` 中添加：

```php
require __DIR__.'/coordinate.php';
```

### 2. API端点

#### 单个坐标转换
```http
POST /api/coordinate/transform
Content-Type: application/json

{
    "lng": 104.072007,
    "lat": 30.663480,
    "h": 500
}
```

#### 批量坐标转换
```http
POST /api/coordinate/batch-transform
Content-Type: application/json

{
    "coordinates": [
        {"lng": 104.072007, "lat": 30.663480},
        {"lng": 116.397477, "lat": 39.909652}
    ],
    "h": 0
}
```

#### 设置七参数
```http
POST /api/coordinate/seven-params
Content-Type: application/json

{
    "dx": -16.9,
    "dy": 156.4,
    "dz": 173.8,
    "rx": 0.00001,
    "ry": 0.00002,
    "rz": 0.00003,
    "s": 1.00004
}
```

#### 获取七参数
```http
GET /api/coordinate/seven-params
```

### 3. 响应格式

成功响应：
```json
{
    "success": true,
    "message": "坐标转换成功",
    "data": {
        "input": {
            "lng": 104.072007,
            "lat": 30.663480,
            "h": 500,
            "coordinate_system": "GCJ02 (高德地图)"
        },
        "output": {
            "lng": 104.071234,
            "lat": 30.662567,
            "h": 500.123,
            "coordinate_system": "CGCS2000"
        },
        "difference": {
            "lng_diff": -0.000773,
            "lat_diff": -0.000913,
            "h_diff": 0.123
        }
    }
}
```

错误响应：
```json
{
    "success": false,
    "message": "参数验证失败",
    "errors": {
        "lng": ["经度参数必填"]
    }
}
```

## 前端调用示例

### JavaScript (Fetch API)

```javascript
// 单个坐标转换
async function transformCoordinate(lng, lat, h = 0) {
    try {
        const response = await fetch('/api/coordinate/transform', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ lng, lat, h })
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('转换成功:', data.data);
            return data.data.output;
        } else {
            console.error('转换失败:', data.message);
            return null;
        }
    } catch (error) {
        console.error('请求失败:', error);
        return null;
    }
}

// 使用示例
transformCoordinate(104.072007, 30.663480, 500)
    .then(result => {
        if (result) {
            console.log(`CGCS2000坐标: ${result.lng}, ${result.lat}`);
        }
    });
```

### jQuery

```javascript
// 批量转换
function batchTransformCoordinates(coordinates, h = 0) {
    $.ajax({
        url: '/api/coordinate/batch-transform',
        method: 'POST',
        dataType: 'json',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            coordinates: coordinates,
            h: h
        },
        success: function(response) {
            if (response.success) {
                console.log('批量转换成功:', response.data);
                response.data.results.forEach((item, index) => {
                    console.log(`点${index}: ${item.output.lng}, ${item.output.lat}`);
                });
            } else {
                console.error('批量转换失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}

// 使用示例
const coordinates = [
    {lng: 104.072007, lat: 30.663480},
    {lng: 116.397477, lat: 39.909652}
];
batchTransformCoordinates(coordinates);
```

## 测试

### 运行测试

```bash
# 运行PHP测试
php tests/CoordinateTransformTest.php

# 或者在Laravel项目中
php artisan test tests/CoordinateTransformTest.php
```

### 运行示例

```bash
# 运行使用示例
php -r "
require_once 'app/Extends/Helpers/CoordinateTransformer.php';
require_once 'app/Extends/Helpers/Examples/CoordinateTransformExample.php';
use App\Extends\Helpers\Examples\CoordinateTransformExample;
CoordinateTransformExample::runAllExamples();
"
```

## 技术说明

### 坐标系介绍

1. **GCJ02（高德坐标系）**：
   - 中国国家测绘局制定的地理信息系统的坐标系统
   - 在WGS84基础上进行了加密偏移
   - 高德地图、腾讯地图等使用此坐标系

2. **CGCS2000（中国大地坐标系2000）**：
   - 中国新一代大地坐标系
   - 与WGS84坐标系非常接近
   - 中国官方推荐使用的坐标系

### 转换精度

- **GCJ02 → WGS84**：精度约为1-2米
- **WGS84 → CGCS2000**：精度取决于七参数的准确性，通常可达厘米级

### 七参数说明

七参数是两个坐标系之间转换的关键参数：

- **dx, dy, dz**：三个平移参数（米）
- **rx, ry, rz**：三个旋转参数（弧度）
- **s**：尺度因子

**注意**：示例中的七参数仅供测试使用，实际应用中需要根据具体地区获取精确的七参数。

## 注意事项

1. **七参数获取**：不同地区的七参数不同，需要向当地测绘部门获取精确参数
2. **坐标范围**：主要适用于中国境内坐标，境外坐标转换精度可能较低
3. **性能考虑**：批量转换时建议每次不超过1000个坐标点
4. **精度要求**：如需高精度转换，建议使用专业测绘软件

## 常见问题

### Q: 为什么转换后的坐标与预期不符？
A: 可能是七参数不准确，建议获取当地精确的七参数。

### Q: 可以转换其他坐标系吗？
A: 当前只支持GCJ02到CGCS2000的转换，如需其他转换可扩展代码。

### Q: 转换精度如何？
A: GCJ02到WGS84精度约1-2米，WGS84到CGCS2000精度取决于七参数准确性。

### Q: 支持海外坐标转换吗？
A: 支持，但海外坐标GCJ02与WGS84基本一致，主要进行WGS84到CGCS2000转换。

## 许可证

本项目遵循MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
