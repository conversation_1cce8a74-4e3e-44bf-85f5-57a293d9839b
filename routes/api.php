<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::prefix('v1')->group(function () {
    //无需登录
    Route::controller(\App\Http\Controllers\Api\V1\PublicController::class)->group(function (){
        Route::get('/captcha','captcha');
        Route::post('/login','login');
        Route::get('/version','version');
    });
    //验证登录
    Route::middleware(['api.token:app,token'])->group(function() {
        //公共接口
        Route::controller(\App\Http\Controllers\Api\V1\CommonController::class)->prefix('common')->group(function (){
            Route::post('/upload','uploadFile')->name("upload");//文件上传
            Route::post('/publish','publish');//文件上传
            Route::get('/get_dict_data','getDictData');//获取字典
        });
        //用户相关接口
        Route::controller(\App\Http\Controllers\Api\V1\UserController::class)->prefix('user')->group(function (){
            Route::get('/info','info');
            Route::post('/logout','logout');
            Route::put('/editpwd','editPwd');
            Route::get('/menu','menu');
            Route::post('/online','online');
            Route::post('/bind_client_id','bind_client_id');
            Route::get('/bind_qdz','bind_qdz');
            Route::post('/bind_qdz','bind_qdz');
        });
        //网格员入户
        Route::controller(\App\Http\Controllers\Api\V1\HoldhouseController::class)->prefix('hold')->group(function (){
            Route::get('/main','main');
            Route::get('/special','special');
            Route::post('/add','add');
            Route::get('/list','list');
            Route::get('/detail','detail');
            Route::get('/getqua','getqua');
            Route::get('/getld','getld');
            Route::get('/getunit','getunit');
            Route::get('/getfloor','getfloor');
            Route::get('/gethouse','gethouse');
        });
        //网格员居民管理
        Route::controller(\App\Http\Controllers\Api\V1\ResidentController::class)->prefix('resident')->group(function (){
            Route::get('/list','list');//列表
            Route::get('/getselect','getselect');//筛选字段 house_id
            Route::post('/delhouse','delhouse');//删除房间  house_id
            Route::post('/del','del');//删除居民  resident_id
            Route::get('/houseinfo','houseinfo'); //房屋基本信息   house_id
            Route::post('/edithouse','edithouse');//编辑房屋信息
            Route::get('/detail','detail'); // 居民id
            Route::post('/edit','edit');//编辑居民信息
            Route::post('/add','add');//新增居民信息
            Route::get('/extend','extend');//新增或编辑居民 获取扩展信息
            Route::get('/check','check');//验证身份证  id_card
        });
        //小区相关接口
        Route::controller(\App\Http\Controllers\Api\V1\QuartersController::class)->prefix('quarters')->group(function (){
            Route::get('/detail','detail');//小区详情
            Route::post('/settingname','settingname');//设置小区党支部/业务公司/居民小组
            Route::post('/delgroup','delgroup');//删除居民分组
            Route::post('/settinguser','settinguser');//新增/编辑人员
            Route::post('/deluser','deluser'); //删除人员
            Route::get('/list','list');//获取楼栋单元列表
        });
        //巡防相关接口
        Route::controller(\App\Http\Controllers\Api\V1\WatchmanController::class)->prefix('watchman')->group(function (){
            Route::get('/index','index');//获取巡防首页系数
            Route::post('/scopelimit','scopelimit');//判断巡防设备是否在巡防的范围内
            Route::post('/start','start');//开始巡防
            Route::post('/end','end');//结束巡防
            Route::get('/list','list'); //获取列表
            Route::get('/detail','detail');//巡防详情
        });
        //网格员工作下派
        Route::controller(\App\Http\Controllers\Api\V1\WorkAssignController::class)->prefix('workassign')->group(function (){
            Route::get('/filter','filter');
            Route::get('/statuscount','statusCount');
            Route::get('/list', "index");
            Route::get('/info/{id}', "info")->where('id', '^[1-9]\d*$');
            Route::post('/record/{id}', "recordSave")->where('id', '^[1-9]\d*$');
            Route::put('/record/{id}', "recordUpdate")->where('id', '^[1-9]\d*$');
            Route::delete('/record/{id}', "recordDelete")->where('id', '^[1-9]\d*$');
            Route::get('/recordlist/{id}', "recordList")->where('id', '^[1-9]\d*$');
            Route::post('/subaudit/{id}', "subAudit")->where('id', '^[1-9]\d*$');
        });
        //考勤
        Route::controller(\App\Http\Controllers\Api\V1\ClockController::class)->prefix('kaoqin')->group(function (){
            Route::get('/getIndex','getIndex');//考勤首屏打卡信息
            Route::post('/checkClockScope','checkClockScope');//检测是否进入打卡范围内
            Route::post('/clock','clock');//打卡
            Route::any('/getMonthClockList','getMonthClockList');//获取月打卡记录
            Route::any('/getDayClockInfo','getDayClockInfo');//获取日打卡记录
            Route::any('/getLeaveType','getLeaveType');//获取请假类型
            Route::any('/getApplyInfo','getApplyInfo');//获取【请假、外出、加班】申请记录
            Route::any('/getApplyList','getApplyList');//获取【请假、外出、加班】申请列表
            Route::post('/addApply','addApply');//发起【请假、外出、加班】申请
            Route::post('/calcDuration','calcDuration');//自动计算申请时长
            Route::post('/editApply','editApply');//修改【请假、外出、加班】申请，不再允许变更类型
            Route::any('/revokeApply','revokeApply');//撤销【请假、外出、加班】申请
            Route::any('/scheduling','scheduling');//城管排班
        });
        //商铺
        Route::controller(\App\Http\Controllers\Api\V1\ShopController::class)->prefix('shop')->group(function (){
            Route::get('/getIndex','getIndex');//入商铺首屏小区及主街列表
            Route::any('/getDicList','getDicList');//获取商铺属性关联字典（类型、行业...）
            Route::any('/getList','getList');//获取商铺列表
            Route::get('/getShopInfo','getShopInfo');//获取商铺详情信息
            Route::post('/addShop','addShop');//新增商铺
            Route::post('/editShop','editShop');//修改商铺
            Route::any('/delShop','delShop');//删除商铺
            Route::any('/getShopHoldList','getShopHoldList');//获取商铺入户记录列表
            Route::get('/getShopHoldInfo','getShopHoldInfo');//获取商铺入户记录
            Route::post('/addShopHold','addShopHold');//新增商铺入户记录
            Route::any('/getIndustrys','getIndustrys');//获取商户行业分类
            Route::any('/extend','extend');//新增商铺对扩展字段处理
        });
        //工作
        Route::controller(\App\Http\Controllers\Api\V1\WorkController::class)->prefix('work')->group(function (){
            Route::any('/gridTemplateList','gridTemplateList');//获取网格上报表单模板列表
            Route::any('/gridTemplateDetail','gridTemplateDetail');//获取网格上报表单项目详情
            Route::any('/gridreport','gridreport');//网格上报
            Route::any('/urbanTemplateList','urbanTemplateList');//获取城管上报表单模板列表
            Route::any('/urbanTemplateDetail','urbanTemplateDetail');//获取城管上报表单项目详情
            Route::any('/urbanreport','urbanreport');//城管上报
            Route::any('/urbanPatrolTemplateList','urbanPatrolTemplateList');//获取城管巡查表单模板列表
            Route::any('/urbanPatrolTemplateDetail','urbanPatrolTemplateDetail');//获取城管巡查表单项目详情
            Route::any('/urbanrPatrol','urbanrPatrol');//城管巡查
            Route::any('/comprehensiveTemplateList','comprehensiveTemplateList');//获取城综治报表单模板列表
            Route::any('/comprehensiveTemplateDetail','comprehensiveTemplateDetail');//获取综治上报表单项目详情
            Route::any('/comprehensivereport','comprehensivereport');//综治上报

            Route::any('/gridReportList','gridReportList');//获取网格上报历史记录
            Route::any('/gridReportDetail','gridReportDetail');//获取网格上报历史记录详情
            Route::any('/gridReportEdit','gridReportEdit');//获取网格上报历史记录详情编辑

            Route::any('/urbanReportList','urbanReportList');//获取城管上报历史记录
            Route::any('/urbanReportDetail','urbanReportDetail');//获取城管上报历史记录详情
            Route::any('/urbanReportEdit','urbanReportEdit');//获取城管上报历史记录详情编辑
            Route::any('/urbanPatrolList','urbanPatrolList');//获取城管巡查历史记录
            Route::any('/urbanPatrolDetail','urbanPatrolDetail');//获取城管巡查历史记录详情
            Route::any('/urbanPatrolEdit','urbanPatrolEdit');//获取城管巡查历史记录详情编辑

            Route::any('/comprehensiveReportList','comprehensiveReportList');//获取综治上报历史记录
            Route::any('/comprehensiveReportDetail','comprehensiveReportDetail');//获取综治上报历史记录详情
            Route::any('/comprehensiveReportEdit','comprehensiveReportEdit');//获取综治上报历史记录详情编辑
        });
        //待办
        Route::controller(\App\Http\Controllers\Api\V1\WaitHandleController::class)->prefix('waithandle')->group(function (){
            Route::get('/home','home');
            Route::get('/count','count');
            Route::get('/list','list');
        });
        //事件
        Route::controller(\App\Http\Controllers\Api\V1\EventController::class)->prefix('event')->group(function (){
            Route::get('/common','common');
            Route::post('/add','add');
            Route::get('/listcount','listcount');
            Route::get('/list','list');
            Route::get('/detail/{id}','detail')->where('id', '^[1-9]\d*$');
            Route::put('/reissue/{id}','reissue')->where('id', '^[1-9]\d*$');
            Route::put('/cancel/{id}','cancel')->where('id', '^[1-9]\d*$');
            Route::post('/dispose/{es_id}','dispose')->where('es_id', '^[1-9]\d*$');
            Route::get('/disposeecho/{es_id}','disposeEcho')->where('es_id', '^[1-9]\d*$');
        });
        //大联动
        Route::controller(\App\Http\Controllers\Api\V1\DldEventController::class)->prefix('dldevent')->group(function (){
            Route::get('/common','common');
            Route::post('/add','add');
            Route::post('/disposal/{id}','disposal')->where('id', '^[1-9]\d*$');
            Route::post('/sign/{id}','sign')->where('id', '^[1-9]\d*$');
            Route::get('/listcount','listcount');
            Route::get('/list','list');
            Route::get('/detail/{id}','detail')->where('id', '^[1-9]\d*$');
        });
        //数字城管
        Route::controller(\App\Http\Controllers\Api\V1\SzcgController::class)->prefix('szcg')->group(function (){
            Route::get('/list','list');
            Route::get('/detail','detail');
            Route::post('/dispose','dispose');
        });
        //风险产所
        Route::controller(\App\Http\Controllers\Api\V1\RiskController::class)->prefix('risk')->group(function (){
            Route::get('/common','common');
            Route::get('/list','list');
            Route::post('/add','add');
            Route::put('/edit','edit');
            Route::get('/detail','detail');
            Route::get('/type','type');
            Route::get('/sitetemplate','sitetemplate');
            Route::delete('/delete/{id}','delete')->where('id', '^[1-9]\d*$');
        });
        //巡查
        Route::controller(\App\Http\Controllers\Api\V1\PatrolController::class)->prefix('patrol')->group(function (){
            Route::get('/info','info');//编辑专用
            Route::get('/del','del');
            Route::post('/edit','edit');
            Route::post('/list','list');
            Route::get('/detail','detail');
            Route::get('/patroldetail','patroldetail');
            Route::get('/adddict','adddict');
            Route::get('/listdict','listdict');
        });
        //隐患
        Route::controller(\App\Http\Controllers\Api\V1\DangerController::class)->prefix('danger')->group(function (){
            Route::post('/revise','revise');
            Route::post('/list','list');
            Route::get('/detail','detail');
            Route::get('/listdict','listdict');
        });
        //事件中心
        Route::controller(\App\Http\Controllers\Api\V1\EventCenterController::class)->prefix('eventcenter')->group(function (){
            Route::get('/auth','auth');
            Route::get('/getrisk','getRisk');
            Route::get('/watchusers','watchUsers');
            Route::get('/getriskchecklist','getRiskCheckList');
            Route::post('/add','add');
        });
        //我发起的
        Route::controller(\App\Http\Controllers\Api\V1\UserInitiateController::class)->prefix('initiate')->group(function (){
            Route::get('/count','count');
            Route::get('/list','list');
        });
    });
});

Route::prefix('manage')->group(function () {
    //无需登录
    Route::controller(\App\Http\Controllers\Api\Manage\PublicController::class)->group(function (){
        Route::get('/captcha','captcha');
        Route::post('/login','login');
        Route::get('/version','version');
    });
    //登录验证接口
    Route::middleware(['api.token:manage,token'])->group(function() {
        //公共接口
        Route::controller(\App\Http\Controllers\Api\Manage\CommonController::class)->group(function (){
            Route::get('/community','community');
            Route::get('/department','department');
            Route::get('/allcommunity','allcommunity');
            Route::get('/communitytwo','communityTwo');
            Route::get('/manageleader','manageLeader');
            Route::post('/upload','uploadfile')->name("upload");//文件上传
            Route::get('/gridmain','gridmain');//首页网格部分数据
            Route::get('/urbanmain','urbanmain');//首页城管部分数据
            Route::get('/getUrbanGroups','getUrbanGroups');//获取城管分组
            Route::get('/get_dict_data','getDictData');//获取字典
            Route::get('/eventmain','eventmain');//获取字典
        });
        //用户接口
        Route::controller(\App\Http\Controllers\Api\Manage\UserController::class)->prefix('user')->group(function (){
            Route::get('/info','info');
            Route::post('/logout','logout');
            Route::put('/editpwd','editPwd');
            Route::get('/menu','menu');
            Route::post('/online','online');
            Route::get('/bind_qdz','bind_qdz');
            Route::post('/bind_qdz','bind_qdz');
        });
        //入户 巡防 人商户
        Route::controller(\App\Http\Controllers\Api\Manage\GridworkController::class)->prefix('gridwork')->group(function (){
            Route::get('/holdmain','holdmain'); //网格入户首页
            Route::get('/holdgridmain','holdgridmain');
            Route::get('/holdlist','holdlist');
            Route::get('/holddetail','holddetail');
            Route::get('/watchmanmain','watchmanmain');
            Route::get('/watchmangridmain','watchmangridmain');
            Route::get('/watchmanlist','watchmanlist');
            Route::get('/watchmandetail','watchmandetail');
            Route::get('/shopmain','shopmain');
            Route::get('/shopgridmain','shopgridmain');
            Route::get('/shoplist','shoplist');
            Route::get('/shopdetail','shopdetail');
        });
        //在干 轨迹(含综治)
        Route::controller(\App\Http\Controllers\Api\Manage\UrbanworkController::class)->prefix('urbanwork')->group(function (){
            Route::get('/list','list'); //在岗列表
            Route::get('/gjlist','gjlist');//轨迹列表
            Route::get('/getgjselect','getgjselect');//筛选项
            Route::get('/gjdetail','gjdetail');//轨迹详情
            Route::get('/stop','stop');//
            Route::get('/zzgjlist','zzgjlist');
            Route::get('/zzgjdetail','zzgjdetail');
            Route::get('/zzgjstop','zzgjstop');
        });
        //基础数据
        Route::controller(\App\Http\Controllers\Api\Manage\BasedataController::class)->prefix('basedata')->group(function (){
            Route::get('/main','main'); //网格入户首页
            Route::get('/synthesize','synthesize');
            Route::get('/getgridlistandres','getgridlistandres');
            Route::get('/getresinfos','getresinfos');
            Route::get('/resinfo','resinfo');
            Route::get('/list','list');
            Route::get('/getselect','getselect');
            Route::get('/reslist','reslist');
            Route::get('/detail','detail');
            Route::get('/getquaselect','getquaselect');
            Route::get('/shopmain','shopmain');
            Route::get('/shoplist','shoplist');
            Route::get('/getshopselect','getshopselect');
            Route::get('/qualist','qualist');
        });
        //工作下派
        Route::controller(\App\Http\Controllers\Api\Manage\WorkAssignController::class)->prefix('workassign')->group(function (){
            Route::get('/workcount','workcount');//首页任务统计
            Route::get('/worklist','worklist');//任务列表
            Route::get('/worklistcount','worklistcount');//任务列表筛选统计
            Route::get('/filter','filter');//任务列表筛选项
            Route::get('/assignuser/{publish_type}', 'assignUser');//根据下派类型获取下派人员列表
            Route::post('/publish','publish');//新增下派任务
            Route::get('/work/{id}','detail')->where('id', '^[1-9]\d*$');//任务详情
            Route::put('/work/{id}','edit')->where('id', '^[1-9]\d*$');//编辑任务
            Route::delete('/work/{id}','delete')->where('id', '^[1-9]\d*$');//删除任务
            Route::put('/cancel/{id}','cancel')->where('id', '^[1-9]\d*$');//取消任务
            Route::get('/community/{id}','community')->where('id', '^[1-9]\d*$');//社区完成情况
            Route::get('/grid/{wac_id}','grid')->where('wac_id', '^[1-9]\d*$');//网格员完成情况
            Route::get('/record/{wag_id}','record')->where('wag_id', '^[1-9]\d*$');//网格员工作详情
            Route::get('/reportinfo/{wac_id}','reportinfo')->where('wac_id', '^[1-9]\d*$');//社区上报情况
            Route::post('/auditstruct/{id}','auditStruct')->where('id', '^[1-9]\d*$');//城运中心审核科室下派
            Route::post('/auditcommunity/{wac_id}','auditCommunity')->where('wac_id', '^[1-9]\d*$');//审核社区上报
            Route::post('/auditgriduser/{wag_id}','auditGridUser')->where('wag_id', '^[1-9]\d*$');//审核网格员上报
            Route::get('/structcc/{id}','structcc')->where('id', '^[1-9]\d*$');//抄送科室人员回显
            Route::post('/structcc/{id}','structcc')->where('id', '^[1-9]\d*$');//抄送科室人员
            Route::get('/assign/{id}','assignMain')->where('id', '^[1-9]\d*$');//分管领导下派回显（主要领导下派）
            Route::post('/assign/{id}','assignMain')->where('id', '^[1-9]\d*$');//分管领导下派（主要领导下派）
            Route::get('/assigncommunity/{id}','assignCommunity')->where('id', '^[1-9]\d*$');//城运下派回显 （主要领导、分管领导下派）
            Route::post('/assigncommunity/{id}','assignCommunity')->where('id', '^[1-9]\d*$');//城运下派 （主要领导、分管领导下派）
            Route::post('/assignhelp/{wac_id}','assignHelp')->where('wac_id', '^[1-9]\d*$');//下派统筹员
            Route::get('/assigngriduser/{wac_id}','assignGridUser')->where('wac_id', '^[1-9]\d*$');//下派网格员回显
            Route::post('/assigngriduser/{wac_id}','assignGridUser')->where('wac_id', '^[1-9]\d*$');//下派网格员
            Route::post('/backgrid/{wac_id}','backgrid')->where('wac_id', '^[1-9]\d*$');//回退网格员
            Route::get('/gridlist/{wac_id}','gridList')->where('wac_id', '^[1-9]\d*$');//下派的网格员列表
            Route::post('/report/{wac_id}','report')->where('wac_id', '^[1-9]\d*$');//上报
        });
        //待办
        Route::controller(\App\Http\Controllers\Api\Manage\WaitHandleController::class)->prefix('waithandle')->group(function (){
            Route::get('/home','home');
            Route::get('/count','count');
            Route::get('/list','list');
        });
        //工作
        Route::controller(\App\Http\Controllers\Api\Manage\WorkController::class)->prefix('work')->group(function (){
            Route::any('/gridReportCount','gridReportCount');//获取网格上报数量
            Route::any('/gridReportList','gridReportList');//获取网格上报记录列表
            Route::any('/gridReportDetail','gridReportDetail');//获取网格上报详情
            Route::any('/gridInstructions','gridInstructions');//网格批示
            Route::any('/urbanReportCount','urbanReportCount');//获取城管上报数量
            Route::any('/urbanReportList','urbanReportList');//城管上报表单列表
            Route::any('/urbanReportDetail','urbanReportDetail');//获取城管上报详情
            Route::any('/urbanInstructions','urbanInstructions');//城管上报批示
            Route::any('/urbanPatrolCount','urbanPatrolCount');//获取城管巡查上报数量

            Route::any('/urbanPatrol','urbanPatrol');//城管巡查模板列表
            Route::any('/urbanPatrolGroupList','urbanPatrolGroupList');//城管巡查分组上报
            Route::any('/urbanPatrolRecord','urbanPatrolRecord');//城管巡查记录
            Route::any('/urbanPatrolRecordDetail','urbanPatrolRecordDetail');//城管巡查记录详情

            Route::any('/urbanPatrolList','urbanPatrolList');//城管巡查表单列表
            Route::any('/urbanPatrolDetail','urbanPatrolDetail');//获取城管巡查详情
            Route::any('/urbanPatrolInstructions','urbanPatrolInstructions');//城管巡查批示
            Route::any('/urbanPatrolCount','urbanPatrolCount');//获取城管巡查上报数量
            Route::any('/comprehensivePatrolCount','comprehensivePatrolCount');//获取综治上报数量
            Route::any('/comprehensiveReportList','comprehensiveReportList');//综治上报表单列表
            Route::any('/comprehensiveReportDetail','comprehensiveReportDetail');//获取综治上报详情
            Route::any('/comprehensiveReportInstructions','comprehensiveReportInstructions');//综治上报批示
        });
        //出勤管理
        Route::controller(\App\Http\Controllers\Api\Manage\KaoqinController::class)->prefix('kaoqin')->group(function (){
            Route::any('/getKaoqinStatusList','getKaoqinStatusList');
            Route::any('/getKaoqinList','getKaoqinList');
            Route::any('/getApplyInfo','getApplyInfo');
            Route::any('/getApplyList','getApplyList');
            Route::any('/getLeaveTypes','getLeaveTypes');
            Route::post('/audit','audit');
            Route::any('/getTypeList','getTypeList');
        });
        //指挥调度
        Route::controller(\App\Http\Controllers\Api\Manage\DispatchController::class)->prefix('dispatch')->group(function (){
            Route::any('/getAllUsers','getAllUsers');
            Route::any('/getUserList','getUserList');
            Route::any('/getUserInfo','getUserInfo');
            Route::any('/getUserWork','getUserWork');
        });
        //事件
        Route::controller(\App\Http\Controllers\Api\Manage\EventController::class)->prefix('event')->group(function (){
            Route::get('/common','common');
            Route::post('/add','add');
            Route::get('/list','list');
            Route::get('/listcount','listcount');
            Route::get('/detail/{id}','detail')->where('id', '^[1-9]\d*$');
            Route::put('/reissue/{id}','reissue')->where('id', '^[1-9]\d*$');
            Route::post('/dispose/{es_id}','dispose')->where('es_id', '^[1-9]\d*$');
            Route::get('/disposeecho/{es_id}','disposeEcho')->where('es_id', '^[1-9]\d*$');
            Route::put('/cancel/{id}','cancel')->where('id', '^[1-9]\d*$');
            Route::any('/ophandle/{es_id}', 'ophandle')->where('es_id', '^[1-9]\d*$');//科室经办人处理
            Route::any('/slhandle/{es_id}', 'slhandle')->where('es_id', '^[1-9]\d*$');//科室科长处理
            Route::post('/disposeaudit/{es_id}', 'disposeAudit')->where('es_id', '^[1-9]\d*$');//处置审核
            Route::post('/backassign/{es_id}', 'backassign')->where('es_id', '^[1-9]\d*$');//回退

            Route::any('/fghandle/{id}', 'fghandle')->where('id', '^[1-9]\d*$');//分管领导处理
            Route::post('/fgback/{id}', 'fgback')->where('id', '^[1-9]\d*$');//分管领导回退
            Route::any('/zyhandle/{id}', 'zyhandle')->where('id', '^[1-9]\d*$');//主要领导处理
        });
        //大联动
        Route::controller(\App\Http\Controllers\Api\Manage\DldEventController::class)->prefix('dldevent')->group(function (){
            Route::get('/common','common');
            Route::get('/list','list');
            Route::get('/listcount','listcount');
            Route::get('/detail/{id}','detail')->where('id', '^[1-9]\d*$');
            Route::post('/sign/{id}','sign')->where('id', '^[1-9]\d*$');
            Route::post('/assign/{id}','assign')->where('id', '^[1-9]\d*$');
            Route::get('/assign/{id}','assign')->where('id', '^[1-9]\d*$');
            Route::post('/verify/{id}','verify')->where('id', '^[1-9]\d*$');
            Route::post('/completed/{id}','completed')->where('id', '^[1-9]\d*$');
            Route::post('/cancel/{id}','cancel')->where('id', '^[1-9]\d*$');
        });
        //数字城管
        Route::controller(\App\Http\Controllers\Api\Manage\SzcgController::class)->prefix('szcg')->group(function (){
            Route::get('/list','list');
            Route::get('/detail','detail');
            Route::post('/handle','handle');
            Route::post('/audit','audit');
            Route::post('/delay','delay');
        });
        //安全巡查
        Route::controller(\App\Http\Controllers\Api\Manage\PatrolController::class)->prefix('patrol')->group(function (){
            Route::post('/list','list');
            Route::get('/detail','detail');
            Route::get('/patroldetail','patroldetail');
            Route::get('/listdict','listdict');
        });
        //安全巡查-隐患
        Route::controller(\App\Http\Controllers\Api\Manage\DangerController::class)->prefix('danger')->group(function (){
            Route::post('/list','list');
            Route::get('/detail','detail');
            Route::get('/listdict','listdict');
            Route::get('/indexcount','indexcount');
        });

        //我发起的
        Route::controller(\App\Http\Controllers\Api\Manage\AdminInitiateController::class)->prefix('initiate')->group(function (){
            Route::get('/count','count');
            Route::get('/list','list');
        });
        //抄送我的
        Route::controller(\App\Http\Controllers\Api\Manage\AdminCarbonCopyController::class)->prefix('cc')->group(function (){
            Route::get('/count','count');
            Route::get('/list','list');
        });

        //风险产所
        Route::controller(\App\Http\Controllers\Api\Manage\RiskController::class)->prefix('risk')->group(function (){
            Route::get('/common','common');
            Route::get('/list','list');
            Route::get('/detail','detail');
            Route::get('/type','type');
            Route::get('/sitetemplate','sitetemplate');
        });
    });
});

