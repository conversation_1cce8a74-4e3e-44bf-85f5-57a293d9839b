<?php
// +----------------------------------------------------------------------
// | 坐标转换API路由
// | Author: AI Assistant
// +----------------------------------------------------------------------

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CoordinateController;

/*
|--------------------------------------------------------------------------
| 坐标转换API路由
|--------------------------------------------------------------------------
|
| 这里定义了高德地图坐标转换为CGCS2000坐标系的相关API路由
|
*/

Route::prefix('coordinate')->group(function () {
    
    // 获取API使用说明
    Route::get('/', [CoordinateController::class, 'getApiDoc'])
        ->name('coordinate.doc');
    
    // 单个坐标转换
    Route::post('transform', [CoordinateController::class, 'transform'])
        ->name('coordinate.transform');
    
    // 批量坐标转换
    Route::post('batch-transform', [CoordinateController::class, 'batchTransform'])
        ->name('coordinate.batch-transform');
    
    // 七参数管理
    Route::get('seven-params', [CoordinateController::class, 'getSevenParams'])
        ->name('coordinate.get-seven-params');
    
    Route::post('seven-params', [CoordinateController::class, 'setSevenParams'])
        ->name('coordinate.set-seven-params');
});

/*
|--------------------------------------------------------------------------
| 使用示例
|--------------------------------------------------------------------------
|
| 1. 单个坐标转换:
|    POST /api/coordinate/transform
|    {
|        "lng": 104.072007,
|        "lat": 30.663480,
|        "h": 500
|    }
|
| 2. 批量坐标转换:
|    POST /api/coordinate/batch-transform
|    {
|        "coordinates": [
|            {"lng": 104.072007, "lat": 30.663480},
|            {"lng": 116.397477, "lat": 39.909652}
|        ],
|        "h": 0
|    }
|
| 3. 设置七参数:
|    POST /api/coordinate/seven-params
|    {
|        "dx": -16.9,
|        "dy": 156.4,
|        "dz": 173.8,
|        "rx": 0.00001,
|        "ry": 0.00002,
|        "rz": 0.00003,
|        "s": 1.00004
|    }
|
| 4. 获取七参数:
|    GET /api/coordinate/seven-params
|
| 5. 获取API文档:
|    GET /api/coordinate/
|
*/
