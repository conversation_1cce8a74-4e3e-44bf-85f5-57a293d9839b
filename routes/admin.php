<?php
use Illuminate\Support\Facades\Route;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
//指挥调度[渲染主页]
Route::controller(\App\Http\Controllers\Admin\System\DispatchController::class)->prefix('dispatch')->name('dispatch.')->group(function (){
    Route::any('/index', 'index')->name('index');
    Route::any('/getUserList', 'getUserList')->name('getUserList'); //获取人员列表
    Route::any('/getUserInfo', 'getUserInfo')->name('getUserInfo'); //获取人员详情
});
//指挥调度-网格员
Route::controller(\App\Http\Controllers\Admin\System\GridUserController::class)->prefix('griduser')->name('griduser.')->group(function (){
    Route::any('/index', 'index')->name('index');
});
//指挥调度-消防队员
Route::controller(\App\Http\Controllers\Admin\System\FirefighterController::class)->prefix('firefighter')->name('firefighter.')->group(function (){
    Route::any('/index', 'index')->name('index');
});
//指挥调度-综治人员
Route::controller(\App\Http\Controllers\Admin\System\ComprehensiveController::class)->prefix('comprehensive')->name('comprehensive.')->group(function (){
    Route::any('/index', 'index')->name('index');
});
//指挥调度-城管队员
Route::controller(\App\Http\Controllers\Admin\System\UrbanController::class)->prefix('urban')->name('urban.')->group(function (){
    Route::any('/index', 'index')->name('index');
});
//登录后操作路由
Route::middleware(['admin.login','admin.auth'])->group(function(){
    //Index控制器
    Route::controller(\App\Http\Controllers\Admin\IndexController::class)->group(function (){
        Route::get('/', 'index')->name('admin.index');
        Route::get('/home', 'home')->name('admin.home');
        Route::get('/download', 'download')->name('admin.download');
        Route::get('/skin', 'skin')->name('admin.skin');
        Route::get('/nav-style', 'navStyle')->name('admin.nav-style');
    });
    //Public控制器
    Route::controller(\App\Http\Controllers\Admin\PublicController::class)->group(function (){
        Route::any('/login','login')->name('admin.login');
        Route::get('/logout','logout')->name('admin.logout');
        Route::get('/noauth','noauth')->name('admin.noauth');
    });
    //Common控制器
    Route::controller(\App\Http\Controllers\Admin\CommonController::class)->group(function (){
        Route::post('/uploadimg','uploadimg')->name('admin.uploadimg');
        Route::post('/uploadvideo','uploadvideo')->name('admin.uploadvideo');
        Route::post('/uploadfile','uploadfile')->name('admin.uploadfile');
        Route::get('/cropper','cropper')->name('admin.cropper');
        Route::any('/lockscreen','lockscreen')->name('admin.lockscreen');
        Route::any('/cacheclear','cacheclear')->name('admin.cacheclear');
        Route::any('/repass','repass')->name('admin.repass');
        Route::any('/getpropertyuser','getPropertyUser')->name('admin.getpropertyuser');
        Route::any('/getshopoperatetype','getShopOperateType')->name('admin.getshopoperatetype');
        Route::any('/getindustryextend','getIndustryExtend')->name('admin.getindustryextend');
        Route::any('/lnglat','lnglat')->name('admin.lnglat');
        Route::any('/getgridbycid','getGridByCid')->name('admin.getgridbycid');
        Route::any('/getadminbysid','getAdminBySid')->name('admin.getadminbysid');
        Route::any('/getmicrogridbygridid','getMicrogridMyGridId')->name('admin.getmicrogridbygridid');
        Route::any('/getgridselectunit','getGridSelectUnit')->name('admin.getgridselectunit');
        Route::any('/getmicrogridselectunit','getMicrogridSelectUnit')->name('admin.getmicrogridselectunit');
        Route::any('/searchAddress','searchAddress')->name('admin.searchAddress');
    });
    //待办
    Route::controller(\App\Http\Controllers\Admin\WaitHandleController::class)->prefix('waithandle')->name('waithandle.')->group(function (){
        Route::get('/count','count')->name('count');
        Route::get('/list','list')->name('list');
    });
    //系统模块
    Route::prefix('system')->name('system.')->group(function (){
        //指挥调度
        Route::controller(\App\Http\Controllers\Admin\System\DispatchController::class)->prefix('dispatch')->name('dispatch.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/getUserList', 'getUserList')->name('getUserList'); //获取人员列表
            Route::any('/getUserInfo', 'getUserInfo')->name('getUserInfo'); //获取人员详情
            Route::any('/viewwork', 'viewwork')->name('viewwork');
            Route::any('/viewguiji', 'viewguiji')->name('viewguiji');
            Route::post('/ajaxguijipoints', 'ajaxguijipoints')->name('ajaxguijipoints');
            Route::post('/ajaxstoppoints', 'ajaxstoppoints')->name('ajaxstoppoints');
        });
        //配置管理
        Route::controller(\App\Http\Controllers\Admin\System\ConfigController::class)->prefix('config')->name('config.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/site', 'site')->name('site');
            Route::post('/dropall', 'dropall')->name('dropall');
        });
        //组织部门
        Route::controller(\App\Http\Controllers\Admin\System\StructController::class)->prefix('struct')->name('struct.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
            Route::any('/tree', 'tree')->name('tree');
        });
        //青政通组织部门
        Route::controller(\App\Http\Controllers\Admin\System\QztstructController::class)->prefix('qztstruct')->name('qztstruct.')->group(function (){
            Route::any('/index', 'index')->name('index');
        });
        //青政通用户
        Route::controller(\App\Http\Controllers\Admin\System\QztuserController::class)->prefix('qztuser')->name('qztuser.')->group(function (){
            Route::any('/index', 'index')->name('index');
        });
        //职位管理
        Route::controller(\App\Http\Controllers\Admin\System\PositionController::class)->prefix('position')->name('position.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/dropall', 'dropall')->name('dropall');
        });
        //职级管理
        Route::controller(\App\Http\Controllers\Admin\System\PositionLevelController::class)->prefix('positionlevel')->name('positionlevel.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/dropall', 'dropall')->name('dropall');
        });
        //后台菜单管理
        Route::controller(\App\Http\Controllers\Admin\System\MenuController::class)->prefix('menu')->name('menu.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/tree', 'tree')->name('tree');
        });
        //业务端菜单
        Route::controller(\App\Http\Controllers\Admin\System\AppMenuController::class)->prefix('appmenu')->name('appmenu.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/tree', 'tree')->name('tree');
        });
        //调度端菜单
        Route::controller(\App\Http\Controllers\Admin\System\ManageMenuController::class)->prefix('managemenu')->name('managemenu.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/tree', 'tree')->name('tree');
        });
        //角色管理
        Route::controller(\App\Http\Controllers\Admin\System\RoleController::class)->prefix('role')->name('role.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/admin', 'admin')->name('admin');
            Route::post('/setstatus', 'setstatus')->name('setstatus');
            Route::any('/auth', 'auth')->name('auth');
            Route::any('/datascope', 'datascope')->name('datascope');
        });
        //业务端角色管理
        Route::controller(\App\Http\Controllers\Admin\System\AppRoleController::class)->prefix('approle')->name('approle.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/admin', 'admin')->name('admin');
            Route::post('/setstatus', 'setstatus')->name('setstatus');
            Route::any('/auth', 'auth')->name('auth');
            Route::any('/datascope', 'datascope')->name('datascope');
        });
        //调度端角色管理
        Route::controller(\App\Http\Controllers\Admin\System\ManageRoleController::class)->prefix('managerole')->name('managerole.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/admin', 'admin')->name('admin');
            Route::post('/setstatus', 'setstatus')->name('setstatus');
            Route::any('/auth', 'auth')->name('auth');
            Route::any('/datascope', 'datascope')->name('datascope');
        });
        Route::controller(\App\Http\Controllers\Admin\System\AdminRoleController::class)->prefix('adminrole')->name('adminrole.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::post('/drop', 'drop')->name('drop');
        });

        Route::controller(\App\Http\Controllers\Admin\System\AppUserRoleController::class)->prefix('appuserrole')->name('appuserrole.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::post('/drop', 'drop')->name('drop');
        });

        Route::controller(\App\Http\Controllers\Admin\System\AppManageRoleController::class)->prefix('appmanagerole')->name('appmanagerole.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::post('/drop', 'drop')->name('drop');
        });
        //人员管理
        Route::controller(\App\Http\Controllers\Admin\System\AdminController::class)->prefix('admin')->name('admin.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/setstatus', 'setstatus')->name('setstatus');
            Route::post('/dropall', 'dropall')->name('dropall');
            Route::any('/tree', 'tree')->name('tree');
            Route::post('/ajaxtreelist', 'ajaxtreelist')->name('ajaxtreelist');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        //人员管理
        Route::controller(\App\Http\Controllers\Admin\System\UserController::class)->prefix('user')->name('user.')->group(function (){
            Route::any('/tree', 'tree')->name('tree');
            Route::post('/ajaxtreelist', 'ajaxtreelist')->name('ajaxtreelist');
        });
        //网格员
        Route::controller(\App\Http\Controllers\Admin\System\GridUserController::class)->prefix('griduser')->name('griduser.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        //微网格员
        Route::controller(\App\Http\Controllers\Admin\System\MicrogridUserController::class)->prefix('microgriduser')->name('microgriduser.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        //消防队员
        Route::controller(\App\Http\Controllers\Admin\System\FirefighterController::class)->prefix('firefighter')->name('firefighter.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/relevance', 'relevance')->name('relevance');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        //综治人员
        Route::controller(\App\Http\Controllers\Admin\System\ComprehensiveController::class)->prefix('comprehensive')->name('comprehensive.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/relevance', 'relevance')->name('relevance');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        //城管队员
        Route::controller(\App\Http\Controllers\Admin\System\UrbanController::class)->prefix('urban')->name('urban.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/relevance', 'relevance')->name('relevance');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        //巡查人员
        Route::controller(\App\Http\Controllers\Admin\System\PatrolUserController::class)->prefix('patroluser')->name('patroluser.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        Route::controller(\App\Http\Controllers\Admin\System\StructRegionController::class)->prefix('structregion')->name('structregion.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/region', 'region')->name('region');
        });
        //登录日志
        Route::controller(\App\Http\Controllers\Admin\System\LoginlogController::class)->prefix('loginlog')->name('loginlog.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::post('/dropall', 'dropall')->name('dropall');
        });
        //自定义字段
        Route::controller(\App\Http\Controllers\Admin\System\SelffieldController::class)->prefix('selffield')->name('selffield.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //工作配置
        Route::controller(\App\Http\Controllers\Admin\System\ExamineConfigController::class)->prefix('examineconfig')->name('examineconfig.')->group(function (){
            Route::any('/griduser', 'griduser')->name('griduser');
            Route::any('/urban', 'urban')->name('urban');
            Route::any('/firefighter', 'firefighter')->name('firefighter');
            Route::any('/comprehensive', 'comprehensive')->name('comprehensive');
            Route::any('/event', 'event')->name('event');
        });
        //网格员巡防范围
        Route::controller(\App\Http\Controllers\Admin\System\GridUserWatchScopeController::class)->prefix('griduserwatchscope')->name('griduserwatchscope.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //网格员打卡范围
        Route::controller(\App\Http\Controllers\Admin\System\GridUserClockScopeController::class)->prefix('griduserclockscope')->name('griduserclockscope.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //消防员打卡范围
        Route::controller(\App\Http\Controllers\Admin\System\FirefighterClockScopeController::class)->prefix('firefighterclockscope')->name('firefighterclockscope.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //综治人员打卡范围
        Route::controller(\App\Http\Controllers\Admin\System\ComprehensiveClockScopeController::class)->prefix('comprehensiveclockscope')->name('comprehensiveclockscope.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //城管队员打卡范围
        Route::controller(\App\Http\Controllers\Admin\System\UrbanClockScopeController::class)->prefix('urbanclockscope')->name('urbanclockscope.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //节假日
        Route::controller(\App\Http\Controllers\Admin\System\HolidaysController::class)->prefix('holidays')->name('holidays.')->group(function (){
            Route::any('/index', 'index')->name('index');
        });
        Route::controller(\App\Http\Controllers\Admin\System\LeaveTypeController::class)->prefix('leavetype')->name('leavetype.')->group(function (){
            Route::any('/index', 'index')->name('index');
        });
        //字典管理
        Route::controller(\App\Http\Controllers\Admin\System\DictTypeController::class)->prefix('dicttype')->name('dicttype.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //字典数据
        Route::controller(\App\Http\Controllers\Admin\System\DictDataController::class)->prefix('dictdata')->name('dictdata.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });

        //巡查
        Route::controller(\App\Http\Controllers\Admin\System\PatrolController::class)->prefix('patrol')->name('patrol.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/setstatus', 'setstatus')->name('setstatus');
            Route::any('/ajaxmultiple', 'ajaxmultiple')->name('ajaxmultiple');
            Route::any('/ajaxmultipleindex', 'ajaxmultipleindex')->name('ajaxmultipleindex');
        });
        //上报
        Route::controller(\App\Http\Controllers\Admin\System\ReportController::class)->prefix('report')->name('report.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/setstatus', 'setstatus')->name('setstatus');
        });
        // 居民标签管理
        Route::controller(\App\Http\Controllers\Admin\System\LabelGroupController::class)->prefix('labelgroup')->name('labelgroup.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //城管分组
        Route::controller(\App\Http\Controllers\Admin\System\UrbanGroupController::class)->prefix('urbangroup')->name('urbangroup.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //网格管理
        Route::controller(\App\Http\Controllers\Admin\System\GridGridController::class)->prefix('gridgrid')->name('gridgrid.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/region', 'region')->name('region');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });
        // 微网格管理
        Route::controller(\App\Http\Controllers\Admin\System\GridMicrogridController::class)->prefix('gridmicrogrid')->name('gridmicrogrid.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/dropall', 'dropall')->name('dropall');
            Route::post('/import', 'import')->name('import');
            Route::post('/export', 'export')->name('export');
        });

        // 出勤配置
        Route::controller(\App\Http\Controllers\Admin\System\KaoqinApprovalLevelsController::class)->prefix('kaoqinapprovallevels')->name('kaoqinapprovallevels.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/config', 'config')->name('config');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/dropall', 'dropall')->name('dropall');
        });
        //考勤打卡
        Route::controller(\App\Http\Controllers\Admin\System\ClockController::class)->prefix('clock')->name('clock.')->group(function (){
            Route::any('/griduser', 'griduser')->name('griduser');
            Route::any('/urban', 'urban')->name('urban');
            Route::any('/firefighter', 'firefighter')->name('firefighter');
            Route::any('/comprehensive', 'comprehensive')->name('comprehensive');
        });
        //城管排班
        Route::controller(\App\Http\Controllers\Admin\System\UrbanSchedulingController::class)->prefix('urbanscheduling')->name('urbanscheduling.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/type', 'type')->name('type');
            Route::any('/scheduling', 'scheduling')->name('scheduling');
            Route::any('/schedulingexport', 'schedulingExport')->name('schedulingexport');
            Route::any('/schedulingimport', 'schedulingImport')->name('schedulingimport');
        });
        //网格员假期余额
        Route::controller(\App\Http\Controllers\Admin\System\GridUserLeaveBalanceController::class)->prefix('griduserleavebalance')->name('griduserleavebalance.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/edit', 'edit')->name('edit');
            Route::any('/clear', 'clear')->name('clear');
            Route::any('/log', 'log')->name('log');
            Route::post('/export', 'export')->name('export');
            Route::post('/import', 'import')->name('import');
        });
        //城管队员假期余额
        Route::controller(\App\Http\Controllers\Admin\System\UrbanLeaveBalanceController::class)->prefix('urbanleavebalance')->name('urbanleavebalance.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/edit', 'edit')->name('edit');
            Route::any('/clear', 'clear')->name('clear');
            Route::any('/log', 'log')->name('log');
            Route::post('/export', 'export')->name('export');
            Route::post('/import', 'import')->name('import');
        });
        //消防队员假期余额
        Route::controller(\App\Http\Controllers\Admin\System\FirefighterLeaveBalanceController::class)->prefix('firefighterleavebalance')->name('firefighterleavebalance.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/edit', 'edit')->name('edit');
            Route::any('/clear', 'clear')->name('clear');
            Route::any('/log', 'log')->name('log');
            Route::post('/export', 'export')->name('export');
            Route::post('/import', 'import')->name('import');
        });
        //综治人员假期余额
        Route::controller(\App\Http\Controllers\Admin\System\ComprehensiveLeaveBalanceController::class)->prefix('comprehensiveleavebalance')->name('comprehensiveleavebalance.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/edit', 'edit')->name('edit');
            Route::any('/clear', 'clear')->name('clear');
            Route::any('/log', 'log')->name('log');
            Route::post('/export', 'export')->name('export');
            Route::post('/import', 'import')->name('import');
        });
        //网格员审批流程
        Route::controller(\App\Http\Controllers\Admin\System\GridUserApprovalProcessController::class)->prefix('griduserapprovalprocess')->name('griduserapprovalprocess.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //城管队员审批流程
        Route::controller(\App\Http\Controllers\Admin\System\UrbanApprovalProcessController::class)->prefix('urbanapprovalprocess')->name('urbanapprovalprocess.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //消防队员审批流程
        Route::controller(\App\Http\Controllers\Admin\System\FirefighterApprovalProcessController::class)->prefix('firefighterapprovalprocess')->name('firefighterapprovalprocess.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //综治人员审批流程
        Route::controller(\App\Http\Controllers\Admin\System\ComprehensiveApprovalProcessController::class)->prefix('comprehensiveapprovalprocess')->name('comprehensiveapprovalprocess.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });

        // 工作下派-工作级别管理
        Route::controller(\App\Http\Controllers\Admin\System\WorkAssignLevelController::class)->prefix('workassignlevel')->name('workassignlevel.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        // 工作下派-工作类别管理
        Route::controller(\App\Http\Controllers\Admin\System\WorkAssignCategoryController::class)->prefix('workassigncategory')->name('workassigncategory.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });

        //商铺行业类别
        Route::controller(\App\Http\Controllers\Admin\System\IndustryController::class)->prefix('industry')->name('industry.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/tree', 'tree')->name('tree');
            Route::any('/ajax', 'ajax')->name('ajax');
        });
        // 版本管理
        Route::controller(\App\Http\Controllers\Admin\System\AppVersionController::class)->prefix('appversion')->name('appversion.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //事件类别
        Route::controller(\App\Http\Controllers\Admin\System\EventCategoryController::class)->prefix('eventcategory')->name('eventcategory.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //事件经办人
        Route::controller(\App\Http\Controllers\Admin\System\EventOperatorController::class)->prefix('eventoperator')->name('eventoperator.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/setting', 'setting')->name('setting');
        });
        //事件配置-大联动账号-社区管理员
        Route::controller(\App\Http\Controllers\Admin\System\LinkageCommunityController::class)->prefix('linkagecommunity')->name('linkagecommunity.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //事件配置-大联动账号-网格员
        Route::controller(\App\Http\Controllers\Admin\System\LinkageGridUserController::class)->prefix('linkagegriduser')->name('linkagegriduser.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/import', 'import')->name('import');
        });
        //事件配置-清单制-管理员
        Route::controller(\App\Http\Controllers\Admin\System\QdzAdminController::class)->prefix('qdzadmin')->name('qdzadmin.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::any('/qdzdefault', 'qdzdefault')->name('qdzdefault');
        });
        //事件配置-清单制-网格员
        Route::controller(\App\Http\Controllers\Admin\System\QdzGridUserController::class)->prefix('qdzgriduser')->name('qdzgriduser.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/import', 'import')->name('import');
        });
        Route::controller(\App\Http\Controllers\Admin\System\UserOnlineController::class)->prefix('useronline')->name('useronline.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/detail', 'detail')->name('detail');
            Route::any('/export', 'export')->name('export');
        });
        Route::controller(\App\Http\Controllers\Admin\System\StatsController::class)->prefix('stats')->name('stats.')->group(function (){
            Route::any('/griduser', 'griduser')->name('griduser');
            Route::any('/urban', 'urban')->name('urban');
            Route::any('/firefighter', 'firefighter')->name('firefighter');
            Route::any('/comprehensive', 'comprehensive')->name('comprehensive');
            Route::post('/ajaxkqdk', 'ajaxkqdk')->name('ajaxkqdk');
            Route::post('/ajaxrwxp', 'ajaxrwxp')->name('ajaxrwxp');
            Route::post('/ajaxxf', 'ajaxxf')->name('ajaxxf');
            Route::post('/ajaxrh', 'ajaxrh')->name('ajaxrh');
            Route::post('/ajaxrsp', 'ajaxrsp')->name('ajaxrsp');
            Route::post('/ajaxzdysb', 'ajaxzdysb')->name('ajaxzdysb');
            Route::post('/ajaxsj', 'ajaxsj')->name('ajaxsj');
            Route::post('/ajaxzdyxc', 'ajaxzdyxc')->name('ajaxzdyxc');
            Route::post('/ajaxaqxc', 'ajaxaqxc')->name('ajaxaqxc');
        });
    });
    //街道社区工作
    Route::prefix('street')->name('street.')->group(function (){
        //小区管理
        Route::controller(\App\Http\Controllers\Admin\Street\QuartersController::class)->prefix('quarters')->name('quarters.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/import', 'import')->name('import');
        });
        //房屋管理
        Route::controller(\App\Http\Controllers\Admin\Street\HouseController::class)->prefix('house')->name('house.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/drop', 'drop')->name('drop');
        });
        //居民管理
        Route::controller(\App\Http\Controllers\Admin\Street\ResidentController::class)->prefix('resident')->name('resident.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/ajaxcount', 'ajaxcount')->name('ajaxcount');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/ajaxres', 'ajaxres')->name('ajaxres');
            Route::any('/ajaxqes', 'ajaxqes')->name('ajaxqes');
            Route::post('/ajaxverifyidcard', 'ajaxverifyidcard')->name('ajaxverifyidcard');
            Route::post('/drop', 'drop')->name('drop');
            Route::get('/detail', 'detail')->name('detail');
            Route::post('/dropall', 'dropall')->name('dropall');
            Route::any('/import', 'import')->name('import');
            Route::any('/export', 'export')->name('export');
            Route::any('/infoexport', 'infoexport')->name('infoexport');
        });
        //居民信息变更记录管理
        Route::controller(\App\Http\Controllers\Admin\Street\ResidentlogController::class)->prefix('residentlog')->name('residentlog.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
        });
        //商铺管理
        Route::controller(\App\Http\Controllers\Admin\Street\ShopController::class)->prefix('shop')->name('shop.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::any('/detail', 'detail')->name('detail');
            Route::post('/drop', 'drop')->name('drop');
            Route::post('/dropall', 'dropall')->name('dropall');
            Route::any('/import', 'import')->name('import');
            Route::any('/export', 'export')->name('export');
        });
        // 商铺信息变更记录管理
        Route::prefix('shop')->name('shop.')->group(function () {
            Route::controller(\App\Http\Controllers\Admin\Street\ShopChangeLogsController::class)->prefix('shopchangelogs')->name('shopchangelogs.')->group(function (){
                Route::any('/index', 'index')->name('index');
                Route::any('/add', 'add')->name('add');
                Route::any('/edit', 'edit')->name('edit');
                Route::post('/drop', 'drop')->name('drop');
                Route::post('/dropall', 'dropall')->name('dropall');
            });
        });
        // 考勤记录管理
        Route::controller(\App\Http\Controllers\Admin\Street\KaoqinController::class)->prefix('kaoqin')->name('kaoqin.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/grid', 'grid')->name('grid');
            Route::any('/cg', 'cg')->name('cg');
            Route::any('/xf', 'xf')->name('xf');
            Route::any('/zz', 'zz')->name('zz');
        });
        // 审批记录管理、我的待办审批
        Route::controller(\App\Http\Controllers\Admin\Street\ApprovalController::class)->prefix('approval')->name('approval.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/task', 'task')->name('task');
            Route::any('/event', 'event')->name('event');
            Route::any('/attendance', 'attendance')->name('attendance');
        });
        // 加班申请记录管理
        Route::controller(\App\Http\Controllers\Admin\Street\KaoqinJiabanController::class)->prefix('kaoqinjiaban')->name('kaoqinjiaban.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/audit', 'audit')->name('audit');
        });
        // 请假调休申请记录管理
        Route::controller(\App\Http\Controllers\Admin\Street\KaoqinLeaveController::class)->prefix('kaoqinleave')->name('kaoqinleave.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/audit', 'audit')->name('audit');
        });
        // 外出申请记录管理
        Route::controller(\App\Http\Controllers\Admin\Street\KaoqinOutController::class)->prefix('kaoqinout')->name('kaoqinout.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/audit', 'audit')->name('audit');
        });

        // 事件管理
        Route::controller(\App\Http\Controllers\Admin\Street\EventListController::class)->prefix('eventlist')->name('eventlist.')->group(function (){
            Route::any('/index', 'index')->name('index');//事件列表
            Route::any('/add', 'add')->name('add');//发起事件
            Route::post('/cancel', 'cancel')->name('cancel');//取消事件
            Route::any('/reissue', 'reissue')->name('reissue');//重新发起事件
            Route::any('/detail', 'detail')->name('detail');//事件详情
            Route::any('/fghandle', 'fghandle')->name('fghandle');//分管领导处理
            Route::any('/fgback', 'fgback')->name('fgback');//分管领导回退
            Route::any('/zyhandle', 'zyhandle')->name('zyhandle');//主要领导处理
        });
        Route::controller(\App\Http\Controllers\Admin\Street\EventStructController::class)->prefix('eventstruct')->name('eventstruct.')->group(function (){
            Route::any('/backassign', 'backAssign')->name('backassign');//回退
            Route::any('/ophandle', 'ophandle')->name('ophandle');//科室经办人处理
            Route::any('/slhandle', 'slhandle')->name('slhandle');//科室科长处理
            Route::any('/dispose', 'dispose')->name('dispose');//处置
            Route::any('/disposeaudit', 'disposeAudit')->name('disposeaudit');//处置审核
            Route::get('/disposedetail', 'disposeDetail')->name('disposedetail');//处置详情
        });
        Route::controller(\App\Http\Controllers\Admin\Street\RiskController::class)->prefix('risk')->name('risk.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::any('/tree', 'tree')->name('tree');
            Route::post('/drop', 'drop')->name('drop');
        });
        Route::controller(\App\Http\Controllers\Admin\Street\RiskTypeController::class)->prefix('risktype')->name('risktype.')->group(function (){
            Route::any('/tree', 'tree')->name('tree');
            Route::any('/ajaxsitetemplate', 'ajaxsitetemplate')->name('ajaxsitetemplate');
        });

        // 大联动
        Route::controller(\App\Http\Controllers\Admin\Street\DldEventController::class)->prefix('dldevent')->name('dldevent.')->group(function (){
            Route::any('/index', 'index')->name('index');//事件列表
            Route::any('/detail', 'detail')->name('detail');//事件详情
            Route::any('/sign', 'sign')->name('sign');//签收
            Route::any('/assign', 'assign')->name('assign');//派发
            Route::any('/verify', 'verify')->name('verify');//审核
            Route::any('/completed', 'completed')->name('completed');//办结
            Route::any('/cancel', 'cancel')->name('cancel');//作废
            Route::post('/export', 'export')->name('export');
        });
    });
    //网格
    Route::prefix('grid')->name('grid.')->group(function (){
        //网格巡防进度
        Route::controller(\App\Http\Controllers\Admin\Grid\PatrolController::class)->prefix('patrol')->name('patrol.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::any('/userlist', 'userlist')->name('userlist');
        });
        //网格巡防详细记录
        Route::controller(\App\Http\Controllers\Admin\Grid\WatchmanController::class)->prefix('watchman')->name('watchman.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::get('/detail', 'detail')->name('detail');
        });
        //网格上报记录
        Route::controller(\App\Http\Controllers\Admin\Grid\ReportController::class)->prefix('report')->name('report.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::any('/import', 'import')->name('import');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/instructions', 'instructions')->name('instructions');
            Route::any('/instructionsedit', 'instructionsedit')->name('instructionsedit');
        });
        //网格入户进度
        Route::controller(\App\Http\Controllers\Admin\Grid\ScheduleController::class)->prefix('schedule')->name('schedule.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::any('/userlist', 'userlist')->name('userlist');
        });
        //网格入户详细记录
        Route::controller(\App\Http\Controllers\Admin\Grid\HoldhouseController::class)->prefix('holdhouse')->name('holdhouse.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::get('/detail', 'detail')->name('detail');
        });
        //商铺入户进度
        Route::controller(\App\Http\Controllers\Admin\Grid\ShopscheduleController::class)->prefix('shopschedule')->name('shopschedule.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::any('/userlist', 'userlist')->name('userlist');
        });
        //商铺入户详细记录
        Route::controller(\App\Http\Controllers\Admin\Grid\HoldshopController::class)->prefix('holdshop')->name('holdshop.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::get('/detail', 'detail')->name('detail');
        });

        // 工作下派管理
        Route::controller(\App\Http\Controllers\Admin\Grid\WorkAssignController::class)->prefix('workassign')->name('workassign.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/add', 'add')->name('add');
            Route::any('/edit', 'edit')->name('edit');
            Route::post('/cancel', 'cancel')->name('cancel');
            Route::post('/drop', 'drop')->name('drop');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/assigngriduser', 'assignGridUser')->name('assigngriduser');
            Route::any('/assigncommunity', 'assignCommunity')->name('assigncommunity');
            Route::any('/assign', 'assign')->name('assign');
            Route::any('/citycentreaudit', 'citycentreaudit')->name('citycentreaudit');
            Route::any('/assignhelp', 'assignHelp')->name('assignhelp');
            Route::any('/structcc', 'structCC')->name('structcc');
            Route::any('/export', 'export')->name('export');
            Route::any('/communitydetail', 'communitydetail')->name('communitydetail');
        });
        Route::controller(\App\Http\Controllers\Admin\Grid\WorkAssignCommunityController::class)->prefix('workassigncommunity')->name('workassigncommunity.')->group(function (){
            Route::any('/audit', 'audit')->name('audit');
            Route::any('/auditdetail', 'auditdetail')->name('auditdetail');
            Route::any('/detail', 'detail')->name('detail');
            Route::any('/report', 'report')->name('report');
            Route::any('/backgrid', 'backgrid')->name('backgrid');
        });
        Route::controller(\App\Http\Controllers\Admin\Grid\WorkAssignGridController::class)->prefix('workassigngrid')->name('workassigngrid.')->group(function (){
            Route::any('/audit', 'audit')->name('audit');
            Route::any('/detail', 'detail')->name('detail');
        });
    });
    //城管
    Route::prefix('urban')->name('urban.')->group(function (){
        //城管上报记录
        Route::controller(\App\Http\Controllers\Admin\Urban\ReportController::class)->prefix('report')->name('report.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/instructions', 'instructions')->name('instructions');
            Route::any('/instructionsedit', 'instructionsedit')->name('instructionsedit');
        });
        //城管巡查记录
        Route::controller(\App\Http\Controllers\Admin\Urban\PatrolController::class)->prefix('patrol')->name('patrol.')->group(function (){
            Route::any('/statistics', 'statistics')->name('statistics');
            Route::any('/ajaxgrouplist', 'ajaxgrouplist')->name('ajaxgrouplist');
            Route::any('/index', 'index')->name('index');
            Route::get('/detail', 'detail')->name('detail');
        });
        //城管轨迹记录
        Route::controller(\App\Http\Controllers\Admin\Urban\TrajectoryController::class)->prefix('trajectory')->name('trajectory.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::get('/viewguiji', 'viewguiji')->name('viewguiji');
        });
        //数字城管
        Route::controller(\App\Http\Controllers\Admin\Urban\SzcgController::class)->prefix('szcg')->name('szcg.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/detail', 'detail')->name('detail');
            Route::any('/audit', 'audit')->name('audit');
            Route::any('/handle', 'handle')->name('handle');
            Route::any('/delay', 'delay')->name('delay');
        });
    });
    //综治
    Route::prefix('comprehensive')->name('comprehensive.')->group(function (){
        //综治上报记录
        Route::controller(\App\Http\Controllers\Admin\Comprehensive\ReportController::class)->prefix('report')->name('report.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::any('/export', 'export')->name('export');
            Route::get('/detail', 'detail')->name('detail');
            Route::any('/instructions', 'instructions')->name('instructions');
            Route::any('/instructionsedit', 'instructionsedit')->name('instructionsedit');
        });
        //城管轨迹记录
        Route::controller(\App\Http\Controllers\Admin\Comprehensive\TrajectoryController::class)->prefix('trajectory')->name('trajectory.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::get('/viewguiji', 'viewguiji')->name('viewguiji');
        });
    });
    //消防工作
    Route::prefix('fire')->name('fire.')->group(function (){
        //综治上报记录
        Route::controller(\App\Http\Controllers\Admin\Fire\PatrolController::class)->prefix('patrol')->name('patrol.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::get('/detail', 'detail')->name('detail');
            Route::get('/ajaxcate1', 'ajaxcate1')->name('ajaxcate1');
            Route::get('/ajaxcate2', 'ajaxcate2')->name('ajaxcate2');
        });
        //城管轨迹记录
        Route::controller(\App\Http\Controllers\Admin\Fire\DangerController::class)->prefix('danger')->name('danger.')->group(function (){
            Route::any('/index', 'index')->name('index');
            Route::post('/export', 'export')->name('export');
            Route::get('/detail', 'detail')->name('detail');
        });
    });
    //工具模块
    Route::prefix('tool')->group(function (){
        Route::controller(\App\Http\Controllers\Admin\Tool\FormController::class)->prefix('form')->group(function (){
            Route::get('/build', 'build');
        });
        Route::controller(\App\Http\Controllers\Admin\Tool\GenController::class)->prefix('gen')->group(function (){
            Route::any('/create', 'create');
        });
        Route::controller(\App\Http\Controllers\Admin\Tool\UploadController::class)->prefix('upload')->group(function (){
            Route::any('/index', 'index');
            Route::any('/add', 'add');
            Route::any('/edit', 'edit');
            Route::post('/drop', 'drop');
            Route::post('/dropall', 'dropall');
        });
    });
});




